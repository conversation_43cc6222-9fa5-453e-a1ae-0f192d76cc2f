from datetime import datetime, <PERSON>elta
import logging
import os
import time
from fastapi import FastAPI, APIRouter
from tortoise import Tortoise
from tortoise.contrib.fastapi import register_tortoise
from dotenv import load_dotenv

load_dotenv()

from apscheduler.schedulers.asyncio import AsyncIOScheduler

from tasks.statistic import (
    group_hot_stat,
    role_daily_hot_stat,
    role_fav_stat,
    role_hot_stat,
    role_rank_stat,
)


import sentry_sdk
import uvicorn


logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s (%(filename)s:%(lineno)d) [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()],
)
log = logging.getLogger(__name__)

dsn = os.getenv("SENTRY_DSN")
sentry_sdk.init(
    dsn=dsn,
    traces_sample_rate=1.0,
    profiles_sample_rate=1.0,
)
app = FastAPI()

router = APIRouter()

scheduler = AsyncIOScheduler()


@router.post("/statistic/run")
async def task_run(task_name: str = ""):
    jobs = scheduler.get_jobs()
    job_names = [job.func_ref for job in jobs]
    for job in jobs:
        if job.func_ref == task_name:
            await job.func()
            return {"status": "ok", "jobs": job_names}
    return {"status": "error", "msg": "task not found", "jobs": job_names}


@router.get("/statistic/start_daily_hot")
async def start_daily_hot(date_index: int = 0):

    if date_index:
        await role_daily_hot_stat.start_job(date_index=date_index)
        return {"status": "ok"}

    # 获取上个月开始时间
    now = datetime.now()

    start_datetime = datetime(now.year, now.month - 1, 1)
    end_datetime = now - timedelta(days=1)
    while start_datetime < end_datetime:
        await role_daily_hot_stat.start_job(
            date_index=int(start_datetime.strftime("%Y%m%d"))
        )
        start_datetime += timedelta(days=1)
    return {"status": "ok"}


@router.get("/statistic/init_rank")
async def init_run_week_and_run_month():
    await role_rank_stat.init_run_week_and_run_month()
    return {"status": "ok"}


app.include_router(router)

Tortoise.init_models(["persistence.models.models"], "models")
register_tortoise(
    app=app,
    db_url=os.environ["MYSQL_URL"],
    modules={"models": ["persistence.models.models"]},
    generate_schemas=False,
)


@app.on_event("startup")
async def startup_event():
    # 每隔5分钟 准点执行一次
    scheduler.add_job(role_fav_stat.start_job, "cron", minute="7,17,27,37,47,57")
    scheduler.add_job(group_hot_stat.start_job, "cron", minute="20,50")
    scheduler.add_job(role_daily_hot_stat.start_job, "cron", hour="12")
    scheduler.add_job(role_hot_stat.start_job, "cron", minute="10,40")
    scheduler.start()


if __name__ == "__main__":
    
    uvicorn.run(app, host="0.0.0.0", port=8904)
