{
    "code": 0,
    "data": {
        "total": 3,
        "size": 10,
        "pages": 1,
        "current": 1,
        "records": [
            {
                "id": 123,
                "keyword_type": 2,
                "keyword_list": [
                    "咨询"
                ],
                "msg_type": 1,
                "msg_content": {
                    "msg_type": 1,
                    "msg_text": "咨询请联系<br>"
                },
                "msg_btn_list": null,
                "btn_page_size": 0,
                "delete_robot_msg": "60-s",
                "delete_src_msg": "0",
                "memo": "备注",
                "skip_admin": 0,
                "is_quote": 1,
                "is_enable": 1,
                "fail_content": "",
                "create_time": "2024-11-14 14:43:48"
            },
            {
                "sn": "AR173156705967",
                "custom_sn": "AR173156705967",
                "keyword_type": 1,
                "keyword_list": [
                    "投诉",
                    "反馈"
                ],
                "msg_type": 2,
                "msg_content": {
                    "msg_type": 2,
                    "msg_media_id": "AgADBAADJqgxG-xyzxyzxyz"
                },
                "msg_btn_list": [
                    {
                        "text": "联系客服",
                        "url": "https://example.com/contact"
                    }
                ],
                "btn_page_size": 1,
                "delete_robot_msg": "30-s",
                "delete_src_msg": "1",
                "memo": "投诉和反馈处理",
                "skip_admin": 1,
                "is_quote": 0,
                "is_enable": 1,
                "fail_content": "抱歉，无法处理您的请求。",
                "create_time": "2024-11-13 10:20:30"
            }
        ]
    }
}



{
    "bot_name": "bg_c919",
    "token": "6500199092:AAGwgTte0h2BoT-W2PZKhfjrCo4zgyvUzTY",
    "group_id": -1002463227172,
    "welcome_config": {},
    "g_white_users": ["5359176631"]
}


  {
    "group_id": -1002463227172,
    "keyword_type": 2,
    "keyword_list": "卡密,激活,密钥,兑换码,充值问题",
    "msg_type": 1,
    "msg_content": {
        "msg_type": 1,
        "msg_text": "官方客服可以帮您解决@ai748_bot"
    },
    "del_bot_msg_delay": 0,
    "del_src_msg_delay": 0,
    "rule_desc": "福利的自动回复",
    "skip_admin": true,
    "is_quote": false,
    "is_enable": true
  }


  {
    "group_id": -1002463227172,
    "keyword_type": 2,
    "keyword_list": "福利",
    "msg_type": 1,
    "msg_content": {
        "msg_type": 1,
        "msg_text": "薅羊毛获得钻石的方式，点击查看https://t.me/playai888/8552/403128"
    },
    "del_bot_msg_delay": 0,
    "del_src_msg_delay": 0,
    "rule_desc": "福利的自动回复",
    "skip_admin": true,
    "is_quote": false,
    "is_enable": true
  }

  {
    "group_id": -1002463227172,
    "keyword_type": 2,
    "keyword_list": "格式,角色设定",
    "msg_type": 1,
    "msg_content": {
        "msg_type": 1,
        "msg_text": "可以在创作者群置顶消息查看作为参考，点击查看https://t.me/playai888/20715/20744"
    },
    "del_bot_msg_delay": 0,
    "del_src_msg_delay": 0,
    "rule_desc": "geshi的自动回复",
    "skip_admin": true,
    "is_quote": false,
    "is_enable": true
  }

  {
    "group_id": -1002463227172,
    "keyword_type": 2,
    "keyword_list": "客服",
    "msg_type": 1,
    "msg_content": {
        "msg_type": 1,
        "msg_text": "官方客服可以帮您解决@ai748_bot"
    },
    "del_bot_msg_delay": 0,
    "del_src_msg_delay": 0,
    "rule_desc": "客服自动回复",
    "skip_admin": true,
    "is_quote": false,
    "is_enable": true
  }


  {
    "group_id": -1002463227172,
    "keyword_type": 2,
    "keyword_list": "创建角色卡,创建群聊",
    "msg_type": 1,
    "msg_content": {
        "msg_type": 1,
        "msg_text": "打开小程序，顶部-创建。小程序入口https://t.me/FancyTavernBot?start=u_118"
    },
    "del_bot_msg_delay": 0,
    "del_src_msg_delay": 0,
    "rule_desc": "角色卡的回复",
    "skip_admin": true,
    "is_quote": false,
    "is_enable": true
  }

    {
    "group_id": -1002463227172,
    "keyword_type": 2,
    "keyword_list": "导入角色卡",
    "msg_type": 1,
    "msg_content": {
        "msg_type": 1,
        "msg_text": "打开小程序，顶部，创建-导入角色，兼容其他平台：酒馆AIv1、酒馆AIv2、BOT3 AI、Chub、Text Generation、Spicychat、Charstar、Crushon等，目前仅兼容PNG格式，请在这些平台导出。\n
        小程序入口https://t.me/FancyTavernBot?start=u_118"
    },
    "del_bot_msg_delay": 0,
    "del_src_msg_delay": 0,
    "rule_desc": "导入角色卡的回复",
    "skip_admin": true,
    "is_quote": false,
    "is_enable": true
  }

  {
    "group_id": -1002463227172,
    "keyword_type": 2,
    "keyword_list": "怎么玩,萌新不会",
    "msg_type": 1,
    "msg_content": {
        "msg_type": 1,
        "msg_text": "Ai角色陪聊，拥有海量角色，玩法多样！角色卡分享：https://t.me/huanmengrole\n群规：https://t.me/playai888/8552/160151\n小程序bot快速入口 @FancyTavernBot\n极速版bot快速入口 @FancyAI2Bot\n官方客服 @ai748_bot"
    },
    "del_bot_msg_delay": 0,
    "del_src_msg_delay": 0,
    "rule_desc": "怎么玩/萌新不会的回复",
    "skip_admin": true,
    "is_quote": false,
    "is_enable": true
  }


  
  ## 关于spam的配置

  link的检测

  {
    "rubbish_type": 1,
    "rubbish_desc": "link的检测",
    "rubbish_condition": "string",
    "rubbish_action": {
      "kick": {
        "kickTime": 0,
        "isKick": 0
      },
      "alert": {
        "isAlert": 1,
        "alertTimes": 0
      },
      "delete": {
        "isDel": 1
      },
      "forbid": {
        "isForbid": 0,
        "forbidTime": 0
      }
    },
    "rubbish_sort": 100,
    "is_enable": true,
    "is_master": false,
    "group_id": -1002463227172,
    "delete_memo": 0,
    "check_user_type": "all",
    "white_link_list":  [
        "https://t.me/FancyTavernBot?start=u_118",
        "https://t.me/FancyAI2Bot?start=u_119",
        "https://t.me/huanmengrole",
        "@ai748_bot",
        "@aihezuo01_bot",
        "@playai888",
        "@FancyTavernBot",
        "@FancyAI2Bot",
        "@huanmengrole",
        "@FancyAIHelperBot",
        "@FancyAIHelper1Bot",
        "@FancyAIHelper2Bot",
        "@FancyAIHelper3Bot",
        "@FancyAIHelper4Bot",
        "@humeng888_bot",
        "@huanmeng888_chat_bot",
        "@huanmeng999_bot",
        "@huanmeng999_chat_bot",
        "@huanmengai_bot",
        "@huanmengaibot",
        "@huanmeng888_bot"
    ],
    "white_user_list": []
  }

  1.添加一个机器人到群组并给予机器人权限～
  2.通过addbot进去群组的管理
  3. webhook的域名问题：线上的
  4. spam和自动回复的设置～