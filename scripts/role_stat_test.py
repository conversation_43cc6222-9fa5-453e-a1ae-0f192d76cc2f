# %%
from datetime import datetime, timezone
import json
import math

import mysql.connector
import tiktoken


def num_tokens_from_string(content: str) -> int:
    if content is None or content == "":
        return 0
    encoding = tiktoken.get_encoding("cl100k_base")
    if content is not str:
        content = str(content)

    num_tokens = len(encoding.encode(content))
    return num_tokens



print(f"start job======================={datetime.now()}")
start_time = datetime.now().timestamp()
local = False

read_conn = mysql.connector.connect(
    user="tgbot_r",
    password="eadfsdEGE56",
    database="tavern",
    host="***********",
    port="3306",
)
cursor = read_conn.cursor()
    # 角色列表
cursor.execute(
        """select
        id,
        uid, 
        data_config
        from role_config
        where privacy = 0 and status = 1 and uid > 0
        """
    )
mid_list = cursor.fetchall()
ret_list = []
for x in mid_list:
    id = int(x[0])
    user_id = int(x[1])
    data_config = x[2]
    if not data_config:
        continue
    data_config = json.loads(str(data_config))
    sum_token = num_tokens_from_string(data_config["description"])
    if "personality" in data_config:
        sum_token += num_tokens_from_string(data_config["personality"])
    if "example_dialog" in data_config:
        sum_token += num_tokens_from_string(data_config["example_dialog"])
    if "muilte_examples" in data_config:
        sum_token += num_tokens_from_string(data_config["muilte_examples"])
    if "muilte_scenes" in data_config:
        sum_token += num_tokens_from_string(data_config["muilte_scenes"])
    if "status_block_init" not in data_config:
        sum_token += num_tokens_from_string(data_config["status_block_init"])
    if "status_block_rule" not in data_config:
        sum_token += num_tokens_from_string(data_config["status_block_rule"])
    if "status_block" not in data_config:
        sum_token += num_tokens_from_string(data_config["status_block"])
    ret_list.append((id, user_id, sum_token))
    print(f"role_id: {id}, user_id: {user_id}, sum_token: {sum_token}")
ret_list.sort(key=lambda x: x[2], reverse=True)

# ret_list写入本地csv文件,Download the csv file from the link below
with open("role_stat.csv", "w") as f:
    f.write("role_id,user_id,sum_token\n")
    for x in ret_list:
        f.write(f"{x[0]},{x[1]},{x[2]}\n")
    # 推广角色用户数据
            
read_conn.close()
