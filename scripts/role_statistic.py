# %%
from datetime import datetime, timezone
import math

import mysql.connector

print(f"start job======================={datetime.now()}")
start_time = datetime.now().timestamp()
local = False

read_conn = mysql.connector.connect(
    user="tgbot_r",
    password="eadfsdEGE56",
    database="tavern",
    host="***********",
    port="3306",
)
insert_conn = None
if local:
    insert_conn = mysql.connector.connect(
        user="root",
        password="UtqY5EjrXVq5qejjwWm2",
        database="tavern_test",
        host="sg-cynosdbmysql-grp-li9atzst.sql.tencentcdb.com",
        port="26583",
    )
if not local:
    insert_conn = mysql.connector.connect(
        user="root",
        password="UtqY5EjrXVq5qejjwWm2",
        database="tavern",
        host="sg-cynosdbmysql-grp-li9atzst.sql.tencentcdb.com",
        port="26583",
    )


insert_cursor = insert_conn.cursor()
cursor = read_conn.cursor()
try:
    # 角色列表
    cursor.execute(
        """select 
        id, 
        created_at,
        uid
        from role_config
        where privacy = 1 and status = 1
        """
    )
    mid_list = cursor.fetchall()
    role_diff_days = {}
    black_role_ids = []
    for x in mid_list:
        created_at = x[1].astimezone(timezone.utc)
        now = datetime.now(timezone.utc)
        diff_day = (now - created_at).days
        role_diff_days[int(x[0])] = diff_day
        # if int(x[2]) == 1747553:
        #     black_role_ids.append(int(x[0]))

    # 推广角色用户数据

    cursor.execute(
        """SELECT start_role,count(1) as num  from user_register_channel 
        WHERE start_role > 0 GROUP BY start_role order by num desc
    """
    )
    mid_list = cursor.fetchall()
    channel_role_register_num = {}
    for x in mid_list:
        channel_role_register_num[int(x[0])] = int(x[1])
    # 角色统计热度值
    cursor.execute(
        """select 
        role_id, 
        count(distinct(conversation_id)) as conv_count,
        count(1) as msg_count,
        count(distinct(user_id)) as user_count,
        sum(consume) as total_diamond,
        count(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and model='claude-3-haiku' THEN 1 END) AS h24_hk_count,
        count(CASE WHEN model='claude-3-haiku' THEN 1 END) AS haiku3_count
        from chat_history_statistic group by role_id
        """
    )
    ret_list = cursor.fetchall()
    data_list = []
    for x in ret_list:
        numerator = int(x[3]) + int(x[1]) + int(x[2]) + int(x[4]) + 666
        diff_days = role_diff_days.get(x[0], 0)
        denominator = math.pow(diff_days + 1, 1 / 100)
        hot = int((numerator / denominator))
        if channel_role_register_num.get(x[0], 0) >= 500:
            hot = int(hot * 0.2)
        haiku3_count = int(x[6])
        total_message = int(x[2])
        if total_message >= 100 * 10000:
            hot = int(hot * 0.001)
        elif total_message >= 50 * 10000:
            hot = int(hot * 0.01)
        elif total_message >= 5 * 10000:
            hot = int(hot * 0.05)
        elif total_message >= 10000:
            hot = int(hot * 0.1)
        hot_temp = hot
        if x[0] in black_role_ids:
            hot  = int(hot/((diff_days+1)*(diff_days+1)))
            hot_temp  = hot
        data_list.append(
            {
                "role_id": int(x[0]),
                "total_conversation": int(x[1]),
                "total_message": total_message,
                "user_count": int(x[3]),
                "total_diamond": int(x[4]),
                "hot_v1": hot,
                "h24_haiku_count": int(x[5]),
                "haiku3_count": haiku3_count,
                "hot_temp": hot_temp,
            }
        )
    sql = """
        INSERT INTO role_statistic (
        role_id, total_conversation, total_message,user_count, total_diamond, hot_v1,h24_haiku_count,haiku3_count,hot_temp
        ) VALUES (
        %(role_id)s, %(total_conversation)s, %(total_message)s, %(user_count)s, %(total_diamond)s, %(hot_v1)s,%(h24_haiku_count)s,%(haiku3_count)s,%(hot_temp)s
    ) ON DUPLICATE KEY UPDATE
        total_conversation = VALUES(total_conversation),
        total_message = VALUES(total_message),
        user_count = VALUES(user_count),
        total_diamond = VALUES(total_diamond),
        hot_v1 = VALUES(hot_v1)+base_count,
        h24_haiku_count = VALUES(h24_haiku_count),
        haiku3_count = VALUES(haiku3_count),
        hot_temp = VALUES(hot_temp)
       """
    insert_cursor.executemany(sql, data_list)
    for data in data_list:
        print(data)
    insert_conn.commit()


except Exception as e:
    print(e)
read_conn.close()
insert_conn.close()
end_time = datetime.now().timestamp()

print(
    f"end job======================={datetime.now()},duration:{end_time - start_time}"
)

# %%
