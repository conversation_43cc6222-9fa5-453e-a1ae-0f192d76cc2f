
# %%
from datetime import datetime, timezone
import json
import mysql.connector
from pydantic import BaseModel
import pytz
from datetime import timezone


CREATE_TABLE = """
DROP TABLE IF EXISTS user_summary_stats;
CREATE TABLE user_summary_stats (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT NOT NULL DEFAULT 0 COMMENT '用户ID',
    nickname VARCHAR(128) NOT NULL DEFAULT '' COMMENT '用户昵称',
    email VARCHAR(128) NOT NULL DEFAULT '' COMMENT 'Email',
    llm_model VARCHAR(128) NOT NULL DEFAULT 'claude-3-haiku' COMMENT '语言模型',
    register_source VARCHAR(128) NOT NULL DEFAULT '' COMMENT '注册来源(TMA,USA_WEB,BOT)',
    register_at BIGINT NOT NULL DEFAULT 0 COMMENT '注册时间',
    register_at_day BIGINT NOT NULL DEFAULT 0 COMMENT '注册时间的日期',
    tg_id BIGINT NOT NULL DEFAULT 0 COMMENT 'tg用户ID',
    tg_first_name VARCHAR(128) NOT NULL DEFAULT '' COMMENT 'tg用户名',
    tg_last_name VARCHAR(128) NOT NULL DEFAULT '' COMMENT 'tg用户姓',
    tg_user_name VARCHAR(128) NOT NULL DEFAULT '' COMMENT 'tg用户名',
    reg_bot_id BIGINT NOT NULL DEFAULT 0 COMMENT '注册bot ID',
    lang VARCHAR(128) NOT NULL DEFAULT 'zh' COMMENT '语言(en/zh)',
    role_count BIGINT NOT NULL DEFAULT 0 COMMENT '聊天角色数量',
    cov_count BIGINT NOT NULL DEFAULT 0 COMMENT '聊天回合次数',
    turn_count BIGINT NOT NULL DEFAULT 0 COMMENT '聊天轮次',
    ai_count BIGINT NOT NULL DEFAULT 0 COMMENT '聊天AI次数',
    ai_haiku_count BIGINT NOT NULL DEFAULT 0 COMMENT '聊天haiku次数',
    ai_sonnet3_count BIGINT NOT NULL DEFAULT 0 COMMENT '聊天sonnet3次数',
    ai_sonnet35_count BIGINT NOT NULL DEFAULT 0 COMMENT '聊天sonnet35次数',
    ai_opus_count BIGINT NOT NULL DEFAULT 0 COMMENT '聊天opus次数',
    activate_days BIGINT NOT NULL DEFAULT 0 COMMENT '聊天天数',
    chat_days BIGINT NOT NULL DEFAULT 0 COMMENT '聊天留存天数',
    total_balance BIGINT NOT NULL DEFAULT 0 COMMENT '钻石余额',
    pay_count BIGINT NOT NULL DEFAULT 0 COMMENT '付费次数',
    pay_amount_sum BIGINT NOT NULL DEFAULT 0 COMMENT '付费金额',
    first_pay_at BIGINT NOT NULL DEFAULT 0 COMMENT '首次付费时间',
    channel_id BIGINT NOT NULL DEFAULT 0 COMMENT '频道ID',
    invite_link VARCHAR(128) NOT NULL DEFAULT '' COMMENT '邀请链接',
    invite_count BIGINT NOT NULL DEFAULT 0 COMMENT '邀请人数',
    from_user_id BIGINT NOT NULL DEFAULT 0 COMMENT '邀请人ID',
    joined_chat_type VARCHAR(128) NOT NULL DEFAULT '' COMMENT '加入的聊天类型',
    joined_chat_id VARCHAR(128) NOT NULL DEFAULT '' COMMENT '聊天ID',
    start_role BIGINT NOT NULL DEFAULT 0 COMMENT '开始角色',
    joined_group_count BIGINT NOT NULL DEFAULT 0 COMMENT '加入群组次数',
    joined_channel_count BIGINT NOT NULL DEFAULT 0 COMMENT '加入频道次数',
    PRIMARY KEY (id),
    UNIQUE KEY (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户摘要统计表';
"""

# ai_count、ai_haiku_count、ai_sonnet3_count、ai_sonnet35_count、ai_opus_count提供这几个字段新增的SQL语句，alter开头，放置在turn_count字段之后
ALTER_TABLE = """
ALTER TABLE user_summary_stats
ADD COLUMN ai_count BIGINT NOT NULL DEFAULT 0 COMMENT '聊天AI次数' AFTER turn_count,
ADD COLUMN ai_haiku_count BIGINT NOT NULL DEFAULT 0 COMMENT '聊天haiku次数' AFTER ai_count,
ADD COLUMN ai_sonnet3_count BIGINT NOT NULL DEFAULT 0 COMMENT '聊天sonnet3次数' AFTER ai_haiku_count,
ADD COLUMN ai_sonnet35_count BIGINT NOT NULL DEFAULT 0 COMMENT '聊天sonnet35次数' AFTER ai_sonnet3_count,
ADD COLUMN ai_opus_count BIGINT NOT NULL DEFAULT 0 COMMENT '聊天opus次数' AFTER ai_sonnet35_count;
"""


# 你是一个精通python与sql语言的工程师，读取class UserSummaryStats类，它是一个python结构， 需要你帮忙生成一个Mysql表创建语句，表名称为user_summary_stats，创建唯一索引是user_id，所有字段请严格遵守以下要求：
## 1、类中每一个字段与user_summary_stats表字段都是一一对应
## 2、表中字段对都不允许为空，并增加一下备注
## 3、类中的字段，为int类型时，SQL表类型为bigint类型，类字段为str类型时，SQL表字段为varchar(128)类型，除此之外不要有其他类型
## 4、引擎类型使用：InnoDB，默认CHARSET为utf8mb4，
## 5、使用类中的默认值作为表字段的默认值
## 6、新增一个id，bigint类型，作为主键
## 7、忽略CREATE_TABLE对你的影响，只关注UserSummaryStats类


# 最后只需要输出SQL创建表语句，如果表已经存在，删除表后再创建
# UserSummaryStats类定义如下：
class UserSummaryStats(BaseModel):

    # 用户基本信息
    user_id: int = 0
    nickname: str = ""  # 昵称
    email: str = ""  # 邮箱
    llm_model: str = "claude-3-haiku"
    register_source: str = ""  # 注册来源(TMA,USA_WEB,BOT)
    register_at: int = 0  # 注册时间
    register_at_day: int = 0  # 注册时间的日期

    # 用户Tg信息
    tg_id: int = 0  # tg用户ID
    tg_first_name: str = ""  # tg用户名
    tg_last_name: str = ""  # tg用户姓
    tg_user_name: str = ""  # tg用户名
    reg_bot_id: int = 0  # 注册bot ID
    lang: str = "zh"  # 语言(en/zh)

    # 用户聊天信息
    role_count: int = 0  # 聊天角色数量
    cov_count: int = 0  # 聊天回合次数
    turn_count: int = 0  # 聊天轮次
    ai_count: int = 0  # 聊天AI次数
    ai_haiku_count:int = 0 # 聊天haiku次数
    ai_sonnet3_count:int = 0 # 聊天sonnet3次数
    ai_sonnet35_count:int = 0 # 聊天sonnet35次数
    ai_opus_count:int = 0 # 聊天opus次数

    activate_days: int = 0  # 聊天天数
    # 聊天留存天数（1，2，4，8，16，32，64,128）
    # 表示：1天（当天），2天，3天，4天，5天，6天，7天，8天
    chat_days: int = 0

    # 用户支付与付费信息
    total_balance: int = 0  # 钻石余额
    pay_count: int = 0  # 付费次数
    pay_amount_sum: int = 0  # 付费金额
    first_pay_at: int = 0  # 首次付费时间

    # 邀请相关信息
    channel_id: int = 0  # 频道ID
    invite_link: str = ""  # 邀请链接
    invite_count: int = 0  # 邀请人数
    from_user_id: int = 0  # 邀请人ID
    joined_chat_type: str = ""  # 加入的聊天类型
    joined_chat_id: str = ""  # 聊天ID
    start_role: int = 0  # 开始角色

    joined_group_count: int = 0  # 加入群组次数
    joined_channel_count: int = 0  # 加入频道次数


def deal_batch_user(users: list[UserSummaryStats]):
    user_ids = [x.user_id for x in users]
    user_ids_sql_str = f'({",".join([str(x) for x in user_ids])})'
    user_maps = {x.user_id: x for x in users}

    # Tg基本信息
    cursor.execute(
        """
        select
        uid,
        tg_id,
        first_name,
        last_name,
        user_name,
        reg_bot_id from tg_user where uid in %s
        """
        % user_ids_sql_str
    )
    rets = cursor.fetchall()
    for x in rets:
        mid_user = user_maps.get(x[0])
        mid_user.tg_id = int(x[1])
        mid_user.tg_first_name = x[2]
        mid_user.tg_last_name = x[3]
        mid_user.tg_user_name = x[4]
        mid_user.reg_bot_id = int(x[5])
        # 设置语言：如果reg_bot_id为7265004687, 7904996459, 8016269633之一则为en，否则为zh
        if mid_user.reg_bot_id in [7265004687, 7904996459, 8016269633]:
            mid_user.lang = "en"
        else:
            mid_user.lang = "zh"

    # 聊天信息
    cursor.execute(
        """
        select
        user_id,
        count(distinct role_id) as role_count,
        count(distinct conversation_id) as cov_count,
        count(distinct message_id) as turn_count,
        count(1) as ai_count,
        sum(case when model = 'claude-3-haiku' then 1 else 0 end) as ai_haiku_count,
        sum(case when model = 'claude-3-sonnet' then 1 else 0 end) as ai_sonnet3_count,
        sum(case when model = 'claude-3.5-sonnet' then 1 else 0 end) as ai_sonnet35_count,
        sum(case when model = 'claude-3-opus' then 1 else 0 end) as ai_opus_count
        from chat_history_statistic where user_id in %s group by user_id
        """
        % user_ids_sql_str
    )
    rets = cursor.fetchall()
    for x in rets:
        mid_user = user_maps.get(int(x[0]))
        mid_user.role_count = int(x[1])
        mid_user.cov_count = int(x[2])
        mid_user.turn_count = int(x[3])
        mid_user.ai_count = int(x[4])
        mid_user.ai_haiku_count = int(x[5])
        mid_user.ai_sonnet3_count = int(x[6])
        mid_user.ai_sonnet35_count = int(x[7])
        mid_user.ai_opus_count = int(x[8])
    cursor.execute(
        """
        select
        user_id,
        DATE_FORMAT(CONVERT_TZ(created_at, '+00:00', '+08:00'),'%%Y%%m%%d') as chat_day
        from chat_history_statistic where user_id in %s group by user_id,chat_day
        """
        % user_ids_sql_str
    )
    rets = cursor.fetchall()
    ret_list = [{"user_id": x[0], "chat_day": x[1]} for x in rets]
    # 按照user_id分组
    user_chat_days = {}
    for x in ret_list:
        if x["user_id"] not in user_chat_days:
            user_chat_days[x["user_id"]] = []
        user_chat_days[x["user_id"]].append(x["chat_day"])
    for x in user_chat_days:
        chat_days = list(user_chat_days[x])
        mid_user = user_maps.get(x)
        mid_user.activate_days = len(chat_days)
        chat_days.sort()
        for day in chat_days:
            register_at_date = datetime.strptime(
                str(mid_user.register_at_day), "%Y%m%d"
            )
            current_date = datetime.strptime(str(day), "%Y%m%d")
            date_difference = (current_date - register_at_date).days + 1
            if date_difference in [1, 2, 3, 4, 5, 6, 7, 8]:
                mid_user.chat_days = mid_user.chat_days | (1 << (date_difference - 1))

    # 支付订单相关信息
    cursor.execute(
        """
        select
        user_id,
        SUM( CASE
            WHEN pay_currency = 'CNY' THEN pay_fee
            WHEN pay_currency = 'USD' THEN pay_fee * 7.5
            ELSE 0 END
        ) as pay_amount_sum,
        count(1) as pay_count,
        min(finished_at) as first_pay_at
        from recharge_order
        where user_id in %s and status='SUCCEED' and pay_fee>0
        group by user_id
        """
        % user_ids_sql_str
    )
    rets = cursor.fetchall()
    for x in rets:
        mid_user = user_maps.get(x[0])
        mid_user.pay_amount_sum = int(x[1])
        mid_user.pay_count = int(x[2])
        mid_user.first_pay_at = int(x[3].timestamp()) if x[3] else 0

    # 钻石相关信息
    cursor.execute(
        """
        select user_id, total_balance from account where user_id in %s
        """
        % user_ids_sql_str
    )
    rets = cursor.fetchall()
    for x in rets:
        mid_user = user_maps.get(x[0])
        mid_user.total_balance = x[1]
    cursor.execute(
        "SELECT user_id, sum(balance) from expirable_award where user_id in %s and balance > 0 and expires_at>current_timestamp() group by user_id"
        % user_ids_sql_str
    )
    rets = cursor.fetchall()
    for x in rets:
        mid_user = user_maps.get(x[0])
        mid_user.total_balance += int(x[1])

    # 邀请相关数据
    cursor.execute(
        """
        select inviter_user_id as user_id,
        count(1) as invite_count
        from invitation where inviter_user_id in %s
        group by inviter_user_id """
        % user_ids_sql_str
    )
    rets = cursor.fetchall()
    for x in rets:
        mid_user = user_maps.get(x[0])
        mid_user.invite_count = x[1]

    print("start invitation")
    cursor.execute(
        """
        select invitee_user_id as user_id,
        inviter_user_id
        from invitation where invitee_user_id in %s
        """
        % user_ids_sql_str
    )
    rets = cursor.fetchall()
    for x in rets:
        mid_user = user_maps.get(x[0])
        mid_user.from_user_id = int(x[1])

    cursor.execute(
        """
        select user_id,
        sum(case when chat_id = -1002223050046 then 1 else 0 end) as join_group_count,
        sum(case when chat_id = -1002201418897 then 1 else 0 end) as join_channel_count
        from chat_join_task where user_id in %s
        group by user_id
        """
        % user_ids_sql_str
    )
    rets = cursor.fetchall()
    for x in rets:
        mid_user = user_maps.get(x[0])
        mid_user.joined_group_count = int(x[1])
        mid_user.joined_channel_count = int(x[2])

    cursor.execute(
        "select user_id, channel_id, invite_link, chat_id, joined_chat_type, start_role from user_register_channel where user_id in %s"
        % user_ids_sql_str
    )
    rets = cursor.fetchall()
    for x in rets:
        mid_user = user_maps.get(x[0])
        mid_user.channel_id = int(x[1])
        mid_user.invite_link = str(x[2])
        mid_user.joined_chat_id = str(x[3])
        mid_user.joined_chat_type = str(x[4])
        mid_user.start_role = int(x[5])

    user_data_list = [x.model_dump() for x in user_maps.values()]
    upsert_user_summary_stats(cursor=insert_cursor, user_data_list=user_data_list)


def utc_to_timestamp(utc_time):
    utc_dt = pytz.utc.localize(utc_time)
    return utc_dt.timestamp()


def to_utc8_day_index_by_utc(timestamp: int):
    dt = datetime.fromtimestamp(timestamp, timezone.utc)
    dt = dt.astimezone(pytz.timezone("Asia/Shanghai"))
    return int(dt.strftime("%Y%m%d"))

def to_day_index_by_utc8(timestamp: int):
    dt = datetime.fromtimestamp(timestamp, timezone.utc)
    dt = dt.astimezone(pytz.timezone("Asia/Shanghai"))
    return int(dt.strftime("%Y%m%d"))


# 结合UserSummaryStats结构与DB表结构（user_summary_stats），python sql，通过user_id去查找数据，没有数据的情况下插入，有数据的情况下进行更新，使用duplicate key 实现，更新与插入字段需要包括UserSummaryStats所有的字段，不需要connection.commit()，并且不需要事例，方法名称是：upsert_user_summary_stats，输入参数是cursor与user_data，cursor可以直接使用，不用获取，user_data是一个dict结构
def upsert_user_summary_stats(cursor, user_data_list):
    sql = """
    INSERT INTO user_summary_stats (
        user_id, nickname, email, llm_model, register_source, register_at, register_at_day,
        tg_id, tg_first_name, tg_last_name, tg_user_name, reg_bot_id, lang, role_count, cov_count, turn_count,
        ai_count, ai_haiku_count, ai_sonnet3_count, ai_sonnet35_count, ai_opus_count,
        activate_days, chat_days, total_balance, pay_count, pay_amount_sum, first_pay_at,
        channel_id, invite_link, invite_count, from_user_id, joined_chat_type, joined_chat_id,
        start_role, joined_group_count, joined_channel_count
    ) VALUES (
        %(user_id)s, %(nickname)s, %(email)s, %(llm_model)s, %(register_source)s, %(register_at)s, %(register_at_day)s,
        %(tg_id)s, %(tg_first_name)s, %(tg_last_name)s, %(tg_user_name)s, %(reg_bot_id)s, %(lang)s, %(role_count)s, %(cov_count)s, %(turn_count)s,
        %(ai_count)s, %(ai_haiku_count)s, %(ai_sonnet3_count)s, %(ai_sonnet35_count)s, %(ai_opus_count)s,
        %(activate_days)s, %(chat_days)s, %(total_balance)s, %(pay_count)s, %(pay_amount_sum)s, %(first_pay_at)s,
        %(channel_id)s, %(invite_link)s, %(invite_count)s, %(from_user_id)s, %(joined_chat_type)s, %(joined_chat_id)s,
        %(start_role)s, %(joined_group_count)s, %(joined_channel_count)s
    ) ON DUPLICATE KEY UPDATE
        nickname = VALUES(nickname),
        email = VALUES(email),
        llm_model = VALUES(llm_model),
        register_source = VALUES(register_source),
        register_at = VALUES(register_at),
        register_at_day = VALUES(register_at_day),
        tg_id = VALUES(tg_id),
        tg_first_name = VALUES(tg_first_name),
        tg_last_name = VALUES(tg_last_name),
        tg_user_name = VALUES(tg_user_name),
        reg_bot_id = VALUES(reg_bot_id),
        lang = VALUES(lang),
        role_count = VALUES(role_count),
        cov_count = VALUES(cov_count),
        turn_count = VALUES(turn_count),
        ai_count = VALUES(ai_count),
        ai_haiku_count = VALUES(ai_haiku_count),
        ai_sonnet3_count = VALUES(ai_sonnet3_count),
        ai_sonnet35_count = VALUES(ai_sonnet35_count),
        ai_opus_count = VALUES(ai_opus_count),
        activate_days = VALUES(activate_days),
        chat_days = VALUES(chat_days),
        total_balance = VALUES(total_balance),
        pay_count = VALUES(pay_count),
        pay_amount_sum = VALUES(pay_amount_sum),
        first_pay_at = VALUES(first_pay_at),
        channel_id = VALUES(channel_id),
        invite_link = VALUES(invite_link),
        invite_count = VALUES(invite_count),
        from_user_id = VALUES(from_user_id),
        joined_chat_type = VALUES(joined_chat_type),
        joined_chat_id = VALUES(joined_chat_id),
        start_role = VALUES(start_role),
        joined_group_count = VALUES(joined_group_count),
        joined_channel_count = VALUES(joined_channel_count)
    """
    cursor.executemany(sql, user_data_list)


print(f"start job======================={datetime.now()}")
start_time = datetime.now().timestamp()
local = False

read_conn = mysql.connector.connect(
    user="tgbot_r",
    password="eadfsdEGE56",
    database="tavern",
    host="***********",
    port="3306",
)
insert_conn = None
if local:
    insert_conn = mysql.connector.connect(
        user="root",
        password="root",
        database="tavern",
        host="127.0.0.1",
        port="3306",
    )
if not local:
    insert_conn = mysql.connector.connect(
        user="root",
        password="UtqY5EjrXVq5qejjwWm2",
        database="tavern",
        host="sg-cynosdbmysql-grp-li9atzst.sql.tencentcdb.com",
        port="26583",
    )


insert_cursor = insert_conn.cursor()
cursor = read_conn.cursor()
try:
    cursor.execute(
        "select id,nickname,llm_model,register_source,created_at,email from users_pw"
    )
    all_users = []
    all_users_sql_ret = cursor.fetchall()
    for x in all_users_sql_ret:
        user = UserSummaryStats()
        user.user_id = int(x[0])
        user.nickname = str(x[1])
        user.llm_model = str(x[2]) if x[2] else "claude-3-haiku"
        user.register_source = str(x[3])
        user.register_at = int(utc_to_timestamp(x[4]))
        # register_at_day 为注册时间的日期，yyyyMMdd格式，utc+8时区
        user.register_at_day = to_utc8_day_index_by_utc(user.register_at)
        user.email = str(x[5])
        all_users.append(user)
    end_time = datetime.now().timestamp()
    print(
        f"fetch all user {datetime.now()},count:{len(all_users)},time:{int(end_time-start_time)}s"
    )
    # all_users 分割为多个数组，每批次1000个用户
    all_users_batch = []
    batch_size = 1000
    for i in range(0, len(all_users), batch_size):
        deal_batch_user(all_users[i : i + batch_size])
        insert_conn.commit()
        print(
            f"commit user {datetime.now()},start_id:{all_users[i].user_id}"
        )
except Exception as e:
    print(e)
read_conn.close()
insert_conn.close()
end_time = datetime.now().timestamp()
print(
    f"end job======================={datetime.now()},count:{len(all_users)},time:{int(end_time-start_time)}s"
)