# %%
#%pip install mysql-connector-python

# %%
from datetime import timezone
import mysql.connector
import pytz

conn = mysql.connector.connect(user='tgbot_r', password='eadfsdEGE56', database='tavern', host='***********', port='3306')

master_conn = mysql.connector.connect(user='tgbot_wr', password='abdKATtvoCB620NsfajOz7FK', database='tavern', host='************', port='3306')


# %%
cursor = conn.cursor()
cursor.execute('select user_id, channel_id, invite_link, chat_id, joined_chat_type, start_role from user_register_channel')

all_users = cursor.fetchall()
all_list = [{'user_id': x[0], 'channel_id': x[1], 'invite_link': x[2], 'chat_id': x[3], 'joined_chat_type': x[4], 'start_role': x[5]} for x in all_users]
all_users = {int(x['user_id']): x for x in all_list}

all_int_uids = [int(x['user_id']) for x in all_list]
all_uids = [str(x['user_id']) for x in all_list]
all_ids_str = f'({",".join(all_uids)})'
cursor.close()

# %%
print(all_users)

# %%
cursor = conn.cursor()
cursor.execute('select user_id, total_balance from account where user_id in %s' % all_ids_str)
ab = cursor.fetchall()
all_account_balances = {x[0]: x[1] for x in ab}
print(all_account_balances)
cursor.close()

# %%
cursor = conn.cursor()
cursor.execute('SELECT user_id, sum(balance) from expirable_award where user_id in %s and balance > 0 and expires_at>current_timestamp() group by user_id' % all_ids_str)
aeb = cursor.fetchall()
all_expirable_balances = {x[0]: int(x[1]) for x in aeb}
print(all_expirable_balances)
cursor.close()

# %%
def to_utc(dt):
    dt = dt.astimezone(pytz.timezone('Asia/Shanghai'))
    print('current timezone:', dt.tzinfo)
    return dt.astimezone(timezone.utc)

cursor = conn.cursor()
cursor.execute('select up.email, up.nickname, up.llm_model, tu.first_name, tu.last_name, tu.user_name, tu.created_at, up.id from users_pw up join tg_user tu on up.id=tu.uid where up.id in %s' % all_ids_str)
aun = cursor.fetchall()
all_user_settings = {x[7]: {'email': x[0], 'nickname': x[1], 'llm_model': x[2], 'first_name': x[3], 'last_name': x[4], 'user_name': x[5], 'created_at': to_utc(x[6])} for x in aun}
print(all_user_settings)
cursor.close()

# %%
print(all_user_settings)

# %%
cursor = conn.cursor()
cursor.execute('select user_id, chat_id from chat_join_task where user_id in %s' % all_ids_str)
cjt = cursor.fetchall()
all_chat_join_tasks = {f'{x[0]}_{x[1]}': True for x in cjt}
print(all_chat_join_tasks)
cursor.close()

# %%
def get_nested_value(d, keys, default=None):
    try:
        for key in keys:
            d = d[key]
        return d
    except KeyError:
        return default

# %%
cursor = master_conn.cursor()
group_id = -*************
channel_id = -*************
for uid in all_int_uids:
    ab = all_account_balances.get(uid, 0)
    eb = all_expirable_balances.get(uid, 0)
    balance = ab + eb
    us = all_user_settings.get(uid, {})
    au = all_users.get(uid, {})
    print(us)
    print(au)
    group_joined = all_chat_join_tasks.get(f'{uid}_{group_id}', False)
    channel_joined = all_chat_join_tasks.get(f'{uid}_{channel_id}', False)
    cursor.execute('insert into user_stats(user_id, tg_first_name, tg_last_name, tg_user_name, nickname, llm_model, account_balance, channel_id, invite_link, joined_chat_id, joined_chat_type, start_role, register_at, joined_group, joined_channel) values(%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s) on duplicate key update tg_first_name=%s, tg_last_name=%s, tg_user_name=%s, nickname=%s, llm_model=%s, account_balance=%s, joined_group=%s, joined_channel=%s, register_at=%s',
                   (uid, us.get('first_name', ''), us.get('last_name', ''), us.get('user_name', ''),
                    us.get('nickname', ''), us.get('llm_model', 'claude-3-haiku'), balance, au.get('channel_id', 0), 
                    au.get('invite_link', ''), au.get('chat_id', 0), au.get('joined_chat_type', ''), 
                    au.get('start_role', 0), us.get('created_at', None), 
                    group_joined, channel_joined,
                    us.get('first_name', ''), us.get('last_name', ''), us.get('user_name', ''), us.get('nickname', ''), us.get('llm_model', 'claude-3-haiku'), balance, group_joined, channel_joined, us.get('created_at', None)))

master_conn.commit()
