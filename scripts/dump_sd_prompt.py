from ai.chat_bot import GPTChatBot
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorCollection, AsyncIOMotorDatabase
import asyncio,json,dotenv
import pandas as pd
dotenv.load_dotenv()

bot = GPTChatBot()

async def get_descriptions() -> list[str]:
    client = AsyncIOMotorClient('mongodb+srv://tg_bot:<EMAIL>/?retryWrites=true&w=majority')
    db = client['tg_bot']
    col = db.get_collection('feedback')
    cursor = col.find().sort({'_id': -1})
    descriptions = []
    for dc in await cursor.to_list(1000):
        choices = dc['choices']
        narrators = list(filter(lambda x: x.startswith('旁白:') and len(x) > 20, choices))
        descriptions.extend(narrators)
    descriptions = list(set(descriptions))
    descriptions.sort(key=lambda x: len(x), reverse=True)
    return descriptions

# async def main():
#     #descriptions = await get_descriptions()
#     df = pd.read_json('sd_prompts_1.jsonl', lines=True)
#     descriptions = df.head(100)['description'].values.tolist()
#     with open('sd_prompts.jsonl', 'a') as f:
#         for d in enumerate(descriptions[:200]):
#             sd, prompt = bot.translate_narrator(d[1])
#             json.dump({'description': d[1], 'sd_prompt': sd}, f, ensure_ascii=False)
#             f.write('\n')
#             print(f'{d[0]}. {d[1]}')

# if __name__ == '__main__':
#     loop = asyncio.get_event_loop()
#     loop.run_until_complete(main())
