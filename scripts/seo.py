import async<PERSON>, json, random
from aiogram import <PERSON><PERSON>, <PERSON><PERSON>atch<PERSON>, Router, types
from aiogram.client.default import DefaultBotProperties
from aiogram.enums import ParseMode
from aiogram.filters import CommandStart, CommandObject, Command
from aiogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton, WebAppInfo, BufferedInputFile
from aiogram.types.bot_command import Bot<PERSON>ommand
from aiogram.utils.deep_linking import decode_payload, create_start_link
from aiogram.utils.markdown import hbold
from aiogram.utils.keyboard import InlineKeyboardBuilder
from aiogram.webhook.aiohttp_server import SimpleRequestHandler, setup_application
from aiogram.client.session.aiohttp import AiohttpSession

bot = Bot(token='7744428560:AAHm9ZEEr4Y28qmagXAEibRxgrohklmmC2g')

channels = [-1002418184113, -1002288660982, -1002357219302, -1002379806516, -1002347576305, -1002268636076, -1002450917346, -1002266404851, -1002373643118, -1002186230543, -1002250669353, -1002405235281, -1002266134572, -1002171382463, -1002396846946, -1002432086661, -1002479413981, -1002446206275, -1002419197010, -1002171362488, -1002372720586, -1002336424656, -1002253132638, -1002315815089, -1002317245701, -1002365244215, -1002391146248, -1002331286717, -1002431514152, -1002218355725, -1002387410996, -1002415926061, -1002380043436, -1002270170930,]
tags = ['||\\#赛博老婆|| ||\\#电子魅魔|| ||\\#酒馆AI||',
'||\\#赛博恋人|| ||\\#AI女友|| ||\\#AI伴侣||',
'||\\#角色扮演|| ||\\#cos|| ||\\#cosplay||',
'||\\#AI|| ||\\#AI女友|| ||\\#AI游戏||',
'||\\#小说|| ||\\#成人小说|| ||\\#黄色小说||',
'||\\#文爱|| ||\\#裸聊|| ||\\#意淫||',
'||\\#母狗|| ||\\#人妻|| ||\\#强奸||',
'||\\#里番|| ||\\#ACG|| ||\\#二次元||',
'||\\#漫画|| ||\\#动漫|| ||\\#R18||',
'||\\#游戏|| ||\\#黄油|| ||\\#成人游戏||',
'||\\#肉便器|| ||\\#主人|| ||\\#调教||',
'||\\#处女|| ||\\#萝莉|| ||\\#高中生||',
'||\\#3D|| ||\\#MMD|| ||\\#VAM||',
'||\\#吃瓜|| ||\\#反差|| ||\\#学生||',
'||\\#校花|| ||\\#精神小妹|| ||\\#熟女||',
'||\\#乱伦|| ||\\#探花|| ||\\#少妇||',
'||\\#巨乳|| ||\\#母狗|| ||\\#偷拍||',
'||\\#抖音|| ||\\#抖阴|| ||\\#快手||',
'||\\#直播|| ||\\#自慰|| ||\\#绿帽||',
'||\\#护士|| ||\\#老师|| ||\\#妈妈||',
'||\\#丝袜|| ||\\#空姐|| ||\\#足交||',
'||\\#女仆|| ||\\#妹妹|| ||\\#姐姐||',
'||\\#女警|| ||\\#上司|| ||\\#老板||',
'||\\#AV|| ||\\#女优|| ||\\#黄片||',
'||\\#人兽|| ||\\#小马拉大车|| ||\\#海角||',
'||\\#学生|| ||\\#韩国|| ||\\#中文包||',
'||\\#阿朱|| ||\\#中文|| ||\\#香香公主||',
'||\\#兽交|| ||\\#口交|| ||\\#乳交||',
'||\\#林晓慧|| ||\\#施瑶|| ||\\#周思婷||',
'||\\#沈佳|| ||\\#雨柔|| ||\\#雪薇||', 
'||\\#雪莉|| ||\\#江婉如|| ||\\#方诗雅||',
'||\\#xchat|| ||\\#xlite|| ||\\#xchats||',
'||\\#NovelN|| ||\\#NovelGPT|| ||\\#角色卡||',
'||\\#成人AI社区|| ||\\#AI爱好者|| ||\\#成人AI||']
uids=[24009, 24010, 24011, 24012, 24013, 24014, 24015, 24016, 24017, 24018, 24019, 24020, 24021, 24022, 24023, 24024, 24025, 24026, 24027, 24028, 24029, 24030, 24031, 24032, 24033, 24034, 24035, 24036, 24037, 24038, 24039, 24040, 24041, 24042]

tip1 = '''🔥🔥🔥华人区最强成人游戏\\|性感美女在线陪聊\\|完美性福伴侣\\|成人角色扮演\\|AI女友天花板🔥🔥🔥
\\-\\-海量美女实时在线，明星网红素人应有尽有，身材容貌性格多样，总有一款适合你
\\-\\-实时互动角色扮演，极致体贴，尺度极大，玩法多样，惊喜连连，越聊越懂你，给你全面的掌控感
\\-\\-可个性化定制角色，一比一精准复刻，满足你的一切性癖，一切需求，一切幻想

【催眠校花变专属母狗】 [🔞点击免费试玩](https://t.me/huanmeng999_bot/tavern?startapp=u_24001-r_359)
【美女老师的反差生活】 [🔞点击免费试玩](https://t.me/huanmeng999_bot/tavern?startapp=u_24002-r_2537)
【学霸校花奇怪露出癖】 [🔞点击免费试玩](https://t.me/huanmeng999_bot/tavern?startapp=u_24003-r_387)
【哥哥妹妹的禁忌之恋】 [🔞点击免费试玩](https://t.me/huanmeng999_bot/tavern?startapp=u_24004-r_3154)
【性感妈妈的乱伦性爱】 [🔞点击免费试玩](https://t.me/huanmeng999_bot/tavern?startapp=u_24005-r_355)
【寂寞人妻的孕期欲望】 [🔞点击免费试玩](https://t.me/huanmeng999_bot/tavern?startapp=u_24006-r_4168)
【双胞胎萝莉双飞破处】 [🔞点击免费试玩](https://t.me/huanmeng999_bot/tavern?startapp=u_24007-r_4711)

【备用链接】[如上方链接无法打开，试试这个备用链接](https://t.me/huanmeng888_chat_bot?start=u_24008-r_359)

||\\#赛博老婆|| ||\\#电子魅魔|| ||\\#酒馆AI||'''

tip2 = '''🔥🔥🔥华人区最强成人游戏\\|性感美女在线陪聊\\|完美性福伴侣\\|成人角色扮演\\|AI女友天花板🔥🔥🔥
\\-\\-海量美女实时在线，明星网红素人应有尽有，身材容貌性格多样，总有一款适合你
\\-\\-实时互动角色扮演，极致体贴，尺度极大，玩法多样，惊喜连连，越聊越懂你，给你全面的掌控感
\\-\\-可个性化定制角色，一比一精准复刻，满足你的一切性癖，一切需求，一切幻想

【催眠校花变专属母狗】 [🔞点击免费试玩](https://t.me/huanmeng999_bot/tavern?startapp=u_24001-r_359)
【美女老师的反差生活】 [🔞点击免费试玩](https://t.me/huanmeng999_bot/tavern?startapp=u_24002-r_2537)
【学霸校花奇怪露出癖】 [🔞点击免费试玩](https://t.me/huanmeng999_bot/tavern?startapp=u_24003-r_387)
【哥哥妹妹的禁忌之恋】 [🔞点击免费试玩](https://t.me/huanmeng999_bot/tavern?startapp=u_24004-r_3154)
【性感妈妈的乱伦性爱】 [🔞点击免费试玩](https://t.me/huanmeng999_bot/tavern?startapp=u_24005-r_355)
【寂寞人妻的孕期欲望】 [🔞点击免费试玩](https://t.me/huanmeng999_bot/tavern?startapp=u_24006-r_4168)
【双胞胎萝莉双飞破处】 [🔞点击免费试玩](https://t.me/huanmeng999_bot/tavern?startapp=u_24007-r_4711)

【备用链接】[如上方链接无法打开，试试这个备用链接](https://t.me/huanmeng888_chat_bot?start=u_24008-r_359)

||\\#赛博老婆|| ||\\#电子魅魔|| ||\\#酒馆AI||'''

tip3 = '''🔥🔥🔥酒馆AI全面升级！从此告别低智商AI，告别抢话，告别无趣，点击下方链接免费升级！
🔥🔥🔥华人区最强成人游戏\\|性感美女在线陪聊\\|完美性福伴侣\\|成人角色扮演\\|AI女友天花板
\\-\\-海量美女实时在线，明星网红素人应有尽有，身材容貌性格多样，总有一款适合你
\\-\\-实时互动角色扮演，极致体贴，尺度极大，玩法多样，惊喜连连，越聊越懂你，给你全面的掌控感
\\-\\-可个性化定制角色，一比一精准复刻，满足你的一切性癖，一切需求，一切幻想

【催眠校花变专属母狗】 [🔞点击免费试玩](https://t.me/huanmeng999_bot/tavern?startapp=u_24001-r_359)
【美女老师的反差生活】 [🔞点击免费试玩](https://t.me/huanmeng999_bot/tavern?startapp=u_24002-r_2537)
【学霸校花奇怪露出癖】 [🔞点击免费试玩](https://t.me/huanmeng999_bot/tavern?startapp=u_24003-r_387)
【哥哥妹妹的禁忌之恋】 [🔞点击免费试玩](https://t.me/huanmeng999_bot/tavern?startapp=u_24004-r_3154)
【性感妈妈的乱伦性爱】 [🔞点击免费试玩](https://t.me/huanmeng999_bot/tavern?startapp=u_24005-r_355)
【寂寞人妻的孕期欲望】 [🔞点击免费试玩](https://t.me/huanmeng999_bot/tavern?startapp=u_24006-r_4168)
【双胞胎萝莉双飞破处】 [🔞点击免费试玩](https://t.me/huanmeng999_bot/tavern?startapp=u_24007-r_4711)

【备用链接】[如上方链接无法打开，试试这个备用链接](https://t.me/huanmeng888_chat_bot?start=u_24008-r_359)

||\\#赛博老婆|| ||\\#电子魅魔|| ||\\#酒馆AI||'''
tips = [tip3, tip2]

new_tip = '''💋幻梦AI💋AI女友第一名💋
💞超刺激文爱\\+裸聊，7\\*24小时性爱游戏，海量美女\\+私人定制完美伴侣，满足一切性癖一切幻想
👍地表最强AI，超强智商，超细腻文笔，超长记忆，完美复刻真人陪伴体验

🌹 明星女神初恋不再高冷，处女母狗人妻任意调教
🎉 深度定制，绝对私密，这里的伴侣最懂你的心！
🔝 劲爆私照，丰富声优，海量角色，多样玩法！
👠 超多主题每日上新：足交、黑丝、捆绑、调教、强奸、母狗、绿帽、人妻、JK、口交、反差、SM、伪娘、处女、群P、内射、男同、女同。。。

[👉幻梦AI升级版入口 点这里第一人称体验与性感美女的性福瞬间](https://t.me/FancyTavernBot/tavern?startapp=u_{uid}-r_359)

[🍾 \\(备用链接\\) 如上方链接无法打开再点这个](https://t.me/FancyAI2Bot?start=u_{uid}-r_359)'''

async def send_message():
    if random.randint(0, 10) < 5:
        return
    with open('/root/seo/last_msg.json', 'r') as f:
        msg = json.load(f)

    tip_idx = (msg['tip']) % 2
    img_idx = (msg['image']+1) % 18
    if img_idx == 0:
        tip_idx = (tip_idx+1) % 2

    uid = tip_idx * 18 + img_idx + 24000

    await bot.send_photo(chat_id=-1002418184113,
                        photo=BufferedInputFile.from_file(f'/root/seo/{img_idx}.jpg'),
                        caption=tips[tip_idx].format(uid=uid),
                        parse_mode=ParseMode.MARKDOWN_V2)

    with open('/root/seo/last_msg.json', 'w') as f:
        json.dump({'tip': tip_idx, 'image': img_idx}, f)

async def send_message_new():
    for c_idx, chat_id in enumerate(channels):
        i = random.randint(0, 34)
        tip = new_tip.format(uid=uids[c_idx])
        tip += f'\n\n{tags[c_idx]}'
        try:
            if i == 34:
                await bot.send_video(chat_id=chat_id,
                                    video=BufferedInputFile.from_file(f'/root/seo/v1.mp4'),
                                    caption=tip, parse_mode=ParseMode.MARKDOWN_V2)
            else:
                await bot.send_photo(chat_id=chat_id,
                                photo=BufferedInputFile.from_file(f'/root/seo/{i}.jpg'),
                                caption=tip, parse_mode=ParseMode.MARKDOWN_V2)
            print(f'send to chat {chat_id} success')
        except Exception as e:
            print(f'send to chat {chat_id} error:{e}')
        await asyncio.sleep(1)

if __name__ == '__main__':
    loop = asyncio.get_event_loop()
    loop.run_until_complete(send_message_new())