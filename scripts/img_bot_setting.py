from aiogram import Bo<PERSON>, types
import asyncio


async def set_bot_commands(bot: <PERSON><PERSON>):
    commands = [
        types.BotCommand(command="recharge", description="💎充值"),
        types.BotCommand(command="help", description="🔥作图设置"),
        types.BotCommand(command="start", description="🎨立即开始"),
        types.BotCommand(command="checkin", description="📅签到"),
        # 可以继续添加其它命令
    ]

    await bot.set_my_commands(commands)

    print("Bot commands set successfully.")

    commands = await bot.get_my_commands()  # 确认命令已设置
    print("Current bot commands:", commands)


async def set_bot_info(bot: Bot):
    bot_info = await bot.get_me()

    await bot.set_my_description(
        "欢迎来到幻夢AI生圖—全网最强AI色图大模型\n\n18+极品色图，只有你想不到，没有我们做不到\n\n 当前支持：二次元、福瑞风、写实、像素风\n未来：支持更多类型（日本AV风等），通通支持\n\nAI陪聊小程序：https://t.me/FancyTavernBot\n\n官方群：https://t.me/+Y2zdn4Rj7HVmYzE0 "
    )
    
    bot_des = await bot.get_my_description()
    print(f"Bot Description: {bot_des}")
    print(
        f"Bot Info: {bot_info.id}, {bot_info.username}, {bot_info.first_name}, {bot_info.last_name}"
    )


token_prod = "8134617725:AAHh_C2IgE7G1OVExQtDYk74DMW13v4s_iI"

token_test = "7512150600:AAFhl7lTFb9DgWnlNPZPvV9b2JttBiE2KgA"

if __name__ == "__main__":

    bot_prod = Bot(token_prod)

    bot = Bot(token=token_test)

    asyncio.run(set_bot_commands(bot))
    asyncio.run(set_bot_commands(bot_prod))
    # asyncio.run(set_bot_info(bot))
    # asyncio.run(set_bot_info(bot_prod))

    mkdown_text = """
    *bold \\*text*
_italic \\*text_
__underline__
~strikethrough~
||spoiler||
*bold _italic bold ~italic bold strikethrough ||italic bold strikethrough spoiler||~ __underline italic bold___ bold*
[inline URL](http://www.example.com/)
[inline mention of a user](tg://user?id=123456789)
![👍](tg://emoji?id=5368324170671202286)
`inline fixed-width code`
```
pre-formatted fixed-width code block
```
```python
pre-formatted fixed-width code block written in the Python programming language
```
>Block quotation started
>Block quotation continued
>Block quotation continued
>Block quotation continued
>The last line of the block quotation
**>The expandable block quotation started right after the previous block quotation
>It is separated from the previous block quotation by an empty bold entity
>Expandable block quotation continued
>Hidden by default part of the expandable block quotation started
>Expandable block quotation continued
>The last line of the expandable block quotation with the expandability mark||
"""

    # asyncio.run(bot.send_message(chat_id=5359176631, text=mkdown_text, parse_mode="MarkdownV2"))
