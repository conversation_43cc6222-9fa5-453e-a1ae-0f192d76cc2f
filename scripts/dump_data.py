# -*- coding=utf-8
import asyncio
from datetime import datetime
from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client
import sys
import os
import logging
import dotenv
import jsonlines, json
from persistence.feedback import FeedbackPersistence
from bson.objectid import ObjectId

dotenv.load_dotenv()
persistence = FeedbackPersistence('mongodb://mongouser:<EMAIL>:27017/test?replicaSet=cmgo-n8moe0qx_0&authSource=admin')

# 正常情况日志级别使用 INFO，需要定位时可以修改为 DEBUG，此时 SDK 会打印和服务端的通信信息
logging.basicConfig(level=logging.INFO, stream=sys.stdout)

# 1. 设置用户属性, 包括 secret_id, secret_key, region等。Appid 已在 CosConfig 中移除，请在参数 Bucket 中带上 Appid。Bucket 由 BucketName-Appid 组成
secret_id = os.environ['COS_SECRET_ID']     # 用户的 SecretId，建议使用子账号密钥，授权遵循最小权限指引，降低使用风险。子账号密钥获取可参见 https://cloud.tencent.com/document/product/598/37140
secret_key = os.environ['COS_SECRET_KEY']   # 用户的 SecretKey，建议使用子账号密钥，授权遵循最小权限指引，降低使用风险。子账号密钥获取可参见 https://cloud.tencent.com/document/product/598/37140
region = 'ap-singapore'      # 替换为用户的 region，已创建桶归属的 region 可以在控制台查看，https://console.cloud.tencent.com/cos5/bucket
                           # COS 支持的所有 region 列表参见 https://cloud.tencent.com/document/product/436/6224
token = None               # 如果使用永久密钥不需要填入 token，如果使用临时密钥需要填入，临时密钥生成和使用指引参见 https://cloud.tencent.com/document/product/436/14048
scheme = 'https'           # 指定使用 http/https 协议来访问 COS，默认为 https，可不填

config = CosConfig(Region=region, SecretId=secret_id, SecretKey=secret_key, Token=token, Scheme=scheme)
client = CosS3Client(config)

def get_last_id() -> str:
    if not os.path.exists('last_id.txt'):
        return '0'
    with open('last_id.txt', 'r') as f:
        return f.readlines()[-1].strip()
    
def save_last_id(last_id: ObjectId) -> None:
    with open('last_id.txt', 'a') as f:
        f.write(f'{last_id}\n')

def save_lines(data: list[dict]) -> str:
    f = f'feedback_{datetime.now().strftime('%Y-%m-%d')}.jsonl'
    with jsonlines.open(f, mode='a') as writer:
        writer.write_all(data)
    return f

def upload_file(f: str): 
    #### 文件流简单上传（不支持超过5G的文件，推荐使用下方高级上传接口）
    # 强烈建议您以二进制模式(binary mode)打开文件,否则可能会导致错误
    with open(f, 'rb') as fp:
        response = client.put_object(
            Bucket='sgp-ai-data-1323765209',
            Body=fp,
            Key=f'feedbacks/{f}',
            StorageClass='STANDARD',
            EnableMD5=False
        )
    return response['ETag']

async def dump_feedback() -> None:
    await persistence.post_init()
    last_id = '65d00b7650528759b9f5f3ac'
    lines = await persistence.get_feedback_result(last_id)
    if len(lines) == 0:
        return
    new_id = lines[-1]['_id']
    for line in lines:
        del line['_id']
        del line['user_id']
    f = save_lines(lines)
    etag = upload_file(f)
    save_last_id(new_id)
    logging.info(f'Uploaded {f} with etag {etag}')

async def main() -> None:
    await persistence.post_init()
    await dump_feedback()

async def dump_and_transpose() -> None:
    await persistence.post_init()
    last_id = '65e873483113bb12ffcefcfe'
    lines = await persistence.get_feedback_result(last_id)
    new_id = lines[-1]['_id']
    save_last_id(new_id)
    result_m5 = []
    result_m12 = []
    result = []
    lu = 0
    zzy = 0
    shv = 0
    lys = 0
    for line in lines:
        if not line['prompt'].startswith('限制级:'):
            continue
        if 'selected' in line and line['selected'] == -1:
            continue
        c_start = line['prompt'].find('下面是对话场景')
        c_end = line['prompt'].find('接下来生成1轮对话:')
        context = line['prompt'][c_start:c_end].strip()
        line['rounds'] = len(context.splitlines()) - 1

        line['choices'] = [x for x in line['choices'] if len(x) < 256]

        if len(line['choices']) <= 5:
            while len(line['choices']) < 5:
                line['choices'].append('')
            result_m5.append(line)
        else:
            while len(line['choices']) < 12:
                line['choices'].append('')
            result_m12.append(line)
        p: str = line['prompt']
        role_start = p.find('角色:') + 4
        role_end = p.find('\n', role_start)
        role = p[role_start:role_end].strip()
        if role == '肖鹿' or role == '小鹿':
            lu += 1
        if role == '沈星慧' or role == '沈慧星':
            shv += 1
        if role == '郑妍梓' or role == '郑梓妍':
            zzy += 1
        if role == '李思云' or role == '李云思':
            lys += 1

        line['pre-annotator'] = line['user_id']
        del line['_id']
        del line['user_id']
        for i, d in enumerate(line['choices']):
            line[f'answer{i+1}'] = d
        del line['choices']
        result.append(line)
    print(f'total: f{len(result)}, 郑妍梓: {zzy}, 沈慧星: {shv}, 李云思: {lys}, 肖鹿: {lu}')
    with open(f'dump_m5_{datetime.now().strftime('%Y-%m-%d')}.json', 'w') as f:
        json.dump(result_m5, f, indent=2, ensure_ascii=False)
    with open(f'dump_m12_{datetime.now().strftime('%Y-%m-%d')}.json', 'w') as f:
        json.dump(result_m12, f, indent=2, ensure_ascii=False)

if __name__ == '__main__':
    loop = asyncio.get_event_loop()
    loop.run_until_complete(main())
