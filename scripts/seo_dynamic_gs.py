import base64
from datetime import datetime
import asyncio, random
import re
import sys

from googleapiclient.discovery import build

from aiogram import Bo<PERSON>
from aiogram.enums import ParseMode
from aiogram.utils.keyboard import InlineKeyboardBuilder


from seo_common import get_credentials, get_medias, logger, setup_logger, get_service_account_credentials

GCP_API_KEY = 'AIzaSyBqS948Hs1xKDYl0nh3srUzVHkRDKVNf6w'
SPREAD_SHEET_ID = '1Vtac2i0CuuxcBT6aq0XGJfX3r8axBisU0N98NP4Xkck'
CHANNEL_INFO_RANGE = '频道信息配置!A2:Z500'
TAG_RANGE = '标签词包!A2:Z500'
TIP_RANGE = '推广文案!A2:Z500'
BUTTON_TEXT_RANGE = '按钮文案!A2:Z500'
LINK_RANGE = '按钮链接!A2:Z500'
BOT_SETTINGS_RANGE = '推送bot设置!A2:K20'

freq_logger = None

bots = {
'hive_role_bot' : Bot(token='**********:AAHobxJLglVe06xTW72hRBwFhQeJZZIAwQk'),
'bee_role_bot' : Bot(token='**********:AAGuey_C0le-gdPrkQ9u9aU0YP4ByVHoSKQ'),
'cow_role_bot' : Bot(token='**********:AAGO83KmPtDeGI1zBOyWdNeNmYo6nkmun8A'),
'eagle_role_bot' : Bot(token='**********:AAFJYAa-xsV7fBWecHuTQh8uWAd5GLHgtm8'),
'wasp_role_bot' : Bot(token='**********:AAGFIX9UyZC_qtcPg-uv-wMAtHD3sr_mmm8'),
'ant_role_bot' : Bot(token='**********:AAGFtRUFGkwRMkU37CGTpA3vlAJ-uQHPq10'),
'lynx_role_bot' : Bot(token='**********:AAFkF1naTW4GSH5Pc6pw7Nn_94zuh7q4kh0'),
'fawn_role_bot' : Bot(token='7737340942:AAGuGOE4aMeXoznMTVRlh4rzEetWUq0ROjo'),
'hawk_role_bot' : Bot(token='7981259522:AAG5hUHF0gMHWbe0XiNO5r1yR5wDcK0Icrk'),
'ram_role_bot' : Bot(token='7320769956:AAEYQqeS2d7-vFKcs0cxcksJQ4xlb0sNQSU'),
'finch_role_bot' : Bot(token='7676625281:AAER5-EOIdloc-ULzbtGd4cdWujSShBdXFg'),
'owl_role_bot' : Bot(token='8102321831:AAE2lb8rmsuqZ0F-XZ8009BwlwUeZDkxIEU'),
'elk_role_bot' : Bot(token='8045910511:AAHoekaZvI3pjqQkuPlEG9olK0le4xP8TDI'),
'mole_role_bot' : Bot(token='7506375341:AAEHQ_5WVzi0klfdCEmb2ZTsjCEI1U8PaMo'),
'cat_role_bot' : Bot(token='7930998558:AAHNgwRUzYe9pIC-0amdXvCuyEuHWeWqcYw'),
'dog_role_bot' : Bot(token='7831919897:AAGB-M7Rr-esOJeKOClP2wAe6fGe9rw9bzk'),
'bird_role_bot' : Bot(token='7729564328:AAHHLy1FzJS4cj5ID1ZKDZUU6C3Q00UaZ5M'),
'monkey_role_bot' : Bot(token='7895044381:AAEOGKESun9x_olg7kBy46BuK3NM_nolgws'),
'elephant_role_bot' : Bot(token='7277255961:AAE7okq-aOXNACzDvedaqQAik4oZB4BGQ9Q'),
'lion_role_bot' : Bot(token='7793776040:AAF5Cx6A-Vz0H9ZIVrbGOX9jO8ceKwUDCA0'),
'zebra_role_bot' : Bot(token='7876273180:AAF1rFtB2y30A2xlxU6fyh5Q7douh-ziXSE'),
'goat_role_bot' : Bot(token='8156005188:AAFMtZJqaQNygzHADkOu-suuJcquWdsiKJ4'),
'rabbit_role_bot' : Bot(token='7549804334:AAGTAYolx_VKA8gEe_rAsPWApcSHOn4Smek'),
'camel_role_bot' : Bot(token='8087483649:AAFtRjMtWCFXfmZqfxkGmXbRPdiqonQWymo'),
'bear_role_bot' : Bot(token='7476101781:AAF3rgML9S43l0tyixAcOMuV7Adz180R5pY'),
'panda_role_bot' : Bot(token='8013291403:AAEwEZoWisLR7BfxqtGL3J6t-eMmtkDvmbc'),
'fox_role_bot' : Bot(token='7737994546:AAFcCM8NxBGHKqQMwVa6AwlNr7Z5BhCfOg8'),
}

def find_bot(name: str):
    name = name.lower()
    name = name.lstrip('@')
    if name in bots:
        return bots[name]
    raise Exception(f'unknown bot name:{name}')

async def send_message(chat_id, content, markup, image_keys: list[str], bot: Bot):
    key = random.choice(image_keys)
    text = str(random.randint(0, 1000000))
    text = text.encode('utf-8')
    text = base64.urlsafe_b64encode(text).decode('utf-8')
    image_url = f'https://sgp-image-1*********.cos.ap-singapore.myqcloud.com/{key}?watermark/2/text/{text}/dissolve/10'
    logger.info(f'send with image url:{image_url}')
    tip = content
    try:
        await bot.send_photo(chat_id=chat_id, photo=image_url, caption=tip,
                             parse_mode=ParseMode.HTML, reply_markup=markup)
        logger.info(f'send to chat {chat_id} success')
    except Exception as e:
        logger.warning(f'send to chat {chat_id} error:{e}')

async def get_sheet_values():
    creds = get_service_account_credentials('/Users/<USER>/Downloads/tanver-9f19822ab41a.json')
    service = build('sheets', 'v4', credentials=creds, cache_discovery=False)
    sheet = service.spreadsheets()
    result = (
        sheet.values()
        .batchGet(spreadsheetId=SPREAD_SHEET_ID, ranges=[CHANNEL_INFO_RANGE, TAG_RANGE, TIP_RANGE, BUTTON_TEXT_RANGE, LINK_RANGE, BOT_SETTINGS_RANGE])
        .execute()
    )
    return result.get('valueRanges', [])

def safe_get(d: dict, r, index):
    if len(r) > index:
        key = r[index]
        if key in d:
            return d[key]
    return list(d.values())[0]

def is_done(r):
    # column M
    return len(r) > 12 and r[12] == '1'

async def process_row(r, tags_map, tips_map, button_texts_map,
                      links_map, bot: Bot, image_keys: list[str]):
    if not is_done(r):
        return

    chat_id = r[1]
    uid = r[2]

    tag = safe_get(tags_map, r, 9) # column J
    tips = safe_get(tips_map, r, 8) # column I
    button_text = safe_get(button_texts_map, r, 10) # column K
    link = safe_get(links_map, r, 11) # column L

    content = random.choice(tips[1:])
    content = content.format(uid=uid)
    # split by comma and space
    tags = re.split(r'[ |,|，|\n]', tag[1])
    tags = random.choices(tags, k=3)
    tags = [f'<span class="tg-spoiler">#{tag}</span>' for tag in tags]
    tag_str = " ".join(tags)
    content += f'\n\n{tag_str}'

    link = random.choice(link[1:]).format(uid=uid)
    btn = random.choice(button_text[1:])
    builder = InlineKeyboardBuilder()
    builder.button(text=btn, url=link)

    await send_message(chat_id, content, builder.as_markup(), image_keys, bot)
    now = datetime.now().timestamp()
    freq_logger.info(f'{chat_id},{now}')

sheet_values = []
loops = 0
bs = []

async def process_sheets_internal(target_bot_name: str):
    global loops, sheet_values, bs
    logger.info(f'loops count {loops}')
    if 0 < loops < 10:
        loops += 1
    else:
        sheet_values = await get_sheet_values()
        loops = 1
    values = sheet_values
    channel_info = []
    tags = []
    tips = []
    button_texts = []
    links = []
    bot_settings = []
    for r in values:
        name = r['range']
        vs = r['values']
        if name.find('频道信息配置') >= 0:
            channel_info = vs
        elif name.find('标签词包') >= 0:
            tags = vs
        elif name.find('推广文案') >= 0:
            tips = vs
        elif name.find('按钮文案') >= 0:
            button_texts = vs
        elif name.find('按钮链接') >= 0:
            links = vs
        elif name.find('推送bot设置') >= 0:
            bot_settings = vs

    for b in bot_settings:
        if b[0].lower().find(target_bot_name) >= 0:
            bs = b
            break

    sleep_time = 60
    enabled = '1'
    run_hours = [x for x in range(24)]
    if len(bs) > 0:
        sleep_time = int(bs[2])
        enabled = bs[4]
        if len(bs) > 3 and len(bs[3]) > 0:
            run_hours = [int(x) for x in bs[3].split(' ')]
    if enabled == '0':
        logger.info(f'bot {target_bot_name} is disabled, sleep {sleep_time} seconds')
        await asyncio.sleep(5 * 60)
        return
    current_hour = datetime.now().hour
    if current_hour not in run_hours:
        logger.info(f'bot {target_bot_name} is not in run hours {run_hours}, sleep {sleep_time} seconds')
        await asyncio.sleep(5 * 60)
        return

    logger.info(f'bot {target_bot_name} is enabled, sleep interval {sleep_time} seconds, run hours {run_hours}')
    bot = find_bot(target_bot_name)
    tags_map = {x[0]: x for x in tags}
    tips_map = {x[0]: x for x in tips}
    button_texts_map = {x[0]: x for x in button_texts}
    links_map = {x[0]: x for x in links}

    media_dirs = list(set([x[7] if len(x) > 7 else 'image01' for x in channel_info]))
    medias = get_medias(media_dirs)
    channel_with_bot_name = [(x[16].lower() if len(x) > 16 else 'hive', x) for x in channel_info]
    channels_for_cur_bot = [x for x in channel_with_bot_name if x[0].find(target_bot_name) >= 0 and is_done(x[1])]

    if len(channels_for_cur_bot) == 0:
        logger.info(f'bot {target_bot_name} has no channels, sleep {sleep_time} seconds')
        await asyncio.sleep(sleep_time)
        return

    sleep_interval = sleep_time / len(channels_for_cur_bot)
    for bot_name, r in channels_for_cur_bot:
        m_key = r[7] if len(r) > 7 else 'image01'
        image_keys = medias[m_key]
        logger.info(f'begin process :{r[0]}, bot: {bot_name}, media: {m_key}')
        await process_row(r, tags_map, tips_map, button_texts_map, links_map, bot, image_keys)
        await asyncio.sleep(sleep_interval)

async def process_sheets(target_bot_name: str):
    while True:
        try:
            await process_sheets_internal(f'{target_bot_name}_role_bot')
        except Exception as e:
            logger.warning(f'process sheets error:{e}')
        # await asyncio.sleep(1)

if __name__ == '__main__':
    if len(sys.argv) < 2:
        logger.error('need bot name')
        sys.exit(1)
    bot_name = sys.argv[1]
    freq_logger = setup_logger(bot_name)
    loop = asyncio.get_event_loop()
    loop.run_until_complete(process_sheets(bot_name))