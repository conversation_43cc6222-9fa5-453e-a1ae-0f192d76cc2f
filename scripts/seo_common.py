from io import BytesIO
import logging
import logging.config
import asyncio, random
import os.path
from PIL import Image

from google.auth.transport.requests import Request
from google.oauth2 import service_account
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build

from aiogram import Bot
from aiogram.types import BufferedInputFile, InputFile
from aiogram.enums import ParseMode

from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client
import requests

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

secret_id = 'IKIDVs31sb8SaXHuJWWRk3Bg2NbgZGl5OWVJ'
secret_key = 'g9OXVHrRs8AzbS6Wo36vYyBB1f5KSNfm'
region = 'ap-singapore'
token = None
scheme = 'https'

config = CosConfig(Region=region, SecretId=secret_id, SecretKey=secret_key, Token=token, Scheme=scheme)
client = CosS3Client(config)

token_path = 'token.json'
SCOPES = ["https://www.googleapis.com/auth/spreadsheets"]

def random_noise_to_image(img_bytes: bytes):
    image = Image.open(BytesIO(img_bytes))

    # Add random noise to the image
    width, height = image.size
    noise = Image.effect_noise((width, height), 10)
    # Convert images to RGBA mode to ensure compatibility
    image = image.convert('RGBA')
    noise = noise.convert('RGBA')

    # Resize noise to match image dimensions
    noise = noise.resize(image.size)

    # Blend images
    image = Image.blend(image, noise, 0.1)
    # Save the modified image to a BytesIO object
    img_byte_arr = BytesIO()
    image.save(img_byte_arr, format='PNG')
    img_byte_arr.seek(0)
    return img_byte_arr

def random_local():
    i = random.randint(0, 33)
    return BufferedInputFile.from_file(f'/root/seo/{i}.jpg')

def get_image_keys_from_cos(dir: str) -> list[str]:
    dir = dir.strip()
    if dir == 'default':
        prefix = 'image01/default/'
    elif dir != 'image01' and dir != '':
        prefix = f'{dir}/'
    else:
        p2 = random.randint(0, 255)
        pf = f'{p2:02x}'
        prefix = f'image01/{pf}'
    response = client.list_objects(
        Bucket='sgp-image-**********',
        Prefix=prefix
    )
    if 'Contents' in response:
        contents = response['Contents']
        return [key['Key'] for key in contents]
    return []

def random_image_from_cos(dir: str) -> InputFile | str:
    if dir == 'default':
        prefix = 'image01/default/'
    elif dir != 'image01':
        prefix = f'{dir}/'
    else:
        p2 = random.randint(0, 255)
        pf = f'{p2:02x}'
        prefix = f'image01/{pf}'
    response = client.list_objects(
        Bucket='sgp-image-**********',
        Prefix=prefix
    )
    if 'Contents' in response:
        contents = response['Contents']
        key = random.choice(contents)
        url = client.get_object_url(
            Bucket='sgp-image-**********',
            Key=key['Key']
        )
        print(f'get url: {url}')
        image_response = requests.get(url)
        image_byte_arr = random_noise_to_image(image_response.content)
        return BufferedInputFile(image_byte_arr.getvalue(), filename="result.png")

    return random_local()

def get_medias(dirs: list[str]) -> dict[str, list[str]]:
    result = {}
    for d in dirs:
        keys = get_image_keys_from_cos(d)
        result[d] = keys
    return result

def get_service_account_credentials(credentials_file: str = 'service_account.json'):
    return service_account.Credentials.from_service_account_file(credentials_file)

def get_credentials(credentials_file: str = 'credentials.json'):
    creds = None
    if os.path.exists(token_path):
        creds = Credentials.from_authorized_user_file(token_path, SCOPES)
    # If there are no (valid) credentials available, let the user log in.
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file(
                credentials_file, SCOPES
            )
            creds = flow.run_local_server(port=0)
        # Save the credentials for the next run
        with open(token_path, "w") as token:
            token.write(creds.to_json())
    return creds

def setup_logger(bot_name: str):
    logger = logging.getLogger(f"frequency_logger_{bot_name}")
    logger.setLevel(logging.DEBUG)

    file_handler = logging.FileHandler(f"frequency_{bot_name}.log")
    file_handler.setLevel(logging.DEBUG)

    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s"
    )
    file_handler.setFormatter(formatter)

    logger.addHandler(file_handler)

    return logger
