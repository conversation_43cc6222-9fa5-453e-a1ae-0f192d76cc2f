# %%
from datetime import datetime, timezone
import math

import mysql.connector

print(f"start job======================={datetime.now()}")
start_time = datetime.now().timestamp()
local = False

read_conn = mysql.connector.connect(
    user="tgbot_r",
    password="eadfsdEGE56",
    database="tavern",
    host="***********",
    port="3306",
)
insert_conn = None
if local:
    insert_conn = mysql.connector.connect(
        user="root",
        password="UtqY5EjrXVq5qejjwWm2",
        database="tavern_test",
        host="sg-cynosdbmysql-grp-li9atzst.sql.tencentcdb.com",
        port="26583",
    )
if not local:
    insert_conn = mysql.connector.connect(
        user="root",
        password="UtqY5EjrXVq5qejjwWm2",
        database="tavern",
        host="sg-cynosdbmysql-grp-li9atzst.sql.tencentcdb.com",
        port="26583",
    )


insert_cursor = insert_conn.cursor()
cursor = read_conn.cursor()
try:
    # 角色列表
    cursor.execute(
        """select 
        id, 
        created_at
        from chat_group_config
        where public = 1 and status = 1
        """
    )
    mid_list = cursor.fetchall()
    role_diff_days = {}
    for x in mid_list:
        created_at = x[1].astimezone(timezone.utc)
        now = datetime.now(timezone.utc)
        diff_day = (now - created_at).days
        role_diff_days[int(x[0])] = diff_day

    # 角色统计热度值
    cursor.execute(
        """select 
        mode_target_id, 
        count(distinct(conversation_id)) as conv_count,
        count(1) as msg_count,
        count(distinct(user_id)) as user_count,
        sum(consume) as total_diamond,
        count(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and model='claude-3-haiku' THEN 1 END) AS h24_haiku_count
        from chat_history_statistic 
        where mode_type = 'group'
        group by mode_target_id
        """
    )
    ret_list = cursor.fetchall()
    data_list = []
    for x in ret_list:
        numerator = int(x[3]) + int(x[1]) + int(x[2]) + int(x[4]) + 666
        diff_days = role_diff_days.get(x[0], 0)
        denominator = math.pow(diff_days + 1, 1 / 100)
        hot = int((numerator / denominator))
        data_list.append(
            {
                "group_id": int(x[0]),
                "total_conversation": int(x[1]),
                "total_message": int(x[2]),
                "user_count": int(x[3]),
                "total_diamond": int(x[4]),
                "hot_v1": hot,
                "h24_haiku_count": int(x[5]),
            }
        )
    sql = """
        INSERT INTO group_statistic (
        group_id, total_conversation, total_message, user_count, total_diamond, hot_v1,h24_haiku_count
        ) VALUES (
        %(group_id)s, %(total_conversation)s, %(total_message)s, %(user_count)s, %(total_diamond)s, %(hot_v1)s,%(h24_haiku_count)s
    ) ON DUPLICATE KEY UPDATE
        total_conversation = VALUES(total_conversation),
        total_message = VALUES(total_message),
        user_count = VALUES(user_count),
        total_diamond = VALUES(total_diamond),
        hot_v1 = VALUES(hot_v1)+base_count,
        h24_haiku_count = VALUES(h24_haiku_count)
    """
    insert_cursor.executemany(sql, data_list)
    for data in data_list:
        print(data)
    insert_conn.commit()


except Exception as e:
    print(e)
read_conn.close()
insert_conn.close()
end_time = datetime.now().timestamp()

print(f"end job======================={datetime.now()},duration's:{int(end_time - start_time)}")