from io import BytesIO
import asyncio, random
import os.path
from PIL import Image

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build

from aiogram import Bo<PERSON>
from aiogram.types import BufferedInputFile, InputFile
from aiogram.enums import ParseMode

from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client
import requests
from seo_common import logger, client

SPREAD_SHEET_ID = '1xbQsnBZOoXp4Yhjx9cMstUJ-4ljmOFWEuKQ_cjS_sFg'
RANGE = 'channels!A2:K200'
SCOPES = ["https://www.googleapis.com/auth/spreadsheets"]

bot = Bot(token='7566072973:AAFAZPdALFEdWCfP-aVdTTgQp4lN2NKYboU')

token_path = 'token.json'

def random_noise_to_image(img_bytes: bytes):
    image = Image.open(BytesIO(img_bytes))

    # Add random noise to the image
    width, height = image.size
    noise = Image.effect_noise((width, height), 10)
    # Convert images to RGBA mode to ensure compatibility
    image = image.convert('RGBA')
    noise = noise.convert('RGBA')

    # Resize noise to match image dimensions
    noise = noise.resize(image.size)

    # Blend images
    image = Image.blend(image, noise, 0.1)
    # Save the modified image to a BytesIO object
    img_byte_arr = BytesIO()
    image.save(img_byte_arr, format='PNG')
    img_byte_arr.seek(0)
    return img_byte_arr

def random_local():
    i = random.randint(0, 33)
    return BufferedInputFile.from_file(f'/root/seo/{i}.jpg')

def random_image_from_cos(dir: str) -> InputFile | str:
    if dir == 'default':
        prefix = 'image01/default/'
    elif dir != 'image01':
        prefix = f'{dir}/'
    else:
        p2 = random.randint(0, 255)
        pf = f'{p2:02x}'
        prefix = f'image01/{pf}'
    response = client.list_objects(
        Bucket='sgp-image-1323765209',
        Prefix=prefix
    )
    if 'Contents' in response:
        contents = response['Contents']
        key = random.choice(contents)
        url = client.get_object_url(
            Bucket='sgp-image-1323765209',
            Key=key['Key']
        )
        logger.info(f'get url: {url}')
        image_response = requests.get(url)
        image_byte_arr = random_noise_to_image(image_response.content)
        return BufferedInputFile(image_byte_arr.getvalue(), filename="result.png")

    return random_local()

async def send_message_new(chat_id, content, dir: str):
    try:
        media = random_image_from_cos(dir)
    except Exception as e:
        logger.warning(f'get image error:{e}')
        return
    tip = content
    try:
        await bot.send_photo(chat_id=chat_id, photo=media,
                            caption=tip, parse_mode=ParseMode.HTML)
        logger.info(f'send to chat {chat_id} success')
    except Exception as e:
        logger.warning(f'send to chat {chat_id} error:{e}')

def get_credentials(credentials_file: str = 'credentials.json'):
    creds = None
    if os.path.exists(token_path):
        creds = Credentials.from_authorized_user_file(token_path, SCOPES)
    # If there are no (valid) credentials available, let the user log in.
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file(
                credentials_file, SCOPES
            )
            creds = flow.run_local_server(port=0)
        # Save the credentials for the next run
        with open(token_path, "w") as token:
            token.write(creds.to_json())
    return creds

async def process_sheets_internal():
    creds = get_credentials()
    service = build('sheets', 'v4', credentials=creds, cache_discovery=False)
    sheet = service.spreadsheets()
    result = (
        sheet.values()
        .get(spreadsheetId=SPREAD_SHEET_ID,
             range=RANGE)
        .execute()
    )
    values = result.get('values', [])
    for row in values:
        if len(row[0]) == 0:
            print('end')
            break
        chat_id = row[1]
        uid = row[2]
        tags = row[4]
        tags = tags.split(' ')
        tags = [f'<span class="tg-spoiler">{tag}</span>' for tag in tags]
        tag_str = ' '.join(tags)
        content = row[3].format(uid=uid)
        content = f'{content}\n\n{tag_str}'
        media_dir = row[5] if len(row) > 6 else 'default'
        logger.info(f'begin send to chat: {chat_id}, {row[0]}')
        await send_message_new(chat_id, content, media_dir.strip())
        await asyncio.sleep(1)

async def process_sheets():
    while True:
        try:
            await process_sheets_internal()
        except Exception as e:
            logger.warning(f'process sheets error:{e}')
        await asyncio.sleep(5)

if __name__ == '__main__':
    loop = asyncio.get_event_loop()
    loop.run_until_complete(process_sheets())