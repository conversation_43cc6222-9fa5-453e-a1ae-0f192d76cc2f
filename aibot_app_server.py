
import asyncio
import datetime
import logging
import os

from typing import List


from aiogram import Bo<PERSON>, <PERSON><PERSON>atch<PERSON>
# from aiogram import LoggingMiddleware
from aiogram.types import Update, InlineKeyboardButton
from aiogram.fsm.storage.memory import MemoryStorage


from dotenv import load_dotenv
from fastapi import FastAP<PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware

from pydantic import BaseModel, Field
import sentry_sdk
from tortoise import Tortoise
from tortoise.contrib.fastapi import register_tortoise
import uvicorn

from common import loggers
from common.models.ai_bot_admin.group_config import BotGroupConfigBO, BotWelcomeConfigBO, BotWelcomeConfigCreate, BotWelcomeConfigUpdate, SpamProtectionRuleCreate,SpamProtectionRuleUpdate, SpamProtectionRuleBO

from services.bot_group.bot_group_service import BotGroupConfigAdminService,AutoReply<PERSON>ule<PERSON><PERSON><PERSON>, SpamProtection<PERSON><PERSON><PERSON>,Bot<PERSON>elcomeConfigHandler,AutoReplyRuleService

from services.bot_group.biz.tg_group_user_msg_biz import GroupMsgUserStatBiz,UserInviteShareStatBiz

from services.bot_group.aibot_help import HandleGroupUserMsgMiddleware, SaveMessageMiddleware,BotSendMsgPrivate
from persistence.models.models_bot_group import BotWelcomeConfigModel,SpamProtectionRule,BotGroupMap

from aibot_handlers.group_help_dp_router import create_group_help_dp_router,bot_handler_manager
from aibot_handlers.user_card_dp_router import create_user_card_router,create_staff_admin_router    



logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s (%(filename)s:%(lineno)d) [%(levelname)s] %(message)s",handlers=[logging.StreamHandler(),loggers.local_bot_help_handler])

log = logging.getLogger(__name__)


FULL_NAME_LEN = 15
# 中间件
# class RateLimitMiddleware:
#     async def __call__(self, handler, event, data):
#         user_id = event.from_user.id
#         current_time = time.time()
#         if current_time - message_cooldowns[user_id] < 5:
#             await event.delete()
#             return
#         message_cooldowns[user_id] = int(current_time)
#         return await handler(event, data)

# 加载环境变量
load_dotenv()

TMA_BOT_TOKEN = os.getenv("TMA_BOT_TOKEN")
CHAT_BOT_TOKEN = os.getenv("CHAT_BOT_TOKEN")

IMAGE_CHECK_API = os.getenv("IMAGE_CHECK_API")


# os.environ['NAI_PASSWORD'] = 'wYxmmK5FP2v$C5w'
# os.environ['NAI_USERNAME'] = '<EMAIL>'
os.environ['NAI_TOKEN'] = 'pst-KBxbYoH2YJDtvdKrq9MeXCzYT1YgQyGhYyqIUsyZrma3GAv1uBX46twT5lwSNonb'



BACK_STATS_MENU = InlineKeyboardButton(text="🏘返回", callback_data='stats')

class BotManager:
    def __init__(self):
        self.bots : dict[str,Bot] = {}
        self.dispatchers = {}
        self.configs = {}
        self.auto_reply_rule_handlers : dict[int,AutoReplyRuleHandler] = {}
        self.spam_protection_rule_handlers : dict[int,SpamProtectionHandler] = {}
        self.g_white_users = {}
        self.welcome_config : dict[int,BotWelcomeConfigHandler] = {}
        self.sender_bot = BotSendMsgPrivate(TMA_BOT_TOKEN,CHAT_BOT_TOKEN) # type: ignore
        
    async def initialize_bots_from_db(self):
        bot_configs = await BotGroupConfigAdminService.get_all_active_bot_configs()
        for bot_config in bot_configs:
            g_white_users = bot_config.g_white_users if isinstance(bot_config.g_white_users, list) else []
            await self.add_bot(bot_config.bot_name, bot_config.bot_token, bot_config.bot_group_id, g_white_users, bot_config.bot_type)

    # config = {"group_chat_id": 123456789,"group_admin_id": 0,"welcome_msg": "Welcome!"}
    async def add_bot(self, bot_name:str, token:str, group_id:int,g_white_users:List[int]=[],bot_type:str="group_help"):
        
        log.info(f"Adding bot {bot_name} ,{group_id} with token {token},{bot_type}")
        if bot_name in self.bots:
            raise ValueError(f"Bot with name {bot_name} already exists.")
        
        bot = Bot(token=token)
        storage = MemoryStorage()
        dp = Dispatcher(bot=bot,storage=storage)
        dp.update.outer_middleware(SaveMessageMiddleware())
        # dp.update.outer_middleware(HandleGroupUserMsgMiddleware())
    

        
        self.bots[bot_name] = bot
        self.dispatchers[bot_name] = dp 

        if bot_type == "group_help":
            #增加群消息，转发用户的消息
            dp.update.outer_middleware(HandleGroupUserMsgMiddleware())
            group_help_router = create_group_help_dp_router()
            dp.include_router(group_help_router)
            #群管理助手,群组消息处理
            
            await bot_handler_manager.load_bot_handler(bot_id=bot.id)
            # welcome_config_handler = BotWelcomeConfigHandler(group_id)
            # await welcome_config_handler.load_bot_welcome_config()
            # asyncio.create_task(welcome_config_handler.start_periodic_loading(60))
            # # self.welcome_config[group_id] = welcome_config_handler
            # bot_handler_manager.add_bot_handler(group_id,welcome_config_handler)

            # auto_repley_handler = AutoReplyRuleHandler(group_id)
            # await auto_repley_handler.load_rules_from_db()
            # # 创建异步任务来定时加载规则 60s
            # asyncio.create_task(auto_repley_handler.start_periodic_loading(60))
            # # self.auto_reply_rule_handlers[group_id] = auto_repley_handler
            # bot_handler_manager.add_bot_handler(group_id,auto_repley_handler)
            
            # spam_protection_rule_handler = SpamProtectionHandler(group_id)
            # await spam_protection_rule_handler.load_rules_from_db()
            # # self.spam_protection_rule_handlers[group_id] = spam_protection_rule_handler
            # asyncio.create_task(spam_protection_rule_handler.start_periodic_loading(60))
            # bot_handler_manager.add_bot_handler(group_id,spam_protection_rule_handler)
        elif bot_type == "user_card_ff":
            user_card_router = create_user_card_router()
            dp.include_router(user_card_router)

        elif bot_type == "staff_admin":
            user_card_staff_amdin_router = create_staff_admin_router()
            dp.include_router(user_card_staff_amdin_router)
        elif bot_type == "staff_admin&user_card_ff":
            user_card_staff_amdin_router = create_staff_admin_router()
            user_card_router = create_user_card_router()
            dp.include_router(user_card_router)
            dp.include_router(user_card_staff_amdin_router)
        elif bot_type == "help_admin":
            user_card_router = create_user_card_router()
            group_help_router = create_group_help_dp_router()
            dp.include_router(group_help_router)
            dp.include_router(user_card_router)
        # elif bot_type == "image_bot":
        #     image_bot_router = create_image_bot_router()
        #     dp.include_router(image_bot_router)
        else:
            log.error(f"Unsupported bot type: {bot_type}")
            self.remove_bot(bot_name)
        
    def remove_bot(self, bot_name):
        if bot_name not in self.bots:
            raise ValueError(f"Bot with name {bot_name} does not exist.")
        
        del self.bots[bot_name]
        del self.dispatchers[bot_name]

    async def set_webhook(self, bot_name):
        if bot_name not in self.bots:
            raise ValueError(f"Bot with name {bot_name} does not exist.")
        
        webhook_url = WEBHOOK_URL_TEMPLATE.format(bot_name=bot_name)
        log.info(f"Setting webhook for bot {bot_name} to {webhook_url}")
        allow_updates = ["message","edited_message","channel_post","edited_channel_post","callback_query","my_chat_member","chat_member"]
        await self.bots[bot_name].set_webhook(url=webhook_url,allowed_updates=allow_updates)

    async def delete_webhook(self, bot_name):
        if bot_name not in self.bots:
            raise ValueError(f"Bot with name {bot_name} does not exist.")
        
        log.info(f"Deleting webhook for bot {bot_name}")
        # await self.bots[bot_name].delete_webhook()

    async def process_update(self, bot_name, update: Update):
        if bot_name not in self.dispatchers:
            log.error(f"Bot with name {bot_name} does not exist.")
            return
        bot = self.bots[bot_name]
        await self.dispatchers[bot_name].feed_update(bot=bot, update=update)







# Initialize FastAPI app and BotManager
sentry_sdk.init(
    dsn="https://<EMAIL>:8443/10",

    traces_sample_rate=0.05
)


app = FastAPI()


# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有 HTTP 方法
    allow_headers=["*"],  # 允许所有 HTTP 头
)

bot_manager = BotManager()

WEBHOOK_HOST = os.getenv("BG_WEBHOOK_HOST")
WEBHOOK_PATH_TEMPLATE = '/webhook/{bot_name}'
WEBHOOK_URL_TEMPLATE = f"{WEBHOOK_HOST}{WEBHOOK_PATH_TEMPLATE}"

# #增加测试的bot
# bot_manager.add_bot(bot_name='ai747_bot',token="7357790700:AAHt_84ueO_t6bN3Pw-ewANthYIW8I4cO_k",config={"group_chat_id":-1002226814292})

class MsgStatsParams(BaseModel):
    grop_id: int = Field(..., description="Example: 12345")
    start_date: str = Field(..., examples=["2023-10-01"])
    end_date: str = Field(..., examples=["2023-10-05"])


Tortoise.init_models(["persistence.models.models_bot_group","persistence.models.models","persistence.models.models_bot_image"], "models")
register_tortoise(
    app=app,
    db_url=os.environ["MYSQL_URL"],
    modules={"models": ["persistence.models.models_bot_group","persistence.models.models","persistence.models.models_bot_image"]},
    generate_schemas=True,
)

# Set webhook routes dynamically
@app.post(WEBHOOK_PATH_TEMPLATE)
async def telegram_webhook(request: Request,bot_name: str):
    try:
        update = Update(**await request.json())
        log.info(f"Received telegram_webhook bot {bot_name},{update.model_dump(exclude_none=True)}")        
        
        await bot_manager.process_update(bot_name, update)
    except Exception as e:
        log.exception(f"process_update error update_id:{update.update_id},error:{e}")
        # raise HTTPException(status_code=500, detail="Request handling error")
    return JSONResponse({"status": "ok"})

@app.get("/sentry-debug")
async def trigger_error():
    division_by_zero = 1 / 0

# API endpoint to add a new bot
@app.post("/add_bot")
async def add_bot(bot_config: BotGroupConfigBO):
    try:
        await bot_manager.add_bot(bot_config.bot_name, bot_config.token,bot_config.group_id,bot_config.g_white_users,bot_config.bot_type)
        await bot_manager.set_webhook(bot_config.bot_name)
        await BotGroupConfigAdminService.save_bot_group_config(bot_config.bot_name, bot_config.token,bot_config.group_id,bot_config.g_white_users,bot_type=bot_config.bot_type)
        return {"message": f"Bot {bot_config} added and webhook set."}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    

@app.post("/add_bot_group_map")
async def add_bot_group_map(bot_id: int,group_id:int,bot_token:str):
    try:
        await BotGroupMap.create(bot_id=bot_id,group_id=group_id,bot_token=bot_token)
        
        await bot_handler_manager.load_bot_handler(bot_id=bot_id)
        return {"message": f"Bot {bot_id} added to group {group_id}."}
    except ValueError as e:
        log.error(f"add_bot_group_map error {bot_id},{group_id}, {e}",exc_info=True)
        raise HTTPException(status_code=400, detail=str(e))


# API endpoint to remove a bot
# @app.post("/remove_bot")
# async def remove_bot(bot_name: str):
#     try:
#         await bot_manager.delete_webhook(bot_name)
#         bot_manager.remove_bot(bot_name)
#         await BotGroupConfigAdminService.delete_bot_group_config(bot_name)
#         return {"message": f"Bot {bot_name} removed and webhook deleted."}
#     except ValueError as e:
#         raise HTTPException(status_code=400, detail=str(e))

#Api endpoint to get all bots
@app.get("/get_all_bots")
async def get_all_bots(v_p:str):
    if v_p != 'bot_123':
        raise HTTPException(status_code=400, detail="Invalid view permission")
    return await BotGroupConfigAdminService.get_all_active_bot_configs()

# Set webhooks on startup
@app.on_event("startup")
async def on_startup():
    
    # Initialize bots from database
    await bot_manager.initialize_bots_from_db()
    for bot_name in bot_manager.bots.keys():
        await bot_manager.set_webhook(bot_name)

    # Start the spam protection task
    asyncio.create_task(SpamProtectionHandler.video_ff_check_task())
    
# Delete webhooks on shutdown
@app.on_event("shutdown")
async def on_shutdown():
    # for bot_name in bot_manager.bots.keys():
    #     await bot_manager.delete_webhook(bot_name)
    pass



@app.post("/author_reply_rules/")
async def create_author_reply_rule(msg_link: str,author_use_name:str,group_user_name:str,group_id_list:List[int]=[-1002223050046,-1002357871790]):
    
    result = await AutoReplyRuleService.add_author_auto_reply_rule(msg_link,author_use_name,group_id_list,group_user_name)
    
    return {"message": result}

# @app.post("/auto_reply_rules/", response_model=AutoReplyRuleBO)
# async def create_auto_reply_rule(rule: AutoReplyRuleCreate):
#     rule_obj = await AutoReplyRule.create(**rule.dict())
#     return AutoReplyRuleBO.from_orm(rule_obj)
# @app.get("/auto_reply_rules/{rule_id}", response_model=AutoReplyRuleBO)
# async def get_auto_reply_rule(rule_id: int):
#     rule = await AutoReplyRule.get(id=rule_id)
#     if not rule:
#         raise HTTPException(status_code=404, detail="AutoReplyRule not found")
#     return AutoReplyRuleBO.from_orm(rule)

# @app.put("/auto_reply_rules/{rule_id}", response_model=AutoReplyRuleBO)
# async def update_auto_reply_rule(rule_id: int, rule: AutoReplyRuleUpdate):
#     rule_obj = await AutoReplyRule.get(id=rule_id)
#     if not rule_obj:
#         raise HTTPException(status_code=404, detail="AutoReplyRule not found")
#     await rule_obj.update_from_dict(rule.dict(exclude_unset=True))
#     await rule_obj.save()
#     return AutoReplyRuleBO.from_orm(rule_obj)

# @app.delete("/auto_reply_rules/{rule_id}", response_model=dict)
# async def delete_auto_reply_rule(rule_id: int):
#     rule = await AutoReplyRule.get(id=rule_id)
#     if not rule:
#         raise HTTPException(status_code=404, detail="AutoReplyRule not found")
#     await rule.delete()
#     return {"message": "AutoReplyRule deleted successfully"}

# @app.get("/auto_reply_rules/", response_model=List[AutoReplyRuleBO])
# async def get_all_auto_reply_rules():
#     rules = await AutoReplyRule.all()
#     return [AutoReplyRuleBO.from_orm(rule) for rule in rules]



@app.post("/spam_protection_rules/", response_model=SpamProtectionRuleBO)
async def create_spam_protection_rule(rule: SpamProtectionRuleCreate):
    rule_obj = await SpamProtectionRule.create(**rule.dict())
    return SpamProtectionRuleBO.from_orm(rule_obj)

@app.get("/spam_protection_rules/{rule_id}", response_model=SpamProtectionRuleBO)
async def get_spam_protection_rule(rule_id: int):
    rule = await SpamProtectionRule.get(rule_id=rule_id)
    if not rule:
        raise HTTPException(status_code=404, detail="SpamProtectionRule not found")
    return SpamProtectionRuleBO.from_orm(rule)

@app.put("/spam_protection_rules/{rule_id}", response_model=SpamProtectionRuleBO)
async def update_spam_protection_rule(rule_id: int, rule: SpamProtectionRuleUpdate):
    rule_obj = await SpamProtectionRule.get(rule_id=rule_id)
    if not rule_obj:
        raise HTTPException(status_code=404, detail="SpamProtectionRule not found")
    await rule_obj.update_from_dict(rule.dict(exclude_unset=True))
    await rule_obj.save()
    return SpamProtectionRuleBO.from_orm(rule_obj)

@app.delete("/spam_protection_rules/{rule_id}", response_model=dict)
async def delete_spam_protection_rule(rule_id: int):
    rule = await SpamProtectionRule.get(rule_id=rule_id)
    if not rule:
        raise HTTPException(status_code=404, detail="SpamProtectionRule not found")
    await rule.delete()
    return {"message": "SpamProtectionRule deleted successfully"}

@app.get("/spam_protection_rules/", response_model=List[SpamProtectionRuleBO])
async def get_all_spam_protection_rules():
    rules = await SpamProtectionRule.all()
    return [SpamProtectionRuleBO.from_orm(rule) for rule in rules]


@app.post("/bot_welcome_configs/", response_model=BotWelcomeConfigBO)
async def create_bot_welcome_config(config: BotWelcomeConfigCreate):
    config_obj = await BotWelcomeConfigModel.create(**config.dict())
    return BotWelcomeConfigBO.from_orm(config_obj)

@app.get("/bot_welcome_configs/{config_id}", response_model=BotWelcomeConfigBO)
async def get_bot_welcome_config(config_id: int):
    config = await BotWelcomeConfigModel.get(id=config_id)
    if not config:
        raise HTTPException(status_code=404, detail="BotWelcomeConfig not found")
    return BotWelcomeConfigBO.from_orm(config)

@app.put("/bot_welcome_configs/{config_id}", response_model=BotWelcomeConfigBO)
async def update_bot_welcome_config(config_id: int, config: BotWelcomeConfigUpdate):
    config_obj = await BotWelcomeConfigModel.get(id=config_id)
    if not config_obj:
        raise HTTPException(status_code=404, detail="BotWelcomeConfig not found")
    await config_obj.update_from_dict(config.dict(exclude_unset=True))
    await config_obj.save()
    return BotWelcomeConfigBO.from_orm(config_obj)

@app.delete("/bot_welcome_configs/{config_id}", response_model=dict)
async def delete_bot_welcome_config(config_id: int):
    config = await BotWelcomeConfigModel.get(id=config_id)
    if not config:
        raise HTTPException(status_code=404, detail="BotWelcomeConfig not found")
    await config.delete()
    return {"message": "BotWelcomeConfig deleted successfully"}

@app.get("/bot_welcome_configs/", response_model=List[BotWelcomeConfigBO])
async def get_all_bot_welcome_configs():
    configs = await BotWelcomeConfigModel.all()
    return [BotWelcomeConfigBO.from_orm(config) for config in configs]

@app.get("/msg_stats/cal_hourly_msg_stats")
async def cal_hourly_msg_stats(grop_id:int,start_date:str,end_date:str):
    
    log.info(f"cal_hourly_msg_stats grop_id:{grop_id},start_date:{start_date},end_date:{end_date}")
    
    start_date_time = datetime.datetime.strptime(start_date, "%Y-%m-%d %H:%M:%S")
    end_date_time = datetime.datetime.strptime(end_date, "%Y-%m-%d %H:%M:%S")
    
    date_hour,msg_stag = await GroupMsgUserStatBiz.cal_user_hours_msg_stat(grop_id,start_date_time,end_date_time)
    
    return {"date_hour":date_hour,"msg_stag":msg_stag}

@app.get("/msg_stats/cal_daily_msg_stats")
async def cal_daily_msg_stats(grop_id:int,start_date:str):
    
    log.info(f"cal_daily_msg_stats grop_id:{grop_id},start_date:{start_date}")

    start_date_time = datetime.datetime.strptime(start_date, "%Y-%m-%d")

    await GroupMsgUserStatBiz.cal_daily_user_msg_stat(grop_id,start_date_time)

@app.get("/inv_stats/cal_daily_inv_stats")
async def cal_daily_inv_stats(start_date:str):
    
    log.info(f"cal_daily_inv_statsstart_date:{start_date}")

    start_date_time = datetime.datetime.strptime(start_date, "%Y-%m-%d")
    await UserInviteShareStatBiz.cal_user_invite_cnt(start_date_time)

@app.get("/inv_stats/get_daily_inv_stats")
async def get_daily_inv_stats(start_date:str):
    
    log.info(f"get_daily_inv_stats start_date:{start_date}")
    start_date_time = datetime.datetime.strptime(start_date, "%Y-%m-%d")
    return await UserInviteShareStatBiz.get_daily_user_invite_stat(start_date_time)

def build_cron__date_exp(date: datetime.datetime):
    minute = date.minute
    hour = date.hour
    day = date.day
    month = date.month
    cron_expression = f"{minute} {hour} {day} {month} *"
    return cron_expression

def build_cron_exp(interval_seconds:int):
    if interval_seconds < 60:
        cron_expression = f"*/{interval_seconds} * * * * *"
    elif interval_seconds < 3600:
        minutes = interval_seconds // 60
        cron_expression = f"*/{minutes} * * * *"
    elif interval_seconds < 86400:
        hours = interval_seconds // 3600
        cron_expression = f"0 */{hours} * * *"
    elif interval_seconds < 604800:
        days = interval_seconds // 86400
        cron_expression = f"0 0 */{days} * *"
    else:
        weeks = interval_seconds // 604800
        cron_expression = f"0 0 1 */{weeks} *"
    
    return cron_expression

# # 定时消息设置相关
# @app.post("/auto_send_msg/send_config")
# async def send_auto_send_msg_config(auto_send_config: dict):
#     log.info(f"send_auto_send_msg_config:{auto_send_config}")
    
#     if auto_send_config['id'] == 0:
    
#         auto_send_message_config = AutoSendMessageConfig(
#         title=auto_send_config['title'],
#         content=auto_send_config['content'],
#         type=auto_send_config['type'],
#         start_time=datetime.datetime.fromisoformat(auto_send_config['startDate']),
#         end_time=datetime.datetime.fromisoformat(auto_send_config['endDate']),
#         enabled=auto_send_config['enabled'],
#         delete_previous=auto_send_config['isDelete'],
#         )
#     else:
#         auto_send_message_config = await AutoSendMessageConfig.get(id=auto_send_config['id'])
#         if not auto_send_message_config:
#             raise ValueError("AutoSendMessageConfig not found")
#         auto_send_message_config.title = auto_send_config['title']
#         auto_send_message_config.content = auto_send_config['content']
#         auto_send_message_config.start_time = datetime.datetime.fromisoformat(auto_send_config['startDate'])
#         auto_send_message_config.end_time = datetime.datetime.fromisoformat(auto_send_config['endDate'])
#         auto_send_message_config.enabled = auto_send_config['enabled']
#         auto_send_message_config.delete_previous = auto_send_config['isDelete']
    
#     auto_send_message_config.to_group_id = auto_send_config['groupId']
#     if "topicId" in auto_send_config:
#         auto_send_message_config.to_thread_id = auto_send_config['topicId']
#     auto_send_message_config.msg_type = 0
#     auto_send_message_config.msg_content ={
#         "msg_type":0,
#         "msg_content":auto_send_config['content']
#     }
    
#     if auto_send_config['type'] == "Date":
#         auto_send_message_config.job_type = "Date"
#         send_time = datetime.datetime.fromisoformat(auto_send_config['runDate'])
#         cron_exe  = build_cron__date_exp(send_time)
#         auto_send_message_config.send_time = send_time
#         auto_send_message_config.cron_expression = cron_exe
#     elif auto_send_config['type'] == "Interval":
#         auto_send_message_config.job_type = "Interval"
#         auto_send_message_config.interval = auto_send_config['interval']  
#         cron_exe = build_cron_exp(auto_send_config['interval'])
#         auto_send_message_config.cron_expression = cron_exe
#         auto_send_message_config.send_time = datetime.datetime.now()
#     else:
#         raise ValueError("Invalid type")
    
#     await auto_send_message_config.save()
   
        
        


# @app.get("/auto_send_msg/list")
# async def list_auto_send_msg() -> list[AutoSendMsgTaskBO]:
#     auto_send_message_configs_list = []
    
#     auto_send_message_configs = await AutoSendMessageConfig.all()
   
#     for auto_send_message_config in auto_send_message_configs:
       
#         auto_send_bot = AutoSendMsgTaskBO(
#            id=auto_send_message_config.id,
#            groupId=auto_send_message_config.to_group_id,
#            topicId=auto_send_message_config.to_thread_id,
#            title=auto_send_message_config.title,
#            content=auto_send_message_config.content,
#            type=auto_send_message_config.job_type,
#            interval=auto_send_message_config.interval,
#            startDate=auto_send_message_config.start_time.isoformat(),
#            endDate=auto_send_message_config.end_time.isoformat(),
#            runDate=auto_send_message_config.send_time.isoformat(),
#            enabled=auto_send_message_config.enabled,
#            isDelete=auto_send_message_config.delete_previous
#        )
       
#         auto_send_message_configs_list.append(auto_send_bot)
        
#     return auto_send_message_configs_list
    
if __name__ == '__main__':
    uvicorn.run(app, host='0.0.0.0', port=9200)