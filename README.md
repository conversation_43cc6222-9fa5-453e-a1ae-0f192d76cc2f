# Telegram Chat Bot

## How to run

1. 安装 [uv](https://github.com/astral-sh/uv)

```sh
curl -LsSf https://astral.sh/uv/install.sh | sh
uv sync
```

2. 安装MongoDB

```sh
docker pull mongo
docker run --name mongo -p 27017:27017 -p 8081:8081 mongo
```

3. 安装MySQL

```sh
docker pull mysql
docker run --name mysql -e MYSQL_ROOT_PASSWORD=root -p 3306:3306 -d mysql
```

创建tg_bot库

```sql
create database tg_bot;
```

3.1 schema 创建
使用 [tortoise-orm](https://github.com/tortoise/tortoise-orm) 库，model在 ./persistence/models 路径下。
同步代码之后使用 `aerich upgrade` 命令更新远程的变更。
models 文件更新之后使用 `aerich migrate` 命令更新本地库，并生成 migration 文件，位于 ./migrations 目录。

4. 创建本地 env 文件，按需修改值

```sh
cp .env.example .env
```

5. 设置代理配置，launch.json，命令行设置
6. 运行

   1) 使用命令行中运行

```sh
# 设置代理配置，地址改为自己的代理
python app.py --role royal
```

2) VSCode 中运行
   编辑lunch.json,把"ALL_PROXY": "http://127.0.0.1:1086",改为自己的本地代理
   .vscode/launch.json 文件中提供了 VSCode 运行需要的配置，可以直接使用 VSCode 的运行和调试功能。

7. sentry配置
   1）测试环境：SENTRY_DSN='https://<EMAIL>/4507445257830400'
   2）线上环境：SENTRY_DSN='https://<EMAIL>/4507473258676224'
   http://sentry.198432.xyz:8080/
   <EMAIL>
   M9DJvDT3pqRwhMzq3Ky2
8. postHug配置
   1）测试 'phc_izXQYMOao5Nsfgzwdkenl78ZOO6kC3Jp2m1GqHz96Do'
   2）线上 'phc_jtSoH3f9HtdBIf4hsL7yX79ADzK6I3DUxsr4a0ye7Mk'

## TencentOS 新机器环境配置

1. 安装 git

```sh
yum install git-core -y
```

2. 安装 Python 环境

```sh
curl -LsSf https://astral.sh/uv/install.sh | sh
uv sync
```

3. 安装 pm2

```shell
# 安装 mise 作为包管理工具
curl https://mise.run | sh
echo "eval \"\$(/root/.local/bin/mise activate bash)\"" >> ~/.bashrc
source ~/.bashrc

# 安装 node lts
mise install node@20.18.0
mise use -g node@20.18.0

# 安装 pm2
npm install -g pm2
```

## Caddy 初始化

1. 安装 go
   ```shell
   mise install go@1.21.11
   mise use -g go@1.21.11
   ```
2. 安装 xcaddy
   ```shell
   go install github.com/caddyserver/xcaddy/cmd/xcaddy@latest
   ```
3. clone caddy-auth 模块
   ```shell
   <NAME_EMAIL>:fancy-potato/caddy-tgauth.git
   ```
4. build caddy
   ```shell
   xcaddy build --with github.com/tavern/caddy-tgauth=/root/caddy-tgauth --with github.com/caddy-dns/cloudflare
   ```

## Caddy 服务化部署

1. 复制文件到相关目录

   ```shell
   cp caddy /usr/bin
   mkdir -p /etc/caddy
   cp Caddyfile /etc/caddy/
   cp caddy.service /etc/systemd/system/
   ```
2. 添加 caddy 用户

   ```shell
   groupadd --system caddy

   useradd --system \
    --gid caddy \
    --create-home \
    --home-dir /var/lib/caddy \
    --shell /usr/sbin/nologin \
    --comment "Caddy web server" \
    caddy

   chown -R caddy:caddy /data/caddy_logs && chmod 755 /data/caddy_logs
   chown -R caddy:caddy /var/lib/caddy
   ```
3. 启动服务

   ```shell
   # 关闭旧服务
   # ./caddy stop

   systemctl daemon-reload
   systemctl enable --now caddy

   HOME=~caddy caddy trust
   systemctl stop caddy
   systemctl start caddy

   # 重启caddy 
   systemctl restart caddy

   # 检查运行状态
   systemctl status caddy --no-pager --full

   journalctl -u caddy --no-pager | less +G
   ```
