
from uuid import uuid4
from pydantic import BaseModel
from tortoise.models import Model
from tortoise import fields
from tortoise.contrib.mysql.indexes import SpatialIndex


# class TimestampMixin():
#     created_at = fields.DatetimeField(auto_now_add=True)
#     updated_at = fields.DatetimeField(auto_now=True)

class BaseIdModel(Model):
    id = fields.BigIntField(pk=True)
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)
    class Meta:
        abstract = True

class MediaGroupMesssage(BaseIdModel):
    chat_id = fields.BigIntField(description="chat id,聊天id")
    message_id = fields.BigIntField()
    media_group_id =fields.BigIntField()
    is_header = fields.BooleanField()
    caption_html = fields.TextField(null=True)
    class Meta:
        table = "dx_bot_media_group_message"


class FormnStatus(BaseIdModel):
    chat_id = fields.BigIntField(index=True, description="chat会话窗口id",default=0)
    message_thread_id = fields.BigIntField(index=True, description="消息id")
    status = fields.CharField(max_length=64, default="opened",description="状态:opened,closed")
    class Meta:
        table = "dx_bot_formn_status"

class MessageMap(BaseIdModel):
    user_chat_message_id = fields.BigIntField(index=True,description="用户聊天消息id")
    group_chat_message_id = fields.BigIntField(index=True,description="群聊消息id")  
    user_id = fields.BigIntField(index=True,description="用户id")
    
    class Meta:
        table = "dx_bot_message_map"

class User(BaseIdModel):
    user_id = fields.BigIntField(index=True)
    first_name = fields.CharField(max_length=64,default="")
    last_name = fields.CharField(max_length=64,default="")
    username = fields.CharField(max_length=64)
    is_premium = fields.BooleanField(default=False)
    message_thread_id = fields.BigIntField(index=True,default=0)
    bot_id = fields.BigIntField(index=True,description="bot id")
    
    class Meta:
        table = "dx_bot_user"
        unique_together = (("user_id", "bot_id"),)






