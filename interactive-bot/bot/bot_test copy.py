from asyncio import sleep
from aiogram import <PERSON><PERSON>, Dispatcher, types
import asyncio
import csv

# 从 .env.prod 文件中读取 API_TOKEN
API_TOKEN = '7533808460:AAHuJ-chiEYu3MQh4KxhHaWNqrYgz2_2E_c' # 幻梦vip_bot群
# API_TOKEN = '7357790700:AAHt_84ueO_t6bN3Pw-ewANthYIW8I4cO_k'

# 初始化 Bot 和 Dispatcher
bot = Bot(token=API_TOKEN)
dp = Dispatcher(bot=bot)

# 定义一个发送消息的函数
async def send_message(chat_id: int, text: str,message_thread_id:int):
    await bot.send_message(chat_id=chat_id, text=text,message_thread_id=message_thread_id,parse_mode='HTML')
    await asyncio.sleep(2)  # 添加延迟以避免触发速率限制

async def send_message_block(chat_id: int, text: str):
    await bot.send_message(chat_id=chat_id, text=text)


# 示例：发送消息到某个聊天
if __name__ == '__main__':
    
    
    chat_id = -1002403997803  # 747群组 ID-1002226814292
    message_thread_id = 0 # 测试id 为118
    uid = 23456
    	# 启动事件循环并发送消息
    
    loop = asyncio.new_event_loop()
    
    forum_t = loop.run_until_complete(bot.create_forum_topic(chat_id=chat_id, name='xxx:#{uid}'))
    print(forum_t.message_thread_id)
    loop.run_until_complete(send_message(chat_id, 'https://t.me/playai666/636394',forum_t.message_thread_id))
    loop.close()
    