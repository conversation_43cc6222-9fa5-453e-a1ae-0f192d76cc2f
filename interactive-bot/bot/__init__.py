import asyncio
import pkg_resources
import os
import sys
import json
import logging
from dotenv import load_dotenv
from tortoise import Tortoise


# 配置日志记录器
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(name)s- %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('log.txt')
    ]
)
logging.getLogger("httpx").setLevel(logging.ERROR)
current_package = os.path.basename(os.path.dirname(__file__))
logger = logging.getLogger(current_package)

# 打开并读取 JSON 文件
with open('bot_cfg.json', 'r', encoding='utf-8') as file:
    bot_config_data = json.load(file)

# 访问解析后的数据

load_dotenv()
async def init_db():
    await Tortoise.init(
        db_url=os.environ["MYSQL_URL"],
        modules={'models': ['db.models']}
    )
    await Tortoise.generate_schemas()


def run_async_init_db():
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:  # 如果当前线程没有事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    loop.run_until_complete(init_db())

# run_async_init_db()

bot_configs = {}
for bot_config in bot_config_data:
    api_id = bot_config['API_ID']
    # config = {
    #     'APP_NAME': bot_config['APP_NAME'],
    #     'BOT_TOKEN': bot_config['BOT_TOKEN'],
    #     'API_HASH': bot_config['API_HASH'],
    #     'Welcome Message': bot_config['WELCOME_MESSAGE'],
    #     'Admin Group ID': bot_config['ADMIN_GROUP_ID'],
    #     'Admin User ID': bot_config['ADMIN_USER_ID'],
    #     'Delete Topic as Forever Ban': bot_config['DELETE_TOPIC_AS_FOREVER_BAN'],
    #     'Delete User Message on Clear Cmd': bot_config['DELETE_USER_MESSAGE_ON_CLEAR_CMD']
    # }
    bot_configs[api_id] = bot_config
