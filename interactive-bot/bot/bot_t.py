from aiogram import <PERSON><PERSON>, Dispatcher, types


# 你的 Telegram 机器人 Token
TOKEN = "**********************************************"
# 目标群组的 chat_id
TARGET_CHAT_ID = -1002410058975

image_token = "8134617725:AAHh_C2IgE7G1OVExQtDYk74DMW13v4s_iI"
TARGET_CHAT_ID = -1002410058975
# share_group_id = -1002410058975  # 替换为实际的群组 ID
share_group_id = -1002538988673  # 替换为实际的群组 ID


async def creat_topic_send_msg():
    bot = Bot(token=TOKEN)

    # 创建一个新的论坛主题
    topic_name = "测试主题-closed"
    # message_thread = await bot.create_forum_topic(chat_id=TARGET_CHAT_ID, name=topic_name)

    message_thread_id = 757

    # await bot.reopen_forum_topic(chat_id=TARGET_CHAT_ID, message_thread_id=message_thread_id)
    # 发送消息到该主题
    await bot.send_message(
        chat_id=TARGET_CHAT_ID,
        text="这是在新主题中的第一条消息---s",
        message_thread_id=message_thread_id,
    )
    print(f"close topic with ID: {message_thread_id}")
    # await bot.close_forum_topic(chat_id=TARGET_CHAT_ID, message_thread_id=message_thread.message_thread_id)

    await bot.send_message(
        chat_id=TARGET_CHAT_ID,
        text=f"这是在新主题中的第二条消息-colsed:{message_thread_id}",
        message_thread_id=message_thread_id,
    )


async def fetch_and_send():
    # 公开消息链接
    message_link = "https://t.me/playai666/636394"

    # 从链接中提取 chat_id 和 message_id
    parts = message_link.split("/")
    chat_id = parts[-2]
    message_id = int(parts[-1])
    print(f"chat_id: {chat_id}, message_id: {message_id}")

    # 获取消息内容
    bot = Bot(token=TOKEN)
    await bot.send_message(chat_id=TARGET_CHAT_ID, text="正在获取消息内容...")

    await bot.send_message(chat_id=TARGET_CHAT_ID, text=message_link)
    # message = await bot.forward_message(chat_id=TARGET_CHAT_ID, from_chat_id=chat_id, message_id=message_id)

    await bot.send_photo(
        chat_id=TARGET_CHAT_ID,
        photo="https://www.fas.scot/wp-content/uploads/2017/09/texel_shearling_tup.jpg",
        caption="这是一张图片",
    )

    await bot.send_photo(
        chat_id=TARGET_CHAT_ID, photo=message_link, caption="这是一张link图片"
    )

    # print(f'已发送消息：{message}')


async def create_image_bot_share_group_topic():

    bot = Bot(token=image_token)
    group_id = share_group_id  # 替换为实际的群组 ID

    share_mapping = {}

    # topic_names = [
    #     "二次元风",
    #     "福瑞风",
    #     "写实风",
    #     "像素风",
    #     "吉卜力风",
    #     "赛博风",
    #     "原初二次元",
    #     "触手Play",
    # ]

    topic_names = [
        "真实风",
    ]

    for topic_name in topic_names:
        # 创建一个新的论坛主题
        message_thread = await bot.create_forum_topic(chat_id=group_id, name=topic_name)
        print(f"创建主题: {topic_name}, 主题ID: {message_thread.message_thread_id}")


import asyncio

if __name__ == "__main__":
    # asyncio.run(fetch_and_send())
    asyncio.run(create_image_bot_share_group_topic())
    # asyncio.run(creat_topic_send_msg())
