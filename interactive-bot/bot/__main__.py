
from multiprocessing import Process, set_start_method

import telegram
from telegram import InlineKeyboardButton, InlineKeyboardMarkup, Update
from telegram.ext import (Application<PERSON><PERSON>er, CommandHandler, ContextTypes,
                          ConversationHandler, MessageHandler,
                          PicklePersistence, filters)
from telegram.helpers import mention_html

from service.message_service import MessageService
from service.user_service import UserService

from db.models import MediaGroupMesssage, FormnStatus, MessageMap, User

# from db.database import SessionMaker, engine
# from db.model import Base, FormnStatus, MediaGroupMesssage, MessageMap, User

# from . import (admin_group_id, admin_user_id, app_name,
#                bot_token, logger, welcome_message, is_delete_topic_as_ban_forever, is_delete_user_messages)

from . import bot_configs as bot_map , logger,run_async_init_db
is_delete_topic_as_ban_forever = 0
is_delete_user_messages = 0
admin_user_id = 0
from telegram.error import BadRequest


async def update_user_db(u: telegram.User):
    n_user = User(user_id=u.id, first_name=u.first_name, last_name="", username=u.username)
    await UserService.add_update_user(n_user)
    

# 延时发送媒体组消息的回调
async def _send_media_group_later(context: ContextTypes.DEFAULT_TYPE):
    job = context.job
    media_group_id = job.data
    _, from_chat_id, target_id, dir,bot_id = job.name.split('_')

    media_group_msgs =await MessageService.get_all_media_group_message(media_group_id)
    chat = await context.bot.get_chat(target_id)
    if dir == 'u2a':
        # 发送给群组
        # u = db.query(User).filter(User.user_id == from_chat_id).first()
        u = await UserService.get_user(user_id=from_chat_id,bot_id=bot_id)
        message_thread_id = u.message_thread_id
        sents = await chat.send_copies(from_chat_id, [m.message_id for m in media_group_msgs] , message_thread_id=message_thread_id)
        for sent, msg in zip(sents, media_group_msgs):
            msg_map = MessageMap(user_chat_message_id=msg.message_id, group_chat_message_id=sent.message_id, user_id=u.user_id)
            await MessageService.add_message_map(msg_map)      
    else:
        # 发送给用户
        sents = await chat.send_copies(from_chat_id, [m.message_id for m in media_group_msgs])
        for sent, msg in zip(sents, media_group_msgs):
            msg_map = MessageMap(user_chat_message_id=sent.message_id, group_chat_message_id=msg.message_id, user_id=target_id)
            await MessageService.add_message_map(msg_map)
            
# 延时发送媒体组消息
async def send_media_group_later(delay: float, chat_id, target_id, media_group_id: int, dir,context: ContextTypes.DEFAULT_TYPE):
    name=f"sendmediagroup_{chat_id}_{target_id}_{dir}_{context.bot.id}"
    context.job_queue.run_once(_send_media_group_later, delay, chat_id=chat_id, name=name, data=media_group_id)
    return name

async def send_contact_card(chat_id, message_thread_id, user: User, update: Update, context: ContextTypes):
    buttons = []
    buttons.append([InlineKeyboardButton(f"{'🏆 高级会员' if user.is_premium else '✈️ 普通会员' }", url=f"https://t.me/{user.username}")])
    # buttons.append([InlineKeyboardButton(f"{'🏆 高级会员' if user.is_premium else '✈️ 测试uid' }", url=f"tg://user?id=6730492130")])
    if user.username:
        buttons.append([InlineKeyboardButton("👤 直接联络", url=f"https://t.me/{user.username}")])
        # buttons.append([InlineKeyboardButton("👤 直接联络kimi", url=f"https://t.me/zzkimi123")])        
        # buttons.append([InlineKeyboardButton("👤 直接联络@ShiYueJiang", url=f"https://t.me/ShiYueJiang")])
    user_photo = await context.bot.get_user_profile_photos(user.id)

    if user_photo.total_count:
        pic = user_photo.photos[0][-1].file_id
        await context.bot.send_photo(chat_id,photo=pic, 
                                    caption=f"👤 {mention_html(user.id, user.first_name)}\n\n📱 {user.id}\n\n🔗 @{user.username if user.username else '无'}",
                                    message_thread_id=message_thread_id, reply_markup=InlineKeyboardMarkup(buttons),
                                    parse_mode='HTML')
    else:
        await context.bot.send_contact(chat_id, phone_number='111', first_name=user.first_name, last_name=user.last_name, 
                                     message_thread_id=message_thread_id, 
                                     reply_markup=InlineKeyboardMarkup(buttons)
                                     )

#start command 消息处理
async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    logger.info(f"start command from {context.bot.id},user: {update.effective_user}")
    bot_id_str = str(context.bot.id)
    logger.debug(f"bot_map: {bot_map.get(bot_id_str)}")
    app_name = bot_map[bot_id_str]['APP_NAME']
    welcome_message = bot_map[bot_id_str]['WELCOME_MESSAGE']
    user = update.effective_user

    n_user = User(user_id=user.id, first_name=user.first_name or "", last_name=user.last_name or "", username=user.username or "",bot_id=context.bot.id)
    try:
        await UserService.add_update_user(n_user)
    except Exception as e:
        logger.error(f"Error adding user: {e}")

    # check whether is admin
    admin_user_id = bot_map[bot_id_str]['ADMIN_USER_ID']
    admin_group_id = bot_map[bot_id_str]['ADMIN_GROUP_ID']
    if user.id == admin_user_id:
        logger.info(f"{user.first_name}({user.id}) is admin")
        try:
            bg = await context.bot.get_chat(admin_group_id)
            if bg.type == 'supergroup' or bg.type == 'group':
                logger.info(f"admin group is {bg.title}")
        except Exception as e:
            logger.error(f"admin group error {e}")
            await update.message.reply_html(f"⚠️⚠️后台管理群组设置错误，请检查配置。⚠️⚠️\n你需要确保已经将机器人 @{context.bot.username} 邀请入管理群组并且给与了管理员权限。\n错误细节：{e}\n请联系 @xxxx 获取技术支持。")
            return ConversationHandler.END
        await update.message.reply_html(f"你好管理员 {user.first_name}({user.id})\n\n欢迎使用 {app_name} 机器人。\n\n 目前你的配置完全正确。可以在群组 <b> {bg.title} </b> 中使用机器人。")
    else:
        await update.message.reply_html(f"{mention_html(user.id, user.full_name)} 同学：\n\n{welcome_message}")



# 消息转发处理 从用户到管理员群组的topic
async def forwarding_message_u2a(update: Update, context: ContextTypes.DEFAULT_TYPE ):
   
    user = update.effective_user
    bot_id_str = str(context.bot.id)
    
    admin_group_id = bot_map[bot_id_str]['ADMIN_GROUP_ID']
    
    logger.info(f"bot_id:{bot_id_str},admin_group_id: {admin_group_id}")
    

    chat_id = admin_group_id
    attachment = update.message.effective_attachment 
    # await update.message.forward(chat_id)
    u = await UserService.get_user(user.id,context.bot.id)
    
    ##todo 优化下，如果用户不存在，进行直接注册
    if u is None:
        logger.error(f"User {user.id} not found in db")
        return
    

    message_thread_id = u.message_thread_id
    if f := await MessageService.get_formn_status(message_thread_id):
        if f.status == 'closed':
            await update.message.reply_html("客服已经关闭对话。如需联系，请利用其他途径联络客服回复和你的对话。")
            return
    if not message_thread_id:
        formn = await context.bot.create_forum_topic(chat_id, name=f"{user.full_name}|{user.id}")
        message_thread_id = formn.message_thread_id
        u.message_thread_id = message_thread_id
        await context.bot.send_message(chat_id, f"新的用户 {mention_html(user.id, user.full_name)} 开始了一个新的会话。", message_thread_id=message_thread_id, parse_mode='HTML')
        

        # await context.bot.send_message(chat_id, f"测试用户 {mention_html(5230980209, "Crazy Pants")} 开始了一个新的会话。", message_thread_id=message_thread_id, parse_mode='HTML')
        
        await send_contact_card(chat_id, message_thread_id, user, update, context)
        await UserService.add_update_user(u)

    params = {
        "message_thread_id": message_thread_id
    }
    
    if update.message.reply_to_message:
        # 用户引用了一条消息。我们需要找到这条消息在群组中的id
        reply_in_user_chat = update.message.reply_to_message.message_id
        if msg_map := await MessageService.get_message_map(reply_in_user_chat):
            params['reply_to_message_id'] =  msg_map.group_chat_message_id
    try:
        if update.message.media_group_id:
            msg = MediaGroupMesssage(chat_id=update.message.chat.id, message_id=update.message.message_id, media_group_id=update.message.media_group_id, is_header=False, caption_html=update.message.caption_html)

            logger.info(f"media_group_id: {update.message.media_group_id},context.user_data: {context.user_data}:update_msg:{update.message}")
            await MessageService.add_media_group_message(msg)
             #去重发送的media group msg
            if update.message.media_group_id != context.user_data.get('current_media_group_id', 0):
                context.user_data['current_media_group_id'] = update.message.media_group_id
                await send_media_group_later(5, user.id, chat_id, update.message.media_group_id, "u2a", context)
            return 
        else:
            chat = await context.bot.get_chat(chat_id)
            sent_msg = await chat.send_copy(update.effective_chat.id, update.message.id, **params)

        msg_map = MessageMap(user_chat_message_id=update.message.id, group_chat_message_id=sent_msg.message_id, user_id=user.id)
        await MessageService.add_message_map(msg_map)    

    except BadRequest as e:
        if is_delete_topic_as_ban_forever:
            await update.message.reply_html(f"发送失败，你的对话已经被客服删除。请联系客服重新打开对话。")
        else:
            u.message_thread_id = 0
            await UserService.update_user_msg_thread_id(u)
            await update.message.reply_html(f"发送失败，你的对话已经被客服删除。请再发送一条消息用来激活对话。")
    except Exception as e:
        await update.message.reply_html(f"发送失败: {e}\n请联系 @xx 汇报这个错误。谢谢")

#转发管理群组的消息到用户
async def forwarding_message_a2u(update: Update, context: ContextTypes.DEFAULT_TYPE):

    message_thread_id = update.message.message_thread_id
    if not message_thread_id:
        # general message, ignore
        return 
    user_id = 0
    if u := await UserService.get_user_by_thread_id(message_thread_id):
        user_id = u.user_id
    if not user_id:
        logger.error(f"消息回复错误：{update.message}")
        return     
    if update.message.forum_topic_created:
        f = FormnStatus(chat_id=0,message_thread_id=update.message.message_thread_id, status='opened')
        await MessageService.add_formn_status(f)
        return 
    if update.message.forum_topic_closed:
        await context.bot.send_message(user_id, "对话已经结束。对方已经关闭了对话。你的留言将被忽略。")
        if f := await MessageService.get_formn_status(message_thread_id):
            f.status = 'closed'
            await MessageService.add_formn_status(f)
        return 
    if update.message.forum_topic_reopened:
        await context.bot.send_message(user_id, "对方重新打开了对话。可以继续对话了。")
        if f := await MessageService.get_formn_status(message_thread_id):
            f.status = 'opened'
            await FormnStatus.add(f)     
        return
    if f := await MessageService.get_formn_status(message_thread_id):
        if f.status == 'closed':
            await update.message.reply_html("对话已经结束。希望和对方联系，需要打开对话。")
            return
    chat_id = user_id
    # 构筑下发送参数
    params = {}
    if update.message.reply_to_message:
        # 群组中，客服回复了一条消息。我们需要找到这条消息在用户中的id
        reply_in_admin = update.message.reply_to_message.message_id
        if msg_map := await MessageService.get_message_map_by_group_chat_message_id(reply_in_admin):
            params['reply_to_message_id'] =  msg_map.user_chat_message_id
    try:
        if update.message.media_group_id:
            # bad_type = "不支持媒体组类型(最好单个发送)。\n如果确定需要，请点击-> /start_to_send_media_group "
            msg = MediaGroupMesssage(chat_id=update.message.chat.id, message_id=update.message.message_id, media_group_id=update.message.media_group_id, is_header=False, caption_html=update.message.caption_html)
            await MessageService.add_media_group_message(msg)  
            if update.message.media_group_id != context.application.user_data[user_id].get('current_media_group_id', 0):
                context.application.user_data[user_id]['current_media_group_id'] = update.message.media_group_id
                await send_media_group_later(5, update.effective_chat.id, user_id, update.message.media_group_id, "a2u", context)
            return 
        else:
            chat = await context.bot.get_chat(chat_id)
            sent_msg = await chat.send_copy(update.effective_chat.id, update.message.id, **params)    
        msg_map = MessageMap(group_chat_message_id=update.message.id, user_chat_message_id=sent_msg.message_id, user_id=user_id)
        await MessageService.add_message_map(msg_map)

    except Exception as e:
        await update.message.reply_html(f"发送失败: {e}\n请稍后重试或者私聊汇报这个问题，谢谢")

async def clear(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user = update.effective_user
    if user.id != admin_user_id:
        await update.message.reply_html("你没有权限执行此操作。")
        return
    await context.bot.delete_forum_topic(update.effective_chat.id, update.message.message_thread_id)
    if not is_delete_user_messages : return 
    if target_user := UserService.get_user_by_thread_id(update.message.message_thread_id):
        all_messages_in_user_chat = MessageService.get_all_messages_by_user_id(target_user.user_id)
        await context.bot.delete_messages(target_user.user_id, [msg.user_chat_message_id for msg in all_messages_in_user_chat])

async def error_in_send_media_group(update: Update, context: ContextTypes.DEFAULT_TYPE):
    await update.message.reply_html("错误的消息类型。退出发送媒体组。后续对话将直接转发。")
    return ConversationHandler.END

async def error_handler(update: object, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Log the error and send a telegram message to notify the developer."""
    # Log the error before we do anything else, so we can see it even if something breaks.
    logger.error(f"Exception while handling an update: {context.error} ")
    logger.error(f"Exception detail is :", exc_info=context.error)





if __name__ == '__main__':
    
    set_start_method('fork')
    # 需要定义到此处，涉及到父进程和子进程共享空间的问题
    def start_bot(bot_config):
        logger.info(f"Starting bot {bot_config['API_ID']} with config {bot_config}")
        pickle_persistence = PicklePersistence(filepath=f"./assets/{bot_config['APP_NAME']}.pickle")
        application = ApplicationBuilder().token(bot_config['BOT_TOKEN']).persistence(persistence=pickle_persistence).build()
        application.add_handler(CommandHandler('start', start, filters.ChatType.PRIVATE))
        application.add_handler(MessageHandler(~filters.COMMAND & filters.ChatType.PRIVATE, forwarding_message_u2a))
        application.add_handler(MessageHandler(~filters.COMMAND & filters.Chat([bot_config['ADMIN_GROUP_ID']]), forwarding_message_a2u))
        application.add_handler(CommandHandler('clear', clear, filters.Chat([bot_config['ADMIN_GROUP_ID']])))
        application.add_error_handler(error_handler)
        # 每个bot 初始化一个db链接
        run_async_init_db()
        application.run_polling() 
    
    
    
 
    
    applications = []

    logger.info(f"Starting bot with config {bot_map}")
    
    processes = []
    
    for bot_id, bot_config in bot_map.items():
        logger.info(f"Starting bot {bot_config['APP_NAME']} with config {bot_config}")
        p = Process(target=start_bot, args=(bot_config,))
        p.start()
        processes.append(p)
    
    for p in processes:
        p.join()
        
        
   