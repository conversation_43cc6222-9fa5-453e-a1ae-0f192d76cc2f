from asyncio import sleep
from aiogram import <PERSON><PERSON>, Dispatcher, types
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

from aiogram.utils.keyboard import Inline<PERSON>eyboardBuilder
from telegram.helpers import mention_html
import asyncio
import csv


#测试

# API_TOKEN = '7357790700:AAHt_84ueO_t6bN3Pw-ewANthYIW8I4cO_k' 
# chat_id = -1002226814292  # 747群组 ID-1002226814292
#     # message_thread_id = 15 # 线上id
# message_thread_id = 262 # 测试id 为262
# #747群组 ID-1002226814292

#正式
API_TOKEN = '7428622943:AAGh1kX5XXwX_tFXZu8daojQmAnwTJVe2To' # 幻梦fansybot
chat_id = -1002202845119  # 幻梦Ai程序媛双向bot群id
message_thread_id = 15 # 线上id

# 初始化 Bot 和 Dispatcher
bot = Bot(token=API_TOKEN)
dp = Dispatcher(bot=bot)

# 定义一个发送消息的函数
async def send_message(chat_id: int, text: str,reply_markup ,message_thread_id:int):
    await bot.send_message(chat_id=chat_id, text=text,reply_markup=reply_markup,message_thread_id=message_thread_id,parse_mode='HTML')
    await asyncio.sleep(3)  # 添加延迟以避免触发速率限制

async def send_message_block(chat_id: int, text: str):
    await bot.send_message(chat_id=chat_id, text=text)


# 示例：发送消息到某个聊天
if __name__ == '__main__':
    
    # chat_id = -1002202845119  # 幻梦Ai程序媛双向bot群id

    
	# chat_id = 5359176631  # 替换为实际的聊天 ID
    # 读取CSV文件
    file_csv = '/Users/<USER>/Downloads/vip_904.csv'
    	# 启动事件循环并发送消息
    
    loop = asyncio.new_event_loop()
    #测试block
    # loop.run_until_complete(send_message_block(5359176631, '你好：'))
     
    # loop.run_until_complete(send_message(chat_id, '0830-0904的付费用户信息：',None,message_thread_id))

    with open(file_csv, 'r') as file:
        reader = csv.reader(file)
        for row in reader:
            # 处理每一行数据
            # 发送消息
            tg_id = row[4]
            u_id = row[3]
            first_name = row[5]
            last_name = row[6]
            p_user_name = ""
            f_l_name = f"{first_name} {last_name}"
            tg_username = row[7]
            channel_id = row[8]
            channel_id = channel_id if channel_id else "--"
            # start_role = row[11]
            if tg_id == 'tg_id':
                continue
            
            
            platform_msg = f"用户id:#{u_id},channel_id:#{channel_id},tg_id:#{tg_id},tg_username:#{tg_username},f_l_name:#{f_l_name},{mention_html(tg_id, "tg_id联系用户")}"
            message_text = platform_msg
            
            builder = InlineKeyboardBuilder()

            # builder.add(InlineKeyboardButton(text="tg_id查看", url=f"tg://user?id={tg_id}"))

            if tg_username:
                builder.add(InlineKeyboardButton(text=f"👤 直接联络:{f_l_name}", url=f"https://t.me/{tg_username}"))
            print(message_text)
    

            loop.run_until_complete(send_message(chat_id,text=message_text,reply_markup=builder.as_markup(),message_thread_id=message_thread_id))
    loop.run_until_complete(bot.session.close())
    loop.close()
    