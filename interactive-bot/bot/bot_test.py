from asyncio import sleep
from aiogram import <PERSON><PERSON>, Dispatcher, types
from aiogram.types import ForumTopic
# from telegram import ForumTopic
from telegram.helpers import mention_html
import asyncio
import csv

# 从 .env.prod 文件中读取 API_TOKEN
# API_TOKEN = '7357790700:AAHt_84ueO_t6bN3Pw-ewANthYIW8I4cO_k' #747群组 ID-1002226814292

# API_TOKEN = '7428622943:AAGh1kX5XXwX_tFXZu8daojQmAnwTJVe2To' # 幻梦fansybot

API_TOKEN = '7533808460:AAHuJ-chiEYu3MQh4KxhHaWNqrYgz2_2E_c' # 幻梦vip_bot群

# 初始化 Bot 和 Dispatcher
bot = Bot(token=API_TOKEN)
dp = Dispatcher(bot=bot)

# 定义一个发送消息的函数
async def send_message(chat_id: int, text: str,message_thread_id:int):
    await bot.send_message(chat_id=chat_id, text=text,message_thread_id=message_thread_id,parse_mode='HTML')
    await asyncio.sleep(3)  # 添加延迟以避免触发速率限制

async def send_message_block(chat_id: int, text: str):
    await bot.send_message(chat_id=chat_id, text=text)

async def create_forum_topic(group_id: int, name: str) -> ForumTopic:
    return await bot.create_forum_topic(chat_id=group_id, name=name)

# 示例：发送消息到某个聊天
if __name__ == '__main__':
    
    chat_id = -1002403997803  # 幻梦Ai大哥群id
    # chat_id = -1002226814292  # 747群组 ID-1002226814292
    
    
    message_thread_id = 15 # 线上id
    # message_thread_id = 262 # 测试id 为262
	# chat_id = 5359176631  # 替换为实际的聊天 ID
    # 读取CSV文件

    #双向转发配置
    FORWARD_GROUP_ID=-1002403997803
    file_csv = '/Users/<USER>/Downloads/vip_user_86.csv'
    	# 启动事件循环并发送消息
    
    loop = asyncio.new_event_loop()
    #测试block
    # loop.run_until_complete(send_message_block(5359176631, '你好：'))
     
    # loop.run_until_complete(send_message(chat_id, '0829的付费用户信息：',message_thread_id))
    
    message_thread = loop.run_until_complete(create_forum_topic(chat_id, 'vip:5359176631的付费用户信息：'))
    
    print(f"message_thread_id:{message_thread.message_thread_id}")
    
    loop.run_until_complete(send_message(chat_id, '幻梦大哥付费用户信息：',message_thread.message_thread_id))

    # with open(file_csv, 'r') as file:
    #     reader = csv.reader(file)
    #     for row in reader:
    #         # 处理每一行数据
    #         # 发送消息
    #         tg_id = row[4]
    #         tg_username = row[7]
    #         if tg_id == 'tg_id':
    #             continue
            
    #         if tg_username:
    #             message_text = f"tgid为:{tg_id}:点开{mention_html(tg_id, 'tg_id查看')},[用户名查看](https://t.me/{tg_username})"
    #         else:
    #          message_text = f"用户tgid为:{tg_id}:{mention_html(tg_id, "tg_id点开")}"# 5230980209 线上用户id
    #         print(message_text)
    #         loop.run_until_complete(send_message(chat_id, message_text,message_thread_id))
    loop.run_until_complete(bot.session.close())
    loop.close()
    