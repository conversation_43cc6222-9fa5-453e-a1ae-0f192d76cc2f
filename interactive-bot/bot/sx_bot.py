
import logging
from aiogram import <PERSON><PERSON>, <PERSON><PERSON>atch<PERSON>, types
# from aiogram import LoggingMiddleware
from aiogram.types import Update
from aiogram.fsm.storage.memory import MemoryStorage
from aiogram.filters import CommandStart

from fastapi import Fast<PERSON><PERSON>, Request, HTTPException
from fastapi.routing import API<PERSON><PERSON>er
import uvicorn

from persistence.models.models_dx_bot import Dx<PERSON><PERSON>, MediaGroupMesssage, FormnStatus, MessageMap
from services.dx_bot.dx_user_service import DxUserService

WEBHOOK_HOST = 'https://b014-114-142-153-114.ngrok-free.app'
WEBHOOK_PATH_TEMPLATE = '/webhook/{bot_name}'
WEBHOOK_URL_TEMPLATE = f"{WEBHOOK_HOST}{WEBHOOK_PATH_TEMPLATE}"

log = logging.getLogger(__name__)
class BotManager:
    def __init__(self):
        self.bots = {}
        self.dispatchers = {}

    def add_bot(self, bot_name, token):
        
        log.info(f"Adding bot {bot_name} with token {token}")
        if bot_name in self.bots:
            raise ValueError(f"Bot with name {bot_name} already exists.")
        
        bot = Bot(token=token)
        storage = MemoryStorage()
        dp = Dispatcher(bot=bot,storage=storage)
        # dp.middleware.setup(LoggingMiddleware())
        
        self.bots[bot_name] = bot
        self.dispatchers[bot_name] = dp

        # Define message handler for the new bot
        @dp.message(CommandStart())
        async def start_from_user(message: types.Message):
            log.info(f"Received /start from user {message.from_user.id} by bot {message.bot}")
            user_id = message.from_user.id
            user_first_name = message.from_user.first_name
            last_name = message.from_user.last_name
            user_name = message.from_user.username
            chat_id = message.chat.id
            bot_id = message.bot.id
            
            n_user = DxUser(user_id=user_id, first_name=user_first_name, last_name=last_name, username=user_name, bot_id=bot_id)
            
            try:
                await DxUserService.add_update_user(n_user)
            except Exception as e:
                log.error(f"Error adding/updating user {user_id}: {e}")
                await message.reply(f"Error adding/updating user {user_id}.请联系管理员")
                return
            await message.reply(f"Hello {user_first_name} {last_name}! I'm {bot_name}.")
            
        @dp.message()
        async def forwarding_message_u2a(message: types.Message):
            
            pass
        
        @dp.message()
        async def forwarding_message_a2u(message: types.Message):
            pass

    def remove_bot(self, bot_name):
        if bot_name not in self.bots:
            raise ValueError(f"Bot with name {bot_name} does not exist.")
        
        del self.bots[bot_name]
        del self.dispatchers[bot_name]

    async def set_webhook(self, bot_name):
        if bot_name not in self.bots:
            raise ValueError(f"Bot with name {bot_name} does not exist.")
        
        webhook_url = WEBHOOK_URL_TEMPLATE.format(bot_name=bot_name)
        await self.bots[bot_name].set_webhook(webhook_url)

    async def delete_webhook(self, bot_name):
        if bot_name not in self.bots:
            raise ValueError(f"Bot with name {bot_name} does not exist.")
        
        await self.bots[bot_name].delete_webhook()

    async def process_update(self, bot_name, update):
        if bot_name not in self.dispatchers:
            raise ValueError(f"Bot with name {bot_name} does not exist.")
        bot = self.bots[bot_name]
        await self.dispatchers[bot_name].feed_update(bot, update) 

# Initialize FastAPI app and BotManager
app = FastAPI()
bot_manager = BotManager()

# Set webhook routes dynamically
@app.post(WEBHOOK_PATH_TEMPLATE)
async def telegram_webhook(request: Request, bot_name: str):
    update = Update(**await request.json())
    log.info(f"Received update for bot {bot_name}")
    await bot_manager.process_update(bot_name, update)

# API endpoint to add a new bot
@app.post("/add_bot")
async def add_bot(bot_name: str, token: str):
    try:
        bot_manager.add_bot(bot_name, token)
        await bot_manager.set_webhook(bot_name)
        return {"message": f"Bot {bot_name} added and webhook set."}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

# API endpoint to remove a bot
@app.post("/remove_bot")
async def remove_bot(bot_name: str):
    try:
        await bot_manager.delete_webhook(bot_name)
        bot_manager.remove_bot(bot_name)
        return {"message": f"Bot {bot_name} removed and webhook deleted."}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

# Set webhooks on startup
@app.on_event("startup")
async def on_startup():
    for bot_name in bot_manager.bots.keys():
        await bot_manager.set_webhook(bot_name)

# Delete webhooks on shutdown
@app.on_event("shutdown")
async def on_shutdown():
    for bot_name in bot_manager.bots.keys():
        await bot_manager.delete_webhook(bot_name)

if __name__ == '__main__':
    uvicorn.run(app, host='0.0.0.0', port=8443) 