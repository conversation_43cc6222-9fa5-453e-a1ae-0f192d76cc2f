from telethon import TelegramClient, events
import asyncio
from telethon.tl.functions.channels import GetFullChannelRequest
from telethon.tl.functions.messages import GetDiscussionMessageRequest

# 替换为你的 API 凭证
api_id = '23280274'
api_hash = '1909c59067ad99191dc250e69e784426'
phone_number = '+447591218161'




# 替换为你想监听和发送消息的频道用户名或 ID
channel_username = '@ai_xiaoce_cc'

discussion_group_id = -1002361790855

# 创建客户端
client = TelegramClient('session11', api_id, api_hash)

@client.on(events.NewMessage(chats=channel_username))
async def handler(event):
    # 打印接收到的消息
    print('Received:', event.message.text)
    
    # 这里可以添加你的消息处理逻辑
    
    #示例：如果接收到的消息是 "Hello"，则回复 "Hi there!"
    if event.message.text == "Hello":
        print("wowowo,Sending: Hi there!")
        # await event.reply("Hi there!")
        try:
            # 获取频道实体
            channel = await client.get_entity(channel_username)
            
            # 获取讨论组实体
            discussion_group = await client.get_entity(discussion_group_id)
            
            # 获取讨论消息
            discussion_message = await client(GetDiscussionMessageRequest(
                channel,
                event.message.id
            ))
            
                        # 获取讨论消息的 ID
            discussion_message_id = discussion_message.messages[0].id
            # 准备评论内容，包括引用
            comment_text = "This is an important message，来玩哦，好玩的狠~!"
            
            # 构造评论链接
            comment_link = f"https://t.me/{channel_username}/{event.message.id}?thread={event.message.id}"
            
            # 在讨论组中发送评论
            await client.send_message(
                discussion_group,
                comment_text,
                link_preview=True,reply_to=discussion_message_id
            )
            print(f"Comment posted for message ID: {event.message.id}")
        except Exception as e:
            print(f"Error posting comment: {e}")
    else:
        print(f"No comment needed for message ID: {event.message.id}")

async def main():
    # 连接到 Telegram
    await client.start(phone=phone_number)
    
    # 获取频道实体
    channel = await client.get_entity(channel_username)
    
    # 发送一条消息到频道
    # await client.send_message(channel, "Hello from the bot!")
    
    print(f"Listening for messages in {channel_username}")
        # 读取最近的消息（这里设置为读取最近的 10 条消息）
    messages = await client.get_messages(channel, limit=10)
    
    for message in messages:
        print(message.text)
    
     
    # 获取频道完整信息
    channel = await client.get_entity(channel_username)
    full_channel = await client(GetFullChannelRequest(channel))
    
    # 检查是否有链接的讨论组
    if full_channel.full_chat.linked_chat_id:
        linked_chat = await client.get_entity(full_channel.full_chat.linked_chat_id)
        print(f"Linked discussion group: @{linked_chat.username}")
    else:
        print("No linked discussion group found.")
    # 保持脚本运行
    await client.run_until_disconnected()

# 运行主函数
with client:
    client.loop.run_until_complete(main())