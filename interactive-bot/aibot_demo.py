
import asyncio
from collections import defaultdict
from datetime import datetime
import logging
import os
import re
import time
from typing import Dict
import json

from aiogram import <PERSON><PERSON>, <PERSON><PERSON>atch<PERSON>, types


from aiogram.types import Update, Message, InlineKeyboardButton, InlineKeyboardMarkup
from aiogram.fsm.storage.memory import MemoryStorage

from aiogram.utils.keyboard import Inline<PERSON>eyboardBuilder

from aiogram.filters import CommandStart, Command
from aiogram import F


from aiogram.filters import ChatMemberUpdatedFilter,IS_MEMBER,IS_NOT_MEMBER
from aiogram.types import ChatMemberUpdated




from dotenv import load_dotenv
from telegram import CallbackQuery







logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s (%(filename)s:%(lineno)d) [%(levelname)s] %(message)s",handlers=[logging.StreamHandler()])
log = logging.getLogger(__name__)



sensitive_words = ["111", "敏感词2", "敏感词3"]
message_cooldowns = defaultdict(lambda: 0)
#用户白名单 tg_id
USER_WHITE_LIST = [123456789, 987654321]

ADMIN_IDS = [123456789, 987654321]
# 中间件
class RateLimitMiddleware:
    async def __call__(self, handler, event, data):
        user_id = event.from_user.id
        current_time = time.time()
        if current_time - message_cooldowns[user_id] < 5:
            await event.delete()
            return
        message_cooldowns[user_id] = int(current_time)
        return await handler(event, data)

# 自定义过滤器
def is_admin(message: Message):
    return message.from_user.id in ADMIN_IDS  # 假设ADMIN_IDS是管理员ID列表

#是否在白名单
def is_user_white_list(message: Message) -> bool:
    return message.from_user.id in USER_WHITE_LIST

#@ai_749_bot
token = "7706615835:AAHFiw4pQ_-cGK8Qffb4_rlY9dwqimazAMs"

bot = Bot(token=token)
storage = MemoryStorage()
dp = Dispatcher(bot=bot,storage=storage)


# In-memory storage for statistics
user_stats = defaultdict(lambda: {
    'messages_today': 0,
    'messages_7days': 0,
    'invites_today': 0,
    'invites_7days': 0,
    'join_today': 0,
    'join_7days': 0
})

last_message_date = defaultdict(lambda: datetime.now())

# Handlers

@dp.message(Command("start"))
async def cmd_start(message: types.Message):
    markup = InlineKeyboardBuilder()
    markup.add(InlineKeyboardButton(text='📊统计', callback_data='stats'))
    await message.answer("设置[ xxx]群组，选择要更改的项目:", reply_markup=markup.as_markup())

@dp.callback_query(F.data == 'stats')
async def process_callback_stats(callback_query: CallbackQuery):
    markup = InlineKeyboardBuilder()
    markup.add(
        InlineKeyboardButton(text="今日发言排名", callback_data='today_msg_stats'),
        InlineKeyboardButton(text="7日发言统计", callback_data='7day_msg_stats'),
        InlineKeyboardButton(text="今日邀请排名", callback_data='today_invite_stats'),
        InlineKeyboardButton(text="7日邀请统计", callback_data='7day_invite_stats'),
        InlineKeyboardButton(text="今日进群统计", callback_data='today_join_stats'),
        InlineKeyboardButton(text="7日进群统计", callback_data='7day_join_stats'),
        InlineKeyboardButton(text="返回首页", callback_data='stats')
    )
    markup.adjust(3,2)
    await bot.answer_callback_query(callback_query.id,text="you click统计菜单:")
    # await bot.send_message(callback_query.from_user.id, "统计菜单:", reply_markup=markup.as_markup())
    await bot.edit_message_text("统计菜单:", chat_id=callback_query.message.chat.id, message_id=callback_query.message.message_id, reply_markup=markup.as_markup())

@dp.callback_query(lambda c: c.data.endswith('_stats'))
async def process_callback_stat_display(callback_query: CallbackQuery):
    stat_type = callback_query.data
    stats = {}

    if stat_type == 'today_msg_stats':
        stats = {user_id: s['messages_today'] for user_id, s in user_stats.items()}
        desc = "今日发言排名"
    elif stat_type == '7day_msg_stats':
        stats = {user_id: s['messages_7days'] for user_id, s in user_stats.items()}
        desc = "7日发言统计"
    elif stat_type == 'today_invite_stats':
        stats = {user_id: s['invites_today'] for user_id, s in user_stats.items()}
        desc = "今日邀请排名"
    elif stat_type == '7day_invite_stats':
        stats = {user_id: s['invites_7days'] for user_id, s in user_stats.items()}
        desc = "7日邀请统计"
    elif stat_type == 'today_join_stats':
        stats = {user_id: s['join_today'] for user_id, s in user_stats.items()}
        desc = "今日进群统计"
    elif stat_type == '7day_join_stats':
        stats = {user_id: s['join_7days'] for user_id, s in user_stats.items()}
        desc = "7日进群统计"

    sorted_stats = sorted(stats.items(), key=lambda item: item[1], reverse=True)[:10]  # Top 10
    stats_message = f"{desc}:\n" + "\n".join([f"{user_id}: {count}" for user_id, count in sorted_stats])

    await bot.send_message(callback_query.from_user.id, stats_message)

@dp.message()
async def handle_message(message: types.Message):
    user_id = message.from_user.id
    now = datetime.now()
    if now.date() != last_message_date[user_id].date():
        user_stats[user_id]['messages_today'] = 0
        last_message_date[user_id] = now

    user_stats[user_id]['messages_today'] += 1
    user_stats[user_id]['messages_7days'] += 1  # Not resetting for simplicity in demo


if __name__ == '__main__':
    asyncio.run(dp.start_polling(bot))