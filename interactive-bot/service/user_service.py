
import logging
import telegram
from db.models import User

logger = logging.getLogger(__name__)

class UserService:
    
    @classmethod
    async def add_update_user(cls,user: User) -> User:
        
        existing_user = await User.filter(user_id=user.user_id,bot_id=user.bot_id).first()
        if existing_user:
            logger.info(f"User {user} already exists")
            
            
            if user.first_name:
                existing_user.first_name = user.first_name
            
            if user.last_name:
                existing_user.last_name = user.last_name
            
            if user.username:
                existing_user.username = user.username
            if user.message_thread_id != 0:
                existing_user.message_thread_id = user.message_thread_id
            return await existing_user.save()
            
        else :
            logger.info(f"Adding new user {user}")
            return await User.create(user_id=user.user_id, first_name=user.first_name, last_name=user.last_name, username=user.username,is_premium=False,bot_id=user.bot_id)
    @classmethod
    async def update_user_msg_thread_id(cls,user: User) -> User:
        existing_user = await User.filter(user_id=user.user_id,bot_id=user.bot_id).first()
        if existing_user:
            logger.info(f"User {user} already exists")
            existing_user.message_thread_id = user.message_thread_id
            return await existing_user.save()
        else:
            return None    

    @classmethod
    async def get_user(cls, user_id,bot_id)->User:
        user = await User.filter(user_id=user_id,bot_id=bot_id).first()
        return user
    @classmethod
    async def get_user_by_thread_id(cls, msg_thread_id:int)->User:
        user = await User.filter(message_thread_id=msg_thread_id).first()
        return user