## Todo

1. 增加一个bot后，只有一个服务生效，另外一个服务必须重启才能生效；原因：重启之后，内存的配置重新加载~
   1. 优化方向，使用redis集中内存配置的方式来进行配置的存储~

## c919——bot测试的配置

{
  "bot_name": "919_bot",
  "token": "6500199092:AAGwgTte0h2BoT-W2PZKhfjrCo4zgyvUzTY",
  "config": {
    "group_chat_id": -1002402329034,
    "group_admin_id": 0,
    "welcome_msg": "Welcome,919 bot!"
  }
}

## ## 幻梦程序媛双向bot配置

```
      {
        "APP_NAME": "幻梦AI程序媛-bot",
        "BOT_TOKEN": "7457866898:AAFhqjW6MLpblzZoffE5WxmIidsrBNEIL1A",
        "API_ID": "7457866898",
        "API_HASH": "AAFhqjW6MLpblzZoffE5WxmIidsrBNEIL1A",
        "WELCOME_MESSAGE": "你好，我是幻梦AI程序媛的bot。\n请问有什么可以帮助你的吗？",
        "ADMIN_GROUP_ID": -1002202845119,
        "ADMIN_USER_ID": 0,
        "DELETE_TOPIC_AS_FOREVER_BAN": 0,
        "DELETE_USER_MESSAGE_ON_CLEAR_CMD": 0
      }

{
  "bot_name": "ai748_bot",
  "token": "7457866898:AAFhqjW6MLpblzZoffE5WxmIidsrBNEIL1A",
  "config": {
    "group_chat_id": -1002202845119,
    "group_admin_id": -1002202845119,
    "welcome_msg": "你好，我是幻梦AI程序媛的bot。\n请问有什么可以帮助你的吗？"
  }
}
```
