import argparse
import asyncio
import logging
import dotenv
import os
import sentry_sdk

from tortoise import Tortoise, run_async
import yaml
from ai.image_tool import ImageBot
from persistence.feedback import FeedbackPersistence
from aerich import Command

dotenv.load_dotenv()

sentry_sdk.init(
    dsn="https://<EMAIL>/4506693877039104",
    # Set traces_sample_rate to 1.0 to capture 100%
    # of transactions for performance monitoring.
    traces_sample_rate=1.0,
    # Set profiles_sample_rate to 1.0 to profile 100%
    # of sampled transactions.
    # We recommend adjusting this value in production.
    profiles_sample_rate=1.0,
)

ROLES: dict[str, Role] = load_roles()

def load_speakers() -> dict[str, str]:
    speakers = {}
    with open('speakers.yaml', 'r') as f:
        ss = yaml.safe_load(f)
        for k, v in ss.items():
            speakers[k] = v['name']
    return speakers

SPEAKERS: dict[str, str] = load_speakers()

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s (%(filename)s:%(lineno)d) [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()],
)
httpx_logger = logging.getLogger("httpx")
# 设置 httpx 的日志级别为 WARNING
httpx_logger.setLevel(logging.WARNING)
logger = logging.getLogger(__name__)
feedback_persistence = FeedbackPersistence(os.environ.get('MONGO_URL', 'tg_bot'))

gpt_bot = GPTChatBot()
image_bot = ImageBot(os.environ['COS_SECRET_ID'], os.environ['COS_SECRET_KEY'], os.environ['COS_BUCKET'])

async def init_db():
    await Tortoise.init(
        db_url = os.environ['MYSQL_URL'],
        modules = {'models': ['persistence.models.models']}
    )
    command = Command(tortoise_config={
        'connections': { 'default': os.environ['MYSQL_URL'] },
        'apps': {
            'models': {
                'models': ['persistence.models.models', 'aerich.models'],
                'default_connection': 'default',
            },
        }
    }, app='models')
    await command.init()
    await command.migrate('update')

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='NloveN Novel Bot')
    parser.add_argument('--role', type=str, default='spoiled', required=False)
    parser.add_argument('--main', type=bool, default=True, required=False, action=argparse.BooleanOptionalAction)
    args = parser.parse_args()
    role = args.role

    if role == 'father':
        FatherBot().start()
    else:
        run_async(init_db())
        tgBot = RoleBot(role, gpt_bot, image_bot, args.main)
        run_async(tgBot.init_role())
        tgBot.init()
