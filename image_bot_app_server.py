
import asyncio
import logging
import os

from typing import List


from aiogram import <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
# from aiogram import LoggingMiddleware
from aiogram.types import Update, InlineKeyboardButton
from aiogram.fsm.storage.memory import MemoryStorage


from dotenv import load_dotenv
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware

from pydantic import BaseModel, Field
import sentry_sdk
from tortoise import Tortoise
from tortoise.contrib.fastapi import register_tortoise
import uvicorn

from common import loggers
from common.models.ai_bot_admin.group_config import BotGroupConfigBO

from services.bot_group.bot_group_service import BotGroupConfigAdminService,AutoReplyRuleHandler, SpamProtectionHandler,BotWelcomeConfigHandler


from services.bot_group.aibot_help import SaveMessageMiddleware,BotSendMsgPrivate
from persistence.models.models_bot_group import BotGroupMap

from aibot_handlers.group_help_dp_router import bot_handler_manager
from aibot_handlers.image_bot_dp_router import create_image_bot_router, create_img_recharge_bot_router,create_image_bot_router_en



logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s (%(filename)s:%(lineno)d) [%(levelname)s] %(message)s",handlers=[logging.StreamHandler(),loggers.local_bot_help_handler])

log = logging.getLogger(__name__)


FULL_NAME_LEN = 15
# 中间件
# class RateLimitMiddleware:
#     async def __call__(self, handler, event, data):
#         user_id = event.from_user.id
#         current_time = time.time()
#         if current_time - message_cooldowns[user_id] < 5:
#             await event.delete()
#             return
#         message_cooldowns[user_id] = int(current_time)
#         return await handler(event, data)

# 加载环境变量
load_dotenv()
WEBHOOK_HOST = os.getenv("IMAGE_WEBHOOK_HOST")
WEBHOOK_PATH_TEMPLATE = '/webhook/{bot_name}'
WEBHOOK_URL_TEMPLATE = f"{WEBHOOK_HOST}{WEBHOOK_PATH_TEMPLATE}"
TMA_BOT_TOKEN = os.getenv("TMA_BOT_TOKEN")
CHAT_BOT_TOKEN = os.getenv("CHAT_BOT_TOKEN")

IMAGE_CHECK_API = os.getenv("IMAGE_CHECK_API")


os.environ['NAI_PASSWORD'] = 'wYxmmK5FP2v$C5w'
os.environ['NAI_USERNAME'] = '<EMAIL>'

os.environ['NAI_TOKEN'] ="pst-KBxbYoH2YJDtvdKrq9MeXCzYT1YgQyGhYyqIUsyZrma3GAv1uBX46twT5lwSNonb"




class BotManager:
    def __init__(self):
        self.bots : dict[str,Bot] = {}
        self.dispatchers = {}
        self.configs = {}
        self.auto_reply_rule_handlers : dict[int,AutoReplyRuleHandler] = {}
        self.spam_protection_rule_handlers : dict[int,SpamProtectionHandler] = {}
        self.g_white_users = {}
        self.welcome_config : dict[int,BotWelcomeConfigHandler] = {}
        self.sender_bot = BotSendMsgPrivate(TMA_BOT_TOKEN,CHAT_BOT_TOKEN) # type: ignore
        
    async def initialize_bots_from_db(self):
        bot_configs = await BotGroupConfigAdminService.get_all_active_bot_configs()
        for bot_config in bot_configs:
            g_white_users = bot_config.g_white_users if isinstance(bot_config.g_white_users, list) else []
            await self.add_bot(bot_config.bot_name, bot_config.bot_token, bot_config.bot_group_id, g_white_users, bot_config.bot_type)

    # config = {"group_chat_id": 123456789,"group_admin_id": 0,"welcome_msg": "Welcome!"}
    async def add_bot(self, bot_name:str, token:str, group_id:int,g_white_users:List[int]=[],bot_type:str="image_bot"):
        
        log.info(f"Adding bot {bot_name} ,{group_id} with token {token},{bot_type}")
        
        if bot_type != "image_bot" and bot_type != "image_bot_en":
            log.error(f"Unsupported bot type: {bot_type}")
            return
        
        if bot_name in self.bots:
            raise ValueError(f"Bot with name {bot_name} already exists.")
        
        bot = Bot(token=token)
        storage = MemoryStorage()
        dp = Dispatcher(bot=bot,storage=storage)
        dp.update.outer_middleware(SaveMessageMiddleware())
        # dp.update.outer_middleware(HandleGroupUserMsgMiddleware())
    

        
        self.bots[bot_name] = bot
        self.dispatchers[bot_name] = dp 
        
        if bot_type == "image_bot":
            image_bot_router = create_image_bot_router()
            img_re_bot_router = create_img_recharge_bot_router()
            
            #多个router 命令得放前面
            dp.include_router(img_re_bot_router)
            dp.include_router(image_bot_router)
            
            await self.set_webhook(bot_name)
            log.info(f"Image bot {bot_name} added with webhook set.")
        elif bot_type == "image_bot_en":
            image_bot_en_router = create_image_bot_router_en()
            
            dp.include_router(image_bot_en_router)
            await self.set_webhook(bot_name)
            log.info(f"Image bot EN {bot_name} added with webhook set.")
            
        
    def remove_bot(self, bot_name):
        if bot_name not in self.bots:
            raise ValueError(f"Bot with name {bot_name} does not exist.")
        
        del self.bots[bot_name]
        del self.dispatchers[bot_name]

    async def set_webhook(self, bot_name):
        if bot_name not in self.bots:
            raise ValueError(f"Bot with name {bot_name} does not exist.")
        
        webhook_url = WEBHOOK_URL_TEMPLATE.format(bot_name=bot_name)
        log.info(f"Setting webhook for bot {bot_name} to {webhook_url}")
        allow_updates = ["message","edited_message","channel_post","edited_channel_post","callback_query","my_chat_member","chat_member"]
        await self.bots[bot_name].set_webhook(url=webhook_url,allowed_updates=allow_updates)


    async def process_update(self, bot_name, update: Update):
        if bot_name not in self.dispatchers:
            log.error(f"Bot with name {bot_name} does not exist.")
            return
        bot = self.bots[bot_name]
        await self.dispatchers[bot_name].feed_update(bot=bot, update=update)





# Initialize FastAPI app and BotManager
sentry_sdk.init(
    dsn="https://<EMAIL>:8443/11",

    traces_sample_rate=0.1
)


app = FastAPI()


# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有 HTTP 方法
    allow_headers=["*"],  # 允许所有 HTTP 头
)

bot_manager = BotManager()



# #增加测试的bot
# bot_manager.add_bot(bot_name='ai747_bot',token="7357790700:AAHt_84ueO_t6bN3Pw-ewANthYIW8I4cO_k",config={"group_chat_id":-1002226814292})


Tortoise.init_models(["persistence.models.models_bot_group","persistence.models.models","persistence.models.models_bot_image"], "models")
register_tortoise(
    app=app,
    db_url=os.environ["MYSQL_URL"],
    modules={"models": ["persistence.models.models_bot_group","persistence.models.models","persistence.models.models_bot_image"]},
    generate_schemas=True,
)

# Set webhook routes dynamically
@app.post(WEBHOOK_PATH_TEMPLATE)
async def telegram_webhook(request: Request,bot_name: str):
    try:
        update = Update(**await request.json())
        log.info(f"Received telegram_webhook bot {bot_name},{update.model_dump(exclude_none=True)}")        
        
        await bot_manager.process_update(bot_name, update)
    except Exception as e:
        log.exception(f"process_update error update_id:{update.update_id},error:{e}")
        # raise HTTPException(status_code=500, detail="Request handling error")
    return JSONResponse({"status": "ok"})

@app.get("/sentry-debug")
async def trigger_error():
    division_by_zero = 1 / 0

# API endpoint to add a new bot
@app.post("/add_bot")
async def add_bot(bot_config: BotGroupConfigBO):
    try:
        await bot_manager.add_bot(bot_config.bot_name, bot_config.token,bot_config.group_id,bot_config.g_white_users,bot_config.bot_type)
        await bot_manager.set_webhook(bot_config.bot_name)
        await BotGroupConfigAdminService.save_bot_group_config(bot_config.bot_name, bot_config.token,bot_config.group_id,bot_config.g_white_users,bot_type=bot_config.bot_type)
        return {"message": f"Bot {bot_config} added and webhook set."}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    

@app.post("/add_bot_group_map")
async def add_bot_group_map(bot_id: int,group_id:int,bot_token:str):
    try:
        await BotGroupMap.create(bot_id=bot_id,group_id=group_id,bot_token=bot_token)
        
        await bot_handler_manager.load_bot_handler(bot_id=bot_id)
        return {"message": f"Bot {bot_id} added to group {group_id}."}
    except ValueError as e:
        log.error(f"add_bot_group_map error {bot_id},{group_id}, {e}",exc_info=True)
        raise HTTPException(status_code=400, detail=str(e))




#Api endpoint to get all bots
@app.get("/get_all_bots")
async def get_all_bots(v_p:str):
    if v_p != 'bot_123':
        raise HTTPException(status_code=400, detail="Invalid view permission")
    return await BotGroupConfigAdminService.get_all_active_bot_configs()

# Set webhooks on startup
@app.on_event("startup")
async def on_startup():
    # Initialize bots from database
    await bot_manager.initialize_bots_from_db()

    


if __name__ == '__main__':
    uvicorn.run(app, host='0.0.0.0', port=9200)