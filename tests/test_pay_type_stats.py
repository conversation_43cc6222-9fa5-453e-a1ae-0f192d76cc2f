"""
按支付类型统计功能测试

测试渠道成功率按支付类型分别统计的功能
"""

import asyncio
import logging
from datetime import datetime, timedelta, UTC
from services import recharge_channel_service
from persistence.models.models import (
    RechargeChannelStats, 
    RechargeChannelEnum, 
    RechargeOrder, 
    RechargeStatusEnum
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def create_test_orders():
    """创建测试订单数据"""
    logger.info("创建测试订单数据...")
    
    base_time = datetime.now(UTC) - timedelta(hours=12)
    test_orders = []
    
    # 为TMPAY渠道创建不同支付类型的订单
    # 支付宝订单：8成功/10总数 = 80%成功率
    for i in range(10):
        status = RechargeStatusEnum.SUCCEED if i < 8 else RechargeStatusEnum.FAILED
        order = RechargeOrder(
            user_id=99999,
            out_order_id=f"test_alipay_{i}",
            amount=10000,
            pay_fee=1000,
            pay_currency='CNY',
            pay_type='alipay',
            recharge_channel=RechargeChannelEnum.TMPAY,
            status=status,
            recharge_product_id="test_product",
            created_at=base_time + timedelta(minutes=i*5)
        )
        await order.save()
        test_orders.append(order)
    
    # 微信订单：6成功/10总数 = 60%成功率
    for i in range(10):
        status = RechargeStatusEnum.SUCCEED if i < 6 else RechargeStatusEnum.FAILED
        order = RechargeOrder(
            user_id=99999,
            out_order_id=f"test_wechat_{i}",
            amount=10000,
            pay_fee=1000,
            pay_currency='CNY',
            pay_type='wechat',
            recharge_channel=RechargeChannelEnum.TMPAY,
            status=status,
            recharge_product_id="test_product",
            created_at=base_time + timedelta(minutes=i*5 + 100)
        )
        await order.save()
        test_orders.append(order)
    
    # 为JLBZF渠道创建订单
    # 支付宝订单：3成功/10总数 = 30%成功率
    for i in range(10):
        status = RechargeStatusEnum.SUCCEED if i < 3 else RechargeStatusEnum.FAILED
        order = RechargeOrder(
            user_id=99999,
            out_order_id=f"test_jlbzf_alipay_{i}",
            amount=10000,
            pay_fee=1000,
            pay_currency='CNY',
            pay_type='alipay',
            recharge_channel=RechargeChannelEnum.JLBZF,
            status=status,
            recharge_product_id="test_product",
            created_at=base_time + timedelta(minutes=i*5 + 200)
        )
        await order.save()
        test_orders.append(order)
    
    # 微信订单：9成功/10总数 = 90%成功率
    for i in range(10):
        status = RechargeStatusEnum.SUCCEED if i < 9 else RechargeStatusEnum.FAILED
        order = RechargeOrder(
            user_id=99999,
            out_order_id=f"test_jlbzf_wechat_{i}",
            amount=10000,
            pay_fee=1000,
            pay_currency='CNY',
            pay_type='wechat',
            recharge_channel=RechargeChannelEnum.JLBZF,
            status=status,
            recharge_product_id="test_product",
            created_at=base_time + timedelta(minutes=i*5 + 300)
        )
        await order.save()
        test_orders.append(order)
    
    logger.info(f"创建了 {len(test_orders)} 个测试订单")
    return test_orders

async def cleanup_test_orders(test_orders):
    """清理测试订单"""
    logger.info("清理测试订单...")
    for order in test_orders:
        await order.delete()
    logger.info("测试订单清理完成")

async def test_pay_type_statistics():
    """测试按支付类型统计"""
    logger.info("=== 测试按支付类型统计 ===")
    
    # 更新TMPAY渠道统计
    updated_stats = await recharge_channel_service.update_channel_success_rate(RechargeChannelEnum.TMPAY)
    
    logger.info(f"TMPAY渠道统计更新完成，共 {len(updated_stats)} 条记录")
    
    for stat in updated_stats:
        logger.info(f"  {stat.pay_type}: 总订单={stat.total_orders}, 成功订单={stat.success_orders}, 成功率={stat.success_rate:.2f}%")
    
    # 更新JLBZF渠道统计
    updated_stats = await recharge_channel_service.update_channel_success_rate(RechargeChannelEnum.JLBZF)
    
    logger.info(f"JLBZF渠道统计更新完成，共 {len(updated_stats)} 条记录")
    
    for stat in updated_stats:
        logger.info(f"  {stat.pay_type}: 总订单={stat.total_orders}, 成功订单={stat.success_orders}, 成功率={stat.success_rate:.2f}%")

async def test_smart_routing_with_pay_type():
    """测试基于支付类型的智能路由"""
    logger.info("=== 测试基于支付类型的智能路由 ===")
    
    # 测试支付宝路由
    alipay_primary, alipay_secondary = await recharge_channel_service.get_channel_queues_for_routing(
        'type1', 'alipay', 10000)
    logger.info(f"支付宝路由 - 主队列: {alipay_primary}, 备用队列: {alipay_secondary}")
    
    # 测试微信路由
    wechat_primary, wechat_secondary = await recharge_channel_service.get_channel_queues_for_routing(
        'type1', 'wechat', 10000)
    logger.info(f"微信路由 - 主队列: {wechat_primary}, 备用队列: {wechat_secondary}")
    
    # 验证路由结果
    # 对于支付宝：TMPAY(80%) > JLBZF(30%)，所以TMPAY应该在前面
    # 对于微信：JLBZF(90%) > TMPAY(60%)，所以JLBZF应该在前面
    
    if 'TMPAY' in alipay_primary and alipay_primary.index('TMPAY') < alipay_primary.index('JLBZF'):
        logger.info("✓ 支付宝路由正确：TMPAY优先于JLBZF")
    else:
        logger.warning("✗ 支付宝路由可能有问题")
    
    if 'JLBZF' in wechat_primary and wechat_primary.index('JLBZF') < wechat_primary.index('TMPAY'):
        logger.info("✓ 微信路由正确：JLBZF优先于TMPAY")
    else:
        logger.warning("✗ 微信路由可能有问题")

async def test_stats_query_optimization():
    """测试统计查询优化"""
    logger.info("=== 测试统计查询优化 ===")
    
    # 测试获取特定支付类型的统计
    alipay_stats = await RechargeChannelStats.filter(pay_type='alipay').all()
    logger.info(f"支付宝统计记录数: {len(alipay_stats)}")
    
    wechat_stats = await RechargeChannelStats.filter(pay_type='wechat').all()
    logger.info(f"微信统计记录数: {len(wechat_stats)}")
    
    all_stats = await RechargeChannelStats.filter(pay_type='ALL').all()
    logger.info(f"全部统计记录数: {len(all_stats)}")
    
    # 测试统计映射构建
    specific_stats = await RechargeChannelStats.filter(pay_type='alipay').all()
    all_stats = await RechargeChannelStats.filter(pay_type='ALL').all()
    
    stats_map = {}
    for stat in all_stats:
        stats_map[stat.channel] = stat
    for stat in specific_stats:
        stats_map[stat.channel] = stat  # 覆盖ALL统计
    
    logger.info(f"统计映射构建完成，共 {len(stats_map)} 个渠道")
    
    for channel, stat in stats_map.items():
        logger.info(f"  {channel.value}: {stat.pay_type} 成功率={stat.success_rate:.2f}%")

async def test_cleanup_stats():
    """清理测试统计数据"""
    logger.info("=== 清理测试统计数据 ===")
    
    # 删除测试相关的统计数据
    await RechargeChannelStats.filter(
        channel__in=[RechargeChannelEnum.TMPAY, RechargeChannelEnum.JLBZF]
    ).delete()
    
    logger.info("测试统计数据清理完成")

async def run_pay_type_stats_tests():
    """运行按支付类型统计的测试"""
    try:
        from tortoise import Tortoise
        
        # 初始化数据库连接
        await Tortoise.init(
            db_url="sqlite://db.sqlite3",  # 根据实际数据库配置修改
            modules={"models": ["persistence.models.models"]}
        )
        
        logger.info("开始按支付类型统计功能测试...")
        
        # 创建测试数据
        test_orders = await create_test_orders()
        
        try:
            # 运行测试
            await test_pay_type_statistics()
            await test_smart_routing_with_pay_type()
            await test_stats_query_optimization()
            
        finally:
            # 清理测试数据
            await cleanup_test_orders(test_orders)
            await test_cleanup_stats()
        
        logger.info("按支付类型统计功能测试完成!")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        raise
    finally:
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(run_pay_type_stats_tests())
