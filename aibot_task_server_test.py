import asyncio
from datetime import datetime
import logging
import os
import time
from fastapi import <PERSON><PERSON><PERSON>, APIRouter
from fastapi.middleware.cors import CORSMiddleware
import pytz
from tortoise import Tortoise
from tortoise.contrib.fastapi import register_tortoise
from dotenv import load_dotenv
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger

from tortoise.functions import Count,Sum

from persistence.models.models_bot_group import TgGroupMessage

from common import loggers
from common.common_constant import Env
from tasks.aibot import cal_group_msg_task
from utils import env_util

load_dotenv()

import sentry_sdk
import uvicorn

load_dotenv()

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s (%(filename)s:%(lineno)d) [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()],
)
log = logging.getLogger(__name__)

# Initialize FastAPI app and BotManager
sentry_sdk.init(
    dsn="https://<EMAIL>:8443/10",

    traces_sample_rate=1.0,
)

# cors_origins = os.getenv("CORS_ORIGINS", "").split(",")

app = FastAPI()
# app.add_middleware(
#     CORSMiddleware,
#     allow_origins=cors_origins,
#     allow_credentials=True,
#     allow_methods=["*"],
#     allow_headers=["*"],
#     expose_headers=[
#         "Conversation-Id",
#         "Message-Id",
#         "Message-Version",
#         "Human-Message-Id",
#     ],
# )
scheduler = AsyncIOScheduler()

router = APIRouter()

@router.get("/tasks")
async def tasks():
    tasks = scheduler.get_jobs()
    r = []
    for task in tasks:
        job = {'name': task.name, 'func': task.func_ref,
               'next_run_time': task.next_run_time.strftime('%Y-%m-%d %H:%M:%S')}
        r.append(job)

    return r

@router.get("/task/run")
async def task(task_name: str):
    jobs = scheduler.get_jobs()
    for job in jobs:
        if job.name == task_name:
            await job.func()
            return {"status": "ok"}
    return {"status": "error", "msg": "task not found"}


app.include_router(router)


Tortoise.init_models(["persistence.models.models_bot_group","persistence.models.models"], "models")
register_tortoise(
    app=app,
    db_url=os.environ["MYSQL_URL"],
    modules={"models": ["persistence.models.models_bot_group","persistence.models.models"]},
    generate_schemas=True,
)

async def run():
    # 初始化数据库连接
    await Tortoise.init(
        db_url='mysql://tgbot_wr:abdKATtvoCB620NsfajOz7FK@172.22.16.12:3306/tavern',  # 替换为你的数据库连接字符串
        modules={'models': ["persistence.models.models_bot_group","persistence.models.models"]}
    )

    #cal_user_last_hour_msg_stat,group_id:-1002357871790
# 2025-01-23 16:05:00,077 (tg_group_user_msg_biz.py:251) [INFO] cal_user_hours_msg_stat,start_date:2025-01-23 15:00:00,end_date:2025-01-23 16:00:00
    group_id = -1002357871790  # 群组id
    start_date = datetime(2025, 1, 23, 8, 0, 0)  # 查开始时间
    end_date = datetime(2025, 1, 23, 9, 0, 0)  # 查结束时间
    print(f"start_date:{start_date}-{end_date}")
    # 查询消息
            # 转换为utc时间,因为数据库存储的是utc时间
    start_utc_time = start_date.astimezone(pytz.utc)
    end_utc_time = end_date.astimezone(pytz.utc)
    print(f"cal_user_hours_msg_stat,start_date:{group_id},{start_date},end_date:{end_date},{start_utc_time},{end_utc_time}")
    user_msg_stat = await TgGroupMessage.filter(group_id=group_id, created_at__gte=start_utc_time, created_at__lt=end_utc_time).annotate(count=Count('id')).group_by('user_id').values('user_id', 'count')
    
    print(f"user_msg_stat:{user_msg_stat}")

    tg_msg = await TgGroupMessage.filter(id = 84970).first()
    # 2025-01-23 00:57:48.220586	2025-01-23 00:57:48.220594
    print(f"tg_msg:{tg_msg.created_at}")

    # 关闭数据库连接
    await Tortoise.close_connections()





if __name__ == "__main__":
    # uvicorn.run(app, host="0.0.0.0", port=9220)
    asyncio.run(run())
