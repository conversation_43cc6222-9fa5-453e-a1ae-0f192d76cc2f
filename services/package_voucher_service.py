from tortoise.transactions import in_transaction
from datetime import datetime, timedelta, UTC
from persistence.models.models import PackageVoucher, RechargeOrder, RechargeProduct, RechargeStatusEnum, RechargeChannelEnum, Account, LedgerEntry, TransactionTypeEnum, TransactionSourceEnum, ExpirableAward, ExpirableStatusEnum, VoucherStatusEnum
from services.reward_calculation_service import RewardCalculationService

async def get_package_voucher(voucher_code: str) -> PackageVoucher | None:
    if voucher_code.startswith('卡'):
        voucher_code = voucher_code[3:].strip()
    return await PackageVoucher.filter(voucher_id=voucher_code).first()

async def redeem_package_voucher(voucher_code: str, user_id: int, used_from: str = 'TMA') -> tuple[bool, str,RechargeOrder | None]:
    if voucher_code.startswith('卡'):
        voucher_code = voucher_code[3:].strip()
    async with in_transaction():
        voucher = await PackageVoucher.filter(voucher_id=voucher_code).first()
        if voucher is None:
            return False, '无效的兑换码',None
        if voucher.status != VoucherStatusEnum.NORMAL:
            return False, '兑换码已经被使用',None
        if voucher.expire_at < datetime.now(UTC):
            return False, '兑换码已过期',None

        recharge: RechargeProduct = await RechargeProduct.filter(recharge_product_id=voucher.recharge_product_id, enabled=True).first() # type: ignore

        history_orders = await RechargeOrder.filter(
            user_id=user_id, status=RechargeStatusEnum.SUCCEED, 
            pay_fee__gt=0).all()
        if len(history_orders) <= 0:
            recharge.reward_amount = await RewardCalculationService.calculate_reward_amount(user_id, recharge)

        if recharge.max_charge_times > 0:
            charged_times = await RechargeOrder.filter(user_id=user_id, recharge_product_id=recharge.recharge_product_id).count()
            if charged_times >= recharge.max_charge_times:
                return False, "Exceed max charge times.",None

        if recharge.cny_price > 0:
            price = recharge.cny_price
            currency = 'CNY'
        else:
            price = recharge.price
            currency = 'USD'
        recharge_order = RechargeOrder(
            user_id=user_id,
            amount=recharge.amount + recharge.reward_amount,
            pay_fee=price,
            pay_currency=currency,
            status=RechargeStatusEnum.SUCCEED,
            recharge_channel=RechargeChannelEnum.PACKAGE_VOUCHER,
            out_order_id=voucher.voucher_id,
            finished_at=datetime.now(UTC),
            recharge_product_id=recharge.recharge_product_id
        )

        await recharge_order.save()

        now = datetime.now(UTC)
        if recharge.charged_expire_delta <= 0:
            account = await Account.filter(user_id=user_id).first()
            leger_entry = LedgerEntry(
                user_id=user_id,
                transaction_id=recharge_order.recharge_order_id,
                amount=recharge_order.amount,
                type=TransactionTypeEnum.CREDIT,
                before_balance=account.total_balance,
                after_balance=account.total_balance + recharge_order.amount,
                source=TransactionSourceEnum.RE_CHARGE,
                description=f"账户余额充值{recharge.price}元",
            )
            await leger_entry.save()

            account.total_balance += recharge_order.amount
            account.charge_balance += recharge_order.amount
            await account.save()
        else:
            # payed amount
            if recharge.amount > 0:
                payed_award = ExpirableAward(user_id=user_id,
                                                out_order_id = recharge_order.recharge_order_id,
                                                total_amount=recharge.amount,
                                                spend_amount=0,
                                                balance=recharge.amount,
                                                status=ExpirableStatusEnum.NORMAL,
                                                expires_at=datetime(2500, 1, 1),
                                                claim_at=now,
                                                from_type='PAYED')
                await payed_award.save()
            if recharge.reward_amount > 0:
                reward_award = ExpirableAward(user_id=user_id,
                                                out_order_id = recharge_order.recharge_order_id,
                                                total_amount=recharge.reward_amount,
                                                spend_amount=0,
                                                balance=recharge.reward_amount,
                                                status=ExpirableStatusEnum.NORMAL,
                                                expires_at=now + timedelta(days=recharge.charged_expire_delta),
                                                claim_at=now)
                await reward_award.save()

        voucher.status = VoucherStatusEnum.USED
        voucher.used_by_user_id = user_id
        voucher.used_at = now
        voucher.used_from = used_from
        await voucher.save()

    return True, 'Success',recharge_order

async def get_today_lottery_voucher(user_id: int) -> list[RechargeOrder]:
    now = datetime.now(UTC).date()
    return await RechargeOrder.filter(user_id=user_id,
                         recharge_channel=RechargeChannelEnum.PACKAGE_VOUCHER, 
                         pay_fee__lte=0, 
                         finished_at__gte=now,
                         finished_at__lt=now + timedelta(days=1),
                         status=RechargeStatusEnum.SUCCEED).all()
    
