"""
服务于终端用户的接口，用户可以报名活动，领取奖励等
"""
from datetime import UTC, datetime, timedelta
import logging
import math
import os
from common.activity_diamond_model import DiamondSeasonConfig, DiamondTaskConfig
from common.common_constant import BotCategory
from persistence.models.models import ActivityDiamondLotteryResult, ActivityDiamondSeason, ActivityDiamondTask, ActivityDiamondTaskParticipant, DiamondSeasonStatus, DiamondTaskStatus, LotteryClaimStatus, ParticipationStatus, RechargeChannelEnum
from tortoise.transactions import in_transaction

from services import gift_award_service, product_service, tg_config_service
from services.role import role_loader_service
from utils import json_util, user_growth_constants

log = logging.getLogger(__name__)

DIAMOND_SEASON_ROLE_MSG = """
🗣️角色卡名: <a href="https://t.me/{tma_bot_user_name}/tavern?startapp=rd_{role_id}">{card_name}（可点击查看）</a>\n"""

# 2025年1月27日4:00-8:00
TIME_TEMPLATE = "香港时间{start_time}-{end_time}"
MSG_PREFIX = TIME_TEMPLATE + "消耗💎返还🟡活动"



_NEW_ENROLL_SUCCESS_FIRST_SENTENCE="""<b>消耗💎返🟡活动报名成功</b>❗️❗️"""

_NEW_ENROLL_MESSAGE_BODY_PART1 = """<b>活动时间</b>：""" + TIME_TEMPLATE + """\n<b>活动目标</b>：消耗💎{diamond_required}，{return_rate}%返还❗️（当前消耗💎0/{diamond_required}）

<b>完成消耗💎返🟡活动的几个必要条件</b>：
1、仅💎可参加，目标消耗{diamond_required}内先用💎，完成目标者在活动结束后1小时自动返还{return_rate}%共{return_diamond}🟡
2、报名成功：收到报名成功的通知才可参加
3、活动中只能使用的聊天模式：{allowed_chat_model}"""


_NEW_ENROLL_MESSAGE_BODY_PART2_SEPCIFIED_ROLES = """4、聊指定角色卡"""

_NEW_ENROLL_MESSAGE_BODY_PART2_SEPCIFIED_ROLES_PART2 = """该时间段参与消耗💎活动的角色卡如下👇"""

_NEW_ENROLL_MESSAGE_BODY_PART2_ALL_ROLES = """4、和任意角色卡聊天都可参与活动"""

# 报名成功的消息（活动支持所有角色）- 同时用于小程序弹窗和TG消息
NEW_ENROLL_SUCCESS_MSG_FOR_ALL_ROLES = _NEW_ENROLL_SUCCESS_FIRST_SENTENCE + "\n\n" + _NEW_ENROLL_MESSAGE_BODY_PART1 + "\n" + _NEW_ENROLL_MESSAGE_BODY_PART2_ALL_ROLES
# 报名成功的消息（活动支持部分角色）-用于小程序弹窗
NEW_ENROLL_SUCCESS_MSG_FOR_SEPCIFIED_ROLES_TMA = _NEW_ENROLL_SUCCESS_FIRST_SENTENCE + "\n\n" + _NEW_ENROLL_MESSAGE_BODY_PART1 + "\n" + _NEW_ENROLL_MESSAGE_BODY_PART2_SEPCIFIED_ROLES
# 报名成功的消息（活动支持部分角色）- 用于TG消息
NEW_ENROLL_SUCCESS_MSG_FOR_SEPCIFIED_ROLES_TG = NEW_ENROLL_SUCCESS_MSG_FOR_SEPCIFIED_ROLES_TMA + "\n" + _NEW_ENROLL_MESSAGE_BODY_PART2_SEPCIFIED_ROLES_PART2

# 小程序弹窗用-活动规则（活动支持所有角色）
_NEW_ENROLL_RULE_MESSAGE_BODY_FOR_ALL_ROLES = _NEW_ENROLL_MESSAGE_BODY_PART1 + "\n" + _NEW_ENROLL_MESSAGE_BODY_PART2_ALL_ROLES
# 小程序弹窗用-活动规则（活动支持部分角色）
_NEW_ENROLL_RULE_MESSAGE_BODY_FOR_SEPCIFIED_ROLES = _NEW_ENROLL_MESSAGE_BODY_PART1 + "\n" + _NEW_ENROLL_MESSAGE_BODY_PART2_SEPCIFIED_ROLES

_ENROLL_NOTICE_MSG_PREFIX = "消耗💎返🟡活动已开启\n您还未报名❗️您还未报名❗️您还未报名❗️"
# 小程序弹窗用-通知报名（活动支持所有角色）
ENROLL_NOTICE_MSG_FOR_ALL_ROLES = _ENROLL_NOTICE_MSG_PREFIX + "\n\n" + _NEW_ENROLL_RULE_MESSAGE_BODY_FOR_ALL_ROLES
# 小程序弹窗用-通知报名（活动支持部分角色）
ENROLL_NOTICE_MSG_FOR_SEPCIFIED_ROLES = _ENROLL_NOTICE_MSG_PREFIX + "\n\n" + _NEW_ENROLL_RULE_MESSAGE_BODY_FOR_SEPCIFIED_ROLES

ENROLL_IS_FULL_MSG = MSG_PREFIX + "已达报名人数上限❗️报名失败。请关注福利频道 @huanmeng_ai ，会提前通知活动开始时间哦❤️"

LAST_TASK_ENROLL_IS_FULL_MSG= MSG_PREFIX + "已达报名人数上限❗️报名失败。本次消耗💎活动即将全部结束❗️❗️关注福利频道 @huanmeng_ai 可以第一时间获得最新活动信息哦❤️"

TASK_EXPIRE_MSG = MSG_PREFIX + "已结束，报名失败。请关注福利频道 @huanmeng_ai ，会提前通知活动开始时间哦"

START_NOTICE_MSG = """{start_notice_config}

🔗<a href="https://t.me/{tma_bot_user_name}?start=act_enroll_{task_id}">通过小程序Bot报名</a>👈
🔗<a href="https://t.me/{bot_user_name}?start=act_enroll_{task_id}">通过TG直聊Bot报名</a>👈"""

LEFT_AMOUNT_NOTICE_MSG = "<b>" + MSG_PREFIX + "剩余{left_amount}个报名名额，快去报名🚀🚀</b>"

RECHARGE_MSG_PART1 = """💎<b>余额不足</b>
<b>尊敬的用户，您当前正在参与消耗💎返🟡活动；</b>
1、目标：消耗{diamond_required}💎
2、当前进度：<b>{diamond_consumption}/{diamond_required}，还差{diamond_left}💎完成</b>

当前💎不足，继续聊天开始消耗🟡，但消耗🟡无法完成目标剩余的💎消耗；
<b>为避免您无法完成活动，损失奖励返还的{return_diamond}🟡，建议：完成充值补充💎，继续完成目标。</b>"""

RECHARGE_MSG = RECHARGE_MSG_PART1 + "\n\n" + """点击下方按钮，选择对应套餐，充值即可。

<b>推荐使用</b>：
微信（钻石直接到账）
支付宝（钻石直接到账）

如有任何问题，请联系官方客服。 @{bot}"""

# 用户报名某一次任务
async def enroll_diamond_season_activity(user_id: int, task_id: str):
    task = await ActivityDiamondTask.filter(task_id=task_id, status=DiamondTaskStatus.ACTIVE.value).first()
    if task is None:
        return None, "任务不存在"
    season = await ActivityDiamondSeason.filter(season_id=task.season_id, status=DiamondSeasonStatus.IN_PROGRESS.value).first()
    if season is None:
        return None, "本次消耗💎活动已终止。关注福利频道 @huanmeng_ai 获得第一手最新活动信息"
    if season.end_at < datetime.now(UTC):
        return None, "本次消耗💎活动已结束。关注福利频道 @huanmeng_ai 获得第一手最新活动信息"
    if task.end_at <= datetime.now(UTC):
        return None, TASK_EXPIRE_MSG.format(start_time=_format_datetime(task.start_at), end_time=_format_datetime(task.end_at))
    
    
    enroll_info = await ActivityDiamondTaskParticipant.filter(user_id=user_id,task_id=task_id).first()
    # 检查是否已经报名
    if enroll_info is not None:
        return None, "您已报名成功，不能重复报名" 
    # 检查是否已经报名满
    result, msg = await check_max_participants(task)
    if not result:
        return None, msg
    # 检查是否已经报名过其他活动
    result, msg = await check_exclusive_enrollment(task, user_id)
    if not result:
        return None, msg
    
    await ActivityDiamondTaskParticipant.create(user_id=user_id, task_id=task_id, season_id=task.season_id, join_at=datetime.now(UTC), task_end_at=task.end_at, status=ParticipationStatus.ENROLLED.value)
    
    tg_msg = await generate_enroll_success_msg(task)
    return json_util.convert_to_list(task.role_ids), tg_msg

async def check_max_participants(task: ActivityDiamondTask):
    task_id = str(task.task_id)
    # 检查是否已经报名满
    if task.max_participants <= await ActivityDiamondTaskParticipant.filter(task_id=task_id).count():
        # 查询最后一场任务
        is_last_task = await _is_last_task(task)
        if is_last_task:
            return False, LAST_TASK_ENROLL_IS_FULL_MSG.format(start_time=_format_datetime(task.start_at), end_time=_format_datetime(task.end_at))
        else:
            return False, ENROLL_IS_FULL_MSG.format(start_time=_format_datetime(task.start_at), end_time=_format_datetime(task.end_at))
    return True, ""
        


# 活动如果配置了不允许和当天其他活动同时报名，检查用户是否已经报名过其他活动
async def check_exclusive_enrollment(task: ActivityDiamondTask, user_id: int):
    task_id = str(task.task_id)
    if task.allow_repeated_enrolled:
        return True, ""
    utc_start_at = task.start_at
    utc8_dt = utc_start_at + timedelta(hours=8)
    # 计算 UTC 时间范围
    # 例如：如果要查询东八区 2024-01-01 这一天
    # UTC 时间范围应该是 2023-12-31 16:00:00 UTC 到 2024-01-01 15:59:59 UTC
    start_utc = utc8_dt.replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(hours=8)
    end_utc = start_utc + timedelta(days=1)
    same_day_task_ids = await ActivityDiamondTask.filter(season_id=task.season_id, status=DiamondTaskStatus.ACTIVE.value, start_at__gte=start_utc.replace(tzinfo=None), start_at__lt=end_utc.replace(tzinfo=None)).exclude(task_id=task_id).values_list('task_id', flat=True)
    # 如果其他活动没有达标，可参加
    if same_day_task_ids and await ActivityDiamondTaskParticipant.filter(user_id=user_id, task_id__in=same_day_task_ids, status__gt=ParticipationStatus.ENROLLED.value).count() > 0:
        is_last_task = await _is_last_task(task)
        if is_last_task:
            return False, "您今天已经参与过一次消耗💎活动，不可再参与❗️消耗💎活动即将结束❗️关注福利频道 @huanmeng_ai 获得第一手最新活动信息❤️"
        else:
            return False, "您今天已经参与过一次消耗💎活动，不可再参与❗️请明天再来❤️"
    return True, ""

async def generate_enroll_success_msg(task: ActivityDiamondTask, is_bot_msg: bool = True):
    allowed_chat_models = json_util.convert_to_list(task.allowed_chat_models)
    chat_products = await product_service.list_chat_product_new()
    allowed_model_names = []
    for product in chat_products:
        if product.mid in allowed_chat_models:
            allowed_model_names.append(product.model_name)
    allowed_model_name_str = ", ".join(allowed_model_names)

    role_ids = json_util.convert_to_list(task.role_ids)
    if -1 in role_ids:
        msg_templae = NEW_ENROLL_SUCCESS_MSG_FOR_ALL_ROLES
    else:
        if is_bot_msg:
            msg_templae = NEW_ENROLL_SUCCESS_MSG_FOR_SEPCIFIED_ROLES_TG
        else:
            msg_templae = NEW_ENROLL_SUCCESS_MSG_FOR_SEPCIFIED_ROLES_TMA
    
    return msg_templae.format(start_time=_format_datetime(task.start_at), end_time=_format_datetime(task.end_at), diamond_required=task.required_diamond_amount, return_rate=task.return_rate/100,return_diamond=int(task.required_diamond_amount*task.return_rate/10000), allowed_chat_model=allowed_model_name_str)

async def generate_notify_enroll_msg(task: ActivityDiamondTask):
    allowed_chat_models = json_util.convert_to_list(task.allowed_chat_models)
    chat_products = await product_service.list_chat_product_new()
    allowed_model_names = []
    for product in chat_products:
        if product.mid in allowed_chat_models:
            allowed_model_names.append(product.model_name)
    allowed_model_name_str = ", ".join(allowed_model_names)

    role_ids = json_util.convert_to_list(task.role_ids)
    if -1 in role_ids:
        tg_msg_templae = ENROLL_NOTICE_MSG_FOR_ALL_ROLES
    else:
        tg_msg_templae = ENROLL_NOTICE_MSG_FOR_SEPCIFIED_ROLES
    
    return tg_msg_templae.format(start_time=_format_datetime(task.start_at), end_time=_format_datetime(task.end_at), diamond_required=task.required_diamond_amount, return_rate=task.return_rate/100,return_diamond=int(task.required_diamond_amount*task.return_rate/10000), allowed_chat_model=allowed_model_name_str)

async def generate_recharege_msg_for_bot(task: ActivityDiamondTask, user_id: int):
    result = await _get_diamond_task_progress_by_uid_helper(user_id, task)
    if result is None:
        return None
    return await _generate_recharege_msg_helper(task, result)

async def _generate_recharege_msg_helper(task: ActivityDiamondTask, progress_result: dict, is_bot_msg: bool = True):
    diamond_consumption = progress_result["diamond_consumption"]
    target_left = task.required_diamond_amount - diamond_consumption
    if target_left <= 0:
        return None
    if is_bot_msg:
        bot = await tg_config_service.get_main_bot_by_category(BotCategory.CUSTOMER)
        return RECHARGE_MSG.format(diamond_required=task.required_diamond_amount, diamond_consumption=diamond_consumption, diamond_left=target_left, return_diamond=int(task.required_diamond_amount*task.return_rate/10000), bot=bot.username)
    else:
        return RECHARGE_MSG_PART1.format(diamond_required=task.required_diamond_amount, diamond_consumption=diamond_consumption, diamond_left=target_left, return_diamond=int(task.required_diamond_amount*task.return_rate/10000))

async def get_diamond_activity_role_msg(role_ids: list[int] | None):
    # -1表示所有角色
    if not role_ids or -1 in role_ids:
        return None
    role_list = await role_loader_service.list_by_ids(role_ids)
    role_map = {role.id: role for role in role_list}
    extra_msg = ''          
    for role_id in role_ids:
        role = role_map.get(role_id)
        if not role or not role.status or not role.privacy:
            continue
        extra_msg += DIAMOND_SEASON_ROLE_MSG.format(role_id=role.id, card_name=role.card_name, tma_bot_user_name=os.getenv("DIAMOND_SEASON_TMA_BOT")) 
    if extra_msg:
        #去掉最后一个换行符
        extra_msg = extra_msg[:-1]
        return extra_msg 
    return None


def _format_datetime(dt_utc: datetime):
    dt_utc8 = dt_utc + timedelta(hours=8)
    formatted_date = dt_utc8.strftime("%Y年%m月%d日")
    start_time = dt_utc8.strftime("%H:%M")
    return f"{formatted_date}{start_time}"

def _format_timestamp_to_utc8(ts: float):
    dt_utc = datetime.fromtimestamp(ts, UTC)
    dt_utc8 = dt_utc + timedelta(hours=8)
    return dt_utc8


async def _is_last_task(task: ActivityDiamondTask):
    last_task = await ActivityDiamondTask.filter(season_id=task.season_id, status=DiamondTaskStatus.ACTIVE.value).order_by('-start_at').first()
    return last_task is None or last_task.task_id == task.task_id

# 查询用户在当前已生效任务中的消费进度
async def get_diamond_task_progress_by_uid(user_id: int):
    _, current_task = await get_current_diamond_task()
    if current_task is None:
        return None
    progress = await _get_diamond_task_progress_by_uid_helper(user_id, current_task)
    if progress is None:
        return None
    msg = await _generate_recharege_msg_helper(current_task, progress, False)
    if msg :
        progress["msg"] = msg
    return progress

async def _get_diamond_task_progress_by_uid_helper(user_id: int, current_task: ActivityDiamondTask):
    task_id = str(current_task.task_id)
    enroll_info = await ActivityDiamondTaskParticipant.filter(user_id=user_id,task_id=task_id).first()
    if enroll_info is None:
        return None
    if enroll_info.diamond_consumption >= current_task.required_diamond_amount:
        diamond_consumption = current_task.required_diamond_amount
    else:
        diamond_consumption = enroll_info.diamond_consumption
    return {"task_id": task_id, "diamond_required": current_task.required_diamond_amount, "diamond_consumption": diamond_consumption}


# 查询哪个任务目前在进行中
async def get_current_diamond_task():
    current_date = datetime.now(UTC).replace(tzinfo=None)
    # 先得查询当前进行中的赛季
    season = await ActivityDiamondSeason.filter(start_at__lte=current_date, end_at__gte=current_date, status=DiamondSeasonStatus.IN_PROGRESS.value).first()
    if season is None:
        return None, None
    
    task = await ActivityDiamondTask.filter(season_id=season.season_id,start_at__lte=current_date, end_at__gte=current_date, status=DiamondTaskStatus.ACTIVE.value).first()

    if task is None:
        return None, None
    return DiamondTaskConfig.from_model(task), task

# 查询用户是否已报名了当前进行中的活动
async def get_enrolled_active_diamond_task_by_user_id(user_id: int):
    _, current_task = await get_current_diamond_task()
    if current_task is None:
        return None, False
    enroll_info = await ActivityDiamondTaskParticipant.filter(user_id=user_id, task_id=str(current_task.task_id)).first()
    if enroll_info is None:
        return current_task, False
    return current_task, True

async def get_current_diamond_task_paticipation_info():
    current_task_config, _ = await get_current_diamond_task()
    if current_task_config is None:
        return None, 0
    user_id_list= await ActivityDiamondTaskParticipant.filter(task_id=current_task_config.task_id).values_list('user_id', flat=True)
    if not user_id_list:
        return current_task_config, 0
    return current_task_config, len(user_id_list)

def generate_left_amount_notice_msg(task: DiamondTaskConfig, left_amount: int):
    chat_bot_name = os.getenv("DIAMOND_SEASON_CHAT_BOT")
    tma_bot_name = os.getenv("DIAMOND_SEASON_TMA_BOT")
    part1_msg = LEFT_AMOUNT_NOTICE_MSG.format(start_time=_format_datetime(datetime.fromtimestamp(task.start_at, UTC)), end_time=_format_datetime(datetime.fromtimestamp(task.end_at, UTC)), left_amount=left_amount)
    part2_msg =  START_NOTICE_MSG.format(start_notice_config=task.start_notice, tma_bot_user_name=tma_bot_name, bot_user_name=chat_bot_name, task_id=task.task_id)
    return part1_msg + "\n\n" + part2_msg

# 查询哪个赛季在预热中
async def get_current_warming_up_diamond_season():
    current_date = datetime.now(UTC).replace(tzinfo=None)
    season = await ActivityDiamondSeason.filter(warming_up_start_at__lte=current_date, warming_up_end_at__gte=current_date, status=DiamondSeasonStatus.IN_PROGRESS.value).first()
    if season is None:
        return None
    return DiamondSeasonConfig.from_model(season)  
    

# 查询完成任务的用户列表
async def get_task_completed_participants(task_id: str):
    return await ActivityDiamondTaskParticipant.filter(task_id=task_id, status__gt=ParticipationStatus.ENROLLED.value).values_list('user_id', flat=True)

# 用户领奖
async def receive_diamond_reward(user_id: int, task_id: str):
    current_date = datetime.now(UTC)
    enroll_info = await ActivityDiamondTaskParticipant.filter(user_id=user_id, task_id=task_id).first()
    if enroll_info is None:
        return None, "未报名该时间段消耗💎活动"
    if enroll_info.status == ParticipationStatus.ENROLLED.value:
        return None, "任务未完成"
    if enroll_info.status == ParticipationStatus.REWARED_CLAIMED.value:
        return None, "已领取该奖励, 不能重复领取"
    if enroll_info.task_end_at > current_date:
        return None, "奖励尚未开放领取"

    
    task = await ActivityDiamondTask.filter(task_id=task_id).first()
    if task is None:
        return None, "该时间段消耗💎活动不存在"
    
    required_diamond_amount = task.required_diamond_amount
    return_rate = task.return_rate
    diamond_returned = math.ceil(required_diamond_amount * return_rate/10000)

    channel = RechargeChannelEnum.CONSUMPTION_ACTIVITY_RETURN
    out_order_id = f"activity_diamond:{user_id}_{task_id}"
    
    lottery_diamond = 0
    
    success = False
    async with in_transaction():
        lottery_result = await ActivityDiamondLotteryResult.filter(task_id=task_id, user_id=user_id).first()
        if lottery_result is not None:
            # 正常情况下，这里不会出现该情况
            if lottery_result.status == LotteryClaimStatus.CLAIMED.value:
                return None, "已领取该奖励, 不能重复领取"
            lottery_diamond = lottery_result.diamond_amount
        # 用户如果中了大奖，不再发放普通奖励
        if lottery_diamond > 0:
            diamond_returned = lottery_diamond
            channel = RechargeChannelEnum.CONSUMPTION_ACTIVITY_GRAND_PRIZE
            out_order_id = f"activity_diamond_lottery:{user_id}_{task_id}"
            # 这个字段完全是为了统计活动的整体数据时方便写代码，所以把ActivityDiamondLotteryResult表里的数据冗余了一份
            enroll_info.obtained_lottery_diamond = diamond_returned
        else:
            enroll_info.obtained_diamond = diamond_returned
        # 发放奖励
        await gift_award_service.add_award_balance_with_charge_order(
            user_id=user_id, amount=diamond_returned,
            channel=channel,
            expire_delta=user_growth_constants.ACTIVITY_DIAMOND_SEASON_AWARD_EXPIRES,
            out_order_id=out_order_id)
        # 更新用户参与状态
        enroll_info.status = ParticipationStatus.REWARED_CLAIMED.value
        await enroll_info.save()
        # 更新抽奖结果状态
        if lottery_result is not None:
            lottery_result.status = LotteryClaimStatus.CLAIMED.value
            await lottery_result.save()
        success = True
    
    if success:
        log.info(f"receive_diamond_reward success: user_id:{user_id}, task_id:{task_id}, returned_diamond:{diamond_returned}")
        if lottery_diamond > 0:
            return diamond_returned, "🎉恭喜获得大奖‼️‼️领取的消耗活动返还🟡已发放到您的账户"
        else:
            return diamond_returned, "领取成功❗️领取的返还🟡已发放到您的账户"
    else:
        log.error(f"receive_diamond_reward failed: user_id:{user_id}, task_id:{task_id}")
        return None, "领取失败,请稍后再试"
    





        



    
    



