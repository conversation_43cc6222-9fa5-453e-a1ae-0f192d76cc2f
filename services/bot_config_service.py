import logging
import os
import random
# from typing import Any, Optional

# from aiogram import <PERSON><PERSON>
from aiogram.client.session.aiohttp import <PERSON>ohttpSession

# from common.common_constant import BotType
# from persistence.models.models import BotConfig
# from aiogram.enums import ParseMode
# from aiogram.types import Chat<PERSON><PERSON>berLeft
# from aiogram import Bo<PERSON>, types
# from aiogram.client.default import DefaultBotProperties

BOT_SENDER_MAP = {}
PROXY = os.environ.get("ALL_PROXY")
SESSION = None
if PROXY:
    SESSION = AiohttpSession(proxy=PROXY)


# async def get_bot_by_id(bot_id: int):
#     bot = await BotConfig.filter(bot_id=bot_id).first()
#     return bot


# async def list_active_bot_by_ids(bot_ids):
#     bots = await BotConfig.filter(bot_id__in=bot_ids).all()
#     bots = [x for x in bots if x.status == 1 and x.init == 1]
#     return bots


# async def list_bot(type: BotType):
#     bots = await BotConfig.filter(type=type.value, status=1, init=1).all()
#     return bots


# async def get_main_bot(type: BotType):
#     bots = await BotConfig.filter(type=type.value, status=1, init=1).all()
#     if not bots:
#         raise Exception("no main bot found")
#     bots.sort(key=lambda x: x.created_at, reverse=True)
#     return bots[0]


# async def get_bot_sender(bot_id: int):
#     if bot_id in BOT_SENDER_MAP:
#         return BOT_SENDER_MAP[bot_id]
#     ret_bot = await BotConfig.filter(bot_id=bot_id).first()
#     if not ret_bot:
#         raise Exception("bot not found")
#     sender_bot = Bot(
#         token=ret_bot.token,
#         default=DefaultBotProperties(parse_mode=ParseMode.HTML),
#         session=SESSION,
#     )
#     BOT_SENDER_MAP[bot_id] = sender_bot
#     return sender_bot


# async def get_helper_sender_bot(need_random: bool = False):
#     if not need_random:
#         ret_bot = await get_main_bot(BotType.HELPER)
#         return await get_bot_sender(ret_bot.bot_id)
#     bots = await list_bot(BotType.HELPER)
#     if not bots:
#         raise Exception("no helper bot found")
#     ret_bot = bots[random.randint(0, len(bots) - 1)]
#     return await get_bot_sender(ret_bot.bot_id)


# async def del_global_message(helper:Optional[Bot], chat_id: int, message_id: int):
#     try:
#         helper = await get_helper_sender_bot()
#         await helper.delete_message(chat_id=chat_id, message_id=message_id)
#     except Exception as e:
#         logging.warning(f"del_global_message failed: {e}")


# bot api
# async def update_bot_desc(chat_id: int, desc: str):
#     helper = await get_helper_sender_bot()
#     await helper.set_chat_description(chat_id=chat_id, description=desc)


# async def get_bot_desc(chat_id: int):
#     helper = await get_helper_sender_bot()
#     chat = await helper.get_chat(chat_id)
#     return chat.description


# async def get_pin_message(chat_id: int):
#     helper = await get_helper_sender_bot()
#     chat = await helper.get_chat(chat_id)
#     if chat.pinned_message:
#         return chat.pinned_message.text
#     return None


# async def pin_global_message(chat_id: int, message: str):
#     helper = await get_helper_sender_bot()
#     ret = await helper.send_message(chat_id=chat_id, text=message)
#     await helper.pin_chat_message(chat_id=chat_id, message_id=ret.message_id)
#     return ret.message_id


# async def update_message(chat_id: int, message_id: int, message: str):
#     helper = await get_helper_sender_bot()
#     await helper.edit_message_text(chat_id=chat_id, message_id=message_id, text=message)


# async def update_photo(chat_id: int, message_id: int, photo: Any, content: str):
#     helper = await get_helper_sender_bot()
#     await helper.edit_message_media(
#         chat_id=chat_id,
#         message_id=message_id,
#         media=types.InputMediaPhoto(
#             media=photo, caption=content, parse_mode=ParseMode.HTML
#         ),
#     )
