import asyncio
import re
import logging


from aiogram import Bo<PERSON>, types

# from aiogram import LoggingMiddleware
from aiogram.types import (
    InlineKeyboardButton,
    InlineKeyboardMarkup,
)

from aiogram.utils.keyboard import Inline<PERSON>eyboardBuilder


from services import product_service
from services.account_service import AccountService

from common import loggers

from services.img_service import ImageBotService


from aibot_handlers.image_server import (
    image_style_dict,
)

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s (%(filename)s:%(lineno)d) [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler(), loggers.local_bot_help_handler],
)

log = logging.getLogger(__name__)


class ImageBotBiz:

    @classmethod
    async def del_msg_delay(
        cls, bot: Bot, chat_id: int, message_id: int, delay: int = 60
    ):
        log.info(f"del_msg_delay: {chat_id},{message_id},{delay}")
        await asyncio.sleep(delay)
        try:
            await bot.delete_message(chat_id=chat_id, message_id=message_id)
        except Exception as e:
            log.error(f"del_msg_delay error: {e}", exc_info=True)

    @staticmethod
    def create_copy_prompt_button(
        bot_username: str, copy_prompt_str: str
    ) -> InlineKeyboardMarkup:
        log.info(f"create_copy_prompt_button: {bot_username},{copy_prompt_str}")
        # 创建键盘并添加按钮
        copy_prompt_keyboard = InlineKeyboardBuilder()
        # 创建一个跳转按钮
        button = InlineKeyboardButton(
            text="偷走咒语☔️",
            url=f"https://t.me/{bot_username}?start={copy_prompt_str}",  # 指定目标 Bot 的链接
        )
        copy_prompt_keyboard.add(button)
        return copy_prompt_keyboard.as_markup()

    @classmethod
    async def send_photo_to_share_group(
        cls, bot: Bot, photo_url: str, style: str, replay_markup: InlineKeyboardMarkup
    ):
        log.info(f"send_photo_to_share_group: {photo_url},{style}")
        try:
            group_id, topic_id = await ImageBotService.get_share_group_topic(
                bot.id, style
            )

            if group_id == 0 or topic_id == 0:
                log.warning(f"Group ID or Topic ID not found for style: {style}")
                return
            sent_msg = await bot.send_photo(
                chat_id=group_id,
                message_thread_id=topic_id,
                photo=photo_url,
                protect_content=True,
                reply_markup=replay_markup,
            )

            log.info(
                f"send_photo_to_share_group: {sent_msg.message_id},{group_id},{topic_id}"
            )
        except Exception as e:
            log.error(f"send_image error: {e},{group_id},{topic_id}", exc_info=True)

    @staticmethod
    def escape_markdown_v2(text: str) -> str:
        # 转义 MarkdownV2 特殊字符
        return re.sub(r"([_*\[\]()~`>#\+\-=|{}.!])", r"\\\1", text)

    @classmethod
    async def handle_copy_prompt(
        cls,
        copy_prompt_id: int,
        message: types.Message,
        bot: Bot,
        user_id,
        image_bot_name: str,
    ):
        """
        处理复制提示词的逻辑
        :param copy_prompt_id: 提示词 ID
        :param message: 消息对象
        :param bot: Bot 实例
        :param user_id: 用户 ID
        """
        log.info(f"handle_copy_prompt:{copy_prompt_id},{message.text}")
        # 处理复制提示词的逻辑
        # 从数据库中获取提示词
        img_task = await ImageBotService.get_img_gen_task_by_id(int(copy_prompt_id))
        if img_task:
            prompt = img_task.prompt
            log.info(f"copy_prompt_id:{copy_prompt_id},prompt:{prompt}")
            # 发送提示词
            prompt_text = f"{prompt}"
            photo_url = img_task.gen_result.get("image_url")  # type: ignore

            balance = await AccountService.get_total_balance(user_id)

            if balance < 50:
                log.warning(f"余额不足,当前余额:{balance},{message.from_user.id}")
                await bot.send_message(
                    chat_id=message.chat.id,
                    text=f'⚠️⚠️余额不足请充值之后再玩～\n\n点击 <a href="https://t.me/{image_bot_name}?start=recharge">这里进行充值</a>',
                    parse_mode="HTML",
                )

                return

            # 对 prompt_text 加粗和斜体
            tip_txt = f"\n\n(🌟✨偷学咒语成功)"
            escaped_text = f"`{prompt_text}`" + ImageBotBiz.escape_markdown_v2(
                tip_txt
            )  # 转义MarkdownV2特殊字符

            sent_msg = await bot.send_photo(
                chat_id=message.chat.id,
                photo=photo_url,  # type: ignore
                caption=escaped_text,
                allow_sending_without_reply=True,
                parse_mode="MarkdownV2",  # 使用MarkdownV2格式
            )
            # sent_msg = await message.reply_photo(photo=photo_url, caption=prompt_text, allow_sending_without_reply=True)  # type: ignore

            if sent_msg:
                log.info(
                    f"copy_prompt_id:{copy_prompt_id},sent_msg:{sent_msg.message_id}"
                )

                try:
                    # 复制提示词，扣除50钻石
                    product = await product_service.get_photo_bot_copy_promt_product()
                    await AccountService.create_pay_order(
                        user_id=user_id,
                        product=product,  # type: ignore
                        role_id=-copy_prompt_id,  # 使用负数来区分角色
                    )  # type: ignore
                except Exception as e:
                    log.error(f"create_pay_order error: {e}", exc_info=True)
                # # 删除消息
                # asyncio.create_task(
                #     del_msg_delay(bot=bot, chat_id=message.chat.id, message_id=sent_msg.message_id, delay=60)  # type: ignore
                # )
        else:
            log.info(f"copy_prompt_id:{copy_prompt_id},not found")
            start_msg = await ImageBotService.get_img_bot_start_msg(bot_id=bot.id)
            sent_msg = await message.reply(
                start_msg, parse_mode="html", allow_sending_without_reply=True
            )

            asyncio.create_task(
                del_msg_delay(bot=bot, chat_id=message.chat.id, message_id=sent_msg.message_id, delay=60)  # type: ignore
            )

    @staticmethod
    def create_one_try_button(
        bot_username: str, try_prompt_str: str, style_txt: str
    ) -> InlineKeyboardMarkup:
        """创建一个试用按钮的键盘
        :param bot_username: 目标 Bot 的用户名
        :param try_prompt_str: 试用提示字符串
        :return: InlineKeyboardMarkup 对象
        """

        log.info(f"create_one_try_button: {bot_username},{try_prompt_str}")
        # 创建键盘并添加按钮
        try_prompt_keyboard = InlineKeyboardBuilder()

        try_txt = ImageBotBiz.escape_markdown_v2(f"{style_txt}我也试试")
        # 创建一个跳转按钮
        button = InlineKeyboardButton(
            text=try_txt,
            url=f"https://t.me/{bot_username}?start={try_prompt_str}",  # 指定目标 Bot 的链接
        )
        try_prompt_keyboard.add(button)
        return try_prompt_keyboard.as_markup()

    @staticmethod
    def create_share_group_photo_button_markup(
        bot_username: str, task_id: int, style_txt: str
    ) -> InlineKeyboardMarkup:
        """创建一个分享按钮的键盘
        :param bot_username: 目标 Bot 的用户名
        :param task_id: 任务 ID
        :return: InlineKeyboardMarkup 对象
        """
        log.info(
            f"create_share_group_photo_button: {bot_username},{task_id},style_txt:{style_txt}"
        )
        # 创建键盘并添加按钮
        share_keyboard = InlineKeyboardBuilder()

        one_try_txt = ImageBotBiz.escape_markdown_v2(f"{style_txt}我也试一试")

        # 创建一个试一试跳转按钮
        one_try_button = InlineKeyboardButton(
            text=one_try_txt,
            url=f"https://t.me/{bot_username}?start=one_try_{task_id}",  # 指定目标 Bot 的链接
        )
        # 创建一个偷走咒语按钮
        copy_prompt_str = f"copy_prompt_{task_id}"
        copy_prompt_button = InlineKeyboardButton(
            text="偷走咒语☔️",
            url=f"https://t.me/{bot_username}?start={copy_prompt_str}",  # 指定目标 Bot 的链接
        )

        share_keyboard.add(copy_prompt_button).add(one_try_button)
        share_keyboard.adjust(1, 1)  # 调整按钮布局
        return share_keyboard.as_markup()

    @classmethod
    async def handle_one_try(
        cls, task_id: int, message: types.Message, bot: Bot, user_id
    ):
        """
        处理试用提示词的逻辑
        :param one_try_task_id: 提示词 ID
        :param message: 消息对象
        :param bot: Bot 实例
        :param user_id: 用户 ID
        """
        log.info(f"handle_one_try:{task_id}")
        img_task = await ImageBotService.get_img_gen_task_by_id(task_id)

        if img_task:
            req_json = img_task.req_json

            style = req_json.get("style", "image_style_1")  # type: ignore

            await ImageBotService.update_b_profile_style(tg_id=user_id, style=style)
            chat_id = message.chat.id
            try:
                sent_msg = await bot.send_message(
                    chat_id=chat_id,
                    text=f"自动切换至风格: {image_style_dict.get(style, image_style_dict['image_style_1'])}",
                )

                asyncio.create_task(
                    ImageBotBiz.del_msg_delay(
                        bot=bot,
                        chat_id=chat_id,
                        message_id=sent_msg.message_id,
                        delay=60,
                    )
                )
            except Exception as e:
                log.error(f"send message error: {e}, chat_id: {chat_id}", exc_info=True)

        # ...existing code...

    @classmethod
    async def send_checkin_group_tip(
        cls, message: types.Message, bot: Bot, invite_link: str
    ):
        tip_text = (
            "「簽到」功能已經轉移至<b>【幻夢AI春宮圖同享】</b>\n"
            f"群組 <a href='{invite_link}'>{invite_link}</a> 簽到，簽到刷新每日的2次免費生圖权益\n\n"
            "生圖群中直接發送「簽到」、「签到」、「打卡」即可獲得生圖次數，點選以下按鈕到群組簽到。"
        )
        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="去【幻夢AI春宮圖同享】群签到", url=f"{invite_link}"
                    )
                ]
            ]
        )
        try:
            sent_msg = await message.reply(
                tip_text, reply_markup=keyboard, parse_mode="HTML"
            )

            # 删除消息 1min 之后
            asyncio.create_task(
                ImageBotBiz.del_msg_delay(
                    bot=bot,
                    chat_id=message.chat.id,
                    message_id=sent_msg.message_id,
                    delay=60,
                )
            )
        except Exception as e:
            log.error(f"send_checkin_group_tip error: {e}", exc_info=True)

    @staticmethod
    def create_retry_button(
        task_id: int, prompt: str, lang: str = "zh"
    ) -> InlineKeyboardMarkup:
        log.info(f"create_retry_button: {task_id},{prompt},{lang}")
        # 创建键盘并添加按钮
        retry_keyboard = InlineKeyboardBuilder()
        if lang == "en":
            txt = "🪄Retry Drawing"
            same_style_txt = "🎨One-Click Same Style"
        else:
            txt = "🎰继续抽卡"
            same_style_txt = "🎨一键同款"

        # 创建一个一键同款按钮
        same_button = InlineKeyboardButton(
            text=same_style_txt,
            switch_inline_query_current_chat=f"{prompt}",
        )

        # 创建一个跳转按钮
        button = InlineKeyboardButton(
            text=txt,
            callback_data=f"retry_task_{task_id}",
        )
        retry_keyboard.add(same_button).add(button)
        retry_keyboard.adjust(1, 1)

        return retry_keyboard.as_markup()