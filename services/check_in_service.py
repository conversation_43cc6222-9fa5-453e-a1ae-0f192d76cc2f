import asyncio
from datetime import datetime, timedelta
import logging
import random
from aiogram import Bot, types
from common.bot_common import CHECK_IN_CHANNEL_MSG_TEMPLATE, CHECK_IN_GROUP_MSG_TEMPLATE, CHECK_IN_NEITHER_MSG_TEMPLATE, Button, CheckInCallback, MessageTemplate
from common.common_constant import BotReplace, ChannelCategory, GroupCategory, Language
from common.entity import UserBioType
from persistence.models.models import RechargeChannelEnum, RechargeOrder, RechargeStatusEnum, User
from services import bot_message_service, tg_config_service, tg_message_service, user_growth_service
from services.user_service import user_service
from services.bot_services import bot as tma_bot, check_in_chat, get_bot_by_register_source, get_bot_name, helper_bots
from utils import date_util, env_const, user_growth_constants
from persistence.redis_client import redis_client
from aiogram.enums import ParseMode

r1_tip = '''<b>🎉🎉🎉 @{username} 签到成功，获得了{amount}🟡！🎉🎉🎉</b>
{additional_tip}
100颗闪亮的金币已经飞入您的账户！虽然今天的礼物比较低调，但坚持就是力量，继续签到，幸运女神迟早会为您加冕皇冠！👑

<b>群友们，来试试手气吧！下一个幸运儿也许就是您！</b>'''

r2_tip = '''<b>🎉🎉🎉 太棒了！@{username} 今天获得了{amount}🟡！🎉🎉🎉</b>
{additional_tip}
200颗金币已在您的账户中闪闪发光！<b>这只是开始，后宫的荣耀之路就在脚下，继续签到，累积更多惊喜吧！</b>

<b>还在观望的群友们，别犹豫，来试试手气，金币雨可能随时降临！</b>'''

r3_tip = '''<b>🎉🎉🎉 恭喜 @{username}，今日签到获赠{amount}🟡！🎉🎉🎉</b>
{additional_tip}
300颗金币的光芒在为您加冕！<b>每一次签到都是对您魅力的认可，您的坚持已然成为后宫之主的象征！</b>

<b>群友们，不要错过这金币雨的狂欢，快来参与签到吧！</b>'''

r4_tip = '''<b>🎉🎉🎉 哇！@{username} 竟然拿下了{amount}🟡！🎉🎉🎉</b>
{additional_tip}
400颗璀璨的金币正在为您欢呼！<b>这是后宫佳丽们的贺礼，您的每一步坚持都将带来更大的荣耀！</b>

<b>群友们，别只做旁观者，来一起体验签到的乐趣吧！金币和宠爱正在等着您！</b>'''
r5_tip = '''<b>🎉🎉🎉 哇塞！@{username} 本次签到收获了{amount}🟡！🎉🎉🎉</b>
{additional_tip}
500颗金币如同浩瀚的星海洒向您！<b>这是帝王般的馈赠，更是对您坚持不懈的褒奖！继续签到，让您的财富和荣耀日日攀升！</b>

<b>群友们，还等什么呢？赶紧加入签到行列，您也可以成为下一个幸运之王！</b>'''
r6_tip = '''<b>🎉🎉🎉 太惊艳了！@{username} 今天获得了{amount}🟡！🎉🎉🎉</b>
{additional_tip}
600颗金币让您的账户瞬间璀璨！<b>后宫的佳丽们正在为您的慷慨喝彩，您的签名将铭刻在这一刻的辉煌之上！</b>

<b>群友们，金币雨正在袭来，赶紧签到，迎接属于您的荣耀时刻吧！</b>'''

r7_tip = '''<b>🎉🎉🎉 超级幸运！@{username} 斩获了{amount}🟡！🎉🎉🎉</b>
{additional_tip}
700颗金币铺满您的宫殿，这是荣耀的象征，也是坚持的嘉奖！<b>每一刻的签到都在为您的未来加冕，继续下去，荣耀无穷！</b>

<b>群友们，加入这场金币雨盛宴，您的幸运时刻就在下一次签到！</b>'''

r8_tip = '''<b>🎉🎉🎉 不可思议！@{username} 收获了{amount}🟡！🎉🎉🎉</b>
{additional_tip}
800颗金币为您织就一条闪亮的荣誉之路！<b>这不仅是金币，更是对您的坚持与决心的绝对认可！让金币雨继续为您庆祝吧！</b>

<b>群友们，感受一下这份震撼吧，赶快签到，更多金币等您来拿！</b>'''

r9_tip = '''<b>🎉🎉🎉 无比震撼！@{username} 今天获得了{amount}🟡！🎉🎉🎉</b>
{additional_tip}
900颗金币的丰厚馈赠，让整个后宫都为您鼓掌！<b>这是对您非凡气质和持之以恒的嘉奖，群里所有人的目光都聚焦在您的荣耀之上！</b>

<b>群友们，这样的幸运就在眼前，别错过！立即签到，金币暴击等着您！</b>'''

r10_tip = '''<b>🏆🏆🏆 王者降临！@{username} 今日签到获得了最高奖励——{amount}🟡！🏆🏆🏆</b>
{additional_tip}
这一刻，您的签名烙印在金币雨的巅峰！<b>这1000颗金币不仅是财富的象征，更是您不断追求卓越的丰碑！后宫的荣耀无出其右！</b>

<b>群友们，这就是签到的魅力！快来尝试，让金币雨也为您降临！</b>'''

check_in_keys = ['签到', '簽到', '打卡']
r_competitor_tip = '''<b>🎉🎉🎉 恭喜 @{username} 签到获得 50{amount}🟡！🎉🎉🎉 </b>'''
with_link_tip = '''<b>👍👍您的个人简介中包含您的幻梦专属邀请链接，签到多获得200🟡</b>'''
with_competitor_tip = '''你存在违反群规行为，个人简介含有链接，签到奖励只能获得50🟡，在 @FancyTavernBot 菜单指令中获取您的 <b>幻梦专属邀请链接</b>，放到个人简介里 <b>签到额外获得 200🟡</b>'''
no_link_tip = '''<b>在您的 TG 个人简介添加幻梦邀请链接将获得额外的 200🟡，可以通过 /invite_link 命令来获取邀请链接，在 TG 隐私设置把个人简介对所有人可见</b>'''


role_channel_tip = f'''👀👀关注 <b>角色卡世界 @{BotReplace.MAIN_GROUP_ROLE.value} </b>，签到可以再多获得 100🟡 哦😃'''
is_in_role_channel_tip = f''' 👍👍您关注了角色世界 @{BotReplace.MAIN_GROUP_ROLE.value} ，签到多获得 100🟡'''


check_in_forward_message_template = MessageTemplate(
    tips=f'''<b>"簽到"功能已經轉移至”機器人”</b>
@{BotReplace.MAIN_TMA_BOT.value} 和 @{BotReplace.MAIN_CHAT_BOT.value} 均可打卡簽到獲得🟡。

<b>機器人中</b>直接發送<b>“簽到”、“签到”、“打卡”</b>或“/checkin”，或點擊<b>以下按鈕</b>。    
''',
    buttons=[
        Button(text="去「幻夢 AI 伴侶」簽到", url=f"https://t.me/{BotReplace.MAIN_TMA_BOT.value}?start=ct"),
        Button(text="去「幻夢陪聊」簽到", url=f"https://t.me/{BotReplace.MAIN_CHAT_BOT.value}?start=ct"),
    ]
)

amount_tip_mapping = {
    '100': r1_tip,
    '200': r2_tip,
    '300': r3_tip,
    '400': r4_tip,
    '500': r5_tip,
    '600': r6_tip,
    '700': r7_tip,
    '800': r8_tip,
    '900': r9_tip,
    '1000': r10_tip
}
outcomes = list(amount_tip_mapping.keys())
probabilities = [0.58, 0.06, 0.08, 0.03, 0.04, 0.01, 0.08, 0.02, 0.04, 0.06]

def check_hour_reward(c: str) -> str:
    cv = int(c)
    # 大于 500 的每小时限 2 次
    if cv <= 500:
        return c
    ds = datetime.now().strftime('%Y-%m-%d-%H')
    rk = f'check_in_hour_reward:{ds}:{c}'
    cv = redis_client.incr(rk)
    if cv and int(str(cv)) <= 2:
        return c
    rd = random.randint(1, 5)
    return str(rd * 100)

def choice_reply_tip() -> tuple[int, str]:
    result = random.choices(outcomes, probabilities)
    c = result[0]
    c = check_hour_reward(c)
    return int(c), amount_tip_mapping[c]

def has_our_link(bio: str) -> bool:
    our_handles = redis_client.smembers('our_handles')
    for h in our_handles:
        h = h.decode('utf-8')
        url = f'https://t.me/{h}'
        handle = f'@{h}'
        if bio.find(url) >= 0 or bio.find(handle) >= 0:
            return True
    return False

def has_competitor_link(bio: str) -> bool:
    has_links = [bio.find(c) >= 0 for c in env_const.COMPETITOR_LINKS]
    return any(has_links)

async def try_get_user_bio(user: User, tg_id: int) -> str | None:
    try:
        bot = get_bot_by_register_source(user.register_source)
        full_user_info = await bot.get_chat(tg_id)
    except:
        if user.register_source.is_tma():
            try:
                full_user_info = await tma_bot.get_chat(tg_id)
            except:
                return None
        else:
            return None
    return full_user_info.bio

async def check_user_bio(user: User, tg_user: types.User) -> UserBioType:
    tg_id = tg_user.id
    bio = await try_get_user_bio(user, tg_id)
    if not bio:
        return UserBioType.NO_LINK
    if has_competitor_link(bio):
        redis_client.sadd('competitor_bio', f'{tg_id} {tg_user.username}')
        logging.info(f'competitor bio: {tg_id}, first_name: {tg_user.first_name}, last_name: [{tg_user.last_name}], username: [{tg_user.username}], bio: [{bio}]')
        return UserBioType.WITH_COMPETITOR_LINK
    if has_our_link(bio):
        return UserBioType.WITH_OUR_LINK
    return UserBioType.NO_LINK

def get_bot_idx(tg_id: int) -> int:
    return tg_id % len(helper_bots)

async def send_group_reply(message: types.Message,
                           reply: str, bot_idx: int, reply_markup: types.InlineKeyboardMarkup | None = None) -> types.Message:
    bot = helper_bots[bot_idx]
    return await bot.send_message(message.chat.id, text=reply, reply_to_message_id=message.message_id, reply_markup=reply_markup)

async def send_forward_tip_message(message: types.Message, content: str, user_id: int):
    now = datetime.now()
    date_str = now.strftime('%Y-%m-%d')
    key = f'check_in_forward_tip_{user_id}_{date_str}'
    if redis_client.exists(key):
        return
    redis_client.set(key, 1, ex=timedelta(days=1))
    bot_idx = get_bot_idx(message.from_user.id)
    send_message = await send_group_reply(message, content, bot_idx)
    bot_idx = message.from_user.id % len(helper_bots)
    bot = helper_bots[bot_idx]
    await tg_message_service.add_deleted_message(send_message, f"HELPER_BOT_{bot_idx}", timedelta(minutes=2),bot_id=bot.id)
    await helper_bots[bot_idx].delete_message(chat_id=message.chat.id, message_id=message.message_id)

async def send_duplicate_tip_message(message: types.Message, content: str, user_id: int) -> types.Message | None:
    now = datetime.now()
    date_str = now.strftime('%Y-%m-%d')
    key = f'check_in_duplicate_tip_{user_id}_{date_str}'
    if redis_client.exists(key):
        return
    redis_client.set(key, 1, ex=timedelta(days=1))
    bot_idx = get_bot_idx(message.from_user.id)
    return await send_group_reply(message, content, bot_idx)


# async def delete_message(message: types.Message, bot: Bot):
#     try:
#         await message.delete()
#         await bot.delete_message(chat_id=message.chat.id, message_id=message.message_id)
#         logging.info(f'delete message success: {message.message_id}')
#     except Exception as e:
#         logging.warning(f"delete message failed {message.message_id}, {e}"
#                         )
# async def delete_old_messages():
#     size = redis_client.llen('check_in_messages')
#     if size <= 1:
#         return
#     messages = redis_client.lpop('check_in_messages', size - 1)
#     for m in messages:
#         m = m.decode('utf-8')
#         ms = m.split(':')
#         logging.info(f'delete check in message: {m}')
#         if len(ms) < 4:
#             ms.append(0)
#         else:
#             ms[-1] = int(ms[-1])
#         if ms[-1] >= 5:
#             logging.warning(f'delete check in message failed for too many times: {m}')
#             continue
#         bot_id = ms[2]
#         bot = helper_bots[int(bot_id)]
#         mids = [int(ms[0])]
#         if ms[1]:
#             mids.append(int(ms[1]))
#         try:
#             await bot.delete_message(chat_id=env_const.CHECK_IN_GROUP_ID,
#                                       message_id=mids[0])
#             logging.info(f'delete check in message success: {mids[0]}')
#         except Exception as e:
#             logging.warning(f"delete check in message failed {mids[0]}, {e}")
#             ms[-1] += 1
#             ms[-1] = str(ms[-1])
#             nw = "".join(ms)
#             redis_client.lpush('check_in_messages', nw)
#         if len(mids) == 1:
#             continue
#         try:
#             await bot.delete_message(chat_id=env_const.CHECK_IN_GROUP_ID,
#                                       message_id=mids[1])
#             logging.info(f'delete check in message success: {mids[1]}')
#         except Exception as e:
#             logging.warning(f"delete check in message failed {mids[1]}, {e}")
#             ms[-1] += 1
#             ms[-1] = str(ms[-1])
#             nw = "".join(ms)
#             redis_client.lpush('check_in_messages', nw)
#             continue
async def new_delete_check_in_message(message: types.Message,replay_message: types.Message | None = None):
    await asyncio.sleep(5)
    await message.delete()
    if replay_message:
        await replay_message.delete()

async def process_in_group_check_in(message: types.Message):
    reply_message = None
    if not message.text or not message.text.strip() in check_in_keys:
        return 
    bot_idx = get_bot_idx(message.from_user.id)
    send_message = await bot_message_service.format_template_replace(check_in_forward_message_template)
    reply_message = await send_group_reply(message, send_message.tips, bot_idx, send_message.as_markup())
    await new_delete_check_in_message(message, reply_message)

async def check_user_in_chat(user_tg_id: int, chat_id: int | str) -> bool:
    bot_idx = get_bot_idx(user_tg_id)
    bot = helper_bots[bot_idx]
    return await check_in_chat(user_tg_id, chat_id, bot)

async def check_user_in_chats(user_tg_id: int,
                              chat_id: int | str,
                              backup_chat_id: int | str) -> bool:
    bot_idx = get_bot_idx(user_tg_id)
    bot = helper_bots[bot_idx]
    in_main = await check_in_chat(user_tg_id, chat_id, bot)
    if in_main:
        return True
    return await check_in_chat(user_tg_id, backup_chat_id, bot)

async def is_in_group_v2(user_tg_id: int) -> bool:
    # category:chat
    group_config_list = await tg_config_service.list_group()
    for group_config in group_config_list:
        target_id = group_config.chat_id     
        is_joined = await check_in_chat(user_tg_id, target_id)
        if is_joined:
            return True
    return False

async def is_in_channel_v2(user_tg_id: int) -> bool:
    # category:welfare
    channel_config_list = await tg_config_service.list_channel(ChannelCategory.WELFARE)
    for channel_config in channel_config_list:
        target_id = channel_config.chat_id
        is_joined = await check_in_chat(user_tg_id, target_id)
        if is_joined:
            return True
    return False


async def is_in_channel(user_tg_id: int) -> bool:
    return await check_user_in_chats(user_tg_id,
                                     env_const.WELFARE_CHANNEL_ID,
                                     env_const.WELFARE_BACKUP_CHANNEL_ID)

async def is_in_role_channel(user_tg_id: int) -> bool:
    return await check_user_in_chat(user_tg_id, user_growth_constants.ROLE_CHANNEL_ID)

#是否在主要的群里
async def is_in_main_group(user_tg_id: int,group_category:GroupCategory) -> bool:
    main_group = await tg_config_service.get_main_group(group_category)
    bot = await tg_config_service.get_group_sender(main_group.chat_id)

    return await check_in_chat(user_tg_id, main_group.chat_id, bot)

async def process_direct_message_check_in(message: types.Message, bot: Bot,language:str = Language.ZH.value) -> bool:
    if language == Language.EN.value:
        return False
    if not message or not message.text:
        return False
    from_user = message.from_user
    text = message.text.strip()
    if text not in check_in_keys:
        return False

    await process_in_bot_check_in(message, bot, from_user)
    return True

async def process_web_check_in(user:User):
    amount =  user_growth_constants.GROUP_CHECK_IN_AWARD_AMOUNT
    ret =  await user_growth_service.create_check_in_task_wit_bio(user.id, amount)
    return ret,0

async def process_in_bot_check_in(message: types.Message, bot: Bot, from_user: types.User | None = None):
    if from_user is None:
        from_user = message.from_user
    user: User = await user_service.get_user_by_tg_id(from_user.id) # type: ignore
    bot_username = get_bot_name(bot)
    in_group = await is_in_group_v2(from_user.id)
    in_channel = await is_in_channel_v2(from_user.id)
    bio_type = await check_user_bio(user, from_user)

    message_template = None
    if not in_group and not in_channel:
        message_template = CHECK_IN_NEITHER_MSG_TEMPLATE
    elif not in_channel:
        message_template = CHECK_IN_CHANNEL_MSG_TEMPLATE
    elif not in_group:
        message_template = CHECK_IN_GROUP_MSG_TEMPLATE
    if message_template:
        message_template = await bot_message_service.format_template_replace(message_template)
        message = await bot.send_message(from_user.id, message_template.tips, reply_markup=message_template.as_markup())
        await tg_message_service.add_check_in_deleted_message(message, message, user.id, timedelta(minutes=2), bot_username,bot.id)
        return 
    
    format_role_channel_tips = await bot_message_service.format_content_replace(role_channel_tip)
    format_in_role_channel_tips = await bot_message_service.format_content_replace(is_in_role_channel_tip)

    logging.info(f'check in user: {user.id}')
    user_name = from_user.first_name
    if from_user.last_name:
        user_name += " "+ from_user.last_name

    last_check_in_amounts = await RechargeOrder.filter(
        user_id=user.id,
        status=RechargeStatusEnum.SUCCEED, 
        recharge_channel=RechargeChannelEnum.CHECK_IN).order_by('-id').limit(3).all()
    l100s = [c.amount for c in last_check_in_amounts if c.amount == 100]
    if len(l100s) == 3:
        logging.info(f'last 3 check in 100 hint: {user.id}')
        amount, tip = 300, r3_tip
    else:
        amount, tip = choice_reply_tip()
    in_role_group = await is_in_main_group(from_user.id,GroupCategory.ROLE)
    role_group = await tg_config_service.get_main_group(GroupCategory.ROLE)
    r = await user_growth_service.create_check_in_task_wit_bio(user.id, amount, bio_type, in_role_group)
    reply_message = None
    if r:
        r_amount = amount if not in_role_group else amount + 100
        if bio_type == UserBioType.WITH_OUR_LINK:
            amount_tip = f'({amount} + 200 + 100)' if in_role_group else f'({amount} + 200)'
            reply_content = f'''<b>🎉🎉🎉 @{user_name} 签到成功，获得了 {r_amount+200}{amount_tip}🟡！🎉🎉🎉</b>

{with_link_tip}'''
        elif bio_type == UserBioType.NO_LINK:
            amount_tip = f'({amount} + 100)' if in_role_group else f''
            reply_content = f'''<b>🎉🎉🎉@{user_name} 签到成功，获得了{r_amount}{amount_tip}🟡！🎉🎉🎉</b>

{no_link_tip}'''
        else:
            r_amount = 50 if not in_role_group else 150
            amount_tip = f'({amount} - {amount - 50}) + 100' if in_role_group else f'({amount} - {amount - 50})'
            reply_content = f'''<b>🎉🎉🎉@{user_name} 获得了{r_amount}{amount_tip}🟡！🎉🎉🎉</b>

{with_competitor_tip}'''
        try:
            if in_role_group:
                reply_content += f'\n\n{format_in_role_channel_tips}'
            else:
                reply_content += f'\n\n{format_role_channel_tips}'
            mt = MessageTemplate(tips=reply_content)
            if not in_role_group:
                mt.buttons=[
                    Button(text="加入角色群", url=f"https://t.me/{role_group.username}"),
                    Button(text="继续签到", callback_data=CheckInCallback()),
                ]
            reply_message = await bot.send_message(from_user.id, mt.tips, reply_markup=mt.as_markup())
        except Exception as e:
            logging.warning(f'check in error: {e}')
    else:
        #已签到
        ds = datetime.now().strftime("%Y-%m-%d")
        out_order_id = f"check_in_role_channel:{user.id}_{ds}"
        join_group_orders = await RechargeOrder.filter(
            user_id=user.id,out_order_id=out_order_id).all()
        logging.info(f'check in user: {user.id},out_order_id:{out_order_id},join group orders: {len(join_group_orders)}')
        try:
            if not join_group_orders and in_role_group:
                await user_growth_service.create_check_in_join_role(user.id)
                content = f'''<b>🎉🎉🎉 @{user_name} 签到成功，获得了加入角色世界奖励 {user_growth_constants.CHECK_IN_IN_ROLE_CHANNEL_REWARD_AMOUNT}🟡！🎉🎉🎉</b>'''
            else:
                content = '''亲爱的，一天只能打卡领金币一次，欢迎明天再来'''
                if bio_type != UserBioType.WITH_OUR_LINK:
                    content += f'\n\n{no_link_tip}'
                if not in_role_group:
                    content += f'\n\n{format_role_channel_tips}'
            reply_message = await bot.send_message(from_user.id, content)
        except Exception as e:
            logging.warning(f'check in error: {e}')
    if reply_message:
        await tg_message_service.add_check_in_deleted_message(message, reply_message, user.id, timedelta(minutes=2), bot_username,bot.id)
    else:
        await tg_message_service.add_deleted_message(message, bot_username, timedelta(minutes=2),bot.id)
