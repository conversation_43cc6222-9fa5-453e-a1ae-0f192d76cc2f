import random
import uuid
from common.models.chat_request import ChatHistoryItem
from common.role_card import (
    Book<PERSON>ntry,
    CharacterBook,
    CharacterBookEdit,
    EntryExtensions,
)
from persistence import char_book_dao
from services import translate_service
from utils import char_book_util, token_util


async def verify_character_book(book: CharacterBookEdit | None):
    if book is None:
        return False, "Character book is required"
    if book.enabled and len(book.entries) == 0:
        return False, "Character book entries are required"
    return True, None


async def get_edit_book_by_id(book_id: str) -> CharacterBookEdit | None:
    character_book = await char_book_dao.get_by_book_id(book_id)
    if not character_book:
        return None
    return CharacterBookEdit(
        name=character_book.name if character_book.name else "",
        book_id=character_book.book_id if character_book.book_id else "",
        entries=character_book.entries if character_book.entries else [],
        enabled=character_book.enabled if character_book.enabled else False,
    )


async def update_character_book(book: CharacterBookEdit) -> CharacterBookEdit | None:
    if not book.book_id:
        book.book_id = str(uuid.uuid4())
        await insert_character_book(book)
        return book
    character_book = await char_book_dao.get_by_book_id(book.book_id)
    if character_book is None:
        return None
    character_book.name = book.name
    character_book.entries = book.entries
    character_book.enabled = book.enabled
    character_book.extensions = book.extensions
    await char_book_dao.update(character_book)
    return await get_edit_book_by_id(book.book_id)


async def insert_character_book(book: CharacterBookEdit) -> CharacterBookEdit:
    if not book.book_id:
        book.book_id = str(uuid.uuid4())
    character_book = CharacterBook(
        name=book.name,
        book_id=book.book_id,
        entries=book.entries,
        enabled=book.enabled,
    )
    await char_book_dao.insert(character_book)
    return book


def assemble_trigger_content(
    book_entry: BookEntry, history_message: list[ChatHistoryItem]
) -> str:
    scan_depth = book_entry.load_scan_depth()
    if scan_depth * 2 >= len(history_message):
        return "\n".join([msg.content for msg in history_message])
    return "\n".join([msg.content for msg in history_message[-scan_depth * 2 :]])


# def _filter_by_user_input(
#     book: CharacterBook | None, history_messages: list[ChatHistoryItem]
# ) -> CharacterBook | None:
#     if not book or not book.entries:
#         return book

#     def check_keys(entry: BookEntry):
#         # 常量，固定插入，非常量，关键词触发
#         if entry.constant:
#             return True
#         # 关键词触发
#         if not entry.keys or len(entry.keys) == 0 or entry.constant is None:
#             return False
#         ## 补充逻辑相关
#         user_input = assemble_trigger_content(entry, history_messages)
#         # 单词完整匹配
#         if len(entry.secondary_keys) == 0:
#             return any(key in user_input for key in entry.keys)

#         if not entry.extensions or entry.extensions.selectiveLogic is None:
#             return False
#         # AND ANY 仅当主键和任何一个可选过滤键处于扫描上下文中时才激活该条目。
#         if entry.extensions.selectiveLogic == 0:
#             key1_exist = any(key in user_input for key in entry.keys)
#             key2_exist = any(key in user_input for key in entry.secondary_keys)
#             return key1_exist and key2_exist
#         # NOT ALL 如果所有可选过滤器都在扫描上下文中，则尽管有主键触发器，仍会阻止激活条目
#         elif entry.extensions.selectiveLogic == 1:
#             key1_exist = any(key in user_input for key in entry.keys)
#             key2_exist = all(key in user_input for key in entry.secondary_keys)
#             return key1_exist and not key2_exist
#         # NOT ANY 仅当主键和可选过滤键均不在扫描上下文中时才激活条目。
#         elif entry.extensions.selectiveLogic == 2:
#             key1_exist = any(key in user_input for key in entry.keys)
#             key2_exist = any(key in user_input for key in entry.secondary_keys)
#             return not key1_exist and not key2_exist
#         # AND ALL 仅当主键和所有可选过滤键都存在时才激活条目。
#         elif entry.extensions.selectiveLogic == 3:
#             key1_exist = all(key in user_input for key in entry.keys)
#             key2_exist = all(key in user_input for key in entry.secondary_keys)
#             return key1_exist and key2_exist
#         return False

#     book.entries = [entry for entry in book.entries if check_keys(entry)]
#     return book


def _filter_by_probability(book: CharacterBook) -> CharacterBook:
    if not book.entries:
        return book

    def check_probability(extensions: EntryExtensions | None):
        if not extensions:
            return True
        if not extensions.probability:
            return True
        if extensions.probability == 100:
            return True
        return random.randint(0, 99) <= extensions.probability

    book.entries = [
        entry for entry in book.entries if check_probability(entry.extensions)
    ]
    return book


def _filter_by_enabled(book: CharacterBook) -> CharacterBook:
    if not book or not book.enabled or not book.entries:
        return book

    book.entries = [entry for entry in book.entries if entry.enabled and entry.content]
    return book


async def chat_effective_book(
    book_id: str, current_language: str, default_language: str
):
    if not book_id:
        return None
    character_book = await char_book_dao.get_by_book_id(book_id)
    if current_language != default_language:
        character_book = await translate_service.format_character_book(
            character_book, current_language
        )
    if not character_book:
        return None
    character_book = _filter_by_probability(character_book)
    character_book = _filter_by_enabled(character_book)
    return character_book
