from datetime import datetime
from common.common_constant import AuthorDefaultName, ChatModeType, Language
from common.role_model import (
    AdminChatGroupEdit,
    AuditChatGroupEdit,
    ChatGroupDetail,
    ChatGroupEdit,
    UserChatGroupEdit,
    UserRoleBrief,
)
from persistence.models.models import ChatGroupConfig, ChatGroupRelation, RoleConfig
from tortoise.transactions import atomic, in_transaction

from services import (
    role_statistic_service,
    tag_service,
    translate_service,
    user_service,
)
from services.role import role_audit_service, role_loader_service
from utils import date_util, json_util, role_util, token_util


async def disable(user_id: int, group_id: int):
    chat_group = await ChatGroupConfig.get(id=group_id)
    if not chat_group or chat_group.user_id != user_id:
        return False
    chat_group.status = False
    await chat_group.save()
    return True

async def load_chat_group_edit(group_id: int):
    chat_group = await ChatGroupConfig.get(id=group_id)
    relations = await ChatGroupRelation.filter(group_id=group_id, deleted=False).all()
    role_ids = [relation.role_id for relation in relations]
    roles = await role_loader_service.list_by_ids(role_ids)
    role_name_map = {role.id: role.role_name for role in roles}
    scenario = role_util.fill_group_scenario_role_name(
        chat_group.scenario, role_name_map
    )
    return ChatGroupEdit(
        id=chat_group.id,
        user_id=chat_group.user_id,
        name=chat_group.name,
        simple_intro=chat_group.simple_intro,
        introduction=chat_group.introduction,
        scenario=scenario,
        avatar=chat_group.avatar,
        role_ids=role_ids,
        sub_tags=json_util.convert_to_list(chat_group.sub_tags),
        author=chat_group.author,
    )


async def create_group(
    user_id: int, chat_group: ChatGroupEdit, nsfw: bool, public: bool = False
) -> ChatGroupEdit:
    roles = await role_loader_service.list_by_ids(chat_group.role_ids)
    role_name_map = {role.id: role.role_name for role in roles}
    chat_group.scenario = role_util.replace_group_scenario_role_name(
        chat_group.scenario, role_name_map
    )
    # 开启事务
    async with in_transaction():
        chat_group_config = ChatGroupConfig(
            user_id=user_id,
            name=chat_group.name,
            simple_intro=chat_group.simple_intro,
            introduction=chat_group.introduction,
            scenario=chat_group.scenario,
            avatar=chat_group.avatar,
            status=True,
            public=public,
            nsfw=nsfw,
            sub_tags=json_util.convert_to_list(chat_group.sub_tags),
            author=chat_group.author,
        )
        await chat_group_config.save()
        group_id = chat_group_config.id
        for role_id in chat_group.role_ids:
            relation = ChatGroupRelation(
                group_id=group_id, role_id=role_id, user_id=user_id
            )
            await relation.save()
        return await load_chat_group_edit(group_id)


async def user_update_group(user_id: int, group: UserChatGroupEdit) -> ChatGroupEdit:
    roles = await role_loader_service.list_by_ids(group.role_ids)
    role_name_map = {role.id: role.role_name for role in roles}
    group.scenario = role_util.replace_group_scenario_role_name(
        group.scenario, role_name_map
    )
    # 开启事务
    async with in_transaction():
        chat_group_config = await ChatGroupConfig.get(id=group.id)
        chat_group_config.name = group.name
        chat_group_config.author = group.author
        chat_group_config.simple_intro = group.simple_intro
        chat_group_config.introduction = group.introduction
        chat_group_config.scenario = group.scenario

        chat_group_config.sub_tags = json_util.convert_to_list(group.sub_tags)
        await chat_group_config.save()
        relation_list = await ChatGroupRelation.filter(group_id=group.id).all()
        relation_map = {relation.role_id: relation for relation in relation_list}
        # 删除
        for mid in relation_list:
            if not bool(mid.role_id in group.role_ids) and not mid.deleted:
                mid.deleted = True
                await mid.save()
        # 新增与更新
        for role_id in group.role_ids:
            if role_id in relation_map:
                relation = relation_map[role_id]
                relation.deleted = False
                await relation.save()
            if role_id not in relation_map:
                relation = ChatGroupRelation(
                    group_id=group.id, role_id=role_id, user_id=user_id
                )
                await relation.save()

        if group.publish_card:
            await role_audit_service.user_submit_audit(
                user_id, ChatModeType.GROUP.value, group.id
            )
        if not group.publish_card:
            await role_audit_service.reset_status_edit(
                user_id, ChatModeType.GROUP.value, group.id
            )
    return await load_chat_group_edit(group.id)


async def admin_update_group(group_id: int, chat_group: AdminChatGroupEdit):
    chat_group_config = await ChatGroupConfig.get(id=group_id)
    chat_group_config.name = chat_group.name
    chat_group_config.introduction = chat_group.introduction
    chat_group_config.simple_intro = chat_group.simple_intro
    chat_group_config.scenario = chat_group.scenario
    chat_group_config.sub_tags = chat_group.sub_tags
    chat_group_config.author = chat_group.author
    await chat_group_config.save()
    return await load_chat_group_edit(group_id)


async def update_group(chat_group: ChatGroupEdit) -> ChatGroupEdit:
    roles = await role_loader_service.list_by_ids(chat_group.role_ids)
    role_name_map = {role.id: role.role_name for role in roles}
    chat_group.scenario = role_util.replace_group_scenario_role_name(
        chat_group.scenario, role_name_map
    )
    # 开启事务
    async with in_transaction():
        chat_group_config = await ChatGroupConfig.get(id=chat_group.id)
        chat_group_config.name = chat_group.name
        chat_group_config.introduction = chat_group.introduction
        chat_group_config.simple_intro = chat_group.simple_intro
        chat_group_config.scenario = chat_group.scenario
        chat_group_config.status = True
        chat_group_config.sub_tags = json_util.convert_to_list(chat_group.sub_tags)
        chat_group_config.author = chat_group.author
        await chat_group_config.save()
        relations = await ChatGroupRelation.filter(group_id=chat_group.id).all()
        relation_map = {relation.role_id: relation for relation in relations}
        for mid in relations:
            mid.deleted = not bool(mid.role_id in chat_group.role_ids)
            await mid.save()
        new_role_ids = [x for x in chat_group.role_ids if x not in relation_map.keys()]
        for role_id in new_role_ids:
            relation = ChatGroupRelation(
                group_id=chat_group.id, role_id=role_id, uid=chat_group_config.user_id
            )
            await relation.save()
        if chat_group.public_card:
            await role_audit_service.user_submit_audit(
                chat_group_config.user_id, ChatModeType.GROUP.value, chat_group.id
            )
        if not chat_group.public_card:
            await role_audit_service.reset_status_edit(
                chat_group_config.user_id, ChatModeType.GROUP.value, chat_group.id
            )
    return await load_chat_group_edit(chat_group.id)


async def list_by_user_id(user_id: int) -> list[ChatGroupDetail]:
    relations = await ChatGroupRelation.filter(user_id=user_id, deleted=False).all()
    role_ids = [relation.role_id for relation in relations]
    role_ids = list(set(role_ids))
    role_config_list = await RoleConfig.filter(id__in=role_ids).all()
    role_config_map = {role.id: role for role in role_config_list}
    user_chat_groups = await ChatGroupConfig.filter(
        user_id=user_id, status=True, public=False
    ).all()
    group_role_map = {}
    for relation in relations:
        if relation.group_id not in group_role_map:
            group_role_map[relation.group_id] = []
        group_role_map[relation.group_id].append(relation.role_id)
    user_chat_groups.sort(key=lambda x: x.created_at, reverse=True)

    def load_roles(role_ids: list[int]):
        role_configs = [role_config_map[role_id] for role_id in role_ids]
        return [UserRoleBrief.from_config_and_audit(role) for role in role_configs]

    audit_map = await role_audit_service.map_by_user_id(
        user_id, ChatModeType.GROUP.value
    )
    public_group_ids = [
        x.open_group_id for x in audit_map.values() if x.open_group_id > 0
    ]
    map_hot = await role_statistic_service.user_group_statistic(public_group_ids)

    rets =  [
        ChatGroupDetail.from_model(
            group,
            load_roles(group_role_map.get(group.id, [])),
            audit_map.get(group.id),
            map_hot.get(group.id, 0),
        )
        for group in user_chat_groups
    ]
    for group in rets:
        role_name_map = {x.id: x.role_name for x in group.roles}
        group.scenario = role_util.fill_group_scenario_role_name(
            group.scenario, role_name_map
        )
    return rets


async def create_config(group_id: int):
    ret = {
        "sum_token_limit": 800,
    }
    if group_id == 0:
        return ret
    chat_group_edit = await load_chat_group_edit(group_id)
    if not chat_group_edit:
        return ret
    role_list = await role_loader_service.list_by_ids(chat_group_edit.role_ids)

    group_roles = [UserRoleBrief.from_config_and_audit(role) for role in role_list]
    # all_tags = await tag_service.list_sub_tags_with_enabled()
    # all_tags = [tag.tag_name for tag in all_tags]
    # tag_orders = await tag_service.list_tags_with_orders()
    # tag_orders = json_util.convert_to_list(tag_orders)
    sub_tag_category_list = await tag_service.list_category_group_sub_tags()
    return {
        # "all_tags": all_tags,
        # "tag_orders": tag_orders,
        "chat_group_edit": chat_group_edit,
        "group_roles": group_roles,
        "sum_token_limit": 800,
        "sub_tag_category_list": sub_tag_category_list,
    }


async def get_original(group_id: int):
    chat_group = await ChatGroupConfig.get(id=group_id)
    return chat_group


async def load_detail_by_id(group_id: int, audit: bool = True) -> ChatGroupDetail:
    chat_group = await ChatGroupConfig.get(id=group_id)
    relations = await ChatGroupRelation.filter(group_id=group_id).all()
    role_ids = [relation.role_id for relation in relations if not relation.deleted]
    deleted_role_ids = [relation.role_id for relation in relations if relation.deleted]
    role_list = await role_loader_service.list_by_ids(role_ids)
    deleted_roles = await role_loader_service.list_by_ids(deleted_role_ids)
    role_name_map = {role.id: role.role_name for role in role_list}
    chat_group.scenario = role_util.fill_group_scenario_role_name(
        chat_group.scenario, role_name_map
    )
    group_audit = None
    role_audit_map = {}
    if audit:
        group_audit = await role_audit_service.get_by_mode(
            ChatModeType.GROUP.value, group_id
        )
        role_audit_map = await role_audit_service.map_by_mode(
            ChatModeType.SINGLE.value, list(role_ids + deleted_role_ids)
        )
    group_roles = [
        UserRoleBrief.from_config_and_audit(role, role_audit_map.get(role.id, None))
        for role in role_list
    ]
    deleted_group_roles = [
        UserRoleBrief.from_config_and_audit(role, role_audit_map.get(role.id, None))
        for role in deleted_roles
    ]
    detail = ChatGroupDetail.from_model(chat_group, group_roles, group_audit)
    detail.deleted_roles = deleted_group_roles
    detail.author_name = await user_service.get_nickname(detail.user_id)
    return detail


async def get_translate_detail_by_id(group_id: int, language: str = Language.ZH.value):
    group = await load_detail_by_id(group_id)
    if not group:
        return None
    if group.def_language == language:
        return group
    translate_map = await translate_service.map_group_task_by_ids([group.id], language)
    if group.id not in translate_map:
        return group
    translate_sub_tag_maps = await tag_service.map_all_tag_names(language)
    translate = translate_map[group.id]
    group.name = translate.name
    group.simple_intro = translate.simple_intro
    group.introduction = translate.introduction
    group.scenario = translate.scenario
    group.sub_tags = [translate_sub_tag_maps.get(tag, tag) for tag in group.sub_tags]
    return group


async def list_translate_detail_by_ids(
    group_ids: list[int], language: str = Language.ZH.value
):
    if not group_ids:
        return []
    groups = await map_detail_by_ids(group_ids)
    translate_map = await translate_service.map_group_task_by_ids(group_ids, language)
    translate_sub_tag_maps = await tag_service.map_all_tag_names(language)
    ret_groups = []
    for group_id in group_ids:
        if group_id not in groups:
            continue
        group = groups[group_id]
        if group_id not in translate_map or group.def_language == language:
            ret_groups.append(group)
            continue
        translate = translate_map[group_id]
        group.name = translate.name
        group.simple_intro = translate.simple_intro
        group.introduction = translate.introduction
        group.scenario = translate.scenario
        group.sub_tags = [
            translate_sub_tag_maps.get(tag, tag) for tag in group.sub_tags
        ]
        ret_groups.append(group)
    return ret_groups


async def map_detail_by_ids(
    group_ids: list[int], audit: bool = True
) -> dict[int, ChatGroupDetail]:
    if not group_ids:
        return {}
    ret = {}
    for group_id in group_ids:
        ret[group_id] = await load_detail_by_id(group_id, audit)
    return ret


async def list_public(nsfw: bool = True) -> list[ChatGroupDetail]:
    filter = {"status": True, "public": True}
    if not nsfw:
        filter["nsfw"] = False
    db_ret = (
        await ChatGroupConfig.filter(**filter).only("id").order_by("-created_at").all()
    )
    group_maps = await map_detail_by_ids([x.id for x in db_ret])

    return [group_maps[x.id] for x in db_ret if x.id in group_maps]


async def list_ids_by_user_index(
    nsfw: bool = True, sub_tags: list[str] = []
) -> list[int]:
    filter = {}
    filter["status"] = True
    filter["public"] = True
    if not nsfw:
        filter["nsfw"] = False
    if sub_tags:
        filter["sub_tags__contains"] = sub_tags
    db_ret = (
        await ChatGroupConfig.filter(**filter).only("id").order_by("-created_at").all()
    )
    return [x.id for x in db_ret]


# 获取用户发布的所有公开群组
async def list_public_id_create_time_by_user_id(
    user_id: int, author_name_shown: bool
) -> list[dict]:
    group_result = (
        await ChatGroupConfig.filter(
            user_id=user_id, status=True, public=True, author=author_name_shown
        )
        .only("id", "created_at")
        .order_by("-created_at")
        .all()
    )
    # mode_type=single&mode_target_id=3295
    return [
        {
            "mode_target_id": group.id,
            "created_at": group.created_at.timestamp(),
            "mode_type": ChatModeType.GROUP.value,
        }
        for group in group_result
    ]


async def list_public_by_translate(
    nsfw: bool = True, language: str = Language.ZH.value
) -> list[ChatGroupDetail]:
    filter = {"status": True, "public": True}
    if not nsfw:
        filter["nsfw"] = False
    db_ret = (
        await ChatGroupConfig.filter(**filter).only("id").order_by("-created_at").all()
    )
    group_ids = [x.id for x in db_ret]
    group_maps = await list_translate_detail_by_ids(group_ids, language)
    return [group_maps[x.id] for x in db_ret if x.id in group_maps]


async def list_public_config() -> list[ChatGroupConfig]:
    filter = {"status": True, "public": True}
    return await ChatGroupConfig.filter(**filter).order_by("-created_at").all()


async def approved_group(
    group_id: int, open_group_id: int, audit_group_edit: AuditChatGroupEdit
):
    open_group_edit = (
        await load_chat_group_edit(open_group_id) if open_group_id > 0 else None
    )
    group_edit = await load_chat_group_edit(group_id)
    roles = await role_loader_service.list_by_ids(group_edit.role_ids)
    nsfw = any([role.nsfw for role in roles])
    if not open_group_edit:
        group_edit.name = audit_group_edit.name
        group_edit.simple_intro = audit_group_edit.simple_intro
        group_edit.introduction = audit_group_edit.introduction
        group_edit.scenario = audit_group_edit.scenario
        group_edit.sub_tags = audit_group_edit.sub_tags
        group_edit.author = audit_group_edit.author
        return await create_group(group_edit.user_id, group_edit, nsfw, True)

    open_group_edit.name = audit_group_edit.name
    open_group_edit.simple_intro = audit_group_edit.simple_intro
    open_group_edit.introduction = audit_group_edit.introduction
    open_group_edit.scenario = audit_group_edit.scenario
    open_group_edit.sub_tags = audit_group_edit.sub_tags
    open_group_edit.author = audit_group_edit.author
    return await update_group(open_group_edit)


async def latest_group_created_at():
    chat_group = (
        await ChatGroupConfig.filter(status=True, public=True)
        .order_by("-created_at")
        .first()
    )
    if chat_group:
        created_at = date_util.add_utc_zone(chat_group.created_at)
        return int(created_at.timestamp())
    return 0


async def map_created_at(ids: list[int]) -> dict[int, datetime]:
    chat_groups = (
        await ChatGroupConfig.filter(id__in=ids)
        .only("id", "created_at")
        .order_by("-created_at")
        .all()
    )
    return {group.id: group.created_at for group in chat_groups}


async def list_user_groups_by_admin():
    chat_groups = (
        await ChatGroupConfig.filter(status=True, public=False, user_id__gt=0)
        .only("id")
        .all()
    )
    ids = [group.id for group in chat_groups]
    group_maps = await map_detail_by_ids(ids)
    return [group_maps[x.id] for x in chat_groups if x.id in group_maps]
