import json
import logging
from common.common_constant import ROLE_MAX_TOKEN, AuditStatus, ChatModeType, Language
from common.role_model import (
    AdminChatGroupEdit,
    AuditChatGroupEdit,
    ChatGroupEdit,
    RoleEditDetail,
    UserChatGroupEdit,
)
from persistence.models.models import RoleAudit
from services import user_service
from services import role_config_service
from services.role import role_audit_service, role_group_service, role_loader_service
from services.role_config_service import RoleConfigService
from utils import (
    exception_util,
    json_util,
    role_util,
    security_util,
    tg_util,
    token_util,
)
from utils.translate_util import _tl


log = logging.getLogger(__name__)


async def get_max_role_count(user_id: int) -> int:
    user = await user_service.get_by_id(user_id)
    payed_user = await user_service.is_payed_user(user_id)
    author = await user_service.is_author(user_id)
    max_role_count = 6
    max_role_count = 15 if payed_user else max_role_count
    max_role_count = 30 if author else max_role_count
    max_role_count = max(max_role_count, user.role_count) if author else max_role_count
    return max_role_count


def _common_verify(role_json: str, user_id: int) -> RoleEditDetail:
    if security_util.contains_xss(role_json):
        tg_util.send_xss_message(str(user_id), "", role_json)
    role_dict = json.loads(role_json)
    role_dict = json_util.remove_nulls(role_dict)
    input_role = RoleEditDetail(**json_util.convert_to_dict(role_dict))
    if input_role and input_role.role_book and input_role.role_book.entries:
        check_ret = [
            entry
            for entry in input_role.role_book.entries
            if entry.enabled
            and not entry.constant
            and token_util.num_tokens_from_string(entry.content) > 1000
        ]
        if check_ret:
            message = "关键词世界书每个条目内容不能超过1000Token，请修改相关条目"
            raise exception_util.verify_exception(message=message)
        check_ret = [
            x for x in input_role.role_book.entries if x.enabled and not x.constant and not x.content
        ]
        if check_ret:
            message = "关键词世界书每个条目内容不能为空，请修改相关条目"
            raise exception_util.verify_exception(message=message)
        
    sum_token = role_util.summary_token(input_role)
    if sum_token > ROLE_MAX_TOKEN:
        raise exception_util.verify_exception(
            message="角色内容总长度超限制，请修改后重试"
        )
    return input_role


async def input_create_error(role_json: str, user_id: int, admin: bool):

    input_role = None
    input_role = _common_verify(role_json, user_id)
    if not input_role:
        log.error(f"role json error,user_id: {user_id},role_json: {role_json}")
        raise exception_util.verify_exception(message="角色参数错误，请联系客服")
    if admin:
        return
    create_count = await role_config_service.user_create_role_count(user_id)
    max_count = await get_max_role_count(user_id)
    if create_count >= max_count:
        log.error(f"role count limit,user_id: {user_id}")
        raise exception_util.verify_exception(message="角色数量超过限制")

async def input_edit_error(role_json: str, user_id: int, admin: bool):
    input_role = None
    input_role = _common_verify(role_json, user_id)
    if input_role.id == 0:
        log.error(f"role id error,user_id: {user_id},role_json: {role_json}")
        raise exception_util.verify_exception(message="角色参数错误，请联系客服")
    if admin:
        return
    # update
    #  verify role owner
    role_config = await RoleConfigService.get_role_config(input_role.id)
    if not role_config:
        log.error(f"role not exist,user_id: {user_id},role_id: {input_role.id}")
        raise exception_util.verify_exception(message="角色不存在")
    if role_config.uid != user_id:
        log.error(f"role permission error,user_id: {user_id},role_id: {input_role.id}")
        raise exception_util.verify_exception(message="角色权限错误")

    if role_config.privacy == True or role_config.status == False:
        log.error(f"un allowed update,user_id: {user_id},role_id: {input_role.id}")
        raise exception_util.verify_exception(message="公开卡或者已下架卡片不允许修改")

    # verify audit status
    role_audit = await role_audit_service.get_role_audit(
        user_id, ChatModeType.SINGLE.value, input_role.id
    )
    if role_audit and role_audit.status == AuditStatus.AUDITING.value:
        log.error(f"Role is under auditing,user_id: {user_id},role_id: {input_role.id}")
        raise exception_util.verify_exception(message="角色正在审核中，不允许修改")


async def admin_group_approved_error(audit_info: RoleAudit) -> str:
    if not audit_info or audit_info.status != AuditStatus.AUDITING.value:
        return "审核信息不存在或状态错误"
    group_detail = await role_group_service.load_detail_by_id(audit_info.mode_target_id)
    if any(role.private_card for role in group_detail.roles):
        return "组内有私密卡片"
    return ""


async def group_create_error(
    chat_group: ChatGroupEdit, user_id: int, language: str
) -> str:
    if not chat_group.role_ids:
        return _tl("缺少角色信息", language)
    if len(chat_group.role_ids) > 4 or len(chat_group.role_ids) < 2:
        return _tl("角色数量错误，群组最多包含4个角色，最好包含2个角色", language)
    role_list = await role_loader_service.list_by_ids(chat_group.role_ids)
    if len(role_list) != len(chat_group.role_ids):
        return _tl("角色不存在", language)
    for role in role_list:
        if not role.status:
            return _tl("包含有无效的角色", language)
        if not role.privacy and role.uid != user_id:
            return _tl("角色权限错误", language)
    token = token_util.num_tokens_from_string(chat_group.scenario)
    if token > 800:
        return _tl("场景不能超过800个Token", language)
    group_list = await role_group_service.list_by_user_id(user_id)
    if len(group_list) >= 3:
        return _tl("每个用户限制最多创建3个群组", language)
    return ""


async def group_update_error(chat_group: AdminChatGroupEdit, language: str) -> str:
    if not chat_group.name:
        return _tl("群组名字不存在", language)
    token = token_util.num_tokens_from_string(chat_group.scenario)
    if token > 800:
        return _tl("场景不能超过800个Token", language)
    return ""


async def user_group_update_error(
    chat_group: UserChatGroupEdit, user_id: int, language: str
) -> str:
    if not chat_group.role_ids:
        return _tl("角色ID错误", language)
    if len(chat_group.role_ids) > 4 or len(chat_group.role_ids) < 2:
        return _tl("角色数量限制，群组最多包含4个角色，最少包含2个角色", language)
    detail = await role_group_service.load_detail_by_id(chat_group.id)
    if not detail:
        return _tl("群组不存在", language)
    if detail.user_id != user_id:
        return _tl("群组权限错误", language)
    role_list = await role_loader_service.list_by_ids(chat_group.role_ids)
    if len(role_list) != len(chat_group.role_ids):
        return _tl("角色不存在", language)

    for role in role_list:
        if not role.status:
            return _tl("角色状态错误", language)
        if not role.privacy and role.uid != user_id:
            return _tl("角色权限错误", language)
    return ""
