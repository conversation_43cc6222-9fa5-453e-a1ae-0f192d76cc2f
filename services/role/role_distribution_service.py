from datetime import timedelta
import re
from common.bot_common import AI_CHANNEL_CONTENT, CHANNEL_CONTENT, EN_CHANNEL_CONTENT
from common.common_constant import ChannelCategory, GroupCategory, Language, RoleReplace
from persistence.models.models import RolePushTask
from services import bot_message_service, tg_config_service
from services.role import role_loader_service
from utils import (
    date_util,
    env_const,
    exception_util,
    request_util,
    response_util,
    str_util,
)


async def check_and_on_role_push_task(role_id: int):
    role_push_task = await RolePushTask.filter(role_id=role_id).first()
    if role_push_task and role_push_task.on_pushing:
        raise exception_util.verify_exception(
            message="当前角色推送正在推送中，请稍后再试"
        )
    if (
        role_push_task
        and role_push_task.finished_at + timedelta(minutes=5) > date_util.utc_now()
    ):
        raise exception_util.verify_exception(
            message="相同角色推送任务5分钟内只能推送一次，请稍后再试"
        )
    if not role_push_task:
        role_push_task = await RolePushTask.create(
            role_id=role_id, success=False, finished_at=0, on_pushing=True
        )
        if role_push_task:
            return True
        raise exception_util.verify_exception(
            message="角色推送任务创建失败，请稍后再试"
        )

    ret = (
        await RolePushTask.filter(role_id=role_id, on_pushing=False)
        .select_for_update()
        .update(on_pushing=True, success=False, finished_at=0)
    )
    if ret == 0:
        raise exception_util.verify_exception(
            message="角色推送任务状态更新失败，请稍后再试"
        )
    return True


async def complete_role_push_task(role_id: int, success: bool):
    ret = (
        await RolePushTask.filter(role_id=role_id, on_pushing=True)
        .select_for_update()
        .update(on_pushing=False, success=success, finished_at=date_util.utc_now())
    )
    if ret == 0:
        return False
    return True


async def role_broadcast(role_id: int):
    role_config = await role_loader_service.load_translated_role(
        role_id, Language.ZH.value, ""
    )
    if not role_config or not role_config.privacy:
        raise exception_util.verify_exception(message="角色不存在、已下架或者未公开")
    channels = await tg_config_service.list_channel(category=ChannelCategory.ROLE)
    for channel in channels:
        content = (
            CHANNEL_CONTENT
            if channel.language == Language.ZH.value
            else EN_CHANNEL_CONTENT
        )
        await bot_message_service.push_role_channel_message(
            channel.chat_id, role_id, content
        )
    role_groups = await tg_config_service.list_group(category=GroupCategory.ROLE)
    for group in role_groups:
        content = (
            CHANNEL_CONTENT
            if group.language == Language.ZH.value
            else EN_CHANNEL_CONTENT
        )
        await bot_message_service.push_role_group_message(
            group.chat_id, role_id, content
        )
    channels = await tg_config_service.list_channel(category=ChannelCategory.AI_ROLE)
    common_channels = [channel.username for channel in channels]
    if not channels:
        return response_util.ok(
            {"role_id": role_id, "common_channels": common_channels}
        )
    intro = str_util.escape_tg_text(role_config.introduction)
    intro, tags = await get_rewrite_intro(intro)
    ai_content = AI_CHANNEL_CONTENT.replace(RoleReplace.ROLE_INTRO.value, intro)
    content = ai_content.replace(RoleReplace.ROLE_SUB_TAGS.value, tags)
    for channel in channels:
        await bot_message_service.push_role_channel_message(
            channel.chat_id, role_id, content, True
        )
    ai_channels = [channel.username for channel in channels]
    return response_util.ok(
        {
            "role_id": role_id,
            "common_channels": common_channels,
            "ai_channels": ai_channels,
        }
    )


async def get_rewrite_intro(intro: str):
    data = {
        "inputs": {},
        "query": intro,
        "response_mode": "blocking",
        "conversation_id": "",
        "user": "tavern-admin",
    }
    header = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {env_const.DIFY_ROLE_REWRITE_TOKEN}",
    }
    # response = requests.post(env_const.DIFY_URL, json=data, headers=header)
    # response_content = response.json().get("answer")
    response = await request_util.post_json(
        env_const.DIFY_URL, data, headers=header, timeout=60
    )
    if not response:
        raise Exception("Failed to get response from Dify")
    response_content = response.get("answer", "")
    new_into, tags = extract_response_parts(response_content)
    return escape_tg_text(new_into), escape_tg_text(tags)


def extract_response_parts(response_content: str) -> tuple[str, str]:
    """
    Extract the three parts from response content: rep content, main content, and tags

    Args:
        response_content: The markdown formatted response content

    Returns:
        tuple containing (rep_content, main_content, tags)
    """
    # Extract content between <rep> tags
    rep_pattern = r"<rep>(.*?)</rep>"
    rep_match = re.search(rep_pattern, response_content, re.DOTALL)
    if not rep_match:
        return "", ""

    rep_content = rep_match.group(1).strip()

    # Extract main content between SoExpert and 【标签】
    main_start = rep_content.find("SoExpert:")
    main_end = rep_content.find("【标签】")

    if main_start == -1 or main_end == -1:
        return rep_content, ""

    main_content = rep_content[main_start + len("SoExpert:") : main_end].strip()

    # Extract tags after 【标签】
    tags = rep_content[main_end + len("【标签】") :].strip()

    return main_content, tags


def escape_tg_text(content: str) -> str:
    return (
        content.replace("-", "\\-")
        .replace("(", "\\(")
        .replace(")", "\\)")
        .replace("!", "\\!")
        .replace("_", "\\_")
        .replace(".", "\\.")
        .replace("=", "\\=")
        .replace("+", "\\+")
        .replace("~", "\\~")
        .replace("*", "\\*")
        .replace("#", "\\#")
    )
