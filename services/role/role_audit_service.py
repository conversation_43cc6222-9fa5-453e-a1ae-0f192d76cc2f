from datetime import datetime
from common.common_constant import Audit<PERSON>tat<PERSON>, ChatModeType
from persistence import role_publish_history_dao
from persistence.models.models import RoleAudit
from persistence.models.mongo_models import RolePublishHistory
from utils import date_util
from tortoise.expressions import Q


async def list_audit_by_type(mode_type: str):
    return await RoleAudit.filter(mode_type=mode_type).all()


async def list_audit(user_id: int, mode_type: str):
    return await RoleAudit.filter(user_id=user_id, mode_type=mode_type).all()


async def list_by_status(status: str):
    return await RoleAudit.filter(status=status).all()


async def list_by_public_audit():
    return await RoleAudit.filter(Q(open_role_id__gt=0) | Q(open_group_id__gt=0)).all()


async def map_by_mode(mode_type: str, mode_target_id: list[int]):
    ret = await RoleAudit.filter(
        mode_type=mode_type, mode_target_id__in=mode_target_id
    ).all()
    return {x.mode_target_id: x for x in ret}


async def get_by_mode(mode_type: str, mode_target_id: int):
    return await RoleAudit.filter(
        mode_type=mode_type, mode_target_id=mode_target_id
    ).first()



async def get_role_audit_by_admin(
    target_id: int, mode_type: str = ChatModeType.SINGLE.value
):
    return await RoleAudit.filter(mode_target_id=target_id, mode_type=mode_type).first()


async def get_role_audit(user_id: int, mode_type: str, target_id: int):
    return await RoleAudit.filter(
        user_id=user_id, mode_type=mode_type, mode_target_id=target_id
    ).first()


async def user_submit_audit(user_id: int, mode_type: str, mode_target_id: int):
    role_audit = await get_role_audit(user_id, mode_type, mode_target_id)
    if role_audit is None:
        await RoleAudit.create(
            user_id=user_id,
            mode_type=mode_type,
            mode_target_id=mode_target_id,
            status=AuditStatus.AUDITING.value,
        )
    else:
        role_audit.status = AuditStatus.AUDITING.value
        await role_audit.save()


async def reset_status_edit(user_id: int, mode_type: str, mode_target_id: int):
    role_audit = await get_role_audit(user_id, mode_type, mode_target_id)
    if not role_audit:
        return False
    if role_audit and role_audit.status == AuditStatus.AUDITING.value:
        return False
    role_audit.status = AuditStatus.EDITING.value
    await role_audit.save()
    return True


async def rejected(audit_id: int, reason: str):
    role_audit = await RoleAudit.filter(id=audit_id).first()
    if role_audit is not None:
        role_audit.status = AuditStatus.REJECTED.value
        role_audit.reason = reason
        await role_audit.save()
    return role_audit


async def update_audit_status(audit_id: int, status: str, reason: str):
    role_audit = await RoleAudit.filter(id=audit_id).first()
    if role_audit is not None:
        role_audit.status = status
        role_audit.reason = reason
        await role_audit.save()


async def publish_role(id: int, open_role_id: int):
    role_audit = await RoleAudit.filter(id=id).first()
    if role_audit is not None:
        role_audit.status = AuditStatus.APPROVED.value
        role_audit.open_role_id = open_role_id
        role_audit.published_at = datetime.now()
        role_audit.published_version = role_audit.published_version + 1
        await role_audit.save()
    return role_audit


async def publish_group(id: int, open_group_id: int):
    role_audit = await RoleAudit.filter(id=id).first()
    if role_audit is not None:
        role_audit.status = AuditStatus.APPROVED.value
        role_audit.open_group_id = open_group_id
        role_audit.published_at = datetime.now()
        role_audit.published_version = role_audit.published_version + 1
        await role_audit.save()


async def map_by_user_id(user_id: int, mode_type: str):
    ret_list = await RoleAudit.filter(user_id=user_id, mode_type=mode_type).all()
    return {x.mode_target_id: x for x in ret_list}


async def add_publish_history(
    user_id: int,
    mode_type: str,
    mode_target_id: int,
    audit: RoleAudit,
    submit_at: datetime,
    publish_data: dict,
    original_data: dict,
    status: str = AuditStatus.APPROVED.value,
    reason: str = "",
):
    next_version = await role_publish_history_dao.next_version(
        mode_type, mode_target_id
    )
    await role_publish_history_dao.insert(
        RolePublishHistory(
            user_id=user_id,
            mode_type=mode_type,
            mode_target_id=mode_target_id,
            publish_version=next_version,
            submit_at=submit_at,
            published_at=audit.updated_at,
            publish_data=publish_data,
            original_data=original_data,
            created_at=date_util.utc_now(),
            updated_at=date_util.utc_now(),
            status=status,
            reason=reason,
        )
    )


async def list_receive_award_audits(user_id: int):
    return await RoleAudit.filter(user_id=user_id, receive_reward=True).all()


async def update_read_receive_reward(
    ids: list[int],
):
    await RoleAudit.filter(id__in=ids).update(read_receive_reward=True)


async def publish_list(mode_type: str, mode_target_id: int):
    return await role_publish_history_dao.list_by_mode(mode_type, mode_target_id)

async def get_latest_publish_list_by_user(user_id: int, count: int = 6):
    return await role_publish_history_dao.get_latest_by_user(user_id, count)

async def publish_max_version(mode_type: str, target_ids: list[int]):
    return await role_publish_history_dao.max_versions(mode_type, target_ids)


# async def receive_audit_reward(
#     user_id: int,
#     mode_type: str,
#     mode_target_id: int,
# ):
#     receive_audits = await list_receive_award_audits(
#         user_id,
#     )
#     task_type = (
#         WelfareTaskType.PUBLISH_CARD
#         if receive_audits
#         else WelfareTaskType.FIRST_PUBLISH_CARD
#     )
#     task = await welfare_service.list_tasks(task_type)
#     if not task:
#         return
#     rewards = await welfare_service.reward_task(user_id, task[0].task_id)
#     if not rewards:
#         return
#     receive_reward_ids = [x.id for x in rewards]
#     await RoleAudit.filter(
#         user_id=user_id,
#         mode_type=mode_type,
#         mode_target_id=mode_target_id,
#     ).update(receive_reward=True, receive_reward_ids=receive_reward_ids)


# async def reward_desc(uid, mode_type: ChatModeType, language: str = Language.ZH.value):
#     role_audits = await RoleAudit.filter(
#         user_id=uid, mode_type=mode_type.value, receive_reward=True
#     ).all()
#     if not role_audits:
#         return {}
#     reward_ids = []
#     for role_audit in role_audits:
#         if not role_audit.receive_reward_ids:
#             continue
#         reward_ids.extend(role_audit.receive_reward_ids)
#     reward_ids = list(set(reward_ids))
#     if not reward_ids:
#         return {}
#     rewards = await welfare_service.list_rewards_by_ids(reward_ids)
#     reward_map = {x.id: x for x in rewards}
#     if not rewards:
#         return {}
#     reward_desc = {}
#     for role_audit in role_audits:
#         if not role_audit.receive_reward_ids:
#             continue
#         role_rewards = [
#             reward_map.get(reward_id)
#             for reward_id in role_audit.receive_reward_ids
#             if reward_map.get(reward_id)
#         ]
#         if not role_rewards:
#             continue
#         titles = [
#             _tl(x.title, language, WelfareTaskReward.__name__)
#             for x in role_rewards
#             if x
#         ]
#         reward_desc[role_audit.mode_target_id] = ",".join(titles)
#     return reward_desc


async def update_receive_reward(
    user_id: int,
    mode_type: str,
    mode_target_id: int,
    receive_reward_ids: list[int],
):
    await RoleAudit.filter(
        user_id=user_id,
        mode_type=mode_type,
        mode_target_id=mode_target_id,
    ).update(receive_reward=True, receive_reward_ids=receive_reward_ids)
