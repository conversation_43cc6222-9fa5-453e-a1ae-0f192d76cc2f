from io import BytesIO
import logging
import uuid

import requests
from common.common_constant import RolePlayType
from common.role_card import RoleCardInfo, TavernCard, ThirdCardInfo
from common.role_model import RoleDataConfig, RoleEditDetail, SceneConfig
from persistence import char_book_dao, third_card_dao
from persistence.models.models import RoleConfig
from services import product_service
from services.role import role_loader_service
from services.role_config_service import RoleConfigService
from utils import card_util, image_util, role_util
from PIL import Image, PngImagePlugin

log = logging.getLogger(__name__)


async def update_role_config(
    role_card_config: RoleCardInfo, third_card: ThirdCardInfo
) -> RoleConfig:
    role_config = await role_loader_service.load_by_id(third_card.role_id)
    if role_config is not None:
        data_config = RoleDataConfig(**role_config.data_config)
        data_config.description = role_card_config.description
        data_config.personality = role_card_config.personality
        data_config.scenario = role_card_config.scenario
        data_config.first_message = role_card_config.first_mes
        data_config.example_dialog = role_card_config.mes_example
        data_config.muilte_scenes = card_util.list_new_scenes(role_card_config)
        role_config.data_config = data_config.model_dump()
        return await RoleConfigService().update_user_role_config(
            uid=0, config=role_config
        )
    return None


async def add_role_config(role_card_config: RoleCardInfo, image_url: str) -> RoleConfig:
    new_role = RoleEditDetail(
        card_name=role_card_config.name,
        name=role_card_config.name,
        role_name=role_card_config.name,
        introduction="",
        role_avatar=image_url,
        description=role_card_config.description,
        personality=role_card_config.personality,
        scenario=role_card_config.scenario,
        first_message=role_card_config.first_mes,
        example_dialog=role_card_config.mes_example,
        muilte_scenes=card_util.list_new_scenes(role_card_config),
        play_type = RolePlayType.PLOT_INFERENCE.value
    )
    products = await product_service.list_chat_product_new()
    product_ids = [x.mid for x in products]
    role_config = new_role.to_role_config(product_ids)
    role_config.privacy = False
    role_config.tags = ",".join(role_card_config.tags) if role_card_config.tags else ""
    role_config.def_language = role_util.detect_language(new_role).value
    if role_card_config.character_book is not None:
        role_card_config.character_book.book_id = str(uuid.uuid4())
        await char_book_dao.insert(role_card_config.character_book)
        role_config.book_id = role_card_config.character_book.book_id

    role_config = await RoleConfigService.add_role_config(role_config)
    return role_config


# async def analysis_third_card(card_id: str) -> RoleCardInfo:
#     third_card = await third_card_dao.get_by_card_id(card_id)
#     # 通过image_url下载图片
#     image_url = third_card.load_image_url()
#     try:
#         response = requests.get(image_url)
#         image = Image.open(BytesIO(response.content))
#         tavern_card = image_util.load_new_image_info(image)
#     except Exception as e:
#         log.error("analysis_third_card error, e: %s", e)
#         return None
#     if tavern_card is None:
#         return None
#     return tavern_card.to_role_card()

# async def analysis_third_card_original(card_id: str) -> str:
#     third_card = await third_card_dao.get_by_card_id(card_id)
#     # 通过image_url下载图片
#     image_url = third_card.load_image_url()
#     try:
#         response = requests.get(image_url)
#         image = Image.open(BytesIO(response.content))
#         return image_util.load_original_image_info(image)
#     except Exception as e:
#         log.error("analysis_third_card error, e: %s", e)
#         return ""


# async def init_or_update_role_config(
#     third_card: ThirdCardInfo, role_card_info: RoleCardInfo
# ):
#     try:
#         if third_card.role_id > 0:
#             return await update_role_config(role_card_info, third_card)
#         else:
#             return await add_role_config(role_card_info, third_card)
#     except Exception as e:
#         log.error("init_or_update_role_config error, e: %s", e, exc_info=True)
#         return None
