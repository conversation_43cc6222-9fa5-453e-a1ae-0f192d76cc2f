
from persistence.models.models import VoiceHistory, VoiceSpeaker
from typing import List, Union
from tortoise.exceptions import DoesNotExist

class VoiceSpeakerService:
    
    @classmethod
    async def get_speaker_by_id(cls, speaker_id:str) -> Union[VoiceSpeaker, None]:
        try:
            speaker = await VoiceSpeaker.get(speaker_id=speaker_id)
            return speaker
        except DoesNotExist:
            return None  
    
    @classmethod
    async def get_all_speakers(cls) -> List[VoiceSpeaker]:
        speakers = await VoiceSpeaker.all()
        return speakers
    
    @classmethod
    async def get_active_speakers(cls) -> List[VoiceSpeaker]:
        speakers = await VoiceSpeaker.filter(status=True)
        return speakers
    
    @classmethod
    async def add_speaker(cls, speaker:VoiceSpeaker) -> Union[VoiceSpeaker, None]:
        speaker = await VoiceSpeaker.create(speaker_id=speaker.speaker_id, name = speaker.name,sample_url = speaker.sample_url,api_url = speaker.api_url,speed = speaker.speed)
        return speaker
    
    @classmethod
    async def add_voice_history(cls, h: VoiceHistory):
        await h.save()