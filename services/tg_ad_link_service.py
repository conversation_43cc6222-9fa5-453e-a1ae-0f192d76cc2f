from utils import utils
from tortoise.transactions import in_transaction
from persistence.models.models import (
    TgAdLink,
    User,
    Account,
    TgAdCampaignLink
)

async def get_or_create_channel(link: str) -> int:
    cur_channel = await TgAdCampaignLink.filter(tg_link=link).first()
    if cur_channel:
        return cur_channel.channel_id
    else:
        user_id = await get_max_channel_user_id()
        return await create_tg_link_channel(link, user_id + 1)

async def get_by_tg_link(link: str) -> TgAdCampaignLink | None:
    return await TgAdCampaignLink.filter(tg_link=link).first()

async def add_tg_ad_link(user_id: int, link: str) -> TgAdCampaignLink:
    chat_name = utils.get_last_path_segment(link)
    return await TgAdCampaignLink.create(channel_id=user_id, tg_link=link, tg_chat_name=chat_name)

async def get_max_channel_user_id() -> int:
    user = await User.filter(id__gt=10000,
                             id__lt=100000).order_by('-id').first()
    return user.id if user else 10000

async def create_tg_link_channel(link: str, next: int) -> int:
    async with in_transaction():
        uid = next
        user = await User.create(id=uid, email=f'dis_{uid}@tg', nickname=f'dis_{uid}',  password='123456')
        await Account.create(user_id=uid, total_balance=0, award_balance=0, charge_balance=0)

        chat_name = utils.get_last_path_segment(link)
        await TgAdCampaignLink.create(channel_id=uid, tg_link=link, tg_chat_name=chat_name)
        return uid

async def add_new_ad_user(uid: int) -> User:
    user = await User.create(id=uid, email=f'dis_{uid}@tg', nickname=f'dis_{uid}', password_hash='')
    await Account.create(user_id=uid, total_balance=0, award_balance=0, charge_balance=0)
    return user

async def list_all_channels() -> list[TgAdCampaignLink]:
    return await TgAdCampaignLink.all()

async def get_or_create_target_link(tg_link: str, channel_id: int, role_id: int, target_type: str, target_link: str) -> TgAdLink:
    cur_link = await TgAdLink.filter(target_link=target_link).first()
    if cur_link:
        return cur_link
    result = await TgAdLink.create(tg_link=tg_link, channel_id=channel_id, role_id=role_id, target_type=target_type, target_link=target_link)
    return result

async def get_all_links() -> list[TgAdLink]:
    return await TgAdLink.all()

async def get_all_links_by_channel(channel_id: int) -> list[TgAdLink]:
    return await TgAdLink.filter(channel_id=channel_id).order_by('-id')