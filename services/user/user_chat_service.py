from datetime import datetime
import re
import uuid
from ai import lite_llm_bot
from common.common_constant import (
    ApiSource,
    ChatChannel,
    ChatModeType,
    ChatPlatform,
    ErrorCode,
    ErrorKey,
    LlmModel,
    PresetReplace,
    ProductType,
    RoleStatusBlockType,
    UserModelFilter,
)
from common.models.chat_model import (
    ChatHistory,
    ChatNextInput,
    GenerateSbtRequest,
    SaveUserSbtRequest,
    SbtDetail,
    SbtItem,
    UserSbtDetail,
)
from common.models.chat_request import ChatHistoryItem, ChatRequest
from common.role_model import RoleDataConfig
from persistence import chat_history_dao
from persistence.models.models import (
    Product,
    RoleConfig,
    SbtConfig,
    User,
    UserSbt,
    UserSbtGenerateHistory,
    UserSbtHistory,
)
from presets.status_block_preset import (
    GENERATE_SBT_AI_MESSAGE,
    GENERATE_SBT_SYSTEM_MESSAGE,
    GENERATE_SBT_USER_MESSAGE,
)
from services import (
    account_service,
    product_service,
    regex_service,
    role_config_service,
    translate_service,
    user_service,
)
from services.chat import chat_message_service, chat_snapshot_service
from services.role import role_group_service, role_loader_service
from services.user import user_benefit_service
from utils import (
    convert_util,
    date_util,
    example_util,
    exception_util,
    message_utils,
    role_util,
    str_util,
)
from utils.translate_util import _tl


async def list_status_block_templates():
    templates = await SbtConfig.filter(enabled=1).all().order_by("sort_order")
    return templates


async def list_stb_by_user(user: User, role_config: RoleConfig, language: str):
    role_name = role_config.role_name if role_config else ""
    templates = await list_status_block_templates()
    templates = [SbtDetail.from_model(x) for x in templates]
    for template in templates:
        template.title = _tl(template.title, language, SbtConfig.__name__)
        template.content = _tl(template.content, language, SbtConfig.__name__)
        template.content_placeholder = _tl(
            template.content_placeholder, language, SbtConfig.__name__
        )
        template.rule = _tl(template.rule, language, SbtConfig.__name__)
        template.rule = str_util.format_char_and_user(
            template.rule, role_name, user.nickname
        )
        template.content = str_util.format_char_and_user(
            template.content, role_name, user.nickname
        )
        template.content_placeholder = str_util.format_char_and_user(
            template.content_placeholder, role_name, user.nickname
        )
    return templates


async def check_config_exists(sbt_id: int) -> bool:
    exists = await SbtConfig.filter(id=sbt_id, enabled=1).exists()
    return exists


async def get_status_block_template_by_id(sbt_id: int):
    return await SbtConfig.get(id=sbt_id)


async def get_user_status_block(user_id: int, conv_id: str) -> UserSbtDetail | None:
    user_sbt = await UserSbt.filter(user_id=user_id, conversation_id=conv_id).first()
    if not user_sbt:
        return None
    template = None
    if user_sbt.sbt_id:
        template = await get_status_block_template_by_id(user_sbt.sbt_id)
    return UserSbtDetail.from_model(user_sbt, template)


async def get_chat_display_usb(
    user: User, role_config: RoleConfig, chat_message: ChatHistory
) -> UserSbtDetail | None:

    status_block = str_util.parse_status_block(chat_message.content)
    status_block = str_util.format_char_and_user(
        status_block, role_config.role_name, user.nickname
    )
    usb_sbt = await get_user_status_block(user.id, chat_message.conversation_id)
    if usb_sbt:
        usb_sbt.content = status_block
        return usb_sbt
    return UserSbtDetail(
        conversation_id=chat_message.conversation_id, content=status_block, rule=""
    )


async def generate_status_block_verify(user_id: int, sbt_request: GenerateSbtRequest):
    balance = await account_service.get_total_balance(user_id)
    product = await product_service.get_online_by_type_first(
        ProductType.GENERATE_STATUS_BLOCK.value
    )
    if not product:
        raise exception_util.verify_exception(message="产品配置有误，请联系客服")

    if balance < product.price:
        raise exception_util.verify_exception(
            error_code=ErrorCode.INSUFFICIENT_BALANCE.value,
            error_key=ErrorKey.INSUFFICIENT_BALANCE.value,
        )
    template = await get_status_block_template_by_id(sbt_request.sbt_id)
    if not template or not template.enabled:
        raise exception_util.verify_exception(message="状态栏模板不存在或已被删除")
    return None


async def generate_status_block(
    user_id: int, language: str, sbt_request: GenerateSbtRequest
) -> str:
    user = await user_service.get_user_by_id(user_id)
    chat_message = await chat_history_dao.get_by_message_id_and_version(
        sbt_request.message_id, sbt_request.version
    )
    if not chat_message:
        raise exception_util.verify_exception(message="消息记录不存在或已被删除")
    history_list = await chat_message_service.display_history_by_conv_id(
        user.id, user.nickname, sbt_request.conversation_id, language=language
    )
    if not history_list:
        return ""
    role_id = chat_message.role_id
    role_config = await role_loader_service.load_translated_role(
        role_id, language, user.nickname, True
    )
    if not role_config:
        raise exception_util.verify_exception(message="角色配置不存在或已被删除")
    role_name = role_config.role_name
    if len(history_list) >= 6:
        history_list = history_list[-6:]
    chat_history = "\n".join([x.content for x in history_list])
    sbt_config = await get_status_block_template_by_id(sbt_request.sbt_id)
    template_content = sbt_config.content if sbt_config else ""
    template_content = str_util.format_char(template_content, role_name)
    template_content = str_util.format_char_and_user(
        template_content, role_name, user.nickname
    )
    rule = sbt_config.rule if sbt_config else ""
    rule = str_util.format_char_and_user(rule, role_name, user.nickname)
    system_message = GENERATE_SBT_SYSTEM_MESSAGE
    user_message = GENERATE_SBT_USER_MESSAGE.replace(
        PresetReplace.STATUS_BAR.value, template_content
    ).replace(PresetReplace.LANGUAGE.value, language)
    user_message = user_message.replace(PresetReplace.STATUS_RULES.value, rule)
    user_message = user_message.replace(PresetReplace.CHAT_HISTORY.value, chat_history)
    product = await product_service.get_online_by_type_first(
        ProductType.GENERATE_STATUS_BLOCK.value
    )
    if not product:
        return ""
    model = product.model if product.model else LlmModel.CLAUDE_3_5_SONNET.value

    async def run_status_block_task(model: str):
        res = await lite_llm_bot.run_task(
            model,
            system_message,
            user_message,
            GENERATE_SBT_AI_MESSAGE,
            max_tokens=600,
            metadata={
                "trace_user_id": user_id,
                "generation_name": "generate_status_block",
                "session_id": f"{user_id}_{sbt_request.conversation_id}",
            },
        )
        search_ret = re.search(r"<StatusBlock>(.*?)</StatusBlock>", res, re.DOTALL)
        return search_ret.group(1).strip() if search_ret else ""

    status_block = await run_status_block_task(model)
    if not status_block:
        status_block = await run_status_block_task(LlmModel.CLAUDE_3_5_HAIKU.value)
    if not status_block:
        return ""
    # 保存生成记录
    await UserSbtGenerateHistory(
        user_id=user_id,
        conversation_id=sbt_request.conversation_id,
        sbt_id=sbt_request.sbt_id,
        message_id=sbt_request.message_id,
        content=status_block,
    ).save()

    # 扣费
    await account_service.AccountService.create_pay_order(user_id, product, role_id)

    return status_block


async def save_user_status_block(
    user_id: int,
    sbt_request: SaveUserSbtRequest,
    product: Product,
    chat_message: ChatHistory,
) -> UserSbtDetail | None:
    if not sbt_request.ai_generate:
        await account_service.AccountService.create_pay_order(
            user_id, product, chat_message.role_id
        )
    user_sbt_history = UserSbtHistory(
        user_id=user_id,
        conversation_id=sbt_request.conversation_id,
        sbt_id=sbt_request.sbt_id,
        message_id=sbt_request.message_id,
        content=sbt_request.content,
        rule=sbt_request.rule,
    )
    await user_sbt_history.save()
    if sbt_request.sbt_id and not sbt_request.rule:
        sbt_config = await get_status_block_template_by_id(sbt_request.sbt_id)
        if sbt_config and sbt_config.rule:
            sbt_request.rule = sbt_config.rule
    usb = await UserSbt.filter(
        user_id=user_id, conversation_id=sbt_request.conversation_id
    ).first()
    if not usb:
        usb = UserSbt(
            user_id=user_id,
            conversation_id=sbt_request.conversation_id,
        )
    usb.sbt_id = sbt_request.sbt_id
    usb.message_id = sbt_request.message_id
    usb.message_version = sbt_request.version
    usb.content = sbt_request.content
    usb.rule = sbt_request.rule
    usb.last_saved_time = int(date_util.now().timestamp())
    await usb.save()
    return await get_user_status_block(user_id, sbt_request.conversation_id)


async def update_message_status_block(
    message_id: str, version: int, status_block: str
) -> bool:
    chat_history = await chat_history_dao.get_by_message_id_and_version(
        message_id, version
    )
    if not chat_history:
        return False
    status_block = str_util.format_status_block(
        status_block, RoleStatusBlockType.NORMAL.value, True
    )
    chat_content = str_util.replace_new_status_block(chat_history.content, status_block)
    chat_history.content = chat_content
    await chat_history_dao.update_content_by_message_id_and_version(
        message_id, version, chat_content
    )
    return True


async def _build_user_base_info(user_id: int, input: ChatNextInput) -> ChatNextInput:
    user = await user_service.get_by_id(user_id)
    input.nickname = user.nickname
    input.user_id = user.id
    input.user_status = user.status
    input.register_source = user.register_source
    input.usb_switch = user.status_block_switch

    payed, reward = await account_service.get_payed_and_reward_total_balance(user.id)
    input.balance = payed + reward
    input.payed_balance = payed
    input.reward_balance = reward
    input.chat_free_benefit = await user_benefit_service.chat_model_count(user)
    
    if user.chat_channel:
        input.chat_channel = user.chat_channel
    user_chat_product = await product_service.get_user_chat_product(user)
    if user_chat_product:
        input.llm_model = user_chat_product.model
        input.user_chat_product_mid = user_chat_product.mid

    async def build_user_model_filter():
        if input.chat_channel == ChatChannel.FREE_BENEFIT.value:
            return UserModelFilter.FREE_BENEFIT.value
        payed_balance = await account_service.AccountService.get_payed_total_balance(
            user.id
        )
        if user_chat_product and user_chat_product.price <= payed_balance:
            need_deduct_diamond, _ = (
                await account_service.AccountService.is_deduct_diamond_first(
                    user.id, user_chat_product, input.role_id
                )
            )
            if need_deduct_diamond:
                return UserModelFilter.FREE_BENEFIT.value
        return UserModelFilter.DEFAULT.value

    input.user_model_filter = await build_user_model_filter()

    # 用户状态栏
    user_status_block = await get_user_status_block(user.id, input.conversation_id)
    if user_status_block:
        input.usb_saved_time = user_status_block.last_saved_time
        input.usb_rule = user_status_block.rule
        input.usb_template_content = user_status_block.template_content
        input.usb_message_id = user_status_block.message_id
        input.usb_message_version = user_status_block.message_version
    return input


async def _build_role_base_info(role_id: int, input: ChatNextInput) -> ChatNextInput:
    if role_id == 0:
        return input
    role_config = await role_config_service.get_by_id(role_id)
    translate_role_config = await role_loader_service.load_translated_role(
        role_id, input.language, "", format=False
    )

    if not role_config or not translate_role_config:
        raise Exception(f"role_config not found,role_id:{role_id}")

    user_name_model = role_util.get_new_user_role_name(
        translate_role_config, input.nickname
    )
    role_config = role_util.format_role_config(
        translate_role_config, user_name_model.request_user_name
    )

    data_config = RoleDataConfig(**convert_util.to_dict(role_config.data_config))
    input.privacy = role_config.privacy
    input.status = role_config.status
    input.nsfw = role_config.nsfw
    input.role_created_uid = role_config.uid
    input.role_name = role_config.role_name
    input.role_chat_type = role_config.chat_type
    input.user_role_name = user_name_model.user_role_name
    input.request_user_name = user_name_model.request_user_name
    input.display_user_name = user_name_model.display_user_name
    input.role_default_language = role_config.def_language

    input.description = data_config.description
    if role_config.switch_en_desc:
        description_en = await translate_service.get_role_description(
            role_id, input.language
        )
        if description_en:
            description_en = str_util.format_char_and_user(
                description_en, role_config.role_name, user_name_model.request_user_name
            )
        input.description = (
            description_en if description_en else data_config.description
        )
    input.personality = data_config.personality
    input.format_example = example_util.analysis_example(
        data_config,
        "[Example Chat]",
        input.request_user_name,
        input.role_name,
    )
    if data_config.statusBlockEnable and input.usb_switch:
        input.status_block_enable = True
        input.status_block_type = data_config.statusBlockType
        input.status_block = str_util.format_status_block(
            data_config.status_block, input.status_block_type
        )
        input.status_rules = data_config.status_block_rule
    if (
        input.usb_saved_time
        and input.usb_switch
        and input.usb_template_content
        and input.usb_rule
    ):
        input.status_block_enable = True
        input.status_block_type = (
            data_config.statusBlockType
            if data_config.statusBlockType
            else RoleStatusBlockType.NORMAL.value
        )
        input.status_block = str_util.format_status_block(
            input.usb_template_content, input.status_block_type
        )
        input.status_rules = input.usb_rule

    # 状态栏数据不存在依然关闭
    if not input.status_block and not input.status_rules:
        input.status_block_enable = False
        input.status_block = str_util.format_char_and_user(
            input.status_block, input.role_name, input.request_user_name
        )
        input.status_rules = str_util.format_char_and_user(
            input.status_rules, input.role_name, input.request_user_name
        )

    if input.mode_type == ChatModeType.GROUP.value:
        group_detail = await role_group_service.load_detail_by_id(input.group_id)
        role_name_map = {x.id: x.role_name for x in group_detail.roles}
        group_detail.scenario = role_util.fill_group_scenario_role_name(
            group_detail.scenario, role_name_map
        )
        input.scenario = str_util.format_user(
            group_detail.scenario, input.request_user_name
        )

    input.replay_len_ratio = data_config.replay_len_ratio

    input.book_id = role_config.book_id
    input.first_message = role_util.format_first_message(
        role_config, user_name_model.request_user_name
    )

    # 快照信息
    chat_snapshot = await chat_snapshot_service.get_user_role_snapshot_by_conv_id(
        input.user_id, input.conversation_id
    )
    if chat_snapshot and chat_snapshot.scenario and chat_snapshot.first_message:
        input.scenario = str_util.format_char_and_user(
            chat_snapshot.scenario, input.role_name, input.request_user_name
        )
        input.first_message = str_util.format_char_and_user(
            chat_snapshot.first_message, input.role_name, input.request_user_name
        )
    return input


async def build_chat_input_param(
    user_id: int,
    payload: ChatRequest,
    language: str,
    api_source: ApiSource = ApiSource.TMA,
) -> ChatNextInput:
    chat_input = ChatNextInput(
        mode_type=payload.mode_type,
        group_id=payload.group_id,
        role_id=payload.role_id,
        input_content=payload.message,
        conversation_id=payload.conversation_id,
        isRetry=payload.isRetry,
        auto_retry=payload.auto_retry,
        retry_message_id=payload.retry_message_id,
        language=language,
        chat_continue=payload.chat_continue,
        input_last_message_id=payload.last_message_id,
        input_last_message_version=payload.last_message_version,
        timestamp_start=int(datetime.now().timestamp()),
        api_version=payload.api_version,
        platform=payload.platform,
        api_source=api_source,
    )
    chat_input = await _build_user_base_info(user_id, chat_input)
    chat_input = await _build_role_base_info(payload.role_id, chat_input)

    original_regex_rules = await regex_service.get_effective_regex_rules()

    chat_input.regex_rules = original_regex_rules
    chat_input.message_id = uuid.uuid4().hex
    chat_input.version = int(datetime.now().timestamp())
    if payload.retry_message_id and payload.isRetry:
        chat_input.message_id = payload.retry_message_id

    return chat_input
