import logging
from typing import Optional
from pydantic import BaseModel

from common.common_constant import Env
from utils import env_util, request_util

# API documentation: http://172.22.0.12:8140/docs


class GenerateImageResponse(BaseModel):
    image_url: Optional[str]  # 图像URL
    request_id: str  # 请求ID
    code: int  # 响应状态码
    message: Optional[str]  # 响应消息
    spent: Optional[str]  # 耗时，单位秒
    nai_prompt: Optional[str] = None  # NAI提示词
    width: Optional[int] = None  # 图像宽度
    height: Optional[int] = None  # 图像高度

    def success(self) -> bool:
        return self.code == 200 and bool(self.image_url)


def image_server_domain() -> str:
    if env_util.get_current_env() == Env.PROD:
        return "http://172.22.0.28:8140"
    return "http://172.22.0.12:8140"


# curl -X POST http://172.22.0.12:8140/card/ -H "Content-Type: application/json" -d '{"style": "1", "prompt": "a beautiful landscape", "resolution": "large_landscape", "request_id": "12345"}'
async def generate_image_request(
    user_id: int,
    tg_id: int,
    style: str,
    prompt: str,
    resolution: str,
    request_id: str,
) -> GenerateImageResponse:
    request_body = {
        "style": style,
        "prompt": prompt,
        "resolution": resolution,
        "request_id": request_id,
        "uid": user_id,
        "tgid": tg_id,
        "source":2
    }
    url = f"{image_server_domain()}/bot/"
    logging.info(f"GenerateImageRequest: user_id={user_id}, request_body={request_body}")
    ret = await request_util.post_json(url, post_data=request_body, timeout=60)
    logging.info(
        f"GenerateImageRet: user_id={user_id}, request_id={request_id}, ret={ret}"
    )
    # {'image_url': 'https://s3.ap-southeast-1.amazonaws.com/group-image.424224.xyz/20250803/cd4acaeabd23448bb075538e58e6e592_8a6082c8accf4b628dd9b00964b464d7.png', 'request_id': 'cd4acaeabd23448bb075538e58e6e592', 'enhanced_prompt': 'liu yifei and hu ge, celebrities, intimate encounter, passionate kissing, undressing each other, exposed breasts, visible genitals, erotic scene, bedroom setting, tangled bedsheets, lustful expressions, bodies intertwined, sexual activity, explicit content, close-up view, intense moment, flesh against flesh, sweat-covered skin, unrestrained desire, adult situation, romantic lighting', 'code': 200, 'msg': 'success', 'spent': '9.52s', 'height': '1216', 'width': '832'}
    if ret and "image_url" in ret and "code" in ret and ret["code"] == 200:
        return GenerateImageResponse(
            image_url=ret.get("image_url"),
            request_id=ret.get("request_id", ""),
            code=ret.get("code", 200),
            message=ret.get("msg", "请求成功"),
            spent=ret.get("spent", ""),
            width=int(ret.get("width", "0")),
            height=int(ret.get("height", "0")),
            nai_prompt=ret.get("enhanced_prompt", ""),
        )
    return GenerateImageResponse(
        image_url=None,
        request_id=request_id,
        code=500,
        message="请求失败",
        spent=None,
    )
