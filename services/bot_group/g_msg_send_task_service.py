

import logging

import pytz


from persistence.models.models_bot_group import AutoSendGroupMessageCount, AutoSendMessageConfig, TgGroupMessage
from services.bot_group.biz.tg_group_user_msg_biz import FFBotMsgBiz

from datetime import datetime
log = logging.getLogger(__name__)


class AutoSendMsgTaskService:
    
    @classmethod
    async def send_group_msg_task(cls,auto_send_msg_config:AutoSendMessageConfig):
        log.info(f"send_group_msg_task begin_to_send: {auto_send_msg_config}")
        send_id = auto_send_msg_config.id
        group_id = auto_send_msg_config.to_group_id
        message_thread_id = auto_send_msg_config.to_thread_id
        msg_type = auto_send_msg_config.msg_type
        msg_dict = auto_send_msg_config.msg_content
        # if msg_type == 0:
        #     msg_content = auto_send_msg_config.msg_content
        #     if isinstance(msg_content, dict) and "msg_content" in msg_content:
        #         msg_text = msg_content["msg_content"]
        #     else:
        #         log.error("msg_content is not a dictionary or 'msg_content' key is missing")
        #         return
        
        # 检查是否达到消息阈值
        if auto_send_msg_config.job_type == "Threshold":
            message_threshold = auto_send_msg_config.message_threshold
            check_re = await cls.check_message_threshold(group_id=group_id, message_threshold=message_threshold,auto_send_id=send_id)
            if check_re == False:
                log.info(f"send_group_msg_task: message_threshold not reached, {group_id},{message_threshold},{msg_dict}")
                return
        
        log.info(f"send_group_msg_begin_to_tg: {group_id},{message_thread_id},{msg_dict}")
        await FFBotMsgBiz.send_group_msg(send_id,group_id,message_thread_id,msg_dict,is_del_pre_msg=auto_send_msg_config.delete_previous) # type: ignore
        
    
    @classmethod
    async def get_auto_send_msg_config_all(cls) -> list[AutoSendMessageConfig]:

        auto_cfg_list = await AutoSendMessageConfig.filter().all()

        return auto_cfg_list
    
    @classmethod
    async def check_message_threshold(cls,group_id: int, message_threshold:int,auto_send_id:int,topic_id: int =0 ):
        # 获取群组的消息计数
        now = datetime.now(tz=pytz.utc)
        group_message_count = await AutoSendGroupMessageCount.get_or_none(group_id=group_id,auto_send_id=auto_send_id)
        if not group_message_count:
            group_message_count = await AutoSendGroupMessageCount.create(group_id=group_id, auto_send_id=auto_send_id,message_count=0,cal_time=now)
            
            return False
        cal_time = group_message_count.cal_time
        cnt = await TgGroupMessage.filter(group_id=group_id,created_at__gte=cal_time).count()
        log.info(f"check_message_threshold: {group_id},{message_threshold},{cnt},{cal_time}")
        if cnt >= message_threshold:

            # 重置消息计数
            group_message_count.message_count = 0
            group_message_count.cal_time = now
            await group_message_count.save()
            return True
        else:
            # 更新消息计数
            group_message_count.message_count = cnt
            await group_message_count.save()
            return False