
import asyncio
from datetime import date, datetime, timedelta
import logging
import os
import random

from aiogram import Bo<PERSON>
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.utils.keyboard import InlineKeyboardBuilder
import pytz
import requests

from aibot_handlers.tg_util import extract_bot_msg_details
from common.models.ai_bot_admin.forward_group_msg import GUserInfoCard, UserInfoBase

from aiogram import  types

from common.models.ai_bot_admin.group_msg_bean import TgMsgContentBO
from persistence.models.models_bot_group import BotMessageTypeEnum, TgGroupMessage, TgGroupUserMap
from services.bot_group.biz.tg_group_user_msg_biz import FFBotMsgBiz, TgGroupMessageBiz, TgGroupUserMapBiz, GroupMsgUserStatBiz, UserInviteShareStatBiz
from utils.date_util import datetime2utc8str

from queue_msg.redis_queue import RedisQueue


REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")

STAFF_MSG_QUEUE = RedisQueue(queue_name='staff_msg_queue',url=REDIS_URL)


log = logging.getLogger(__name__)

IMAGE_CHECK_API = os.getenv("IMAGE_CHECK_API")

# 生成一个从 1970 年 1 月 1 日开始的 UTC 时间
default_reg_time = datetime(1970, 1, 1)

class GroupUserCardInfoService:
    @staticmethod
    def cal_join_group_days(join_time: datetime)->int:
        today = datetime.now()
        join_time = join_time.replace(tzinfo=None)
        join_group_days = (today - join_time).days
        return join_group_days
    @classmethod
    async def get_guser_info_card(cls, user_map: TgGroupUserMap,msg: dict) -> GUserInfoCard | None:
        
        tg_id = user_map.tg_id
        user_info_base = await TgGroupUserMapBiz.query_user_info_base(tg_id)
        
        if user_info_base is None:
            user_info_base = UserInfoBase(tg_id=tg_id, nickname="未知", reg_time=default_reg_time, user_id=0)
        
        user_info_base.nickname = user_map.tg_nickname
        user_info_base.username = user_map.tg_username
        
        if user_info_base.nickname == None:
            user_info_base.nickname = "未知"
        if user_info_base.username == None:
            user_info_base.username = "未知"
        
        total_fee = await TgGroupUserMapBiz.get_user_total_charge(user_info_base.user_id)
        
        join_group_days = cls.cal_join_group_days(user_map.created_at)
        
        today_messages = await TgGroupMessageBiz.query_latest_5_today_messages(tg_id)
        
        fomat_msgs = cls.fromt_group_msgs(today_messages)
        return GUserInfoCard(
            **user_info_base.dict(),
            join_group_days=join_group_days,
            total_fee=total_fee,
            recent_messages=fomat_msgs
        )

    @staticmethod
    def fromt_group_msgs(group_msgs:list[TgGroupMessage])->list[str]:
        
        if not group_msgs:
            return ["暂无消息"]
        
        fromt_group_msgs = []
        i =1
        for msg in group_msgs:
            msg_date = datetime2utc8str(msg.created_at)
            msg_url = f"https://t.me/{msg.group_user_name}/{msg.message_id}"
            if msg.message_type == BotMessageTypeEnum.TEXT:
                fromt_group_msgs.append(f'{i}:{msg_date}:' + msg.message_text + f':({msg_url})')
            else:
                fromt_group_msgs.append(f'{i}:{msg_date}:[多媒体内容]({msg_url})')
            i += 1
        
        return fromt_group_msgs
                
    
    @classmethod
    async def add_user_msg_queue(cls, msg: dict):
        log.info(f"add_user_msg_queue:{msg}")
        
        can_add = await STAFF_MSG_QUEUE.check_add_queue_with_1min(msg['tg_id'])
        if not can_add:
            log.info(f"add_user_msg_queue tg_id:{msg['tg_id']} 1分钟内已经添加过了")
            return
        else:
            # await STAFF_MSG_QUEUE.enqueue(msg['message_id'],**msg)
            await cls.handle_user_group_msg(msg)
    
    @classmethod
    async def process_queue_task(cls):
        while True:
            msg_queue = await STAFF_MSG_QUEUE.dequeue()
            if msg_queue and "message_id" in msg_queue:
                try:
                    log.info(f"STAFF_MSG_QUEUE  got message: {msg_queue}")
                    await cls.handle_user_group_msg(msg_queue)
                    await asyncio.sleep(6)
                except Exception as e:
                    print(f"Error process_queue message :{msg_queue},{e}")
            else:
                log.debug(f"{STAFF_MSG_QUEUE} queue is empty")
                await asyncio.sleep(10)   
          
    @classmethod
    async def handle_user_group_msg(cls, msg: dict):
        
        log.info(f"handle_user_group_msg:{msg}")
        tg_id = msg["tg_id"]
        f_group_id = msg["f_group_id"]
        f_bot_id = msg["f_bot_id"]
        nickname = msg["nickname"]
        user_name = msg["username"]
        
        tg_user_map,new = await TgGroupUserMapBiz.get_or_create_tguser_map(tg_id, f_group_id, f_bot_id, user_name, nickname)
        
        user_info_card = await cls.get_guser_info_card(tg_user_map,msg)
                
        user_info_text = cls.generate_user_info_message(user_info_card)  # type: ignore
        user_card_markup = cls.gen_u_info_keybord_markup(tg_id=tg_id)
        
        to_bot = FFBotMsgBiz.get_ff_bot()
        g_id, t_id = FFBotMsgBiz.get_group_and_topic_id()
     
        tg_user_map.to_group_id= g_id
        tg_user_map.to_topic_id = t_id
        old_msg_id = tg_user_map.to_message_id
        to_bot_id = to_bot.id

        try:
         # type: ignore
            sent_msg =await to_bot.send_message(g_id, message_thread_id=t_id, text=user_info_text, reply_markup=user_card_markup)
            tg_user_map.to_message_id = sent_msg.message_id
            
            await TgGroupUserMapBiz.update_tg_group_user_map(tg_user_map)
            
            await TgGroupUserMapBiz.add_group_user_map_sent(tg_id, g_id, to_bot_id, sent_msg.message_id)
            if not new:
                # 删除老的用户消息
                user_map_sent = await TgGroupUserMapBiz.get_group_user_map_sent(g_id, old_msg_id)
                
                if user_map_sent:
                    old_bot = FFBotMsgBiz.get_ff_bot_by_id(user_map_sent.to_bot_id)
                    await old_bot.delete_message(chat_id=g_id,  message_id=old_msg_id,request_timeout=5)
        except Exception as e:
                log.error(f"handler_user_group_msg error:{msg}, {e}",exc_info=True)

    @staticmethod
    def generate_user_info_message(user_card_info:GUserInfoCard)->str:
        user_info_message = (
            f"用户信息:\n"
            f"- 用户TG昵称: #{user_card_info.nickname}\n"
            f"- 用户TG用户名: #{user_card_info.username}\n"
            f"- 用户TGID: #{user_card_info.tg_id}\n"
            f"- 幻梦注册时间: {user_card_info.reg_time}\n"
            f"- 幻梦uid: #{user_card_info.user_id}\n"
            f"- 用户在群里的累计天数: {user_card_info.join_group_days}\n"
            f"- 用户在幻梦的累计充值金额: {user_card_info.total_fee}\n"
            f"- 用户今日最近5条消息:\n"
        )
        for message in user_card_info.recent_messages:
            user_info_message += f"-{message}\n"
        return user_info_message
    
    @staticmethod
    def generate_edit_forward_message_(user_card_info:GUserInfoCard,gap_sec:int)->str:
        user_info_message = (
            f"用户编辑了了消息:\n"
            f"- 用户TG昵称: #{user_card_info.nickname}\n"
            f"- 用户TG用户名: #{user_card_info.username}\n"
            f"- 用户TGID: #{user_card_info.tg_id}\n"
            f"- 幻梦注册时间: {user_card_info.reg_time.replace(tzinfo=pytz.timezone("Asia/Shanghai")).strftime("%Y-%m-%d %H:%M:%S")}\n"
            f"- 幻梦uid: #{user_card_info.user_id}\n"
            f"- 用户在群里的累计天数: {user_card_info.join_group_days}\n"
            f"- 用户在幻梦的累计充值金额: {user_card_info.total_fee}\n"
            f"- 消息间隔时间:{round(gap_sec/60)}分钟\n"
        )
        return user_info_message
   
    @classmethod
    async def get_user_card_info_and_key_markup(cls,tg_id:int)->tuple[str,InlineKeyboardMarkup]:
        
        user_map_info = await TgGroupUserMapBiz.get_tg_user_map(tg_id)
        
        
        if user_map_info is None:
            
            user_map_info = TgGroupUserMap(tg_id=tg_id,f_group_id=0,f_bot_id=0,tg_nickname="群内消息未知",tg_username="未知",created_at=default_reg_time)
        user_card_info = await cls.get_guser_info_card(user_map_info, {}) 
        
        user_card_info_str = cls.generate_user_info_message(user_card_info) # type: ignore
        u_info_keybord_markup = cls.gen_u_info_keybord_markup(tg_id)
        
        return user_card_info_str,u_info_keybord_markup
    
    
    @classmethod
    async def get_edit_card_info_str(cls,tg_id:int,gap_sec:int)->str:
        
        user_map_info = await TgGroupUserMapBiz.get_tg_user_map(tg_id)
        
        
        if user_map_info is None:
            
            user_map_info = TgGroupUserMap(tg_id=tg_id,f_group_id=0,f_bot_id=0,tg_nickname="群内消息未知",tg_username="未知",created_at=default_reg_time)
        user_card_info = await cls.get_guser_info_card(user_map_info, {}) 
        
        user_card_info_str = cls.generate_edit_forward_message_(user_card_info,gap_sec)  # type: ignore
        
        return user_card_info_str
    
    
    
    @classmethod
    async def get_u_recent_msg_str(cls,tg_id:int,days_cx:int)->str:

        
        messages = await TgGroupMessageBiz.query_latest_n_days_messages(tg_id, days_cx)
        
        if not messages:
            return "暂无消息"
        
        fromt_group_msgs = cls.fromt_group_msgs(messages)
        return "\n".join(fromt_group_msgs)        
        
    @staticmethod
    def gen_u_info_keybord_markup(tg_id :int) -> InlineKeyboardMarkup:
        # 生成内联键盘按钮
        inline_kb = InlineKeyboardBuilder()

        msg_cx_kb = InlineKeyboardBuilder()
        msg_cx_kb.add(
                InlineKeyboardButton(text="发言查询", callback_data='choose_next_button'),
                InlineKeyboardButton(text="今日发言", callback_data=f'{tg_id}:today_msg_cx'),
                InlineKeyboardButton(text="7日发言", callback_data=f'{tg_id}:7day_msg_cx'),
            )
        msg_cx_kb.adjust(1,2)
        
        d_msg_kb = InlineKeyboardBuilder()
        d_msg_kb.add(
            InlineKeyboardButton(text='🗑删除消息', callback_data=f'{tg_id}'),
            InlineKeyboardButton(text='立即删除所有消息', callback_data=f'{tg_id}:all_msg_del'),
            InlineKeyboardButton(text='立即删除所有图片', callback_data=f'{tg_id}:all_img_msg_del')
            # InlineKeyboardButton(text='10分钟后删除所有图片', callback_data=f'{tg_id}:10_min_img_msg_del'),
            # InlineKeyboardButton(text='30分钟后删除所有图片', callback_data=f'{tg_id}:30_min_img_msg_del')
            
        )
        d_msg_kb.adjust(1,2)
        forbid_kb = InlineKeyboardBuilder()
        forbid_kb.add(
            InlineKeyboardButton(text='🔞禁言', callback_data=f'{tg_id}:mute'),
            InlineKeyboardButton(text='禁言5分钟', callback_data=f'{tg_id}:5min_msg_forbid'),
            InlineKeyboardButton(text='禁言10分钟', callback_data=f'{tg_id}:10min_msg_forbid'),
            InlineKeyboardButton(text='禁言30分钟', callback_data=f'{tg_id}:30min_msg_forbid'),
            InlineKeyboardButton(text='禁言1小时', callback_data=f'{tg_id}:1hour_msg_forbid'),
        )
        forbid_kb.adjust(1,2)
        kick_ban_kb = InlineKeyboardBuilder()
        kick_ban_kb.add(
            InlineKeyboardButton(text='‼️踢出&封锁‼️', callback_data=f'{tg_id}'),
            InlineKeyboardButton(text='🚷踢出', callback_data=f'{tg_id}:now_kick'),
            InlineKeyboardButton(text='💣封锁', callback_data=f'{tg_id}:forever_ban')
        )
        kick_ban_kb.adjust(1,2)
        
        un_mute_ban_kb = InlineKeyboardBuilder()
        un_mute_ban_kb.add(
            InlineKeyboardButton(text='🔓解除禁言', callback_data=f'{tg_id}:unmute'),
            InlineKeyboardButton(text='🔓解封', callback_data=f'{tg_id}:unban')
        )
        un_mute_ban_kb.adjust(1,1)
        inline_kb.attach(msg_cx_kb)
        inline_kb.attach(d_msg_kb).attach(forbid_kb).attach(kick_ban_kb).attach(un_mute_ban_kb)
        return inline_kb.as_markup()

    @classmethod
    async def process_group_edited_msg(cls,group_id:int,msg_id:int,tg_id:int,msg_content:TgMsgContentBO):
        
        log.info(f"process_group_edited_msg: {group_id} {msg_id},{tg_id} {msg_content}")
        # 获取群组的消息
        group_msg_list = await TgGroupMessage.filter(group_id=group_id,message_id=msg_id).all().order_by('created_at').limit(2)
        
        tg_msg_bo_list = []
        for group_msg in group_msg_list:
            # 更新消息内容
            raw_msg_content = group_msg.raw_data
            
            raw_message = types.Message.model_validate_json(raw_msg_content)
            
            bot = await FFBotMsgBiz.get_g_map_bot(group_id)
            tg_msg_bo = await extract_bot_msg_details(raw_message,bot_f=bot) # type: ignore
            tg_msg_bo_list.append(tg_msg_bo)

        # 获取用户信息
        u_card_info = await cls.get_edit_card_info_str(tg_id,msg_content.gap_time_sec)
        try:
            # 发送消息到群组
            await FFBotMsgBiz.forward_edited_group_msg(group_id,msg_id,u_card_info,tg_msg_bo_list)
        except Exception as e:
            log.error(f"process_group_edited_msg error:{e}",exc_info=True)
        




        
     