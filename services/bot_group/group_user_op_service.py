from collections import defaultdict
from enum import Enum
import logging
import os
import time
from typing import Dict, List
from aiogram import Bot, types
from aiogram.exceptions import TelegramAPIError
from dotenv import load_dotenv



from common import loggers
from common.models.ai_bot_admin.ims_moderation_common import SpamRubbishType
from common.models.ai_bot_admin.user_card_admin_bean import OpAction, OpActionType
from persistence.models.models_bot_group import BotMessageTypeEnum, TgGroupMessage
from services.bot_group.biz.tg_group_user_msg_biz import TgGroupMessageBiz, TgGroupUserMapBiz,FFBotMsgBiz

from services.bot_group.biz.op_log_biz import OpLogBiz

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s (%(filename)s:%(lineno)d) [%(levelname)s] %(message)s",handlers=[logging.StreamHandler(),loggers.local_bot_help_handler])

log = logging.getLogger(__name__)


load_dotenv()
bot_tokes = os.getenv('GROUP_BOT_TOKENS').split(',')



class GroupUserOpService:
    def __init__(self, bot_tokens: list[str]):
        self.bots:dict[int,Bot] = {}
        for bot_token in bot_tokens:
            bot_id, token = bot_token.split(':')
            self.bots[int(bot_id)] = Bot(bot_token)

    def get_bot(self, bot_id: int) -> Bot:
        return self.bots.get(bot_id)  # type: ignore

    @staticmethod
    def group_tg_msgs(messages: List[TgGroupMessage]) ->  Dict[int, Dict[int, List[int]]]:
        """
        按照 group_id 分组消息，并将消息 ID 组成一个列表

        :param messages: 包含消息字典的列表，每个字典包含 'group_id' 和 'message_id'
        :return: 按照 group_id 分组的消息 ID 列表
        """
        grouped_msgs = defaultdict(lambda: defaultdict(list))
    
        for message in messages:
            bot_id = message.bot_id
            group_id = message.group_id
            message_id = message.message_id
            grouped_msgs[bot_id][group_id].append(message_id)

        return grouped_msgs


    async def exec_op_action(self, action: OpAction):
        
        await OpLogBiz.save_op_log(action)
        
        if action.action_type == OpActionType.ClEAR_ALERT:
            await self.clear_alert_op(action.tg_id,action.group_id, action.action_type)
        
        if action.action_type == OpActionType.DELETE_MSG:
            await self.del_message_id_op(action.group_id, action.tg_id, action.message_id)
        
        #删除用户消息操作
        if action.action_type in [OpActionType.DELETE_ALL, OpActionType.DELETE_ALL_IMAGES, OpActionType.DELETE_IMAGES_10_MIN, OpActionType.DELETE_IMAGES_30_MIN]:
            
            await self.del_message_op(action.tg_id, action.action_type)
            
        elif action.action_type in [OpActionType.FORBID_5_MIN, OpActionType.FORBID_10_MIN, OpActionType.FORBID_30_MIN, OpActionType.FORBID_1_HOUR]:
            await self.forbid_user_op(action.tg_id, action.action_type) 
            
        elif action.action_type in OpActionType.all_kick_actions():
            await self.ban_user_op(action.tg_id, action.action_type)
        elif action.action_type == OpActionType.BAN:
            await self.ban_user_op(action.tg_id, action.action_type)
            
            #删除用户消息操作,封禁的时候
            await self.del_message_op(action.tg_id, OpActionType.DELETE_ALL)
        elif action.action_type == OpActionType.UNMUTE:
            await self.forbid_user_op(action.tg_id, OpActionType.UNMUTE)
        elif action.action_type == OpActionType.UNBAN:
            await self.unban_user_op(action.tg_id, OpActionType.UNBAN)
            
            #解封的时候同事clear_alert
            await self.clear_alert_op(action.tg_id,action.group_id, action.action_type)
            
        

        tg_group_user = await TgGroupUserMapBiz.get_tg_user_map(action.tg_id)
        
        if tg_group_user is not None and tg_group_user.tg_nickname is not None:
            action.tg_nick_name = tg_group_user.tg_nickname
        await FFBotMsgBiz.send_op_msg(action)
    
    async def del_message_id_op(self, group_id:int,tg_id:int, message_id:int):
        del_tg_msg = await TgGroupMessageBiz.query_user_message_by_id(group_id, message_id)
        
        if del_tg_msg is not None:
            await self.delete_user_messages(del_tg_msg.bot_id, group_id, tg_id, [message_id])
            await TgGroupMessageBiz.updae_u_msg_deleted([del_tg_msg])
    
    async def clear_alert_op(self, tg_id:int,group_id:int, action_type: OpActionType):
        #清除警告
        await TgGroupMessageBiz.clear_user_spam_behavior_cnt(tg_id=tg_id,group_id=group_id,rubbish_type=SpamRubbishType.PHOTO_SPAM,cnt=action_type.duration)
        

    async def del_message_op(self,tg_id:int,action_type: OpActionType):
        if action_type == OpActionType.DELETE_ALL:
            ## delete all messages 48 hours内的支持删除
            del_tg_msgs = await TgGroupMessageBiz.query_latest_n_days_messages(tg_id, 2)
            
            del_group_msgs = self.group_tg_msgs(del_tg_msgs)
            
            for bot_id, del_message_ids in del_group_msgs.items():
                for group_id, message_ids in del_message_ids.items():
                    await self.delete_user_messages(bot_id, group_id, tg_id, message_ids)
            
            await TgGroupMessageBiz.updae_u_msg_deleted(del_tg_msgs)
        elif action_type in [OpActionType.DELETE_ALL_IMAGES, OpActionType.DELETE_IMAGES_10_MIN, OpActionType.DELETE_IMAGES_30_MIN]:
            del_tg_msgs = await TgGroupMessageBiz.query_user_n_days_messages_by_type(tg_id, 2, BotMessageTypeEnum.PHOTO)
            
            del_group_msgs = self.group_tg_msgs(del_tg_msgs)
            
            for bot_id, del_message_ids in del_group_msgs.items():
                for group_id, message_ids in del_message_ids.items():
                    await self.delete_user_messages(bot_id, group_id, tg_id, message_ids)
        
            await TgGroupMessageBiz.updae_u_msg_deleted(del_tg_msgs)
            
    
    async def forbid_user_op(self, tg_id:int, action_type: OpActionType):
        
        tg_group_user_list = await TgGroupUserMapBiz.get_all_tg_user_map(tg_id)
        
        for tg_group_user in tg_group_user_list:
            bot_id = tg_group_user.f_bot_id
            group_id = tg_group_user.f_group_id

            await self.mute_user(bot_id, group_id, tg_id, action_type.duration)


    async def ban_user_op(self, tg_id:int, action_type: OpActionType):
        tg_group_user_list = await TgGroupUserMapBiz.get_all_tg_user_map(tg_id)
        
        for tg_group_user in tg_group_user_list:
            bot_id = tg_group_user.f_bot_id
            group_id = tg_group_user.f_group_id
            
            await self.kick_ban_user(bot_id, group_id, tg_id, action_type.duration)
    
    async def unban_user_op(self, tg_id:int, action_type: OpActionType):
        tg_group_user_list = await TgGroupUserMapBiz.get_all_tg_user_map(tg_id)
        
        for tg_group_user in tg_group_user_list:
            bot_id = tg_group_user.f_bot_id
            group_id = tg_group_user.f_group_id
            
            await self.unban_user(bot_id, group_id, tg_id)      
    
    async def unban_user(self, bot_id: int, chat_id: int, user_id: int):
        log.info(f"unban_user: {bot_id},{chat_id},{user_id}")
        bot = self.get_bot(bot_id)
        try:
            await bot.unban_chat_member(chat_id, user_id,only_if_banned=True)
        except TelegramAPIError as e:
            # 处理异常
            log.error(f"Error unban user {user_id} in chat {chat_id}: {e}")
        except Exception as e:
            log.error(f"Error unban user {user_id} in chat {chat_id}: {e}")
    
    async def mute_user(self, bot_id: int, chat_id: int, user_id: int, until_date: int):
        log.info(f"mute_user: {bot_id},{chat_id},{user_id},{until_date}")
        # unix seconds
        
        bot = self.get_bot(bot_id)
        try:
    
            if until_date == -1:
                permissions = types.ChatPermissions(
                    can_send_messages=True,
                    can_send_photos=True,
                    can_send_media_messages=True,
                    can_send_other_messages=True,
                    can_send_audios=True,
                    can_send_documents=True,
                    can_send_videos=True,
                    can_send_stickers=True,
                    can_send_games=True,
                    can_send_inline=True,
                    can_send_animations=True,
                    can_send_voice_notes=True,
                    can_send_contacts=True,
                    can_send_gifs=True
                )
                await bot.restrict_chat_member(chat_id, user_id, permissions=permissions, request_timeout=5)
            else:
                until_date = until_date + int(time.time())
                permissions = types.ChatPermissions(
                    can_send_messages=False,
                )
                await bot.restrict_chat_member(chat_id, user_id, permissions=permissions, until_date=until_date, request_timeout=5)
        except TelegramAPIError as e:
            # 处理异常
            log.error(f"Error muting user ,retry {user_id} in chat {chat_id}: {e}")
            await bot.restrict_chat_member(chat_id, user_id, permissions=permissions, until_date=until_date ,request_timeout=5)
        except Exception as e:
            log.error(f"Error muting user {user_id} in chat {chat_id}: {e}")

    #until_date 为0时，永久剔除,60s 为剔除
    async def kick_ban_user(self, bot_id: int, chat_id: int, user_id: int,until_date: int):
        
        log.info(f"kick_ban_user: {bot_id},{chat_id},{user_id},{until_date}")
        bot = self.get_bot(bot_id)
        
        until_date = until_date + int(time.time())
        try:
            await bot.ban_chat_member(chat_id, user_id,until_date=until_date)
        except TelegramAPIError as e:
            # 处理异常
            log.error(f"Error kicking user ,retry {user_id} from chat {chat_id}: {e}")
            
            await bot.ban_chat_member(chat_id, user_id, until_date=until_date ,request_timeout=5)
        except Exception as e:
            log.error(f"Error kicking user {user_id} from chat {chat_id}: {e}")


    async def delete_message(self, bot_id: int, chat_id: int, message_id: int):
        bot = self.get_bot(bot_id)
        try:
            await bot.delete_message(chat_id, message_id)
        except TelegramAPIError as e:
            # 处理异常 重试
            log.error(f"Error deleting message {message_id} in chat {chat_id}: {e}")
            
        except Exception as e:
            log.error(f"Error deleting message {message_id} in chat {chat_id}: {e}")

    async def delete_user_messages(self, bot_id: int, chat_id: int, user_id: int,message_ids: list[int]):
        bot = self.get_bot(bot_id)
        
        if  not bot:
            log.warning(f" deleting messages from user {user_id} in group chat {chat_id}: bot not found{bot_id}")
            return
        
        log.info(f"delete_user_messages: {bot_id},{chat_id},{user_id},{message_ids}")
        try:
            msg_len = len(message_ids)
            if msg_len > 100:
                for i in range(0, msg_len, 100):
                    await bot.delete_messages(chat_id, message_ids[i:i + 100])
            else:
                await bot.delete_messages(chat_id, message_ids)
        except TelegramAPIError as e:
            # 处理异常
            print(f"Error deleting messages from user {user_id} in chat {chat_id}: {e}")




# 实例初始化
group_user_op_service =GroupUserOpService(bot_tokes)