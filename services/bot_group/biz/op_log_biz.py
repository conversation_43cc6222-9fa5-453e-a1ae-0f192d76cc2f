

import pytz
from common.models.ai_bot_admin.user_card_admin_bean import OpAction

from  persistence.models.models_bot_group import OpLog,JoinGroupUserProcessHistory

class OpLogBiz:
    
    @classmethod
    async def save_op_log(cls,op_action:OpAction,op_type:int=0):
        
        await OpLog.create(op_type=op_type,tg_id=op_action.tg_id,group_id=op_action.group_id,op_action=op_action.model_dump_json(),op_action_type=op_action.action_type.code) 
    @classmethod
    async def get_user_join_group_history_log(cls, tg_id: int) -> str:
        """
        获取用户加入群组的处理历史log
        """
        history_list = await JoinGroupUserProcessHistory.filter(tg_id=tg_id).order_by("-created_at").limit(10)
        
        if not history_list:
            return ""

        return "\n".join([f"{history.created_at.astimezone(tz=pytz.timezone('Asia/Shanghai'))}: {history.process_reason}" for history in history_list])