import asyncio
from datetime import date, datetime, timedelta
import logging
import os
import random

from aiogram import Bo<PERSON>
from dotenv import load_dotenv
import pytz
from common.models.ai_bot_admin.group_msg_bean import TgMsgContentBO
from common.models.ai_bot_admin.user_card_admin_bean import OpAction
from persistence.models.models_bot_group import (
    BotGroupMap,
    BotMessageTypeEnum,
    DailyShareUserStats,
    DailyUserMessageStats,
    TgGroupMessage,
    TgGroupUserMap,
    TgGroupMsgSentMap,
    TgGroupMessage,
    HoursUserMessageStats,
    DailyInviteUserStats,
    AutoSendMsgHistory,
    UserSpamBehavior,
    TgGroupForwardMessage,
)
from persistence.models.models import (
    RechargeStatusEnum,
    TelegramUser,
    User,
    Account,
    RechargeOrder,
    Invitation,
    UserRoleShare,
)

from aiogram.types import ContentType, InlineKeyboardMarkup
from aiogram.exceptions import TelegramBadRequest

from common.models.ai_bot_admin.forward_group_msg import UserInfoBase
from tortoise.transactions import in_transaction
from tortoise.exceptions import IntegrityError, DoesNotExist
from tortoise.functions import Count, Sum

from common.models.ai_bot_admin.ims_moderation_common import MediaModerateConstants


load_dotenv()
logger = logging.getLogger(__name__)

# Todo 改成配置的形式
# TO_GROUP_ID = -*************
# TO_TOPIC_ID = 801
# IMAGE_TO_TOPIC_ID = 802
TO_GROUP_ID = int(os.getenv("TO_GROUP_ID", 0))
TO_TOPIC_ID = int(os.getenv("TO_TOPIC_ID", 0))
IMAGE_TO_TOPIC_ID = int(os.getenv("IMAGE_TO_TOPIC_ID", 0))
MSG_STAT_TO_TOPIC_ID = int(os.getenv("MSG_STAT_TO_TOPIC_ID", 0))
NORMAL_IMAGE_TO_TOPIC_ID = int(os.getenv("NORMAL_IMAGE_TO_TOPIC_ID", 0))
EDITE_MSG_TO_TOPIC_ID = int(os.getenv("EDITE_MSG_TO_TOPIC_ID", 0))
WARN_MSG_TO_TOPIC_ID = int(os.getenv("WARN_MSG_TO_TOPIC_ID", 0))

FORWARD_CHANNEL_IDS = [-1002312293664]


# TO_BOT_TOKEN = "7706615835:AAHFiw4pQ_-cGK8Qffb4_rlY9dwqimazAMs"


FF_BOT_TOKENS = os.getenv("FF_BOT_TOKENS").split(",")

ff_bots = [Bot(token=x) for x in FF_BOT_TOKENS]


def get_ff_bot():
    return ff_bots[random.randint(0, len(ff_bots) - 1)]


class TgGroupMessageBiz:

    @classmethod
    async def query_user_message_by_id(
        cls, group_id: int, message_id: int
    ) -> TgGroupMessage | None:
        return await TgGroupMessage.filter(
            group_id=group_id, message_id=message_id
        ).first()

    @classmethod
    async def query_latest_n_days_messages(
        cls, user_id: int, days: int
    ) -> list[TgGroupMessage]:
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        days_ago = today - timedelta(days=days)
        messages = (
            await TgGroupMessage.filter(user_id=user_id, created_at__gte=days_ago)
            .order_by("-created_at")
            .limit(500)
            .all()
        )
        return messages

    @classmethod
    async def updae_u_msg_deleted(cls, tg_g_msg_list: list[TgGroupMessage]):
        for tg_g_msg in tg_g_msg_list:
            tg_g_msg.is_deleted = True

        if tg_g_msg_list and len(tg_g_msg_list) > 0:
            await TgGroupMessage.bulk_update(tg_g_msg_list, fields=["is_deleted"])

    @classmethod
    async def query_user_n_days_messages_by_type(
        cls, user_id: int, days: int, message_type: BotMessageTypeEnum
    ) -> list[TgGroupMessage]:
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        days_ago = today - timedelta(days=days)
        messages = await TgGroupMessage.filter(
            user_id=user_id, created_at__gte=days_ago, message_type=message_type
        ).all()
        return messages

    @classmethod
    async def query_latest_5_today_messages(cls, user_id: int):
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        messages = (
            await TgGroupMessage.filter(user_id=user_id, created_at__gte=today_start)
            .order_by("-created_at")
            .limit(5)
            .all()
        )
        return messages

    @classmethod
    async def query_today_messages(cls, user_id: int) -> list[TgGroupMessage]:
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        messages = (
            await TgGroupMessage.filter(user_id=user_id, created_at__gte=today_start)
            .order_by("-created_at")
            .all()
        )
        return messages

    @classmethod
    async def query_last_7_days_messages(cls, user_id: int):
        today = datetime.now()
        seven_days_ago = today - timedelta(days=7)
        messages = await TgGroupMessage.filter(
            user_id=user_id, created_at__gte=seven_days_ago
        ).all()
        return messages

    @classmethod
    async def clear_user_spam_behavior_cnt(
        cls, tg_id: int, group_id: int, rubbish_type: int, cnt: int
    ):
        logger.info(
            f"clear_user_spam_behavior_cnt: tg_id={tg_id},group_id={group_id},rubbish_type={rubbish_type},cnt={cnt}"
        )
        u_spam_cnt = await UserSpamBehavior.get_or_none(
            user_id=tg_id, group_id=group_id, rubbish_type=rubbish_type
        )
        if u_spam_cnt:
            u_spam_cnt.rubbbish_cnt = 0
            await u_spam_cnt.save(update_fields=["rubbbish_cnt"])


class TgGroupUserMapBiz:
    @classmethod
    async def query_user_info_base(cls, tg_id: int) -> UserInfoBase | None:
        tg_user = await TelegramUser.get_or_none(tg_id=tg_id)

        if tg_user:
            nickname = tg_user.first_name + " " + tg_user.last_name
            return UserInfoBase(
                tg_id=tg_user.tg_id,
                nickname=nickname,
                reg_time=tg_user.created_at,
                user_id=tg_user.uid,
            )
        return None

    # 获取用户的charge 单位元
    @classmethod
    async def get_user_total_charge(cls, user_id: int) -> int:

        pay_order = await RechargeOrder.filter(
            user_id=user_id, status=RechargeStatusEnum.SUCCEED, pay_fee__gt=0
        ).all()

        total_fee = 0

        if pay_order:
            total_fee = sum(
                [
                    (
                        order.pay_fee * 7.5
                        if order.pay_currency == "usd"
                        else order.pay_fee
                    )
                    for order in pay_order
                ]
            )
        return int(total_fee / 100000)

    # 获取用户加入群组的天数
    @classmethod
    async def get_user_join_group_days(cls, tg_id: int) -> int:
        return 10

    @classmethod
    async def get_or_create_tguser_map(
        cls, tg_id: int, group_id: int, bot_id: int, tg_username: str, tg_nickname: str
    ) -> tuple[TgGroupUserMap, bool]:
        async with in_transaction() as connection:
            try:
                tguser_map = await TgGroupUserMap.get(tg_id=tg_id, f_group_id=group_id)
                created = False
            except DoesNotExist:
                try:
                    tguser_map = await TgGroupUserMap.create(
                        tg_id=tg_id,
                        f_group_id=group_id,
                        f_bot_id=bot_id,
                        tg_nickname=tg_nickname,
                        tg_username=tg_username,
                    )
                    created = True
                except IntegrityError:
                    tguser_map = await TgGroupUserMap.create(
                        tg_id=tg_id,
                        f_group_id=group_id,
                        f_bot_id=bot_id,
                        tg_nickname=tg_nickname,
                        tg_username=tg_username,
                    )
                    created = False
            return tguser_map, created

    @classmethod
    async def get_tg_user_map(cls, tg_id: int) -> TgGroupUserMap | None:
        return await TgGroupUserMap.filter(tg_id=tg_id).first()

    @classmethod
    async def get_all_tg_user_map(cls, tg_id: int) -> list[TgGroupUserMap]:
        return await TgGroupUserMap.filter(tg_id=tg_id).all()

    @classmethod
    async def update_tg_group_user_map(cls, tg_user_map: TgGroupUserMap):
        await tg_user_map.save()

    @classmethod
    async def add_group_user_map_sent(
        cls, tg_id: int, to_group_id: int, to_bot_id: int, to_message_id: int
    ):
        await TgGroupMsgSentMap.create(
            tg_id=tg_id,
            to_group_id=to_group_id,
            to_bot_id=to_bot_id,
            to_message_id=to_message_id,
        )

    @classmethod
    async def get_group_user_map_sent(cls, to_group_id: int, to_message_id: int):
        return await TgGroupMsgSentMap.filter(
            to_group_id=to_group_id, to_message_id=to_message_id
        ).first()


class FFBotMsgBiz:

    @staticmethod
    def get_ff_bot() -> Bot:
        return ff_bots[random.randint(0, len(ff_bots) - 1)]

    @staticmethod
    def get_ff_bot_by_id(bot_id: int) -> Bot:  # type: ignore
        for bot in ff_bots:
            if bot_id == bot.id:
                return bot

    @staticmethod
    def get_bot_by_token(bot_token: str) -> Bot:

        return Bot(token=bot_token)

    @staticmethod
    def get_group_and_topic_id():
        return TO_GROUP_ID, TO_TOPIC_ID

    @staticmethod
    def get_msg_stat_topic_id():
        return TO_GROUP_ID, MSG_STAT_TO_TOPIC_ID

    @staticmethod
    def generate_op_message(op_action: OpAction) -> str:
        tg_user_name = f"用户TG昵称:{op_action.tg_nick_name}\n"
        return f"{tg_user_name}TGID:{op_action.tg_id}被{op_action.operator_nickname} {op_action.action_type.description}了"

    @classmethod
    async def send_op_msg(cls, op_action: OpAction):
        message = cls.generate_op_message(op_action)
        await cls.send_message(TO_GROUP_ID, TO_TOPIC_ID, message)

    @classmethod
    async def send_message(
        cls,
        chat_id: int,
        message_thread_id: int,
        message: str,
        parse_mode: str = "HTML",
    ):
        bot = get_ff_bot()
        logger.info(f"send_message: chat_id={chat_id}, message={message}")
        await bot.send_message(
            chat_id=chat_id,
            message_thread_id=message_thread_id,
            text=message,
            parse_mode=parse_mode,
        )

    @classmethod
    async def send_group_warn_msg(cls, group_id: int, msg_id: int, warn_msg: str):
        logger.info(f"send_group_warn_msg: group_id={group_id}, msg_id={msg_id}")
        bot = await cls.get_g_map_bot(group_id)
        if bot:
            try:
                group_msg = await TgGroupMessage.filter(
                    group_id=group_id, message_id=msg_id
                ).first()

                user_name = (
                    f'<a href="https://t.me/{group_msg.user_name}">{group_msg.user_name}</a>'
                    if group_msg.user_name
                    else ""
                )

                warn_msg = f"{user_name}[{group_msg.user_id}]发送垃圾消息！\n"
                message_thread_id = (
                    group_msg.message_thread_id if group_msg.message_thread_id else 0
                )
                await bot.send_message(
                    chat_id=group_id,
                    message_thread_id=message_thread_id,
                    text=warn_msg,
                    parse_mode="HTML",
                )

                # 同时转发到内部群
            except Exception as e:
                logger.error(f"send_group_warn_msg error:{e}")
            finally:
                await bot.session.close()

    @staticmethod
    async def del_tg_msg_delay(
        bot: Bot, group_id: int, msg_id: int, delay_sec: int = 60
    ):
        try:
            await asyncio.sleep(delay_sec)
            await bot.delete_message(chat_id=group_id, message_id=msg_id)
        except Exception as e:
            logger.error(f"del_tg_msg_by_id error:{msg_id},{e}")
        finally:
            await bot.session.close()

    @classmethod
    async def send_to_group_msg_by_group_id(
        cls,
        group_id: int,
        msg_text: str,
        mark_up: InlineKeyboardMarkup,
        message_thread_id: int = 0,
    ):
        logger.info(
            f"send_to_group_msg: group_id={group_id}, message_thread_id={message_thread_id}, msg_text={msg_text}"
        )
        bot = await cls.get_g_map_bot(group_id)
        if bot:
            try:

                msg_sent = await bot.send_message(
                    chat_id=group_id,
                    message_thread_id=message_thread_id,
                    text=msg_text,
                    reply_markup=mark_up,
                    parse_mode="HTML",
                )

                # asyncio.create_task(cls.del_tg_msg_delay(bot,group_id,msg_sent.message_id))

            except Exception as e:
                logger.error(f"send_group_warn_msg error:{e}")
            finally:
                await bot.session.close()
        else:
            logger.warning(f"send_to_group_msg,group_id:{group_id} bot_token not found")

    @classmethod
    async def forward_warn_msg_ganbu_group(
        cls, card_txt: str, card_mark_up: InlineKeyboardMarkup
    ):
        bot = get_ff_bot()
        logger.info(
            f"forward_msg_ganbu_group: group_id={TO_GROUP_ID},topic:{WARN_MSG_TO_TOPIC_ID}, message={card_txt}"
        )
        try:
            await bot.send_message(
                chat_id=TO_GROUP_ID,
                text=card_txt,
                message_thread_id=WARN_MSG_TO_TOPIC_ID,
                reply_markup=card_mark_up,
                parse_mode="HTML",
            )
        except Exception as e:
            logger.error(f"forward_msg_ganbu_group error:{e}")
        finally:
            await bot.session.close()

    @classmethod
    async def send_image_check_photo_msg(cls, msg_check: dict):

        logger.info(f"send_image_check_photo_msg: msg={msg_check}")
        bot = get_ff_bot()
        msg_url = f"https://t.me/{msg_check['group_username']}/{msg_check['msg_id']}"

        message = (
            f"[消息链接]({msg_url})\n"
            f"<b>图片检测结果</b>:\n"
            f"from用户: #{msg_check['username']}\n"
            f"tg_id: #{msg_check['tg_id']}\n"
            f"<b>检测结果如下</b>:\n"
            f"建议:<b>{msg_check['suggestions']}</b>,标签:<i>{msg_check['check_label']}</i>\n"
            # f'<a href="{msg_check["image_key"]}">点击查看图片</a>'
        )

        image_key = msg_check["image_key"]
        file_type = msg_check["file_type"]
        file_id = msg_check["file_id"]
        message_thread_id = NORMAL_IMAGE_TO_TOPIC_ID
        if msg_check["suggestions"] == MediaModerateConstants.BLOCK:
            logger.info(
                f"check_block_msg: {msg_check['group_id']},{msg_check["msg_id"]}"
            )
            # zhuanfa
            message_thread_id = IMAGE_TO_TOPIC_ID

        try:
            if file_type == "photo":
                sent_msg = await bot.send_photo(
                    chat_id=TO_GROUP_ID,
                    message_thread_id=message_thread_id,
                    photo=image_key,
                    caption=message,
                    parse_mode="HTML",
                )
            elif file_type == "sticker":
                sent_msg = await bot.send_sticker(
                    chat_id=TO_GROUP_ID,
                    message_thread_id=message_thread_id,
                    sticker=file_id,
                )
                await sent_msg.reply(message, parse_mode="HTML")

            elif file_type == "animation":
                sent_msg = await bot.send_animation(
                    chat_id=TO_GROUP_ID,
                    message_thread_id=message_thread_id,
                    animation=image_key,
                    caption=message,
                    parse_mode="HTML",
                )
            elif file_type == "video":
                sent_msg = await bot.send_video(
                    chat_id=TO_GROUP_ID,
                    message_thread_id=message_thread_id,
                    video=image_key,
                    caption=message,
                    parse_mode="HTML",
                )
            else:
                sent_msg = await bot.send_message(
                    chat_id=TO_GROUP_ID,
                    message_thread_id=message_thread_id,
                    text=message,
                    parse_mode="HTML",
                )
        except Exception as e:
            logger.error(f"send_image_check_photo_msg error:{e}")
            sent_msg = await bot.send_message(
                chat_id=TO_GROUP_ID,
                message_thread_id=message_thread_id,
                text=message,
                parse_mode="HTML",
            )

        logger.info(f"send_image_check_photo_msg_end:{sent_msg.message_id}")

    @classmethod
    async def send_image_check_message(cls, msg_check: dict):
        bot = get_ff_bot()
        msg_url = f"https://t.me/{msg_check['group_username']}/{msg_check['msg_id']}"

        message = (
            f"[消息链接]({msg_url})\n"
            f"<b>图片检测结果</b>:\n"
            f"from用户: #{msg_check['username']}\n"
            f"tg_id: #{msg_check['tg_id']}\n"
            f"<b>检测结果如下</b>:\n"
            f"建议:<b>{msg_check['suggestions']}</b>,标签:<i>{msg_check['check_label']}</i>\n"
            f'<a href="{msg_check["image_key"]}">点击查看图片</a>'
        )

        if msg_check["suggestions"] == "Pass" or msg_check["check_label"] == "Ad":
            logger.info(f"ignore  check: msg={message}")
            return
        await bot.send_message(
            TO_GROUP_ID,
            message_thread_id=IMAGE_TO_TOPIC_ID,
            text=message,
            parse_mode="HTML",
        )

        if msg_check["suggestions"] == "Block":
            await cls.del_group_msg(msg_check["group_id"], msg_check["msg_id"])

    @classmethod
    async def del_group_msg(cls, group_id: int, message_id: int) -> bool:
        logger.info(f"del_group_msg,group_id:{group_id},message_id:{message_id}")
        bot = await cls.get_g_map_bot(group_id)
        if bot:
            try:
                return await bot.delete_message(chat_id=group_id, message_id=message_id)
            except Exception as e:
                logger.error(f"del_msg error:{e}")
            finally:
                await bot.session.close()
        else:
            logger.error(f"del_msg,group_id:{group_id} bot_token not found")
        return False

    @classmethod
    async def get_g_map_bot(cls, group_id: int) -> Bot | None:
        bot_g_map = await BotGroupMap.filter(group_id=group_id).first()
        if bot_g_map:
            return Bot(token=bot_g_map.bot_token)
        return None
    
    @classmethod
    async def get_g_map_bot_token(cls, group_id: int) -> str | None:
        bot_g_map = await BotGroupMap.filter(group_id=group_id).first()
        if bot_g_map:
            return bot_g_map.bot_token
        return None
    @classmethod
    async def _send_msg_and_save_history(
        cls,
        send_id: int,
        group_id: int,
        topic_id: int,
        message_dict: dict,
        history_id: int,
        bot: Bot ,
        is_del_pre_msg: bool = False
    ) :
        logger.info(
            f"_send_msg_and_save_history,send_id:{send_id},group_id:{group_id},topic_id:{topic_id},history_id:{history_id}"
        )
        
        sent_msg = None
        if message_dict["msg_type"] == 0:
            sent_msg = await bot.send_message(
                chat_id=group_id,
                message_thread_id=topic_id,
                text=message_dict["msg_content"],
                parse_mode="HTML",
            )
        elif message_dict["msg_type"] == 1:
            image_url = message_dict["image_url"]
            msg_text = message_dict["msg_content"]
            sent_msg = await bot.send_photo(
                chat_id=group_id,
                message_thread_id=topic_id,
                photo=image_url,
                caption=msg_text,
                parse_mode="HTML",
            )
        if is_del_pre_msg:
            last_sent = (
                await AutoSendMsgHistory.filter(
                    auto_send_id=send_id, group_id=group_id
                )
                .exclude(id=history_id)
                .order_by("-created_at")
                .first()
            )
            try:
                if last_sent:
                    logger.info(
                        f"delete_message,group_id:{group_id},message_id:{last_sent.message_id}"
                    )
                    await bot.delete_message(
                        chat_id=group_id, message_id=last_sent.message_id
                    )
            except Exception as e:
                logger.error(f"delete_message error:{e}")
        
        if sent_msg:
            send_result = sent_msg.model_dump_json(
                exclude_none=True, exclude_unset=True, warnings=False
            )
            await AutoSendMsgHistory.filter(id=history_id).update(
                message_id=sent_msg.message_id, send_ack=True, send_result=send_result
            )
        else:
            logger.warning(
                f"send_msg_and_save_history,send_id:{send_id},group_id:{group_id},topic_id:{topic_id} sent_msg is None"
            )


    @classmethod
    async def send_group_msg(
        cls,
        send_id: int,
        group_id: int,
        topic_id: int,
        message_dict: dict,
        is_del_pre_msg: bool = False,
    ):

        logger.info(
            f"send_group_msg,send_id:{send_id},group_id:{group_id},topic_id:{topic_id},message:{message_dict}"
        )

        history = await AutoSendMsgHistory.create(
            auto_send_id=send_id, group_id=group_id, message=message_dict
        )
        bot_g_map = await BotGroupMap.filter(group_id=group_id).first()

        if bot_g_map is None:
            logger.error(f"send_group_msg,group_id:{group_id} not found")
            return
        try:
            bot = await cls.get_g_map_bot(group_id)

            if bot:
                await cls._send_msg_and_save_history(
                    send_id=send_id,
                    group_id=group_id,
                    topic_id=topic_id,
                    message_dict=message_dict,
                    history_id=history.id,
                    bot=bot,  # type: ignore
                    is_del_pre_msg=is_del_pre_msg,
                )

        except TelegramBadRequest as e:
            if "TOPIC_CLOSED" in str(e):
                logger.error(f"目标主题已关闭，无法发送消息: {group_id}, {topic_id}")
                # 重新打开topic 并发送
                await cls.reopen_topic_and_send(group_id, topic_id, history.id, message_dict)
            else:
                logger.error(f"send_msg TelegramBadRequest: {e},{group_id},{topic_id}", exc_info=True)

        except Exception as e:
            logger.error(f"send_group_msg error:{e}", exc_info=True)

    @classmethod
    async def reopen_topic_and_send(
        cls, group_id: int, topic_id: int, history_id:int,message_dict: dict
    ):
        logger.info(
            f"reopen_topic_and_send,group_id:{group_id},topic_id:{topic_id},message_dict:{message_dict}"
        )
        try:
            bot = await cls.get_g_map_bot(group_id)
            # 重新打开主题
            open_r = await bot.reopen_forum_topic(chat_id=group_id, message_thread_id=topic_id)
            if not open_r:
                logger.error(
                    f"reopen_topic_and_send,group_id:{group_id},topic_id:{topic_id} reopen failed"
                )
                return
            else:
                logger.info(
                    f"reopen_topic_and_send,group_id:{group_id},topic_id:{topic_id} reopen success"
                )
                await cls._send_msg_and_save_history(
                    send_id=message_dict.get("send_id", 0),
                    group_id=group_id,
                    topic_id=topic_id,
                    message_dict=message_dict,
                    history_id=history_id,
                    bot=bot, # type: ignore
                    is_del_pre_msg=message_dict.get("is_del_pre_msg", False),
                )
                # 关闭主题
                await bot.close_forum_topic(
                    chat_id=group_id, message_thread_id=topic_id
                )
        except Exception as e:
                logger.error(f"reopen_topic_and_send error:{e}", exc_info=True)

    
    @classmethod
    async def forward_edited_group_msg(
        cls,
        group_id: int,
        msg_id: int,
        user_text: str,
        edit_msg_list: list[TgMsgContentBO],
    ):
        bot = cls.get_ff_bot()
        if bot:

            try:
                topic_id = EDITE_MSG_TO_TOPIC_ID
                await bot.send_message(
                    chat_id=TO_GROUP_ID,
                    message_thread_id=topic_id,
                    text=user_text,
                    parse_mode="HTML",
                )

                msg_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                i = 0
                for edit_msg in edit_msg_list:
                    try:
                        msg_type = edit_msg.msg_type
                        msg_date = (
                            edit_msg.date.replace(tzinfo=pytz.utc)
                            .astimezone(tz=pytz.timezone("Asia/Shanghai"))
                            .strftime("%Y-%m-%d %H:%M:%S")
                        )
                        if i == 0:
                            msg_text = f"第一次发送的历史消息\n发送时间:{msg_date}\ntext:{edit_msg.msg_text}"
                        else:
                            if edit_msg.edit_date:
                                msg_date = datetime.fromtimestamp(
                                    edit_msg.edit_date
                                ).strftime("%Y-%m-%d %H:%M:%S")
                            msg_text = f"第{i}次重新编辑的消息\n发送时间:{msg_date}\ntext:{edit_msg.msg_text}"
                        i += 1
                        logger.info(
                            f"forward_edited_group_msg,group_id:{TO_GROUP_ID},msg_type:{msg_type},edit_msg:{edit_msg}"
                        )
                        if msg_type == ContentType.PHOTO and edit_msg.file_url:
                            await bot.send_photo(
                                chat_id=TO_GROUP_ID,
                                message_thread_id=topic_id,
                                photo=edit_msg.file_url,
                                caption=msg_text,
                                parse_mode="HTML",
                            )
                        elif msg_type == ContentType.ANIMATION and edit_msg.file_id:
                            await bot.send_animation(
                                chat_id=TO_GROUP_ID,
                                message_thread_id=topic_id,
                                animation=edit_msg.file_id,
                                caption=msg_text,
                                parse_mode="HTML",
                            )
                        elif msg_type == ContentType.VIDEO and edit_msg.file_id:
                            await bot.send_video(
                                chat_id=TO_GROUP_ID,
                                message_thread_id=topic_id,
                                video=edit_msg.file_id,
                                caption=msg_text,
                                parse_mode="HTML",
                            )
                        elif msg_type == ContentType.STICKER and edit_msg.file_id:
                            await bot.send_sticker(
                                chat_id=TO_GROUP_ID,
                                message_thread_id=topic_id,
                                sticker=edit_msg.file_id,
                            )
                        elif msg_type == ContentType.TEXT:
                            await bot.send_message(chat_id=TO_GROUP_ID, message_thread_id=topic_id, text=msg_text, parse_mode="HTML")  # type: ignore

                        else:
                            logger.warning(
                                f"forward_edited_group_msg,group_id:{TO_GROUP_ID},msg_type:{msg_type} not support"
                            )
                    except Exception as e:
                        logger.error(
                            f"forward_edited_group_msg error:{e}", exc_info=True
                        )
            finally:
                await bot.session.close()
        else:
            logger.warning(f"forward_edited_group_msg,group_id:{TO_GROUP_ID} not found")

            return


class GroupMsgUserStatBiz:

    @classmethod
    async def get_nickname_username(cls, tg_id: int) -> tuple[str, str]:
        tg_user_map = await TgGroupUserMap.filter(tg_id=tg_id).first()
        if tg_user_map:

            tg_nickname = tg_user_map.tg_nickname if tg_user_map.tg_nickname else ""
            tg_username = tg_user_map.tg_username if tg_user_map.tg_username else ""
            return tg_nickname, tg_username
        return "", ""

    @classmethod
    async def cal_user_last_hour_msg_stat(cls, group_id: int):
        logger.info(f"cal_user_last_hour_msg_stat,group_id:{group_id}")
        now = datetime.now().replace(minute=0, second=0, microsecond=0)
        start_date = now - timedelta(hours=1)
        await cls.cal_user_hours_msg_stat(group_id, start_date, now)

        # #计算转发的消息统计
        # await cls.cal_user_last_hour_foward_msg_stat(start_date,now)
        # #计算 转发频道的统计
        # await cls.cal_daily_user_f_msg_stat(start_date.date())

        # 同时计算最新的一天的统计
        return await cls.cal_daily_user_msg_stat(group_id, start_date.date())

    @classmethod
    async def cal_user_today_msg_stat(cls, group_id: int):
        logger.info(f"cal_user_today_msg_stat,group_id:{group_id}")
        now = datetime.now()
        start_date = now
        return await cls.cal_daily_user_msg_stat(group_id, start_date.date())

    @classmethod
    async def cal_user_hours_msg_stat(
        cls, group_id: int, start_date: datetime, end_date: datetime
    ):

        # 转换为utc时间,因为数据库存储的是utc时间
        start_utc_time = start_date.astimezone(pytz.utc).replace(tzinfo=None)
        end_utc_time = end_date.astimezone(pytz.utc).replace(tzinfo=None)

        logger.info(
            f"cal_user_hours_msg_stat,start_date:{group_id},{start_date},end_date:{end_date},{start_utc_time},{end_utc_time}"
        )

        user_msg_stat = (
            await TgGroupMessage.filter(
                group_id=group_id,
                created_at__gte=start_utc_time,
                created_at__lt=end_utc_time,
            )
            .annotate(count=Count("id"))
            .group_by("user_id")
            .values("user_id", "count")
        )

        stat_date = start_date.date()
        stat_hour = start_date.hour

        logger.info(
            f"write user_hours_msg_stat,user_msg_stat:{stat_date},{stat_hour},{user_msg_stat}"
        )

        for user_stat in user_msg_stat:
            user_id = user_stat["user_id"]
            count = user_stat["count"]
            await cls.add_or_update_user_hours_msg_stat(
                stat_date, stat_hour, user_id, count, group_id
            )

        # 计算转发的消息统计
        await cls.cal_user_last_hour_foward_msg_stat(
            start_utc_time, end_utc_time, stat_date, stat_hour
        )

        return start_date, user_msg_stat

    @classmethod
    async def cal_user_last_hour_foward_msg_stat(
        cls, start: datetime, end: datetime, st_date: date, st_hour: int
    ):
        logger.info(
            f"cal_user_last_hour_forward_msg_stat,start:{start},end:{end},st_date:{st_date},st_hour:{st_hour}"
        )

        for forward_channel_id in FORWARD_CHANNEL_IDS:
            logger.info(
                f"cal_user_last_hour_forward_msg_stat,forward_channel_id:{forward_channel_id}"
            )
            # 获取转发的消息
            user_forward_stat = (
                await TgGroupForwardMessage.filter(
                    created_at__gte=start, created_at__lt=end
                )
                .filter(forward_from_id=forward_channel_id)
                .annotate(count=Count("id"))
                .group_by("user_id")
                .values("user_id", "count")
            )

            for user_stat in user_forward_stat or []:
                user_id = user_stat["user_id"]
                count = user_stat["count"]
                logger.info(
                    f"write user_forward_msg_stat,user_id:{user_id},count:{count}"
                )
                await cls.add_or_update_user_hours_msg_stat(
                    st_date, st_hour, user_id, count, forward_channel_id
                )

    @classmethod
    async def add_or_update_user_hours_msg_stat(
        cls, stat_date: date, stat_hour: int, user_id: int, count: int, group_id: int
    ):
        logger.info(
            f"add_or_update_user_hours_msg_stat,stat_date:{stat_date},stat_hour:{stat_hour},user_id:{user_id},count:{count},group_id:{group_id}"
        )
        try:
            user_stat = await HoursUserMessageStats.get(
                st_date=stat_date, st_hour=stat_hour, user_id=user_id, group_id=group_id
            )
            user_stat.message_count = count
            await user_stat.save()
        except DoesNotExist:

            tg_nic_kname, tg_username = await cls.get_nickname_username(user_id)
            await HoursUserMessageStats.create(
                st_date=stat_date,
                st_hour=stat_hour,
                user_id=user_id,
                message_count=count,
                group_id=group_id,
                tg_nick_name=tg_nic_kname,
                tg_username=tg_username,
            )

    @classmethod
    async def add_or_update_user_daily_stat(
        cls, stat_date: date, user_id: int, count: int, group_id: int, user_cnt: int = 1
    ):
        logger.info(
            f"add_or_update_user_daily_stat,stat_date:{stat_date},user_id:{user_id},count:{count},group_id:{group_id}"
        )
        try:
            user_stat = await DailyUserMessageStats.get(
                date=stat_date, user_id=user_id, group_id=group_id
            )
            user_stat.message_count = count
            user_stat.user_cnt = user_cnt
            await user_stat.save()
        except DoesNotExist:
            tg_nic_kname, tg_username = await cls.get_nickname_username(user_id)
            await DailyUserMessageStats.create(
                date=stat_date,
                user_id=user_id,
                message_count=count,
                group_id=group_id,
                tg_nick_name=tg_nic_kname,
                tg_username=tg_username,
                user_cnt=user_cnt,
            )

    @classmethod
    async def cal_daily_user_msg_stat(cls, group_id: int, stat_date: date):
        logger.info(
            f"cal_today_user_msg_stat,group_id:{group_id},stat_date:{stat_date}"
        )

        daily_user_stat = (
            await HoursUserMessageStats.filter(st_date=stat_date, group_id=group_id)
            .annotate(count=Sum("message_count"))
            .group_by("user_id")
            .values("user_id", "count")
        )
        total_msg_cnt = 0
        total_user_cnt = 0
        for user_stat in daily_user_stat:
            user_id = user_stat["user_id"]
            count = user_stat["count"]
            total_msg_cnt += count
            total_user_cnt += 1
            logger.info(
                f"write user_msg_stat,user_id:{user_id},count:{count},{group_id}"
            )
            await cls.add_or_update_user_daily_stat(stat_date, user_id, count, group_id)

        await cls.add_or_update_user_daily_stat(
            stat_date, group_id, total_msg_cnt, group_id, total_user_cnt
        )

        # 同时计算转发的消息统计
        await cls.cal_daily_user_f_msg_stat(stat_date)

        return total_msg_cnt, total_user_cnt

    @classmethod
    async def cal_daily_user_f_msg_stat(cls, stat_date: date):
        logger.info(f"cal_daily_user_f_msg_stat,stat_date:{stat_date}")
        for forward_channel_id in FORWARD_CHANNEL_IDS:
            logger.info(
                f"cal_daily_user_f_msg_stat,forward_channel_id:{forward_channel_id}"
            )
            # 获取转发的消息
            group_id = forward_channel_id

            daily_user_stat = (
                await HoursUserMessageStats.filter(st_date=stat_date, group_id=group_id)
                .annotate(count=Sum("message_count"))
                .group_by("user_id")
                .values("user_id", "count")
            )
            total_msg_cnt = 0
            total_user_cnt = 0
            for user_stat in daily_user_stat:
                user_id = user_stat["user_id"]
                count = user_stat["count"]
                total_msg_cnt += count
                total_user_cnt += 1
                logger.info(
                    f"write_f_user_msg_stat,user_id:{stat_date},{user_id},count:{count},{group_id}"
                )
                await cls.add_or_update_user_daily_stat(
                    stat_date, user_id, count, group_id
                )

            await cls.add_or_update_user_daily_stat(
                stat_date, group_id, total_msg_cnt, group_id, total_user_cnt
            )

    @classmethod
    async def get_daily_user_msg_stat(
        cls, group_id: int, stat_date: date, offset: int = 0
    ) -> list[DailyUserMessageStats]:
        logger.info(
            f"get_daily_user_msg_stat,group_id:{group_id},stat_date:{stat_date}"
        )
        user_msg_stat = (
            await DailyUserMessageStats.filter(date=stat_date, group_id=group_id)
            .order_by("-message_count")
            .offset(offset)
            .limit(100)
            .all()
        )

        return user_msg_stat

    @classmethod
    async def get_daily_user_msg_stat_total(
        cls, group_id: int, stat_date: date
    ) -> tuple[int, int]:
        logger.info(
            f"get_daily_user_msg_stat_by_user_id,group_id:{group_id},stat_date:{stat_date}"
        )

        user_msg_stat = await DailyUserMessageStats.filter(
            date=stat_date, group_id=group_id, user_id=group_id
        ).first()
        if user_msg_stat:
            return user_msg_stat.message_count, user_msg_stat.user_cnt
        return 0, 0

    @classmethod
    async def get_hours_user_msg_stat(
        cls, group_id: int, stat_date: date, stat_hour: int
    ) -> list[HoursUserMessageStats]:
        logger.info(
            f"get_hours_user_msg_stat,group_id:{group_id},stat_date:{stat_date},stat_hour:{stat_hour}"
        )
        user_msg_stat = (
            await HoursUserMessageStats.filter(
                st_date=stat_date, st_hour=stat_hour, group_id=group_id
            )
            .order_by("-message_count")
            .all()
        )

        return user_msg_stat

    @classmethod
    async def get_forward_hours_stat_by_uid(
        cls, uid: int, stat_date: date, stat_hour: int
    ) -> int:
        logger.info(f"get_forward_stat_by_uid:{uid},{stat_date},{stat_hour}")

        forward_count = 0

        user_f_msg_cnt = (
            await HoursUserMessageStats.filter(
                st_date=stat_date, st_hour=stat_hour, user_id=uid
            )
            .filter(group_id__in=FORWARD_CHANNEL_IDS)
            .all()
        )

        if user_f_msg_cnt:
            forward_count = sum([msg.message_count for msg in user_f_msg_cnt])

        return forward_count

    @classmethod
    async def get_forward_daily_stat_by_uid(cls, uid: int, stat_date: date) -> int:
        logger.info(f"get_forward_stat_by_uid:{uid},{stat_date}")

        f_cnt = 0
        user_f_msg_cnt = (
            await DailyUserMessageStats.filter(date=stat_date, user_id=uid)
            .filter(group_id__in=FORWARD_CHANNEL_IDS)
            .all()
        )

        if user_f_msg_cnt:
            f_cnt = sum([msg.message_count for msg in user_f_msg_cnt])

        return f_cnt


class UserInviteShareStatBiz:

    @classmethod
    async def add_or_update_user_invite_stat(
        cls, stat_date: date, user_id: int, count: int
    ):
        # logger.info(f"add_or_update_user_invite_stat,stat_date:{stat_date},user_id:{user_id},count:{count}")

        tg_nick_name = ""
        tg_username = ""
        try:

            user_stat = await DailyInviteUserStats.get(date=stat_date, user_id=user_id)
            user_stat.invite_count = count
            await user_stat.save()
        except DoesNotExist:

            tg_user = await TelegramUser.get_or_none(uid=user_id)

            if tg_user:
                tg_nick_name = tg_user.first_name + " " + tg_user.last_name
                tg_username = tg_user.user_name
            await DailyInviteUserStats.create(
                date=stat_date,
                user_id=user_id,
                invite_count=count,
                tg_nick_name=tg_nick_name,
                tg_username=tg_username,
            )

    @classmethod
    async def cal_user_invite_cnt(cls, cal_date: datetime):
        logger.info(f"cal_user_invite_cnt,cal_date:{cal_date}")

        cal_utc_time = (
            cal_date.replace(hour=0, minute=0, second=0, microsecond=0)
            .astimezone(pytz.utc)
            .replace(tzinfo=None)
        )
        start_date = cal_utc_time
        end_date = cal_utc_time + timedelta(days=1)

        logger.info(f"cal_user_invite_cnt,start_date:{start_date},end_date:{end_date}")
        user_invite_cnt = (
            await Invitation.filter(invited_at__gte=start_date, invited_at__lt=end_date)
            .annotate(count=Count("id"))
            .group_by("inviter_user_id")
            .values("inviter_user_id", "count")
        )

        logger.info(f"cal_user_invite_cnt,user_invite_cnt:{user_invite_cnt}")
        for user_stat in user_invite_cnt:
            user_id = user_stat["inviter_user_id"]
            count = user_stat["count"]
            st_date = cal_date.date()
            await cls.add_or_update_user_invite_stat(st_date, user_id, count)

    @classmethod
    async def add_or_update_user_share_stat(
        cls, stat_date: date, user_id: int, count: int
    ):
        logger.info(
            f"add_or_update_user_share_stat,stat_date:{stat_date},user_id:{user_id},count:{count}"
        )

        tg_nick_name = ""
        tg_username = ""
        try:
            user_stat = await DailyShareUserStats.get(date=stat_date, user_id=user_id)
            user_stat.share_count = count
            await user_stat.save()
        except DoesNotExist:
            tg_user = await TelegramUser.get_or_none(uid=user_id)

            if tg_user:
                tg_nick_name = tg_user.first_name + " " + tg_user.last_name
                tg_username = tg_user.user_name
            await DailyInviteUserStats.create(
                date=stat_date,
                user_id=user_id,
                share_count=count,
                tg_nick_name=tg_nick_name,
                tg_username=tg_username,
            )
        except IntegrityError:
            user_stat = await DailyShareUserStats.get(date=stat_date, user_id=user_id)
            user_stat.share_count = count
            await user_stat.save()

    @classmethod
    async def cal_user_share_cnt(cls, cal_date: datetime):
        logger.info(f"cal_user_share_cnt,cal_date:{cal_date}")

        start_utc_time = (
            cal_date.replace(hour=0, minute=0, second=0, microsecond=0)
            .astimezone(pytz.utc)
            .replace(tzinfo=None)
        )
        end_utc_time = start_utc_time + timedelta(days=1)

        share_stats = (
            await UserRoleShare.filter(
                created_at__gte=start_utc_time, created_at__lt=end_utc_time
            )
            .annotate(count=Count("id"))
            .group_by("user_id")
            .values("user_id", "count")
        )

        for user_stat in share_stats:
            user_id = user_stat["user_id"]
            count = user_stat["count"]
            st_date = cal_date.date()
            await cls.add_or_update_user_share_stat(st_date, user_id, count)

    @classmethod
    async def get_daily_user_share_stat(
        cls, stat_date: date
    ) -> list[DailyShareUserStats]:
        logger.info(f"get_daily_user_share_stat,stat_date:{stat_date}")
        share_stats = (
            await DailyShareUserStats.filter(date=stat_date)
            .order_by("-share_count")
            .all()
        )
        return share_stats

    @classmethod
    async def get_daily_user_invite_stat(cls, stat_date: date):
        logger.info(f"get_daily_user_invite_stat,stat_date:{stat_date}")
        invite_stats = (
            await DailyInviteUserStats.filter(date=stat_date)
            .order_by("-invite_count")
            .all()
        )
        return invite_stats
