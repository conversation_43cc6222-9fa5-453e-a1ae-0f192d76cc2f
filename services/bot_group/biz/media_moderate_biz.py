

import logging
from tencentcloud.common import credential
from tencentcloud.vm.v20210922.vm_client import VmClient
from tencentcloud.ims.v20201229.ims_client import ImsClient
from tencentcloud.ims.v20201229.models import ImageModerationRequest,ImageModerationResponse
from tencentcloud.vm.v20210922.models import StorageInfo, TaskInput,CreateVideoModerationTaskRequest,CreateVideoModerationTaskResponse,DescribeTaskDetailRequest,DescribeTaskDetailResponse,DescribeTasksRequest,DescribeTasksResponse,ImageResult,TaskResult


from persistence.models.models_bot_group import MediaModerationTask
from common.models.ai_bot_admin.ims_moderation_common import MediaModerateConstants

from utils.ims_util import upload_img_to_cos

secret_id = "IKIDcJ7ubJow78xjLm7YnvzzvpfQ361mbQBu"
secret_key = "riXEbqumRvCF6lDSVlpzzmPpU2NWTGsI"

vm_biz_type = "tg_e_002"

ims_biz_type = "10000"

# host = "ims.tencentcloudapi.com"
vm_region = "ap-singapore"
vm_credential = credential.Credential(secret_id, secret_key)

log = logging.getLogger(__name__)

# 实例化一个认证对象，入参需要传入腾讯云账户密钥对 secretId，secretKey
vim_client = VmClient(credential=vm_credential, region=vm_region)

ims_client = ImsClient(credential=vm_credential, region=vm_region)

class IMSModerationResult:
    def __init__(self,suggestions:str,check_label:str,request_id:str=""):
        self.task_id = ""
        self.request_id = request_id
        self.status = ""
        self.suggestions = suggestions
        self.check_label = check_label
        self.description = ""
        self.raw_response = ""

class IMSModerationInput:
    def __init__(self,image_url:str,file_unique_id:str,group_id:int,msg_id:int,tg_id:int):
        self.image_url = image_url
        self.file_unique_id = file_unique_id
        self.group_id = group_id
        self.msg_id = msg_id
        self.tg_id = tg_id
    
class IMSModerationBiz:
    
    @classmethod
    async def add_media_task(cls,msg_dict:dict):
        
        await MediaModerationTask.update_or_create(
            defaults={
                "file_unique_id": msg_dict["file_unique_id"],
                "tg_id": msg_dict["tg_id"],
                "image_url": msg_dict["image_url"],
                "file_type": msg_dict["file_type"],
                "msg_dict": msg_dict,
                "suggestion": MediaModerateConstants.INIT,
            },
           group_id = msg_dict["group_id"],
           msg_id = msg_dict["msg_id"]
        )
    
    @classmethod
    async def add_admin_skip_media_task(cls,msg_dict:dict,check_label:str="admin_skip"):
        
        await MediaModerationTask.update_or_create(
            defaults={
                "file_unique_id": msg_dict["file_unique_id"],
                "tg_id": msg_dict["tg_id"],
                "image_url": msg_dict["image_url"],
                "file_type": msg_dict["file_type"],
                "msg_dict": msg_dict,
                "suggestion": MediaModerateConstants.PASS,
                "need_del": 0,
                "del_ack": 1,
                "check_label": check_label
            },
           group_id = msg_dict["group_id"],
           msg_id = msg_dict["msg_id"]
        )
    
    @classmethod
    async def check_existing_media_task(cls, file_unique_id: str) -> MediaModerationTask|None:
        return await MediaModerationTask.filter(file_unique_id=file_unique_id).exclude(suggestion=MediaModerateConstants.INIT).first()
       
    
    @classmethod
    async def update_media_task(cls,group_id:int,msg_id:int,request_id:str,suggestions:str,check_label:str,image_key:str):    
       
        need_del = 0
        if suggestions == "Block":
            need_del = 1
        await MediaModerationTask.filter(group_id=group_id,msg_id=msg_id).update(request_id=request_id,suggestion=suggestions,check_label=check_label,need_del=need_del,image_key=image_key)
        
    
    @classmethod
    async def upadte_media_task_ack(cls,group_id:int,msg_id:int,del_ack:int):
        
        await MediaModerationTask.filter(group_id=group_id,msg_id=msg_id).update(del_ack=del_ack)

    @classmethod
    async def image_moderation(cls,image_url:str,file_id:str,file_unique_id:str,chat_id:int) ->IMSModerationResult:
        
        log.info(f"image_moderation image_url: {image_url},file_id: {file_id},file_unique_id: {file_unique_id},chat_id: {chat_id}")
        
        e_task = await cls.check_existing_media_task(file_unique_id)
        
        if e_task:
            log.info(f"image_moderation task already exist: {e_task}")
            return IMSModerationResult(suggestions=e_task.suggestion,check_label=e_task.check_label,request_id=e_task.request_id)
        
        
        ims_request = ImageModerationRequest()
        ims_request.BizType = ims_biz_type
        ims_request.FileUrl = image_url
        
        log.info(f"image_moderation request: {ims_request}")
        try:
            c_result = ims_client.ImageModeration(ims_request)
            log.info(f"image_moderation result: {c_result}")
            
            ims_result = IMSModerationResult(c_result.Suggestion,c_result.Label) # type: ignore
            ims_result.request_id = c_result.RequestId # type: ignore
            ims_result.raw_response = c_result.to_json_string()
            
            return ims_result
        except Exception as e:
            log.error(f"image_moderation error: {e}",exc_info=True)
            return IMSModerationResult("ERROR","photo")    
        
    
    @classmethod
    def video_moderation(cls,image_url:str,file_unique_id:str,chat_id:int) ->IMSModerationResult:
        
        # 构造 StorageInfo 对象
        storage_info = StorageInfo()
        storage_info.Type = "URL"  # 或者 "COS"
        storage_info.Url = image_url# 当 Type 为 URL 时需要设置
        
        task_input = TaskInput()
        task_input.DataId = file_unique_id
        task_input.Name = "s&emoij"
        task_input.Input = storage_info
        
        vm_request =CreateVideoModerationTaskRequest()
        vm_request.Type = "VIDEO"
        vm_request.BizType = vm_biz_type
        vm_request.Tasks = [task_input]
        
        ims_m_r = IMSModerationResult("INIT","emoij")
        
        log.info(f"video_moderation request: {vm_request}")
        try:
            c_result = vim_client.CreateVideoModerationTask(vm_request)
            ims_m_r.request_id = c_result.RequestId # type: ignore
            ims_m_r.task_id = c_result.Results[0].TaskId # type: ignore

            
            ims_m_r.raw_response = c_result.to_json_string()
        except Exception as e:
            log.error(f"video_moderation error: {e}",exc_info=True)
            return ims_m_r
        log.info(f"video_moderation create_task result: {c_result}")
        
        return ims_m_r
        
    @classmethod
    def video_moderation_check_result(cls,task_id:str) ->IMSModerationResult:
        
        log.info(f"video_moderation_check_result task_id: {task_id}")
        
        ims_result = IMSModerationResult("INIT","emoij")
        vm_request = DescribeTaskDetailRequest()
        vm_request.TaskId = task_id
        
        log.info(f"video_moderation_result request: {vm_request}")
        try:
            c_response = vim_client.DescribeTaskDetail(vm_request)
            
            log.info(f"video_moderation_result result: {c_response}")
            
            ims_result.suggestions = c_response.Suggestion # type: ignore
            ims_result.status = c_response.Status # type: ignore
            ims_result.raw_response = c_response.to_json_string()
            ims_result.request_id = c_response.RequestId # type: ignore
            v_labels = c_response.Labels or []
            check_label = ""
            for label in v_labels:
                check_label += label.Label + ","
            
            if check_label == "":
                ims_result.check_label = c_response.Label  if c_response.Label else "" 
            else:
                ims_result.check_label = check_label
        except Exception as e:
            log.error(f"video_moderation_check_result error: {e}",exc_info=True)
            
        return ims_result
    
    
    

    
    
    