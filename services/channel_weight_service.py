import logging
from typing import Dict, List, Optional
from dataclasses import dataclass

from persistence.models.models import (
    RechargeChannelStats,
    RechargeChannelControl,
    RechargeChannelAlert
)
from utils import tg_util

logger = logging.getLogger(__name__)
monitor_chat_id = tg_util.channel_monitor_chat_id

@dataclass
class ChannelWeightData:
    """渠道权重数据"""
    channel: str
    pay_type: str
    success_rate: float
    current_weight: int
    total_orders: int
    is_active: bool


@dataclass
class WeightAdjustmentResult:
    """权重调整结果"""
    channel: str
    pay_type: str
    old_weight: int
    new_weight: int
    reason: str
    alert_level: Optional[str] = None


class ChannelWeightService:
    """渠道权重动态调整服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.decision_logs = []  # 收集决策过程中的日志信息
    
    async def adjust_channel_weights_by_pay_type(self, pay_type: str) -> List[WeightAdjustmentResult]:
        """按支付类型调整所有渠道权重"""
        self.decision_logs = []  # 清空上次的决策日志
        self.logger.info(f"开始调整支付类型 {pay_type} 的渠道权重")
        self.decision_logs.append(f"📊 开始调整支付类型 {pay_type} 的渠道权重")
        
        # 1. 获取该支付类型下所有渠道数据
        channels = await self._get_channels_by_pay_type(pay_type)
        
        if not channels:
            self.logger.warning(f"支付类型 {pay_type} 无可用渠道")
            self.decision_logs.append(f"⚠️ 支付类型 {pay_type} 无可用渠道")
            return []
        
        self.decision_logs.append(f"📈 获取到 {len(channels)} 个渠道数据：")
        for ch in channels:
            self.decision_logs.append(f"   - {ch.channel}: 成功率 {ch.success_rate:.2%}, 当前权重 {ch.current_weight}%, 订单量 {ch.total_orders}")
        
        # 2. 应用调整规则
        results = []
        
        # 规则4: 处理成功率低于38%的渠道
        results.extend(await self._apply_rule_4_below_38_percent(channels))
        
        # 规则5: 处理成功率低于45%的渠道
        results.extend(await self._apply_rule_5_below_45_percent(channels))
        
        # 规则6: 淘汰与最高成功率差距大的渠道
        results.extend(await self._apply_rule_6_performance_gap(channels))
        
        # 规则7: 剩余渠道的权重分配
        results.extend(await self._apply_rule_7_remaining_channels(channels))
        
        # 3. 批量更新权重
        await self._batch_update_weights(results)
        
        self.logger.info(f"完成权重调整，共调整 {len(results)} 个渠道")
        return results
    
    async def _get_channels_by_pay_type(self, pay_type: str) -> List[ChannelWeightData]:
        """获取指定支付类型的所有渠道数据"""
        stats = await RechargeChannelStats.filter(pay_type=pay_type).all()
        controls = await RechargeChannelControl.filter(pay_type=pay_type, enabled=True).all()

        control_dict = {(c.channel.value, c.pay_type): c for c in controls}
        total_weight = sum(c.ratio for c in controls if c.ratio > 0 and c.enabled)
        channels = []
        for stat in stats:
            channel_key = (stat.channel.value, stat.pay_type)
            control = control_dict.get(channel_key)
            
            if control and control.ratio > 0:  # 只处理启用的渠道
                channels.append(ChannelWeightData(
                    channel=stat.channel.value,
                    pay_type=stat.pay_type,
                    success_rate=stat.time_window_success_rate,
                    current_weight=int(control.ratio/ total_weight * 100),
                    total_orders=stat.window_total_orders,
                    is_active=control.enabled
                ))
        
        # 按成功率排序（降序）
        channels.sort(key=lambda x: x.success_rate, reverse=True)
        return channels
    
    async def _apply_rule_4_below_38_percent(self, channels: List[ChannelWeightData]) -> List[WeightAdjustmentResult]:
        """规则4: 处理成功率低于38%的渠道"""
        results = []
        below_38_channels = [ch for ch in channels if ch.success_rate < 0.38]
        
        self.decision_logs.append(f"\n📊 规则4 - 处理成功率低于38%的渠道:")
        
        if not below_38_channels:
            self.decision_logs.append(f"  ✅ 没有渠道成功率低于38%，跳过规则4")
            return results
        
        self.decision_logs.append(f"  📋 发现 {len(below_38_channels)} 个渠道成功率低于38%:")
        for ch in below_38_channels:
            self.decision_logs.append(f"     - {ch.channel}: {ch.success_rate:.2%}")
        
        # 检查是否所有渠道都低于38%
        all_below_38 = len(below_38_channels) == len(channels)
        
        if all_below_38:
            # 所有渠道都低于38%，不调整，只报警
            self.decision_logs.append(f"  🚨 紧急情况：所有渠道成功率都低于38%，终止调整")
            await self._create_emergency_alert(
                channels[0].pay_type,
                "ALL_CHANNELS_CRITICAL",
                f"支付类型 {channels[0].pay_type} 所有渠道成功率都低于38%"
            )
            self.logger.critical(f"支付类型 {channels[0].pay_type} 所有渠道成功率都低于38%，终止调整")
            return results
        
        # 部分渠道低于38%，将其权重设为0
        self.decision_logs.append(f"  🔄 将低于38%的渠道权重设为0:")
        for channel in below_38_channels:
            if channel.current_weight > 0:
                self.decision_logs.append(f"     {channel.channel}: {channel.current_weight}% -> 0% (成功率 {channel.success_rate:.2%})")
                results.append(WeightAdjustmentResult(
                    channel=channel.channel,
                    pay_type=channel.pay_type,
                    old_weight=channel.current_weight,
                    new_weight=0,
                    reason=f"成功率 {channel.success_rate:.2%} 低于38%临界值",
                    alert_level="CRITICAL"
                ))
                # 更新内存中的权重，影响后续规则
                channel.current_weight = 0
        
        return results
    
    async def _apply_rule_5_below_45_percent(self, channels: List[ChannelWeightData]) -> List[WeightAdjustmentResult]:
        """规则5: 处理成功率低于45%的渠道（排除已在规则4中处理的）"""
        results = []
        
        self.decision_logs.append(f"\n📊 规则5 - 处理成功率低于45%的渠道:")
        
        # 获取38-45%之间的渠道
        between_38_45_channels = [
            ch for ch in channels 
            if 0.38 <= ch.success_rate < 0.45 and ch.current_weight > 0
        ]
        
        if not between_38_45_channels:
            self.decision_logs.append(f"  ✅ 没有渠道成功率在38-45%范围，跳过规则5")
            return results
        
        self.decision_logs.append(f"  📋 发现 {len(between_38_45_channels)} 个渠道成功率在38-45%范围:")
        for ch in between_38_45_channels:
            self.decision_logs.append(f"     - {ch.channel}: {ch.success_rate:.2%}")
        
        # 检查是否还有成功率高于45%的渠道
        above_45_channels = [
            ch for ch in channels 
            if ch.success_rate >= 0.45 and ch.current_weight > 0
        ]
        
        if not above_45_channels:
            # 所有剩余渠道都低于45%，选择成功率最高的设为100%
            remaining_channels = [ch for ch in channels if ch.current_weight > 0]
            self.decision_logs.append(f"  🚨 所有剩余渠道都低于45%，选择最高成功率渠道")
            if remaining_channels:
                highest_channel = max(remaining_channels, key=lambda x: x.success_rate)
                self.decision_logs.append(f"  📈 选中最高成功率渠道: {highest_channel.channel} ({highest_channel.success_rate:.2%})")
                
                # 其他渠道权重设为0
                for channel in remaining_channels:
                    if channel.channel != highest_channel.channel:
                        self.decision_logs.append(f"     {channel.channel}: {channel.current_weight}% -> 0%")
                        results.append(WeightAdjustmentResult(
                            channel=channel.channel,
                            pay_type=channel.pay_type,
                            old_weight=channel.current_weight,
                            new_weight=0,
                            reason="剩余渠道中成功率不是最高",
                            alert_level="CRITICAL"
                        ))
                        channel.current_weight = 0
                
                # 最高成功率渠道设为100%
                if highest_channel.current_weight != 100:
                    self.decision_logs.append(f"     {highest_channel.channel}: {highest_channel.current_weight}% -> 100%")
                    results.append(WeightAdjustmentResult(
                        channel=highest_channel.channel,
                        pay_type=highest_channel.pay_type,
                        old_weight=highest_channel.current_weight,
                        new_weight=100,
                        reason=f"剩余渠道中成功率最高 ({highest_channel.success_rate:.2%})",
                        alert_level="EMERGENCY"
                    ))
                    highest_channel.current_weight = 100
        else:
            # 还有高于45%的渠道，将38-45%的渠道权重设为10%-15%
            count_38_45 = len(between_38_45_channels)
            target_weight = 15 if count_38_45 == 1 else 10
            self.decision_logs.append(f"  🔄 还有高于45%的渠道，将38-45%的渠道权重设为{target_weight}%:")
            
            for channel in between_38_45_channels:
                if channel.current_weight != target_weight:
                    self.decision_logs.append(f"     {channel.channel}: {channel.current_weight}% -> {target_weight}% (成功率 {channel.success_rate:.2%})")
                    results.append(WeightAdjustmentResult(
                        channel=channel.channel,
                        pay_type=channel.pay_type,
                        old_weight=channel.current_weight,
                        new_weight=target_weight,
                        reason=f"成功率 {channel.success_rate:.2%} 在38-45%区间，设为{target_weight}%",
                        alert_level="WARNING"
                    ))
                    channel.current_weight = target_weight
        
        return results
    
    async def _apply_rule_6_performance_gap(self, channels: List[ChannelWeightData]) -> List[WeightAdjustmentResult]:
        """规则6: 淘汰与最高成功率差距大的渠道"""
        results = []
        
        self.decision_logs.append(f"\n📊 规则6 - 淘汰与最高成功率差距大的渠道:")
        
        # 获取当前权重大于0的渠道
        active_channels = [ch for ch in channels if ch.current_weight > 0]
        
        if len(active_channels) <= 1:
            self.decision_logs.append(f"  ✅ 活跃渠道不足，跳过规则6")
            return results
        
        # 找到成功率最高的渠道
        highest_channel = max(active_channels, key=lambda x: x.success_rate)
        highest_rate = highest_channel.success_rate
        self.decision_logs.append(f"  📈 最高成功率渠道: {highest_channel.channel} ({highest_rate:.2%})")
        
        # 找出差距>=20%的渠道
        gap_20_percent_channels = [
            ch for ch in active_channels 
            if ch.channel != highest_channel.channel and (highest_rate - ch.success_rate) >= 0.20
        ]
        
        if not gap_20_percent_channels:
            self.decision_logs.append(f"  ✅ 没有与最高成功率差距>=20%的渠道，跳过规则6")
            return results
        
        self.decision_logs.append(f"  📋 发现 {len(gap_20_percent_channels)} 个与最高成功率差距>=20%的渠道:")
        for ch in gap_20_percent_channels:
            gap = highest_rate - ch.success_rate
            self.decision_logs.append(f"     - {ch.channel}: {ch.success_rate:.2%} (差距 {gap:.2%})")
        
        # 检查是否所有其他渠道都与最高成功率差>=20%
        other_channels = [ch for ch in active_channels if ch.channel != highest_channel.channel]
        all_gap_20_percent = len(gap_20_percent_channels) == len(other_channels)
        
        if all_gap_20_percent:
            # 所有其他渠道都差距>=20%，仅保留成功率第二的渠道
            self.decision_logs.append(f"  🚨 所有其他渠道都差距>=20%，仅保留成功率第二的渠道")
            if len(other_channels) > 0:
                second_highest = max(other_channels, key=lambda x: x.success_rate)
                self.decision_logs.append(f"  📈 保留第二高成功率渠道: {second_highest.channel} ({second_highest.success_rate:.2%})")
                
                # 除了第二高的，其他都设为0
                for channel in gap_20_percent_channels:
                    if channel.channel != second_highest.channel:
                        self.decision_logs.append(f"     {channel.channel}: {channel.current_weight}% -> 0% (差距 {highest_rate - channel.success_rate:.2%})")
                        results.append(WeightAdjustmentResult(
                            channel=channel.channel,
                            pay_type=channel.pay_type,
                            old_weight=channel.current_weight,
                            new_weight=0,
                            reason=f"与最高成功率差距 {highest_rate - channel.success_rate:.2%} >=20%",
                            alert_level="WARNING"
                        ))
                        channel.current_weight = 0
        else:
            # 存在差距<20%的渠道，将差距>=20%的渠道权重设为0
            self.decision_logs.append(f"  🔄 存在差距<20%的渠道，将差距>=20%的渠道权重设为0:")
            for channel in gap_20_percent_channels:
                self.decision_logs.append(f"     {channel.channel}: {channel.current_weight}% -> 0% (差距 {highest_rate - channel.success_rate:.2%})")
                results.append(WeightAdjustmentResult(
                    channel=channel.channel,
                    pay_type=channel.pay_type,
                    old_weight=channel.current_weight,
                    new_weight=0,
                    reason=f"与最高成功率差距 {highest_rate - channel.success_rate:.2%} >=20%",
                    alert_level="WARNING"
                ))
                channel.current_weight = 0
        
        return results
    
    async def _apply_rule_7_remaining_channels(self, channels: List[ChannelWeightData]) -> List[WeightAdjustmentResult]:
        """规则7: 剩余渠道的权重分配"""
        results = []
        
        self.decision_logs.append(f"\n📊 规则7 - 剩余渠道的权重分配:")
        
        # 获取剩余的活跃渠道（权重>0且不是规则4,5固定的权重）
        remaining_channels = [
            ch for ch in channels 
            if ch.current_weight > 0 and ch.current_weight not in [10, 15]  # 排除规则5设置的固定权重
        ]
        
        if len(remaining_channels) <= 1:
            self.decision_logs.append(f"  ✅ 剩余活跃渠道不足，跳过规则7")
            return results
        
        self.decision_logs.append(f"  📋 剩余 {len(remaining_channels)} 个活跃渠道:")
        for ch in remaining_channels:
            self.decision_logs.append(f"     - {ch.channel}: 成功率 {ch.success_rate:.2%}, 当前权重 {ch.current_weight}%")
        
        # 按成功率排序
        remaining_channels.sort(key=lambda x: x.success_rate, reverse=True)
        
        highest_rate = remaining_channels[0].success_rate
        second_rate = remaining_channels[1].success_rate if len(remaining_channels) > 1 else 0
        
        rate_difference = highest_rate - second_rate
        self.decision_logs.append(f"  📈 最高({highest_rate:.2%})与第二({second_rate:.2%})成功率差距: {rate_difference:.2%}")
        
        # 根据成功率差距确定权重分配策略
        if rate_difference >= 0.15:  # 差距15%及以上
            highest_weight = 60
            self.decision_logs.append(f"  🔄 差距>=15%，最高成功率渠道分配60%权重")
        elif rate_difference >= 0.10:  # 差距10%及以上但低于15%
            highest_weight = 50
            self.decision_logs.append(f"  🔄 差距>=10%但<15%，最高成功率渠道分配50%权重")
        else:  # 差距10%以下
            highest_weight = 40
            self.decision_logs.append(f"  🔄 差距<10%，最高成功率渠道分配40%权重")
        
        # 计算剩余权重
        remaining_weight = 100 - highest_weight
        
        # 规则4和5处理的渠道占用的权重
        fixed_weight_channels = [ch for ch in channels if ch.current_weight in [10, 15]]
        fixed_weight_total = sum(ch.current_weight for ch in fixed_weight_channels)
        
        # 调整剩余权重
        remaining_weight = max(0, 100 - highest_weight - fixed_weight_total)
        
        # 为最高成功率渠道分配权重
        if remaining_channels[0].current_weight != highest_weight:
            self.decision_logs.append(f"     {remaining_channels[0].channel}: {remaining_channels[0].current_weight}% -> {highest_weight}%")
            results.append(WeightAdjustmentResult(
                channel=remaining_channels[0].channel,
                pay_type=remaining_channels[0].pay_type,
                old_weight=remaining_channels[0].current_weight,
                new_weight=highest_weight,
                reason=f"最高成功率 {highest_rate:.2%}，与第二名差距 {rate_difference:.2%}",
                alert_level=None
            ))
            remaining_channels[0].current_weight = highest_weight
        
        # 为其他渠道均分剩余权重
        if len(remaining_channels) > 1 and remaining_weight > 0:
            other_channels = remaining_channels[1:]
            weight_per_channel = remaining_weight // len(other_channels)
            self.decision_logs.append(f"  🔄 其他渠道均分剩余{remaining_weight}%权重，每个渠道{weight_per_channel}%:")
            
            for channel in other_channels:
                if channel.current_weight != weight_per_channel:
                    self.decision_logs.append(f"     {channel.channel}: {channel.current_weight}% -> {weight_per_channel}%")
                    results.append(WeightAdjustmentResult(
                        channel=channel.channel,
                        pay_type=channel.pay_type,
                        old_weight=channel.current_weight,
                        new_weight=weight_per_channel,
                        reason=f"剩余渠道均分权重",
                        alert_level=None
                    ))
                    channel.current_weight = weight_per_channel
        
        return results
    
    async def _batch_update_weights(self, results: List[WeightAdjustmentResult]):
        messages = []
        
        # 添加决策过程日志
        if self.decision_logs:
            decision_summary = '\n'.join(self.decision_logs)
            messages.append(f"🔍 决策过程:\n{decision_summary}")
            messages.append("\n" + "="*50 + "\n")
        
        # 添加最终调整结果
        adjustment_messages = []
        for result in results:
            try:
                # 更新统计表
                await RechargeChannelStats.filter(
                    channel=result.channel,
                    pay_type=result.pay_type
                ).update(
                    current_weight=result.new_weight,
                    auto_adjusted_weight=result.new_weight
                )
                
                # TODO: 更新控制表
                # await RechargeChannelControl.filter(
                #     channel=result.channel,
                #     pay_type=result.pay_type
                # ).update(ratio=result.new_weight)
                
                # 创建告警（如果需要）
                if result.alert_level:
                    await self._create_weight_adjustment_alert(result)

                m = f"权重调整: {result.channel}-{result.pay_type} {result.old_weight}% -> {result.new_weight}% ({result.reason})"
                adjustment_messages.append(m)
                self.logger.info(m)
            except Exception as e:
                error_msg = f"更新权重失败 {result.channel}-{result.pay_type}: {str(e)}"
                adjustment_messages.append(error_msg)
                self.logger.error(error_msg)
        
        if adjustment_messages:
            messages.append(f"📋 最终调整结果 ({len(adjustment_messages)}个渠道):")
            messages.extend(adjustment_messages)
        else:
            messages.append("✅ 没有需要调整的渠道")
        
        # 发送通知
        await tg_util.sm({
                "title": "渠道权重调整结果-DRY RUN",
                "messages": '\n'.join(messages)
            }, chat_id=monitor_chat_id)

    async def _create_weight_adjustment_alert(self, result: WeightAdjustmentResult):
        """创建权重调整告警"""
        try:
            await RechargeChannelAlert.create(
                channel=result.channel,
                pay_type=result.pay_type,
                alert_type="WEIGHT_ADJUSTMENT",
                alert_level=result.alert_level,
                message=f"权重从 {result.old_weight}% 调整为 {result.new_weight}%: {result.reason}",
                phone_notified=result.alert_level == "EMERGENCY"
            )
        except Exception as e:
            self.logger.error(f"创建权重调整告警失败: {str(e)}")
    
    async def _create_emergency_alert(self, pay_type: str, alert_type: str, message: str):
        try:
            ...
        except Exception as e:
            self.logger.error(f"创建紧急告警失败: {str(e)}")
    
    async def get_channel_weight_summary(self, pay_type: str) -> Dict:
        """获取渠道权重摘要"""
        channels = await self._get_channels_by_pay_type(pay_type)
        
        total_weight = sum(ch.current_weight for ch in channels)
        active_channels = [ch for ch in channels if ch.current_weight > 0]
        
        return {
            "pay_type": pay_type,
            "total_channels": len(channels),
            "active_channels": len(active_channels),
            "total_weight": total_weight,
            "channels": [
                {
                    "channel": ch.channel,
                    "success_rate": ch.success_rate,
                    "weight": ch.current_weight,
                    "orders": ch.total_orders
                }
                for ch in channels
            ]
        }


# 全局实例
channel_weight_service = ChannelWeightService()