import asyncio
from datetime import timedelta
from hashlib import md5
import hashlib
import logging
from dotenv import load_dotenv
from litellm import (
    ChatCompletionAssistantMessage,
    ChatCompletionCachedContent,
    ChatCompletionSystemMessage,
    ChatCompletionUserMessage,
)

from common.common_constant import (
    Env,
    LlmCacheType,
    LlmModel,
)
from common.models.chat_model import ChatNextInput


from langchain.schema import HumanMessage, SystemMessage, AIMessage

from langchain_core.messages.base import BaseMessage
from persistence import redis_client, vol_engine_context_history_dao
from persistence.models.models import LlmModelCacheHistory, VolEngineContext
from persistence.models.mongo_models import VolEngineContextHistory
from persistence.presets import Scenario
from utils import (
    date_util,
    env_util,
    exception_util,
    request_util,
    token_util,
)

log = logging.getLogger(__name__)


async def cache_message(input_message: list[BaseMessage], input: ChatNextInput):
    def transform_message(message: BaseMessage, use_cache: bool = False):
        if isinstance(message, HumanMessage):
            return ChatCompletionUserMessage(content=str(message.content), role="user")
        elif isinstance(message, AIMessage):
            return ChatCompletionAssistantMessage(
                content=str(message.content), role="assistant"
            )
        elif isinstance(message, SystemMessage):
            return (
                ChatCompletionSystemMessage(
                    content=message.content,
                    role="system",
                    cache_control=ChatCompletionCachedContent(type="ephemeral"),
                )
                if use_cache
                else ChatCompletionSystemMessage(content=message.content, role="system")
            )
        else:
            raise ValueError(f"Unsupported message type: {type(message)}")

    def check_use_cache():
        if input.cache_type == LlmCacheType.EPHEMERAL_FLAG.value:
            return True
        if input.cache_type == LlmCacheType.CONTEXT_API.value and input_message[-1].type != "ai":
            return True
        return False

    if not check_use_cache():
        ret_messages = [transform_message(x) for x in input_message]
        return ret_messages, input.request_model
    if input.cache_type == LlmCacheType.CONTEXT_API.value and input.use_backup:
        ret_messages = [transform_message(x) for x in input_message]
        return ret_messages, input.request_model + "-backup"

    if input.cache_type == LlmCacheType.CONTEXT_API.value:
        ret_messages, request_model = await ds_cache_record(input, input_message)
        ret_messages = [transform_message(x) for x in ret_messages]
        return ret_messages, request_model
    if (
        input.cache_type == LlmCacheType.EPHEMERAL_FLAG.value
        and input_message[0].type == "system"
    ):
        cache_message = transform_message(input_message[0], use_cache=True)
        ret_messages = [cache_message] + [
            transform_message(x) for x in input_message[1:]
        ]
        return ret_messages, input.request_model
    return [transform_message(x) for x in input_message], input.request_model


async def ark_request(model_id: str, content: str, third_api_key: str):
    body = {
        "model": model_id,
        "mode": "common_prefix",
        "messages": [{"role": "system", "content": str(content)}],
        "ttl": int(timedelta(hours=12).total_seconds()),
    }
    url = "https://ark.cn-beijing.volces.com/api/v3/context/create"
    authorization = f"Bearer {third_api_key}"
    if env_util.get_current_env() == Env.LOCAL:
        inner_response = await request_util.post_json(
            url="http://tavern-admin-api.655356.xyz/no_auth/llm_model/cache/ark_request",
            post_data={
                "url": url,
                "post_data": body,
                "authorization": authorization,
            },
        )
        if inner_response and "data" in inner_response:
            response = inner_response["data"]
            return response
        return None

    response = await request_util.post_json(
        url=url,
        post_data=body,
        headers={
            "Authorization": authorization,
            "Content-Type": "application/json",
        },
        timeout=5,
        max_retries=1,
    )
    return response


@exception_util.async_ignore_catch_exception
async def ark_post(
    cache_id: str, input: ChatNextInput, content: str, lock_key: str = "ark_post_lock"
):
    now_timestamp = int(date_util.now().timestamp())
    response = await ark_request(
        model_id=input.third_model_id,
        content=content,
        third_api_key=input.third_api_key,
    )
    if not response:
        log.warning(
            f"ArkPost failed, no response,user_id:{input.user_id} cache_id: {cache_id}"
        )
        redis_client.release_lock(lock_key, cache_id)
        return None
    id = response.get("id", "")
    ttl = int(response.get("ttl", 0))
    if not id or not ttl:
        log.error(f"ArkPost failed,user_id:{input.user_id} response: {response}")
        redis_client.release_lock(lock_key, cache_id)
        return None
    log.info(
        f"ArkPost success,user_id:{input.user_id},cache_id:{cache_id},response: {response}"
    )
    context_record = await VolEngineContext.filter(
        cache_id=cache_id, third_model_id=input.third_model_id
    ).first()
    if not context_record:
        context_record = VolEngineContext(
            cache_id=cache_id, third_model_id=input.third_model_id
        )
    context_record.expired_at = now_timestamp + ttl
    context_record.request_model = input.request_model
    context_record.context_id = id
    context_record.role_id = input.role_id
    context_record.user_id = input.user_id
    context_record.conversation_id = input.conversation_id
    context_record.message_id = input.message_id
    context_record.version = input.version
    await context_record.save()
    history = VolEngineContextHistory(
        context_id=context_record.context_id,
        cache_id=cache_id,
        third_model_id=input.third_model_id,
        request_model=input.request_model,
        cache_content=str(content),
        user_id=input.user_id,
        role_id=input.role_id,
        message_id=input.message_id,
        conv_id=input.conversation_id,
        created_at=date_util.now(),
        updated_at=date_util.now(),
    )
    await vol_engine_context_history_dao.insert(history)

    redis_client.release_lock(lock_key, cache_id)
    return None


async def ds_cache_record(input: ChatNextInput, input_messages: list[BaseMessage]):
    if input_messages and input_messages[-1].type == "ai":
        log.error(f"UnSupport lastMsg,uid:{input.user_id},model: {input.preset_model}")
        return input_messages, input.request_model
    if input_messages and input_messages[0].type != "system":
        log.error(f"UnSupport firstMsg,uid:{input.user_id},model: {input.preset_model}")
        return input_messages, input.request_model
    sys_msg_content = str(input_messages[0].content)
    token = token_util.num_tokens_from_string(sys_msg_content)
    cache_id = md5(sys_msg_content.encode("utf-8")).hexdigest()
    context_record = await VolEngineContext.filter(
        cache_id=cache_id, request_model=input.request_model
    ).first()
    now_timestamp = int(date_util.now().timestamp())
    if input.auto_retry:
        return input_messages, input.request_model
    if context_record and context_record.expired_at > now_timestamp:
        input.read_token = token
        input.context_id = context_record.context_id
        input_messages = input_messages[1:]
        log.info(
            f"HitCache,user_id:{input.user_id},cache_id:{cache_id},context_id:{input.context_id},model:{input.request_model}"
        )
        return input_messages, input.request_model
    if not input.third_model_id or not input.third_api_key:
        logging.error(f"ParamEmpty,preset_model: {input.request_model}")
        return input_messages, input.request_model
    lock_name = "ark_post_lock"
    lock = redis_client.acquire_lock(lock_name, cache_id, 10)
    if not lock:
        return input_messages, input.request_model

    input.created_token = token
    asyncio.create_task(ark_post(cache_id, input, sys_msg_content, lock_name))
    return input_messages, input.request_model


@exception_util.async_ignore_catch_exception
async def ds_cache_statistic(
    input: ChatNextInput, message: list[BaseMessage], input_token_sum: int
):
    system_message = ""
    for msg in message:
        if msg.type == "system":
            system_message += str(msg.content)
            continue
        break
    if not system_message:
        return
    cache_id = hashlib.md5(system_message.encode()).hexdigest()
    preset_model = input.preset_model
    role_id = input.role_id
    cache_token = token_util.num_tokens_from_string(system_message)
    await LlmModelCacheHistory.create(
        cache_id=cache_id,
        preset_model=preset_model,
        role_id=role_id,
        sum_token=input_token_sum,
        cache_token=cache_token,
        conversation_id=input.conversation_id,
        message_id=input.message_id,
        version=input.version,
        user_id=input.user_id,
    )
