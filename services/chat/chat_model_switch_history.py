from datetime import datetime
import logging

from common.common_constant import ModelEventType
from persistence.models.models import UserModelEventHistory

log = logging.getLogger(__name__)


# 在直聊bot里切换模型的事件记录
async def add_model_switch_history_in_bot(
    user_id: int,
    event_type: str,
    from_model: str,
    from_model_name: str,
    to_model: str,
    to_model_name: str,
    from_chat_channel: str = "",
    to_chat_channel: str = "",
):
    return await UserModelEventHistory.create(
        user_id=user_id,
        event_type=event_type,
        event_ts=int(datetime.now().timestamp()),
        from_model=from_model,
        from_model_name=from_model_name,
        to_model=to_model,
        to_model_name=to_model_name,
        from_chat_channel=from_chat_channel,
        to_chat_channel=to_chat_channel,
    )


# 小程序：用户聊天过程中自动或手动切换模型的事件的记录
async def add_model_switch_history(
    user_id: int,
    mode_type: str,
    mode_target_id: int,
    conversation_id: str,
    message_id: str,
    event_type: str,
    from_model: str,
    from_model_name: str,
    to_model: str,
    to_model_name: str,
    from_chat_channel:str,
    to_chat_channel:str
):

    return await UserModelEventHistory.create(
        user_id=user_id,
        mode_type=mode_type,
        mode_target_id=mode_target_id,
        conversation_id=conversation_id,
        message_id=message_id,
        event_type=event_type,
        event_ts=int(datetime.now().timestamp()),
        from_model=from_model,
        from_model_name=from_model_name,
        to_model=to_model,
        to_model_name=to_model_name,
        from_chat_channel=from_chat_channel,
        to_chat_channel=to_chat_channel,
    )


# 按时间升序获取event历史
async def get_model_switch_history(
    user_id: int, mode_type: str, mode_target_id: int, conversation_id: str
):
    return (
        await UserModelEventHistory.filter(
            user_id=user_id,
            mode_type=mode_type,
            mode_target_id=mode_target_id,
            conversation_id=conversation_id,
        )
        .order_by("event_ts")
        .all()
    )


async def _get_last_model_switch_history(
    user_id: int, mode_type: str, mode_target_id: int, conversation_id: str
):
    return (
        await UserModelEventHistory.filter(
            user_id=user_id,
            mode_type=mode_type,
            mode_target_id=mode_target_id,
            conversation_id=conversation_id,
        )
        .order_by("-event_ts")
        .first()
    )


async def try_add_chat_entry_model_history(
    user_id: int,
    mode_type: str,
    mode_target_id: int,
    conversation_id: str,
    message_id: str,
    model_mid: str,
    model_name: str,
):
    last_history = await _get_last_model_switch_history(
        user_id, mode_type, mode_target_id, conversation_id
    )
    # 如果上次的记录类型也是进入聊天页面，并且消息ID和模型都相同，则不记录
    if (
        last_history
        and last_history.message_id == message_id
        and last_history.event_type == ModelEventType.CHAT_ENTRY.value
        and last_history.to_model == model_mid
    ):
        return False
    await UserModelEventHistory.create(
        user_id=user_id,
        mode_type=mode_type,
        mode_target_id=mode_target_id,
        conversation_id=conversation_id,
        message_id=message_id,
        event_type=ModelEventType.CHAT_ENTRY,
        event_ts=int(datetime.now().timestamp()),
        from_model=model_mid,
        from_model_name=model_name,
        to_model=model_mid,
        to_model_name=model_name,
    )
