# 解析bot当前语言
from common.common_constant import Language
from persistence.models.models import User


def parse_bot_language(language_code: str | None) -> Language:
    if not language_code or language_code == "en" or language_code.startswith("en-"):
        return Language.EN
    if language_code == "zh-hans":
        return Language.ZH
    if language_code == "zh-hant" or language_code.startswith("zh-"):
        return Language.ZH_TW

    for lang in Language:
        prefix = lang.value.split("-")[0] if "-" in lang.value else lang.value
        if language_code == prefix or language_code.startswith(prefix + "-"):
            return lang
    return Language.EN


async def get_user_language(
    user: User | None, bot_lang: str, language_code: str | None
) -> Language:
    if bot_lang == Language.ZH.value:
        return Language.ZH
    if user and user.language and user.language in Language.fetch_all():
        return Language(user.language)
    if not language_code:
        return Language.EN
    language = parse_bot_language(language_code)
    if language  == Language.ZH:
        return Language.EN
    return language