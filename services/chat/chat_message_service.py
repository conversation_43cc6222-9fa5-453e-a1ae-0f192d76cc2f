from datetime import datetime
from itertools import groupby
import logging
import random
import re
from typing import Dict
import uuid

from openai import APIConnectionError
from ai import lite_llm_bot, new_chat_bot
from common.chat_bot_model import ChatStreamResponse
from common.common_constant import (
    ChatChannel,
    ChatModeType,
    ChatPlatform,
    Language,
    LiteLlmErrorType,
    LlmCacheType,
    ModelEventType,
    PresetReplace,
    ProductType,
    ChatApiVersion,
    UserModelFilter,
)
from common.models.chat_model import (
    BuildHistoryQuery,
    ChatHistory,
    ChatHistoryStatus,
    ChatHistoryType,
    ChatNextInput,
    HistoryRequest,
    SelectModelRet,
    UserSbtDetail,
)
from common.models.chat_request import (
    ChatConversation,
    ChatHistoryItem,
    ChatHistoryResponse,
    ChatRequest,
    ImpersonateRequest,
    ModelEventItem,
)
from common.role_model import RoleDataConfig
from persistence import chat_history_dao, presets_dao
from persistence.presets import <PERSON><PERSON><PERSON>
from services import (
    config_service,
    model_service,
    preset_service,
    product_service,
    regex_service,
    role_access_service,
    role_config_service,
    translate_service,
    user_alt_profile_service,
    user_role_service,
    user_service,
)
from services.account_service import AccountService
from services.chat import chat_model_switch_history, chat_result_service
from services.chat.history import chat_history_service
from services.role import (
    character_book_service,
    role_audit_service,
    role_group_service,
    role_loader_service,
)
from services.user import user_benefit_service
from utils import (
    char_book_util,
    convert_util,
    date_util,
    example_util,
    exception_util,
    json_util,
    response_util,
    role_util,
    str_util,
    tg_util,
    token_util,
)
from persistence.models.models import (
    LlmModelConfig,
    Product,
    RegexOption,
    RoleConfig,
    User,
    UserAltConfigStatus,
    UserRegisterSource,
    UserModelEventHistory,
    UserRoleSnapshot,
    UserStatus,
)
from utils import message_utils
from utils.translate_util import _tl


log = logging.getLogger(__name__)


def verify_chat_param(user: User, role_config: RoleConfig):
    if role_access_service.allow_chat(user, role_config) == False:
        return "Not Your Role"
    if not role_config.status:
        return "Role has been disabled"
    if user.status == UserStatus.CHAT_BLACK.value:
        return "Account Error,Please contact the administrator"
    return None


# async def _build_user_base_info(
#     user_id: int, chat_input: ChatNextInput
# ) -> ChatNextInput:
#     user = await user_service.get_by_id(user_id)
#     chat_input.nickname = user.nickname
#     chat_input.user_id = user.id
#     chat_input.user_status = user.status
#     chat_input.register_source = user.register_source
#     chat_input.status_block_switch = user.status_block_switch

#     chat_input.balance = await AccountService.get_total_balance(user.id)
#     chat_input.payed_balance = await AccountService.get_payed_total_balance(user.id)
#     chat_input.chat_free_benefit = await user_benefit_service.chat_model_count(user)
#     if user.chat_channel:
#         chat_input.chat_channel = user.chat_channel
#     user_chat_product = await product_service.get_user_chat_product(user)
#     if user_chat_product:
#         chat_input.llm_model = user_chat_product.model
#         chat_input.user_chat_product_mid = user_chat_product.mid

#     async def build_user_model_filter():
#         if chat_input.chat_channel == ChatChannel.FREE_BENEFIT.value:
#             return UserModelFilter.FREE_BENEFIT.value
#         if chat_input.chat_channel == ChatChannel.PAID.value:
#             payed_balance = await AccountService.get_payed_total_balance(user.id)
#             if user_chat_product and user_chat_product.price <= payed_balance:
#                 need_deduct_diamond, _ = await AccountService.is_deduct_diamond_first(
#                     user.id, user_chat_product, chat_input.role_id
#                 )
#                 if need_deduct_diamond:
#                     return UserModelFilter.FREE_BENEFIT.value
#         return UserModelFilter.DEFAULT.value

#     chat_input.user_model_filter = await build_user_model_filter()

#     # 用户状态栏
#     return chat_input


# async def _build_role_base_info(
#     role_id: int, chat_input: ChatNextInput
# ) -> ChatNextInput:
#     if role_id == 0:
#         return chat_input
#     role_config = await role_config_service.get_by_id(role_id)
#     translate_role_config = await role_loader_service.load_translated_role(
#         role_id, chat_input.language, "", format=False
#     )

#     if not role_config or not translate_role_config:
#         raise Exception(f"role_config not found,role_id:{role_id}")

#     user_name_model = role_util.get_new_user_role_name(
#         translate_role_config, chat_input.nickname
#     )
#     role_config = role_util.format_role_config(
#         translate_role_config, user_name_model.request_user_name
#     )

#     data_config = RoleDataConfig(**convert_util.to_dict(role_config.data_config))
#     chat_input.privacy = role_config.privacy
#     chat_input.status = role_config.status
#     chat_input.nsfw = role_config.nsfw
#     chat_input.role_created_uid = role_config.uid
#     chat_input.role_name = role_config.role_name
#     chat_input.role_chat_type = role_config.chat_type
#     chat_input.user_role_name = user_name_model.user_role_name
#     chat_input.request_user_name = user_name_model.request_user_name
#     chat_input.display_user_name = user_name_model.display_user_name
#     chat_input.role_default_language = role_config.def_language

#     chat_input.description = data_config.description
#     if role_config.switch_en_desc:
#         description_en = await translate_service.get_role_description(
#             role_id, chat_input.language
#         )
#         if description_en:
#             description_en = str_util.format_char_and_user(
#                 description_en, role_config.role_name, user_name_model.request_user_name
#             )
#         chat_input.description = (
#             description_en if description_en else data_config.description
#         )
#     chat_input.personality = data_config.personality
#     chat_input.format_example = example_util.analysis_example(
#         data_config,
#         "[Example Chat]",
#         chat_input.request_user_name,
#         chat_input.role_name,
#     )
#     if data_config.statusBlockEnable:
#         chat_input.status_block_enable = data_config.statusBlockEnable
#         chat_input.status_block_type = data_config.statusBlockType
#         chat_input.status_block = str_util.format_status_block(
#             data_config.status_block, data_config.statusBlockType
#         )
#         chat_input.status_rules = data_config.status_block_rule
#         # 状态栏数据不存在依然关闭
#         if (
#             not data_config.status_block
#             and not data_config.status_block_init
#             and not data_config.status_block_rule
#         ):
#             chat_input.status_block_enable = False

#     if chat_input.mode_type == ChatModeType.GROUP.value:
#         group_detail = await role_group_service.load_detail_by_id(chat_input.group_id)
#         role_name_map = {x.id: x.role_name for x in group_detail.roles}
#         group_detail.scenario = role_util.fill_group_scenario_role_name(
#             group_detail.scenario, role_name_map
#         )
#         chat_input.scenario = str_util.format_user(
#             group_detail.scenario, chat_input.request_user_name
#         )

#     chat_input.replay_len_ratio = data_config.replay_len_ratio

#     chat_input.book_id = role_config.book_id
#     chat_input.first_message = role_util.format_first_message(
#         role_config, user_name_model.request_user_name
#     )

#     return chat_input


# async def build_chat_input_param(
#     user_id: int, payload: ChatRequest, language: str
# ) -> ChatNextInput:
#     chat_input = ChatNextInput(
#         mode_type=payload.mode_type,
#         group_id=payload.group_id,
#         role_id=payload.role_id,
#         input_content=payload.message,
#         conversation_id=payload.conversation_id,
#         isRetry=payload.isRetry,
#         auto_retry=payload.auto_retry,
#         retry_message_id=payload.retry_message_id,
#         language=language,
#         chat_continue=payload.chat_continue,
#         input_last_message_id=payload.last_message_id,
#         input_last_message_version=payload.last_message_version,
#         timestamp_start=int(datetime.now().timestamp()),
#         api_version=payload.api_version,
#     )
#     chat_input = await _build_user_base_info(user_id, chat_input)
#     chat_input = await _build_role_base_info(payload.role_id, chat_input)

#     original_regex_rules = await regex_service.get_effective_regex_rules()

#     chat_input.regex_rules = original_regex_rules
#     chat_input.message_id = uuid.uuid4().hex
#     chat_input.version = int(datetime.now().timestamp())
#     if payload.retry_message_id and payload.isRetry:
#         chat_input.message_id = payload.retry_message_id

#     return chat_input


# history相关逻辑


async def latest_cov_id(user_id: int, req: HistoryRequest) -> str:
    if req.mode_type == ChatModeType.GROUP.value:
        return await chat_history_dao.get_latest_conversation_id(
            user_id, req.mode_type, req.get_mode_target_id()
        )
    else:
        return await chat_history_dao.get_single_latest_conversation_id(
            user_id, req.role_id
        )


async def init_first_history_message(
    user: User,
    req: HistoryRequest,
    platform=ChatPlatform.TMA.value,
    user_snapshot: UserRoleSnapshot | None = None,
):
    req.conversation_id = uuid.uuid4().hex
    if req.mode_type != ChatModeType.SINGLE.value:
        return req
    role_config = await role_loader_service.load_translated_role(
        req.role_id, req.language, PresetReplace.USER.value
    )
    if not role_config:
        return req
    first_message = role_util.format_first_message_on_save(role_config)
    if not first_message:
        return req
    if user_snapshot and user_snapshot.first_message:
        first_message = user_snapshot.first_message
    version = int(datetime.now().timestamp())
    chat_history = ChatHistory(
        user_id=user.id,
        mode_type=req.mode_type,
        mode_target_id=req.get_mode_target_id(),
        role_id=role_config.id,
        content=first_message,
        conversation_id=req.conversation_id,
        message_id=uuid.uuid4().hex,
        type=ChatHistoryType.AI,
        timestamp=int(datetime.now().timestamp()),
        version=version,
        platform=platform,
    )
    await chat_history_dao.insert_message(message=chat_history)
    return req


# 获取指定条数的原始历史消息,按时间正序排列
async def get_latest_history_msg(
    user_id: int, conversation_id: str, count: int
) -> list[ChatHistory]:
    user_history = await chat_history_dao.list_user_history(conversation_id)
    if not user_history:
        return []
    history = [
        max(g, key=lambda x: x.timestamp)
        for k, g in groupby(user_history, key=lambda x: x.message_id)
    ]
    if any([x.user_id != user_id for x in history]):
        return []
    return history[-count:]


async def get_last_message_id_by_conv_id(user_id: int, conversation_id: str):
    return await chat_history_dao.get_last_message_id_by_conv_id(
        user_id, conversation_id
    )


async def get_display_message(
    user_id: int, message_id: str, version: int, language: str, user_sbt: UserSbtDetail
) -> ChatHistoryItem | None:
    chat_history = await chat_history_dao.get_by_message_id_and_version(
        message_id, version
    )
    user = await user_service.get_user_by_id(user_id)
    if not chat_history or chat_history.user_id != user_id:
        return None
    query = BuildHistoryQuery(
        user_id=user_id,
        nickname=user.nickname,
        mode_type=chat_history.mode_type,
        mode_target_id=chat_history.mode_target_id,
        conversation_id=chat_history.conversation_id,
        language=language,
        regex_option="",
        add_name=False,
        use_display_user_name=True,
    )
    if user_sbt and user.status_block_switch:
        query.usb_switch = user.status_block_switch
        query.usb_saved_time = user_sbt.last_saved_time
        query.usb_message_id = user_sbt.message_id
        query.usb_message_version = user_sbt.message_version

    history_ret = await chat_history_service.build_history(
        query=query, user_history=[chat_history]
    )

    if not history_ret.history_list:
        return None
    return history_ret.history_list[0]


async def build_history(
    query: BuildHistoryQuery, user_history: list[ChatHistory] = []
) -> list[ChatHistoryItem]:
    if not user_history:
        user_history = await chat_history_dao.list_user_history(query.conversation_id)
    history = [
        max(g, key=lambda x: x.timestamp)
        for k, g in groupby(user_history, key=lambda x: x.message_id)
    ]
    if any([x.user_id != query.user_id for x in history]):
        return []

    not_repair_msg_id = ""
    if (
        history
        and history[-1].type == ChatHistoryType.AI.value
        and history[-1].chat_continue
    ):
        not_repair_msg_id = history[-1].message_id
    for his in history:
        if (
            his.message_id == not_repair_msg_id
            or his.type == ChatHistoryType.HUMAN.value
        ):
            continue
        his.content = message_utils.repair_content(his.content)

    role_ids = list(set([x.role_id for x in history]))
    all_role_list = await role_loader_service.load_translated_roles(
        role_ids, "", query.language, format=False
    )
    role_name_maps = {x.id: x.role_name for x in all_role_list}
    history_nicknames = await user_alt_profile_service.get_user_history_nicknames(
        query.user_id
    )
    final_user_name = (
        query.request_user_name if query.use_request_user_name else query.nickname
    )
    history = message_utils.process_user_name(query, history, history_nicknames)
    rules = await regex_service.get_effective_regex_rules()
    rules = [
        rule
        for rule in rules
        if query.regex_option and query.regex_option in rule.options
    ]
    ret: list[ChatHistoryItem] = []
    # 相同AI消息深度相同
    depth = 0
    index = len(history)
    while index > 0:
        index -= 1
        x = history[index]
        role_name = role_name_maps.get(x.role_id, "")
        if index < len(history) - 1 and x.type != history[index + 1].type:
            depth += 1

        content = message_utils.process_new_regex_rules(
            rules,
            x.content,
            x.type,
            role_name,
            final_user_name,
            depth,
        )
        # content = re.sub(r"<!--.*?-->", "", content, flags=re.DOTALL)
        content = re.sub(r"<wit>.*?</wit>", "", content, flags=re.DOTALL)
        content = re.sub(f"{role_name}[:：]", "", content, flags=re.DOTALL)
        if query.add_name:
            content = message_utils.add_name_to_content(
                content, x.type, role_name, final_user_name
            )
        # 移除状态栏
        if message_utils.require_remove_status_block(query, x):
            content = message_utils.remove_status_block(content)
        ret.append(
            ChatHistoryItem(
                content=content,
                type=x.type,
                timestamp=x.timestamp,
                version=x.version,
                voice_url=x.voice_url,
                message_id=x.message_id,
                role_id=x.role_id,
                photo_url=x.photo_url,
                photo_id=x.photo_id,
                retry_photos=x.retry_photos,
                can_continue_replay=False,
            )
        )
    ret.reverse()

    if history and history[-1].type == ChatHistoryType.AI.value:
        ret[-1].can_continue_replay = history[-1].chat_continue
    return ret


async def display_history_by_conv_id(
    user_id: int, nickname: str, conv_id: str, language: str = Language.ZH.value
) -> list[ChatHistoryItem]:
    history = await chat_history_dao.get_last_message_by_conv_id(user_id, conv_id)
    if not history:
        return []
    history_query = BuildHistoryQuery(
        user_id=user_id,
        nickname=nickname,
        mode_type=history.mode_type,
        mode_target_id=history.mode_target_id,
        conversation_id=conv_id,
        language=language,
        regex_option="",
        add_name=True,
        use_display_user_name=True,
    )
    history_ret = await chat_history_service.build_history(query=history_query)
    return history_ret.history_list


async def list_user_history(
    user: User, request: HistoryRequest, user_snapshot: UserRoleSnapshot | None = None
) -> ChatHistoryResponse:
    history_query = BuildHistoryQuery(
        user_id=user.id,
        nickname=user.nickname,
        mode_type=request.mode_type,
        mode_target_id=request.get_mode_target_id(),
        conversation_id=request.conversation_id,
        language=request.language,
        regex_option="",
        add_name=False,
        use_display_user_name=True,
        usb_switch=user.status_block_switch,
        # regex_option=RegexOption.FORMAT_DISPLAY.value,
    )
    history_ret = await chat_history_service.build_history(history_query)
    chat_list = history_ret.history_list
    # 获取当前聊天历史中的模型切换相关事件
    model_event_map = await _handle_model_event_record(chat_list, history_query, user)
    ret = ChatHistoryResponse(
        conversation_id=request.conversation_id,
        user_id=user.id,
        chat_list=chat_list,
        model_event_map=model_event_map,
    )
    # 获取当前聊天历史中的图片点赞状态
    if request.mode_type == ChatModeType.SINGLE.value:
        ret.photos_likes_map = (
            await user_role_service.get_photo_likes_map_by_conversation_id(
                user.id, request.role_id, request.conversation_id
            )
        )

    for key, values in ret.model_event_map.items():
        for item in values:
            item.from_model_name = _tl(
                item.from_model_name, history_query.language, Product.__name__
            )
            item.to_model_name = _tl(
                item.to_model_name, history_query.language, Product.__name__
            )
    if request.mode_type == ChatModeType.SINGLE.value:
        single_role = await role_loader_service.load_translated_role(
            request.role_id, request.language, user.nickname
        )
        if not single_role:
            return ret
        data_config = RoleDataConfig(**convert_util.to_dict(single_role.data_config))
        ret.role_id = single_role.id
        ret.role_name = single_role.role_name
        ret.card_name = single_role.card_name
        ret.role_avatar = str_util.format_avatar(single_role.role_avatar)
        ret.introduction = single_role.introduction
        ret.scenario = role_util.format_display_scenario(single_role, user.nickname)
        ret.private_card = bool(not single_role.privacy)
        ret.support_photo = single_role.support_photo
        ret.chat_type = single_role.chat_type
        ret.support_product_ids = (
            await role_access_service.get_support_product_list_by_role(single_role)
        )
        switch_model, _, _ = await role_access_service.check_model_switch_needed(
            single_role, user
        )
        ret.switch_model = switch_model
        if user_snapshot and user_snapshot.scenario:
            ret.scenario = str_util.format_char_and_user(
                user_snapshot.scenario, single_role.role_name, user.nickname
            )
        if (
            data_config.muilte_scenes
            and data_config.muilte_scenes[0].user_first_messages
        ):
            ret.user_first_messages = [x for x in data_config.muilte_scenes[0].user_first_messages if x.message]
        return ret
    group_info = await role_group_service.get_translate_detail_by_id(
        request.get_mode_target_id(), request.language
    )
    audit_info = await role_audit_service.get_by_mode(
        request.mode_type, request.get_mode_target_id()
    )
    if audit_info:
        ret.audit_status = audit_info.status
    if group_info:
        role_ids = [x.id for x in group_info.roles]
        ret.introduction = str_util.format_user(group_info.introduction, user.nickname)
        ret.scenario = str_util.format_user(group_info.scenario, user.nickname)
        ret.group_name = group_info.name
        ret.roles = group_info.roles
        ret.support_product_ids = (
            await role_access_service.get_support_product_list_by_role_ids(role_ids)
        )
        switch_model = await role_access_service.check_need_model_switch(
            ret.support_product_ids, user
        )
        ret.switch_model = switch_model
        ret.private_card = group_info.private_card
        ret.deleted_roles = group_info.deleted_roles
    return ret


# 获取当前聊天历史中的模型切换相关事件
async def _handle_model_event_record(
    chat_list: list[ChatHistoryItem], history_query: BuildHistoryQuery, user: User
):
    if not chat_list:
        return {}

    model_event_list = await chat_model_switch_history.get_model_switch_history(
        history_query.user_id,
        history_query.mode_type,
        history_query.mode_target_id,
        history_query.conversation_id
    )
    if not model_event_list:
        # 处理进入聊天页面的事件
        entity_map = {}
        await _handle_chat_entry_model_event(
            history_query, chat_list[-1].message_id, entity_map, user
        )
        return entity_map

    timestamp_msg_id_map = {x.timestamp: x.message_id for x in chat_list}
    timestamp_list = list(timestamp_msg_id_map.keys())
    timestamp_list.sort()

    msg_id_set = set(timestamp_msg_id_map.values())

    entity_map = {}
    for model_event in model_event_list:
        if model_event.message_id in msg_id_set:
            _put_model_event_to_map(entity_map, model_event.message_id, model_event)
        else:
            event_ts = model_event.event_ts
            # 从timestamp_list中找到最后一个小于等于event_ts的时间戳
            target_ts = _find_last_less_than_or_equal(timestamp_list, event_ts)
            if target_ts is None:
                continue
            targe_message_id = timestamp_msg_id_map.get(target_ts)
            _put_model_event_to_map(entity_map, targe_message_id, model_event)  # type: ignore

    # 处理进入聊天页面的事件
    await _handle_chat_entry_model_event(
        history_query, chat_list[-1].message_id, entity_map, user
    )

    return entity_map


async def _handle_chat_entry_model_event(
    history_query: BuildHistoryQuery,
    last_message_id: str,
    entity_map: Dict[str, list],
    user: User,
):
    if not last_message_id:
        return
    current_model_product = await product_service.get_user_chat_product(user)
    if not current_model_product:
        current_model_product = await product_service.get_default_chat_product()
    # 最新一条消息的最后一个 event 是进入聊天页面事件，并且使用的模型名称跟此次进入聊天页面的模型名称相同，则不记录
    if last_message_id in entity_map:
        event_list = entity_map.get(last_message_id, [])
        if event_list:
            last_event = event_list[-1]
            if (
                last_event.event_type == ModelEventType.CHAT_ENTRY.value
                and last_event.to_model == current_model_product.mid
            ):
                return
    # 记录进入聊天页面的事件
    await chat_model_switch_history.add_model_switch_history(
        user_id=user.id,
        mode_type=history_query.mode_type,
        mode_target_id=history_query.mode_target_id,
        conversation_id=history_query.conversation_id,
        message_id=last_message_id,
        event_type=ModelEventType.CHAT_ENTRY.value,
        from_model=current_model_product.mid,
        from_model_name=current_model_product.display_name,
        to_model=current_model_product.mid,
        to_model_name=current_model_product.display_name,
        from_chat_channel=user.chat_channel,
        to_chat_channel=user.chat_channel,
    )

    chat_entry_event = ModelEventItem(
        event_type=ModelEventType.CHAT_ENTRY.value,
        from_model=current_model_product.mid,
        from_model_name=current_model_product.display_name,
        to_model=current_model_product.mid,
        to_model_name=current_model_product.display_name,
        from_chat_channel=user.chat_channel,
        to_chat_channel=user.chat_channel,
    )
    # 将此次进入聊天页面的事件添加到消息ID对应的事件列表中
    model_event_list = entity_map.get(last_message_id, [])
    model_event_list.append(chat_entry_event)
    entity_map[last_message_id] = model_event_list


def _find_last_less_than_or_equal(timestamp_list, event_ts):
    if not timestamp_list or event_ts < timestamp_list[0]:
        return None

    left, right = 0, len(timestamp_list) - 1
    result = None

    # Binary search to find the last timestamp <= event_ts
    while left <= right:
        mid = (left + right) // 2
        if timestamp_list[mid] <= event_ts:
            # Found a candidate, but there might be a larger one still <= event_ts
            result = timestamp_list[mid]
            left = mid + 1
        else:
            # Current timestamp is too large, look in the left half
            right = mid - 1

    return result


def _put_model_event_to_map(
    entity_map: Dict[str, list[ModelEventItem]],
    message_id: str,
    model_event: UserModelEventHistory,
):
    event_item_list: list = entity_map.get(message_id, [])
    event_item = ModelEventItem(
        event_type=model_event.event_type,
        from_model=model_event.from_model,
        from_model_name=model_event.from_model_name,
        to_model=model_event.to_model,
        to_model_name=model_event.to_model_name,
        from_chat_channel=ChatChannel.safe_parse(model_event.from_chat_channel).value,
        to_chat_channel=ChatChannel.safe_parse(model_event.to_chat_channel).value,
    )
    # 此处是按时间戳升序排列的
    event_item_list.append(event_item)
    entity_map[message_id] = event_item_list


# chat相关逻辑
async def save_user_message(
    user: User,
    input: ChatNextInput,
    payload: ChatRequest,
    platform=ChatPlatform.TMA.value,
):
    content = message_utils.process_on_edit_rules(
        payload.message, input.regex_rules, True, input.role_name, input.user_role_name
    )
    content = message_utils.remove_prefix_user_name(content, input.display_user_name)
    message = await chat_history_dao.get_last_message_by_conv_id(
        user.id,
        payload.conversation_id,
    )

    if (
        message
        and message.type == ChatHistoryType.HUMAN.value
        and message.content == content
    ):
        log.warning(
            f"Duplicate message detected, user_id: {user.id}, conversation_id: {payload.conversation_id}, content: {content}"
        )
    chat_history = ChatHistory(
        user_id=user.id,
        mode_type=payload.mode_type,
        mode_target_id=payload.get_mode_target_id(),
        role_id=input.role_id,
        content=content,
        conversation_id=payload.conversation_id,
        message_id=uuid.uuid4().hex,
        type=ChatHistoryType.HUMAN,
        timestamp=int(datetime.now().timestamp()),
        version=int(datetime.now().timestamp()),
        platform=platform,
    )
    await chat_history_dao.insert_message(message=chat_history)
    return chat_history


async def delete_message(user_id: int, message_ids: list[str]):
    list = await chat_history_dao.list_by_message_ids(message_ids)
    un_exist_list = [x for x in list if x.user_id != user_id]
    if un_exist_list:
        return response_util.json_param_error("permission denied")
    await chat_history_dao.update_message_status(
        user_id, message_ids, ChatHistoryStatus.DELETED.value
    )
    return response_util.success({"modify_count": len(message_ids)})

async def del_messages(user_id: int, message_ids: list[str]):
    list = await chat_history_dao.list_by_message_ids(message_ids)
    un_exist_list = [x for x in list if x.user_id != user_id]
    if un_exist_list:
        raise exception_util.verify_exception(message="无权限删除他人消息")
    return await chat_history_dao.update_message_status(
        user_id, message_ids, ChatHistoryStatus.DELETED.value
    )
    


async def build_character_book(input: ChatNextInput) -> ChatNextInput:
    character_book = await character_book_service.chat_effective_book(
        input.book_id, input.language, input.role_default_language
    )
    if not character_book or not character_book.entries:
        return input

    for entry in character_book.entries:
        entry.content = str_util.format_char_and_user(
            entry.content, input.role_name, input.request_user_name
        )
    input.constant_book_entries = char_book_util.list_constant_book_entries(
        character_book
    )
    keywords_book_entries = char_book_util.list_keywords_book_entries(
        character_book, input.history
    )
    max_token = 1000
    ret_keywords_book_entries = []
    if keywords_book_entries:
        for mid in keywords_book_entries:
            max_token -= token_util.num_tokens_from_string(mid.content)
            if max_token < 0:
                break
            ret_keywords_book_entries.append(mid)
    input.keywords_book_entries = ret_keywords_book_entries
    return input


# 从用户自己配置的身份介绍中抽取有效的身份介绍作为补充
async def build_user_custom_persona(input: ChatNextInput) -> ChatNextInput:
    active_profile = await user_alt_profile_service.get_user_active_profile(
        input.user_id
    )
    if not active_profile or not active_profile.persona_setting:
        return input

    valid_persona_content = active_profile.persona_setting
    if not valid_persona_content:
        return input
    # 临时支持
    if active_profile.nickname and active_profile.nickname in valid_persona_content:
        valid_persona_content = valid_persona_content.replace(
            active_profile.nickname, input.request_user_name
        )

    input.user_personality = str_util.format_char_and_user(
        valid_persona_content, input.role_name, input.request_user_name
    )
    return input


async def build_request_history(input: ChatNextInput) -> ChatNextInput:
    # create history
    history_query = BuildHistoryQuery(
        user_id=input.user_id,
        nickname=input.nickname,
        request_user_name=input.request_user_name,
        mode_type=input.mode_type,
        mode_target_id=(
            input.group_id
            if input.mode_type == ChatModeType.GROUP.value
            else input.role_id
        ),
        conversation_id=input.conversation_id,
        language=input.language,
        add_name=input.add_name_prefix,
        regex_option=RegexOption.FORMAT_PROMPT.value,
        use_request_user_name=True,
        usb_switch=input.usb_switch,
        usb_saved_time=input.usb_saved_time,
        usb_message_id=input.usb_message_id,
        usb_message_version=input.usb_message_version,
        filter_repeat_message=True
    )
    build_history_ret = await chat_history_service.build_history(query=history_query)
    history_list = build_history_ret.history_list
    if input.retry_message_id and input.isRetry:
        history_list = [
            x for x in history_list if x.message_id != input.retry_message_id
        ]
    input.first_ai_message = message_utils.get_request_first_ai_message(
        history_query, history_list, input
    )
    if history_list and history_list[-1].type == ChatHistoryType.HUMAN.value:
        input.last_user_message = history_list[-1].content
        input.last_message_id = len(history_list) - 1
    input.history = history_list

    last_ai_message = (
        history_list[-1].content
        if history_list and history_list[-1].type == ChatHistoryType.AI.value
        else ""
    )

    if input.chat_continue and last_ai_message:
        input.chat_continue_ai_msg = (
            f"<rep>{last_ai_message}"
            if "<rep>" not in last_ai_message
            else last_ai_message
        )
        input.chat_continue_message_id = history_list[-1].message_id
        input.history = input.history[:-1]
        input.message_id = history_list[-1].message_id
    input.history = message_utils.repair_last_status_block(
        input.history,
        history_query,
        build_history_ret.latest_status_block,
        input.status_block_type,
    )
    return input


async def build_impersonate_request_history(input: ChatNextInput) -> ChatNextInput:
    input = await build_request_history(input)
    for msg in input.history:
        if msg.type == "ai" and (
            "<sp>" in msg.content or "<StatusBlock>" in msg.content
        ):
            # remove all <StatusBlock> content
            msg.content = re.sub(
                r"<StatusBlock>.*?</StatusBlock>", "", msg.content, flags=re.DOTALL
            )
            # remove all <sp> content
            msg.content = re.sub(r"<sp>.*?</sp>", "", msg.content, flags=re.DOTALL)
            # remove all xml tags in fm
            msg.content = re.sub(r"<[^>]+>", "", msg.content)
        msg.type = (
            ChatHistoryType.AI.value
            if msg.type == ChatHistoryType.HUMAN.value
            else ChatHistoryType.HUMAN.value
        )
        if msg.type == ChatHistoryType.AI.value:
            msg.content = f"<ca>\n{msg.content}\n</ca>"
    input.first_ai_message = ""
    return input


async def build_model_config(chat_input: ChatNextInput, scenario: int) -> ChatNextInput:
    preset_model = chat_input.llm_model
    product_type = (
        ProductType.CHAT if scenario == Scenario.CHAT.value else ProductType.IMPERSONATE
    )
    product_type = (
        ProductType.CHAT_CONTINUE if chat_input.chat_continue else product_type
    )
    product = await product_service.get_by_type_and_mid(
        product_type.value, chat_input.user_chat_product_mid
    )
    if not product:
        log.error(f"product not found, user_id: {chat_input.user_id}")
        raise exception_util.param_error("product not found")
    preset_model = product.model

    if not chat_input.chat_continue and not chat_input.fixed_preset_model:
        history_models = await chat_history_dao.list_chat_llm_models(
            chat_input.user_id, chat_input.conversation_id, scenario
        )
        # preset_model = await preset_service.select_model_v2(history_models, chat_input)
        select_water = await preset_service.select_model_v3(history_models, chat_input)
        preset_model = select_water.to_llm_model
        chat_input.select_water_model = select_water.to_llm_model
        if chat_input.auto_retry and select_water.backup_to_llm_models:
            preset_model = select_water.backup_to_llm_models[0]
    if chat_input.fixed_preset_model:
        preset_model = chat_input.fixed_preset_model
    # 实际模型
    llm_model_config = await LlmModelConfig.filter(llm_model=preset_model).first()
    if not llm_model_config or not llm_model_config.enabled:
        raise exception_util.param_error("model not supported")

    request_model = llm_model_config.request_llm_model
    add_water = bool(preset_model != chat_input.llm_model)

    llm_model_map = await config_service.map_llm_model_to_int()
    preset_model_int = llm_model_map.get(preset_model)
    if not preset_model_int:
        log.error(f"llm_model not found, user_id: {chat_input.user_id}")
        raise exception_util.param_error("model not supported")

    chat_input.preset_dict = await presets_dao.get_preset_v3(
        preset_model_int,
        scenario,
        chat_input.nsfw,
        chat_input.register_source,
    )
    # sys config
    names_behavior = chat_input.preset_dict.get("names_behavior", "0")
    chat_input.preset_model = preset_model
    chat_input.request_model = request_model
    chat_input.llm_model_support_params = json_util.convert_to_list(
        llm_model_config.support_params
    )
    chat_input.add_name_prefix = True if names_behavior == 2 else False
    chat_input.deal_regex_rule = False
    chat_input.add_water = add_water
    chat_input.chat_scenario = scenario


    chat_input.product_display_name = product.display_name
    chat_input.product_price = product.price

    def check_use_cache() -> bool:
        if not llm_model_config.cache_type:
            return False
        if Scenario.CHAT.value != scenario:
            return False
        if (
            llm_model_config.cache_type == LlmCacheType.CONTEXT_API.value
            and chat_input.chat_continue
        ):
            return False
        return True

    # 只有聊天使用缓存
    if check_use_cache():
        chat_input.cache_type = llm_model_config.cache_type
        chat_input.third_api_key = llm_model_config.third_api_key
        chat_input.third_model_id = llm_model_config.third_model_id
        chat_input.context_id = ""

    chat_input.llm_request_cluster = llm_model_config.request_cluster
    chat_input.llm_request_base_url = config_service.get_lite_llm_base_url(
        llm_model_config.request_cluster
    )
    water_config = await config_service.get_water_config(
        chat_input.llm_model, preset_model
    )
    if water_config:
        chat_input.out_sleep_min = water_config.out_sleep_min
        chat_input.out_sleep_max = water_config.out_sleep_max
        chat_input.out_skip_count = water_config.out_skip_count
    return chat_input


async def impersonate_def_role_id(request: ImpersonateRequest) -> int:
    if request.role_id == 0 and request.group_id == 0:
        return 0
    if request.mode_type == ChatModeType.SINGLE.value and request.role_id:
        return request.role_id
    if request.mode_type == ChatModeType.GROUP.value and request.group_id:
        group_info = await role_group_service.load_detail_by_id(request.group_id)
        if not group_info:
            return 0
        # random roles
        index = random.randint(0, len(group_info.roles) - 1)
        return group_info.roles[index].id
    return request.role_id


NSFW_BAN_CATEGORY = [
    "hate",
    "hate/threatening",
    "illicit",
    "illicit/violent",
    "self-harm",
    "self-harm/intent",
    "violence/graphic",
]
NSFW_BAN_THRESHOLD = {
    "harassment": 0.9,
    "harassment/threatening": 0.4,
    "sexual": 0.9,
    "sexual/minors": 0.4,
    "violence": 0.9,
}


def verify_input_message(
    input: str, nsfw: bool, user: User, check_register_source: bool = True
) -> bool:
    if (
        check_register_source
        and user.register_source != UserRegisterSource.USA_WEB.value
    ):
        return True
    try:
        categories, scores_map = new_chat_bot.moderation_input_check(input)
        if not categories or not scores_map:
            return True
        if not any(categories.values()):
            return True

        ban_ret = False
        if nsfw:
            ban_category = any([categories.get(x, False) for x in NSFW_BAN_CATEGORY])
            ban_score = any(
                [
                    scores_map.get(x, 0) >= NSFW_BAN_THRESHOLD.get(x, 1)
                    for x in NSFW_BAN_THRESHOLD.keys()
                ]
            )
            ban_ret = ban_category or ban_score
        else:
            ban_ret = any(categories.values())

        if any(categories.values()):
            message = {
                "title": "Moderation Alert",
                "user_id": user.id,
                "input": input,
                "categories": categories,
                "scores": scores_map,
                "ban_ret": ban_ret,
            }
            tg_util.send_message(message)

        log.info(
            f"Moderation verify_input_message,ban_ret:{ban_ret},categories: {categories}, scores: {scores_map}"
        )
        return not ban_ret
    except Exception as e:
        log.error(f"verify_input_message error:{e}")

    return True


async def list_conversation(
    user_id: int, mode_type: str, mode_target_id: int, language: str = Language.ZH.value
) -> list[ChatConversation]:
    history_list = await chat_history_dao.list_conversation_ids(
        user_id, mode_type, mode_target_id
    )
    history_list.sort(key=lambda x: x["min_timestamp"], reverse=True)
    ret = []
    max_index = len(history_list)
    for history in history_list:
        mid = ChatConversation(
            title=_tl("存档", language) + "-" + str(max_index),
            conversation_id=history["conversation_id"],
            first_chat_at=date_util.timestamp2utc8str(history["min_timestamp"]),
            latest_chat_at=date_util.timestamp2utc8str(history["max_timestamp"]),
        )
        ret.append(mid)
        max_index -= 1
    return ret


# 直接返回中断数据
async def check_interrupt_message(input: ChatNextInput):
    if not input.input_last_message_id and not input.input_last_message_version:
        return None
    if not input.history:
        return None
    if (
        input.history[-1].message_id == input.input_last_message_id
        and str(input.history[-1].version) == input.input_last_message_version
    ):
        return None

    his_list = await chat_history_dao.list_user_history(input.conversation_id)
    if not his_list or len(his_list) < 2:
        return None
    if his_list[-2].message_id != input.input_last_message_id:
        return None
    if str(his_list[-2].version) != input.input_last_message_version:
        return None
    if his_list[-1].type != ChatHistoryType.AI.value:
        return None
    chat_history = his_list[-1]
    query = BuildHistoryQuery(
        user_id=input.user_id,
        nickname=input.nickname,
        mode_type=chat_history.mode_type,
        mode_target_id=chat_history.mode_target_id,
        conversation_id=chat_history.conversation_id,
        language=input.language,
        regex_option="",
        add_name=False,
        use_display_user_name=True,
    )
    res_list = await build_history(query,[chat_history])
    return res_list[0] if res_list else None

async def llm_call_auth_retry(input: ChatNextInput):
    request_auto_retry = input.auto_retry

    async def call_by_exception():
        try:
            await model_service.log_model_request(input.preset_model, 1)
            chat_response = await lite_llm_bot.stream_call(input)
            if chat_response and chat_response.success and chat_response.response:
                first_item = await anext(chat_response.response)
                chat_response.first_chunk = first_item
                return chat_response
            await chat_result_service.error_handler(
                input, error_type=chat_response.error_type
            )
            return chat_response
        except Exception as e:
            log.error(
                f"llm_call_auth_retry error, user_id: {input.user_id}, error: {e}"
            )
            await chat_result_service.error_handler(input, exception=e)
            return ChatStreamResponse(
                success=False, error_type=LiteLlmErrorType.REQUEST_ERROR.value
            )

    chat_response = await call_by_exception()
    if chat_response.success and chat_response.response:
        return chat_response

    # 如果有 backup 的缓存类型且未使用 backup，则切换到 backup
    if input.cache_type == LlmCacheType.CONTEXT_API.value and not input.use_backup:
        input.use_backup = True
        chat_response = await call_by_exception()
        if chat_response.success and chat_response.response:
            return chat_response
    # 请求是自动重试，llm调用失败,切换到原有模型
    input.auto_retry = False if request_auto_retry else True
    input = await build_model_config(input, input.chat_scenario)
    return await call_by_exception()
