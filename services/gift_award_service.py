from datetime import UTC, datetime, timedelta
from common.entity import GiftAward
from persistence.models.models import (
    ExpirableAward,
    ExpirableStatusEnum,
    RechargeChannelEnum,
    RechargeOrder,
    RechargeStatusEnum,
    UserRegisterSource,
)

def get_gift_award(register_source: str | None = None) -> GiftAward:
    if register_source == UserRegisterSource.USA_WEB.value:
        return GiftAward(
            id="G1",
            amount=5000,
            title="新用户限时享豪礼",
            desc="恭喜你被幸运女神选中，领取成功5000金币，快去选个角色聊天吧！",
            expire_delta=timedelta(days=31),
        )
    return GiftAward(
        id="G1",
        amount=1000,
        title="新用户限时享豪礼",
        desc="恭喜你被幸运女神选中，领取成功1000金币，快去选个角色聊天吧！",
        expire_delta=timedelta(days=31),
    )


async def add_award(award: ExpirableAward) -> ExpirableAward:
    await award.save()
    return award


async def get_user_award(user_id: int) -> list[ExpirableAward]:
    return await ExpirableAward.filter(user_id=user_id).all()


async def get_award_by_user_gift(user_id: int, gift_id: str) -> ExpirableAward | None:
    return await ExpirableAward.filter(user_id=user_id, out_order_id=gift_id).first()


async def add_award_by_user_gift(user_id: int, gift: GiftAward) -> ExpirableAward:
    now = datetime.now(UTC)
    return await ExpirableAward.create(
        user_id=user_id,
        out_order_id=gift.id,
        balance=gift.amount,
        total_amount=gift.amount,
        status=ExpirableStatusEnum.NORMAL,
        claim_at=now,
        expires_at=now + gift.expire_delta,
    )


async def get_award_balance(user_id: int) -> list[ExpirableAward]:
    return await ExpirableAward.filter(
        user_id=user_id,
        status=ExpirableStatusEnum.NORMAL,
        expires_at__gt=datetime.now(UTC).replace(tzinfo=None),
    ).all()

async def get_award_balance_with_expired(user_id: int) -> list[ExpirableAward]:
    return await ExpirableAward.filter(
        user_id=user_id,
        status=ExpirableStatusEnum.NORMAL
    ).all()


async def add_award_balance_with_charge_order(
    user_id: int,
    amount: int,
    channel: RechargeChannelEnum,
    expire_delta: timedelta,
    out_order_id: str,
) -> ExpirableAward:
    now = datetime.now(UTC)
    recharge_order = RechargeOrder(
        user_id=user_id,
        amount=amount,
        pay_fee=0,
        status=RechargeStatusEnum.SUCCEED,
        recharge_channel=channel,
        out_order_id=out_order_id,
        finished_at=now,
    )
    await recharge_order.save()

    expirable_award = ExpirableAward(
        user_id=user_id,
        out_order_id=recharge_order.recharge_order_id,
        total_amount=amount,
        spend_amount=0,
        balance=amount,
        status=ExpirableStatusEnum.NORMAL,
        expires_at=now + expire_delta,
        claim_at=now,
    )
    await expirable_award.save()
