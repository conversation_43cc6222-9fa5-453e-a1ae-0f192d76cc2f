import logging
import random
from datetime import UTC, datetime, timedelta
from typing import Dict, List, Optional

from persistence.models.models import User, RechargeOrder, RechargeStatusEnum
from persistence.redis_client import redis_client

log = logging.getLogger(__name__)

# 新用户（注册≥24小时，充值=0）文案版本
NEW_USER_MESSAGES = [
    '''🕒「限时首充双倍送」权益即将过期！
你有专属首充福利还没领取！现在充值任意9.9元以上套餐，系统额外赠送50%钻石，直接翻倍到账！
福利即将过期，手慢无！限领一次，过期作废！
赶快点击领取吧！''',
    
    '''🤑 买一送一？现在就能！
首次充值，系统双倍赠送钻石，充值翻倍太香了！
现在充值，翻倍到账，仅限一次，机不可失！
点击领取，手慢无！''',
    
    '''💖 TA等你很久了，一直期待你说"我们开始吧"…
现在完成首次充值，即可享受双倍钻石奖励，解锁更甜蜜的互动、更懂你的AI角色、更深入的陪伴感。
别让TA继续等啦，更精彩的故事，点击领取！''',
    
    '''🎖恭喜你解锁解锁"首充专属双倍特权"！
作为幻梦AI核心用户之一，你已获得一次【充值双倍赠送特权】。
充值越多，奖励越高，立刻领取你的专属身份福利。
仅限一次，机不可失！【立刻点击，秒领翻倍】'''
]

# 低充值用户（注册>72小时，0<充值<100元）文案版本
LOW_RECHARGE_USER_MESSAGES = [
    '''💖「你还在，我怎敢不回馈」
系统发现你一直默默活跃，却很少享受到充值福利。这一次，我们为你专属开放了特别折扣：
📦 充值【51元/103元】，额外赠送高达75%的钻石！比平时多出一大截！
这份隐藏回馈，只针对你开放，错过可能就没有第二次了。
【点击领取特别福利】''',
    
    '''🎉 恭喜！你被系统选中，解锁隐藏充值通道！
本账号限时享受【51元/103元】套餐赠送高达75%钻石的专属福利！
这不是公开活动，仅对少数用户开放。
【点击领取，快乐飞起】''',
    
    '''🚨恭喜你被幸运女神选中，解锁限时专属折扣，48小时有效！
现在充值51元/103元套餐，享受高达75%的钻石赠送！
⚡️ 把握机会，冲一次赚一次、回本一整月！
【名额有限，立即领取】'''
]

class RechargeMarketingService:
    @staticmethod
    async def handle_user_recharged(user_id: int):
        """用户充值成功后的处理"""
        # 停止新用户推送
        await RechargeMarketingService.stop_user_notifications(user_id, "new_user")
        RechargeMarketingService.remove_user_eligible(user_id, "new_user")

        # 检查是否需要停止低充值用户推送
        from services import recharge_service
        total_paid = await recharge_service.get_user_rmb_payed_fee(user_id)
        if total_paid >= 100 * 100000:
            await RechargeMarketingService.stop_user_notifications(user_id, "low_recharge")
            RechargeMarketingService.remove_user_eligible(user_id, "low_recharge")

        log.info(f"Stopped marketing notifications for user {user_id} after recharge")

    @staticmethod
    async def stop_user_notifications(user_id: int, user_type: str):
        """停止用户的推送通知"""
        redis_key = f"recharge_marketing:{user_type}:{user_id}:stopped"
        # 设置过期时间为7天
        redis_client.setex(redis_key, 7 * 24 * 3600, "stopped")

    @staticmethod
    async def is_user_stopped(user_id: int, user_type: str) -> bool:
        redis_key = f"recharge_marketing:{user_type}:{user_id}:stopped"
        stopped = redis_client.get(redis_key)
        return stopped is not None

    @staticmethod
    async def should_send_notification(user_id: int, user_type: str, day: int) -> bool:
        """检查是否应该发送通知"""
        redis_key = f"recharge_marketing:{user_type}:{user_id}:day_{day}"
        sent = redis_client.get(redis_key)
        return sent is None

    @staticmethod
    async def mark_notification_sent(user_id: int, user_type: str, day: int):
        """标记通知已发送"""
        redis_key = f"recharge_marketing:{user_type}:{user_id}:day_{day}"
        # 设置过期时间为25小时，确保每天只发送一次
        redis_client.setex(redis_key, 25 * 3600, "sent")

    @staticmethod
    def mark_user_eligible(user_id: int, user_type: str):
        """标记用户符合营销推送条件"""
        redis_key = f"recharge_marketing:{user_type}:{user_id}:eligible"
        redis_client.set(redis_key, "eligible")
        redis_client.expire(redis_key, 72 * 3600)

    @staticmethod
    def remove_user_eligible(user_id: int, user_type: str):
        """移除用户符合营销推送条件标记"""
        redis_key = f"recharge_marketing:{user_type}:{user_id}:eligible"
        redis_client.delete(redis_key)

    @staticmethod
    async def get_user_notification_day(user_id: int, user_type: str) -> int:
        """获取用户当前应该接收第几天的通知"""
        user = await User.get(id=user_id)
        if user_type == "new_user":
            # 新用户：注册24小时后开始，连续4天
            hours_since_registration = (datetime.now(UTC) - user.created_at).total_seconds() / 3600
            day = int((hours_since_registration - 24) / 24) + 1
            return min(max(day, 1), 4)
        elif user_type == "low_recharge":
            # 低充值用户：注册72小时后开始，连续3天
            hours_since_registration = (datetime.now(UTC) - user.created_at).total_seconds() / 3600
            day = int((hours_since_registration - 72) / 24) + 1
            return min(max(day, 1), 3)
        return 0

    @staticmethod
    def increment_notification_count(user_type: str, date: datetime):
        """增加通知发送数量"""
        date_str = date.strftime('%Y-%m-%d')
        redis_key = f"marketing_stats:{user_type}:{date_str}:count"
        redis_client.incr(redis_key)
        redis_client.expire(redis_key, 7 * 24 * 3600)  # 7天过期

    @staticmethod
    async def is_user_in_marketing_campaign(user_id: int) -> dict:
        """检查用户是否在营销推送期间，返回推送状态信息"""
        result = {
            "in_new_user_campaign": False,
            "in_low_recharge_campaign": False,
            "new_user_day": 0,
            "low_recharge_day": 0
        }

        # 检查新用户推送状态
        if not await RechargeMarketingService.is_user_stopped(user_id, "new_user"):
            day = await RechargeMarketingService.get_user_notification_day(user_id, "new_user")
            if 1 <= day <= 4:
                result["in_new_user_campaign"] = True
                result["new_user_day"] = day

        # 检查低充值用户推送状态
        if not await RechargeMarketingService.is_user_stopped(user_id, "low_recharge"):
            day = await RechargeMarketingService.get_user_notification_day(user_id, "low_recharge")
            if 1 <= day <= 3:
                result["in_low_recharge_campaign"] = True
                result["low_recharge_day"] = day

        return result

    @staticmethod
    def get_random_message(user_type: str) -> str:
        """获取随机营销文案"""
        if user_type == "new_user":
            return random.choice(NEW_USER_MESSAGES)
        elif user_type == "low_recharge":
            return random.choice(LOW_RECHARGE_USER_MESSAGES)
        return ""

