import logging
from persistence.models.models import UserAltConfigStatus, UserAltPersonas, UserPersonaConfig
from utils import token_util

log = logging.getLogger(__name__)

## 已废弃的service
async def update_user_alt_persona(user_id: int, persona_config_list: list[UserPersonaConfig]):
    if len(persona_config_list) > 3:
        return False
    persona_settings = []
    for persona_config in persona_config_list:
        persona_config.description = persona_config.description.strip()
        if len(persona_config.description) == 0:
            return False
        token_count = token_util.num_tokens_from_string(persona_config.description)
        if token_count > 80:
            return False
        status = persona_config.status
        if status != UserAltConfigStatus.ACTIVE.value and status != UserAltConfigStatus.INACTIVE.value:
            return False
        persona_settings.append(persona_config.model_dump())
    
    result = await UserAltPersonas.filter(user_id=user_id).first()
    if not result:
        await UserAltPersonas.create(user_id=user_id, persona_settings=persona_settings)
    else:
        result.persona_settings = persona_settings
        await result.save()
    return True


async def get_user_all_alt_persona(user_id: int):
    persona_config = await UserAltPersonas.filter(user_id=user_id).first()
    if not persona_config:
        return []
    persona_settings = persona_config.persona_settings
    return [
        UserPersonaConfig.model_validate(content) for content in persona_settings
    ]

    
