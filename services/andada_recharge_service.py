from decimal import Decimal
import hashlib
import logging
import requests
import os, stripe
import uuid
from datetime import datetime, timedelta, UTC
from dotenv import load_dotenv
from tortoise.transactions import in_transaction
from common.entity import EvmTransaction, OkxDeposit, RechargeSetting
from persistence.models.models import (
    Account,
    ChargeAmountGenerator,
    ExpirableAward,
    ExpirableStatusEnum,
    LedgerEntry,
    RechargeChannelEnum,
    RechargeOrder,
    RechargeProduct,
    RechargeProductTypeEnum,
    RechargeStatusEnum,
    TransactionTypeEnum,
    TransactionSourceEnum,
    USDTRechargeOrder,
)
from services import gift_award_service
from utils import env_const

andada_recharge_host = os.environ['ANDADA_RECHARGE_HOST']
andada_recharge_app_id = os.environ['ANDADA_RECHARGE_APP_ID']
andada_recharge_app_key = os.environ['ANDADA_RECHARGE_APP_KEY']
andada_recharge_notify_url = f'{os.environ['TG_WEBHOOK_URL']}/andada_recharge/notify'
andada_recharge_return_url = f'{os.environ['TG_WEBHOOK_URL']}/andada_recharge/result'

async def create_deposit_order(user_id: int, recharge_product_id: str) -> RechargeOrder | None:
    recharge_product = await RechargeProduct.filter(recharge_product_id=recharge_product_id).first()
    if recharge_product is None:
        return None

    return await create_recharge_order_with_product(user_id, recharge_product)

async def create_recharge_order_with_product(user_id: int, recharge_product: RechargeProduct) -> RechargeOrder | None:
    try:
        order = RechargeOrder(
            user_id=user_id,
            out_order_id=str(uuid.uuid4()),
            amount=recharge_product.amount + recharge_product.reward_amount,
            pay_fee=recharge_product.cny_price,
            pay_currency='CNY',
            recharge_channel=RechargeChannelEnum.ANDADA,
            status=RechargeStatusEnum.INIT,
            recharge_product_id=recharge_product.recharge_product_id
        )
        await order.save()
        return order
    except Exception as e:
        logging.exception(e)
        return None

async def pay_success(recharge_order_id: str,
                      out_order_id: str,
                      raw_data: str) -> RechargeOrder:
    async with in_transaction():
        order: RechargeOrder = await RechargeOrder.get(recharge_order_id=recharge_order_id)
        if order.status == RechargeStatusEnum.SUCCEED:
            return order
        recharge_product: RechargeProduct = await RechargeProduct.filter(recharge_product_id=order.recharge_product_id).first() # type: ignore

        now = datetime.now(UTC)
        order.out_order_id = out_order_id
        order.status = RechargeStatusEnum.SUCCEED
        order.finished_at = now
        order.raw_response = raw_data
        await order.save()

        # payed amount
        payed_award = ExpirableAward(user_id=order.user_id,
                                        out_order_id = order.recharge_order_id,
                                        total_amount=recharge_product.amount,
                                        spend_amount=0,
                                        balance=recharge_product.amount,
                                        status=ExpirableStatusEnum.NORMAL,
                                        expires_at=datetime(2200, 1, 1),
                                        claim_at=now,
                                        from_type='PAYED')
        await payed_award.save()
        if recharge_product.reward_amount > 0:
            reward_award = ExpirableAward(user_id=order.user_id,
                                            out_order_id = order.recharge_order_id,
                                            total_amount=recharge_product.reward_amount,
                                            spend_amount=0,
                                            balance=recharge_product.reward_amount,
                                            status=ExpirableStatusEnum.NORMAL,
                                            expires_at=now + timedelta(days=recharge_product.charged_expire_delta),
                                            claim_at=now)
            await reward_award.save()
        return order

def create_andada_order(order: RechargeOrder, type: str, client_ip: str):
    params = {
        'pid': andada_recharge_app_id,
        'type': type,
        'out_trade_no': order.recharge_order_id,
        'money': f'{(Decimal(order.pay_fee) / 1000 / 100):.2f}',
        'notify_url': andada_recharge_notify_url,
        'return_url': andada_recharge_return_url,
        'name': '幻梦AI套餐',
        'clientip': client_ip
    }
    sorted_params = sorted(params.items(), key=lambda x: x[0])
    sign_str = '&'.join(f'{k}={v}' for k, v in sorted_params) + andada_recharge_app_key
    sign = hashlib.md5(sign_str.encode()).hexdigest()
    params['sign'] = sign.lower()
    params['sign_type'] = 'MD5'

    # post params as application/x-www-form-urlencoded
    resp = requests.post(f'{andada_recharge_host}/mapi.php', data=params, 
                        proxies=env_const.RECHARGE_PROXY)
    logging.info(f'andada_recharge_response: {resp.text}')
    return resp.json()