import asyncio
import datetime
import json
import logging
from aiogram import Bo<PERSON>
from aiogram.types import Message
import pytz

from persistence.models.models import TgDeletedMessage
from utils import env_util

log = logging.getLogger(__name__)

default_expire_delta = datetime.timedelta(hours=2)


async def del_msg(
    message_id: int,
    bot_id: int,
    chat_id: int,
    expire_delta: datetime.timedelta | None = None,
):
    if expire_delta is None:
        expire_delta = default_expire_delta
    expire_at = datetime.datetime.now(datetime.UTC) + expire_delta
    ret = await TgDeletedMessage.create(
        message_id=message_id,
        chat_id=chat_id,
        message_type="message",
        message="",
        user_id=-1,
        tg_deleted=False,
        bot="TMA",
        expire_at=expire_at,
        bot_id=bot_id,
    )
    log.info(
        f"add_check_in_deleted_message: message_id={ret.message_id},content={ret.message}"
    )


async def del_message_now(message: Message):
    async def del_task():
        try:
            await asyncio.sleep(3)  # Ensure the message is sent before deletion
            await message.delete()
        except Exception as e:
            log.error(f"del_message_now: {e}")
    asyncio.create_task(del_task())


async def add_deleted_message(
    message: Message | None = None,
    bot: str = "TMA",
    expire_delta: datetime.timedelta | None = None,
    bot_id: int = 0,
    user_id: int = -1,
):
    if not message:
        return
    try:
        if expire_delta is None:
            expire_delta = default_expire_delta
        expire_at = datetime.datetime.now(datetime.UTC) + expire_delta
        ret = await TgDeletedMessage.create(
            message_id=message.message_id,
            chat_id=message.chat.id,
            message_type="message",
            message=message.text,
            user_id=user_id,
            tg_deleted=False,
            bot=bot,
            expire_at=expire_at,
            bot_id=bot_id,
        )
        log.info(
            f"add_check_in_deleted_message: message_id={ret.message_id},content={ret.message}"
        )
    except Exception as e:
        log.error(f"add_check_in_deleted_message: {e}")


async def add_deleted_message_from_raw(
    raw: dict,
    reply_message: Message | None = None,
    expire_delta: datetime.timedelta | None = None,
    bot_id: int = 0,
):
    try:
        message = raw.get("message")
        if expire_delta is None:
            expire_delta = default_expire_delta
        expire_at = datetime.datetime.now(datetime.UTC) + expire_delta
        if message:
            chat = message.get("chat")
            message_id = message.get("message_id")
            chat_id = chat.get("id")
            ret = await TgDeletedMessage.create(
                message_id=message_id,
                chat_id=chat_id,
                message_type="text",
                message=message.get("text"),
                user_id=-1,
                tg_deleted=False,
                expire_at=expire_at,
                bot_id=bot_id,
            )
            log.info(
                f"add_deleted_message_from_raw: message_id={ret.message_id},content={ret.message}"
            )
        if reply_message:
            ret = await TgDeletedMessage.create(
                message_id=reply_message.message_id,
                chat_id=reply_message.chat.id,
                message_type="message_reply",
                message=reply_message.text,
                user_id=-1,
                tg_deleted=False,
                expire_at=expire_at,
            )
            log.info(
                f"add_check_in_deleted_message: message_id={ret.message_id},content={ret.message}"
            )
    except Exception as e:
        log.error(f"add_check_in_deleted_message: {e}")


async def add_check_in_deleted_message(
    message: Message,
    replay_message: Message,
    user_id: int,
    expire_delta: datetime.timedelta | None = None,
    bot: str = "TMA",
    bot_id: int = 0,
):
    try:
        if expire_delta is None:
            expire_delta = default_expire_delta
        expire_at = datetime.datetime.now(datetime.UTC) + expire_delta
        ret = await TgDeletedMessage.create(
            message_id=message.message_id,
            chat_id=message.chat.id,
            message_type="check_in",
            message=message.text,
            user_id=user_id,
            tg_deleted=False,
            expire_at=expire_at,
            bot=bot,
            bot_id=bot_id,
        )
        log.info(
            f"add_check_in_deleted_message: message_id={ret.message_id},content={ret.message}"
        )
        ret = await TgDeletedMessage.create(
            message_id=replay_message.message_id,
            chat_id=replay_message.chat.id,
            message_type="check_in_replay",
            message=replay_message.text,
            user_id=user_id,
            tg_deleted=False,
            expire_at=expire_at,
            bot=bot,
            bot_id=bot_id,
        )
        log.info(
            f"add_check_in_deleted_message: message_id={ret.message_id},content={ret.message}"
        )
    except Exception as e:
        log.error(f"add_check_in_deleted_message: {e}")


async def flag_deleted_message(id: int):
    ret = await TgDeletedMessage.filter(id=id, tg_deleted=False).update(tg_deleted=True)
    return ret > 0


async def list_expired_message(limit: int) -> list[TgDeletedMessage]:
    last_id = await TgDeletedMessage.filter().order_by("-id").first()
    if last_id is None:
        return []
    start_id = last_id.id - 10000
    start_id = start_id if start_id > 0 else 0
    now_time = datetime.datetime.now(pytz.utc).replace(tzinfo=None)
    ret_list = await TgDeletedMessage.filter(
        expire_at__lt=now_time,
        id__gt=start_id,
        tg_deleted=False,
        bot_id__gt=0,
    ).limit(limit)
    return ret_list
