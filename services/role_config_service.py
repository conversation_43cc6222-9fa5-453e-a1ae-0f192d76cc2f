import asyncio
import copy
from datetime import datetime
import io
import json
import math
from typing import Optional
from typing import Any, Dict
import uuid
from PIL import Image
from fastapi import UploadFile
from common.common_constant import AuditStatus, AuthorDefaultName, ChatModeType, CosPrefix, Env, Language, RoleChatType, RoleFilterTag, RoleLevelType, RolePlayType, RoleSortType, RoleSubTag, RoleTag
from common.role_card import CharacterBook, CharacterBookEdit, TavernCard
from common.role_model import AdminCardDetail, CardDetail, RoleConfigResponse, RoleDataConfig, RoleEditDetail, RoleFilterRequest, RoleFilterResponse, RoleFilterResponseV1, RoleFilterResponseV2, RoleRes, SceneConfig, TagNode, UserRoleBrief
from common.translate_model import TranslateRole
from persistence import char_book_dao
from persistence.models.models import RoleAudit, RoleConfig, RoleCategoryOrder, RoleOperationConfig, Role<PERSON>rder
from services import product_service, role_statistic_service, tag_service, translate_service, user_dislike_service, user_like_service, welfare_service
from services import user_service
from services.role import character_book_service, role_audit_service, role_group_service, role_loader_service
from services.user_service import UserService
from utils import cos_util, image_util, json_util
from utils.image_util import load_new_image_info, remove_image_info
from utils import card_util, char_book_util, role_util, str_util

class RoleConfigService:

    @classmethod
    async def get_role_config(cls, config_id: int) -> RoleConfig | None:
        role_config = await RoleConfig.filter(id=config_id).first()
        if role_config:
            return role_config
        else:
            return None
        
    @classmethod
    async def update_user_role_config(cls,uid:int, config: RoleConfig) -> RoleConfig:
        config = role_util.reformat_sub_tags(config)
        operation_config = json_util.convert_to_dict(config.operation_config)
        operation_config = RoleOperationConfig(**operation_config)
        if operation_config.user_role_name == '{{user}}':
            operation_config.user_role_name = ''
        config.operation_config = operation_config.model_dump()
        existing_config = await RoleConfig.get(id=config.id)
        existing_config.role_name = config.role_name
        existing_config.card_name = config.card_name
        if config.role_avatar or config.role_avatar != "None":
            existing_config.role_avatar = config.role_avatar
        existing_config.introduction = config.introduction
        existing_config.data_config = config.data_config
        existing_config.speaker_id = config.speaker_id
        existing_config.tags = config.tags
        existing_config.sub_tags = config.sub_tags
        existing_config.nsfw = config.nsfw
        existing_config.book_id = config.book_id
        #existing_config.support_model_config = config.support_model_config
        existing_config.excluded_product_ids = config.excluded_product_ids
        existing_config.operation_config = config.operation_config
        existing_config.level_type = config.level_type
        existing_config.status = config.status
        existing_config.privacy = config.privacy
        existing_config.def_language = config.def_language
        existing_config.image_nsfw = config.image_nsfw
        existing_config.real_role = config.real_role
        existing_config.play_type = config.play_type
        existing_config.chat_type = config.chat_type
        await existing_config.save()
        return existing_config

    @classmethod
    async def add_role_config(cls, config: RoleConfig) -> RoleConfig:
        config = role_util.reformat_sub_tags(config)
        operation_config = json_util.convert_to_dict(config.operation_config)
        operation_config = RoleOperationConfig(**operation_config)
        if operation_config.user_role_name == '{{user}}':
            operation_config.user_role_name = ''
        config.operation_config = operation_config.model_dump()
            
        return await RoleConfig.create(card_name=config.card_name,
                                       role_name=config.role_name,
                                       role_avatar=config.role_avatar,
                                       introduction=config.introduction,
                                       data_config=config.data_config,
                                       uid=config.uid,
                                       speaker_id=config.speaker_id,
                                       tags=config.tags,
                                       sub_tags=config.sub_tags,
                                       nsfw=config.nsfw,
                                       book_id=config.book_id,
                                       excluded_product_ids=config.excluded_product_ids,
                                       #support_model_config=config.support_model_config,
                                        level_type=config.level_type,
                                        operation_config=config.operation_config,
                                        privacy=config.privacy,
                                        def_language=config.def_language,
                                        image_nsfw=config.image_nsfw,
                                        real_role=config.real_role,
                                        play_type=config.play_type,
                                        chat_type=config.chat_type,
                                       )

    @classmethod
    async def add_update_role_orders(cls, category: str, orders: list[int]):
        existing_orders = await RoleCategoryOrder.filter(category=category).first()
        if existing_orders:
            existing_orders.orders = orders
            await existing_orders.save()
        else:
            await RoleCategoryOrder.create(category=category, orders=orders)

    
    async def role_token_count(data_config:dict,book:CharacterBookEdit):
        data_config = RoleDataConfig(**data_config)
        role_token_count = role_util.num_token_dict(data_config)
        role_token_count['role_book'] = char_book_util.sum_token(book)
        return role_token_count

    @classmethod
    async def get_created_config(cls,role_id:int)->RoleConfigResponse:
        role = await RoleConfig.filter(id=role_id).first() 
        ret = RoleConfigResponse()
        if not role:
            return ret
        products = await product_service.list_chat_product_new()
        product_ids = [x.mid for x in products]
        ret.role = RoleEditDetail.from_role_config(role, product_ids)


        role_book = await char_book_dao.get_by_book_id(role.book_id)
        if role_book:
            ret.role.role_book = CharacterBookEdit.from_model(role_book)
        ret.role_token_count = await RoleConfigService.role_token_count(role.data_config,ret.role.role_book)
        return ret
        
    async def analysis_card_img(image:UploadFile):
        img_bytes = image.file.read()
        content_type = image.content_type
        image_type = content_type.split("/")[1] if content_type else ""
        file_name = image.filename
        image_util.save_cards_to_local(img_bytes, image_type, str(file_name))

        new_image = Image.open(io.BytesIO(img_bytes))
        tavern_card:TavernCard = load_new_image_info(new_image)
        new_image = remove_image_info(new_image)
        # 读取image的bytes
        img_byte_arr = io.BytesIO()
        new_image.save(img_byte_arr, format=image_type)
        file_name = cos_util.random_name(CosPrefix.ROLE.value, f"{image_type}")
        image_url = cos_util.upload_to_s3(img_byte_arr.getvalue(), file_name,str(content_type))
        # image_url = image_util.upload_image(img_byte_arr.getvalue(), f"{uuid.uuid4().hex}.{image_type}", "tr-avatar-1323765209", content_type)
        role_card_config = tavern_card.to_role_card()
        character_edit_book = None
        if role_card_config.character_book:
            character_edit_book = CharacterBookEdit.from_model(role_card_config.character_book)
        # 只支持酒馆的图片
        new_role = RoleEditDetail(
            card_name=role_card_config.name,
            name=role_card_config.name,
            role_name=role_card_config.name,
            introduction="",
            role_avatar=image_url,
            description=role_card_config.description,
            personality=role_card_config.personality,
            scenario=role_card_config.scenario,
            first_message=role_card_config.first_mes,
            example_dialog=role_card_config.mes_example,
            muilte_scenes=card_util.list_new_scenes(role_card_config),
            role_book=character_edit_book,
        )
      
        #解析自定义参数
        if tavern_card.data and tavern_card.data.cus_data:
            new_role = RoleEditDetail(**tavern_card.data.cus_data)
            new_role.role_avatar = image_url
            new_role.id = 0
        sum_token_limit = 8000

        products = await product_service.list_chat_product_new()
        product_ids = [x.mid for x in products]
        role_config = new_role.to_role_config(product_ids)

        role_token_count = await RoleConfigService.role_token_count(role_config.data_config,character_edit_book)
        ret =  {
                "role_config": new_role.model_dump(),
                "role_token_count":role_token_count ,
                'sum_token_limit':sum_token_limit,
        }
        if role_card_config.character_book:
            ret["role_book"] = role_card_config.character_book.model_dump()
        return ret
    
    @staticmethod
    async def load_tavern_card(role_id)-> TavernCard:
        role_config = await RoleConfigService.get_role_config(role_id)
        if role_config is None:
            return None
        book = None
        user_name = 'FancyMe官方'
        if role_config.book_id is not None:
            book = await char_book_dao.get_by_book_id(role_config.book_id)
        if role_config.uid > 0:
            try:
                user = await UserService().get_user_by_id(role_config.uid)
                user_name = user.nickname
            except Exception as e:
                user_name = 'FancyMe官方'
            
        ret:TavernCard = TavernCard.from_model(role_config,book,user_name)
        products = await product_service.list_chat_product_new()
        product_ids = [x.mid for x in products]
        role_edit_detail = RoleEditDetail.from_role_config(role_config, product_ids)
        if role_config.book_id:
            role_edit_detail.role_book = await character_book_service.get_edit_book_by_id(role_config.book_id)
        ret.data.cus_data = role_edit_detail.model_dump()
        return ret
    
            
    # role interface for user
    @staticmethod
    async def list_effective_role_by_uid(uid:int,nickname:str)->list[UserRoleBrief]:
        config = await RoleConfig.filter(uid=uid, status=True,privacy=False).all()
        if config is None:
            return []
        audit_list = await role_audit_service.list_audit(uid,ChatModeType.SINGLE.value)
        audit_reward_desc = await welfare_service.reward_desc(uid)
        role_audit_map = {x.mode_target_id: x for x in audit_list}
        role_public_ids = {x.mode_target_id: x.open_role_id for x in role_audit_map.values()}
        open_role_hot = await role_statistic_service.user_role_statistic(list(role_public_ids.values()))
        open_role_like_count = await user_like_service.get_like_count_by_role_ids(list(role_public_ids.values()))
        open_role_dislike_count = await user_dislike_service.get_dislike_count_by_role_ids(list(role_public_ids.values()))
        ret_list:list[UserRoleBrief] = [UserRoleBrief.from_config_and_audit(x,role_audit_map.get(x.id)) for x in config]
        for mid_ret in ret_list:
            mid_ret.introduction = str_util.format_char_and_user(mid_ret.introduction,mid_ret.role_name,nickname)
            mid_ret.simple_intro = str_util.format_char_and_user(mid_ret.simple_intro,mid_ret.role_name,nickname)
            mid_ret.public_role_id = role_public_ids.get(mid_ret.id,0)
            mid_ret.popular_count = str_util.format_hot(open_role_hot.get(mid_ret.public_role_id, 0))
            mid_ret.deducted_popular_count = str_util.format_hot(open_role_dislike_count.get(mid_ret.public_role_id, 0) * 10000)
            final_popular_count = open_role_hot.get(mid_ret.public_role_id, 0) - open_role_dislike_count.get(mid_ret.public_role_id, 0) * 10000
            if final_popular_count < 0:
                final_popular_count = 0
            mid_ret.final_popular_count = str_util.format_hot(final_popular_count)
            mid_ret.like_count = open_role_like_count.get(mid_ret.public_role_id, 0)
            mid_ret.author_name = nickname
            mid_ret.reward_desc = audit_reward_desc.get(mid_ret.id, "")
        return ret_list
    
    @staticmethod
    async def old_list_filter_roles_by_user(nsfw:bool)->list[RoleConfig]:
        filter_list =  await RoleConfig.filter(status=True,privacy=True).all()
        if nsfw == False:
            filter_list = [x for x in filter_list if x.nsfw == False]
        return filter_list
    
    @staticmethod
    async def format_and_trans_role_configs(role_configs:list[RoleConfig],nickname:str,language:str = Language.ZH.value)->list[RoleConfig]:
        role_ids = [x.id for x in role_configs]
        trans_role_maps :dict[int,TranslateRole] = await translate_service.map_role_tasks_by_ids(language,role_ids)
        ret:list[RoleConfig] = []
        for role_config in role_configs:
            if language != role_config.def_language:
                role_config = role_util.translate_role_config(role_config,trans_role_maps.get(role_config.id,None))
            sub_tags = json_util.convert_to_list(role_config.sub_tags)
            role_config.sub_tags = role_util.translate_role_sub_tags(sub_tags, language)
            role_config = role_util.format_role_config(role_config,nickname)
            role_config.tags = RoleTag.translate_by_source(role_config.tags,language)
            ret.append(role_config)
        return ret

# async def format_and_trans_role_config(role_config:RoleConfig,nickname:str,language:str = Language.ZH.value)->RoleConfig:
#     ret_list = await RoleConfigService.format_and_trans_role_configs([role_config],nickname,language)
#     return ret_list[0]

async def get_by_id(config_id: int) -> RoleConfig | None:
    return await RoleConfig.filter(id=config_id).first()

async def map_role_order(tag:str)-> dict[int,int]:
    role_order = await RoleCategoryOrder.filter(category=tag).first()
    if role_order is None:
        return {}
    return {x: i for i, x in enumerate(role_order.orders)}


async def list_with_popular(nickname:str,nsfw:bool,current_language:str)->list[RoleRes]:
    original_roles: list[RoleConfig] = (
        await RoleConfigService.old_list_filter_roles_by_user(nsfw=nsfw)
    )
    popular_map: dict[int, int] = await role_statistic_service.role_hot_all()
    roles: list[RoleRes] = []
    original_roles = await RoleConfigService.format_and_trans_role_configs(
        original_roles, nickname, current_language
    )
    products = await product_service.list_chat_product_new()
    product_ids = [x.mid for x in products]
    for mid_role in original_roles:
        role = RoleRes.from_model_with_config(mid_role, False,True, product_ids)
        role.popular_count = popular_map.get(role.id, 0)
        roles.append(role)
    return roles

async def update_character_book(book:CharacterBookEdit):
    character_book = await char_book_dao.get_by_book_id(book.book_id)
    if character_book is None:
        return False
    character_book.name = book.name
    character_book.entries = book.entries
    character_book.enabled = book.enabled
    character_book.extensions = book.extensions
    return await char_book_dao.update(character_book)


async def create_role(role:RoleEditDetail,avatar_img:UploadFile|None,user_id:int = 0)->RoleEditDetail:
    upload_avatar = cos_util.upload_image(avatar_img, CosPrefix.ROLE)
    role.role_avatar = upload_avatar if upload_avatar else role.role_avatar
    products = await product_service.list_chat_product_new()
    product_ids = [x.mid for x in products]
    new_role = role.to_role_config(product_ids, user_id)
    new_role.privacy = False if user_id>0 else True
    new_role.status = True
    new_role.def_language = role_util.detect_language(role).value
        
    if role.role_book:
        role_book = await character_book_service.insert_character_book(role.role_book)
        new_role.book_id = role_book.book_id

    role_config = await RoleConfigService.add_role_config(new_role)
    
    return await load_role_edit_detail(role_config.id)

async def group_create_roles(nsfw:bool,offset:int,limit:int,language:str)->RoleFilterResponse:
    ret_ids = await role_loader_service.list_public_ids_by_params(nsfw=nsfw,real_role=True)
    count = len(ret_ids)
    hot_map = await role_statistic_service.role_hot_all()
    ret_ids.sort(key=lambda x: hot_map.get(x,0), reverse=True)
    ret_ids = ret_ids[offset : offset + limit]
    roles = await role_loader_service.load_translated_roles(ret_ids,language,"")
    roles = [UserRoleBrief.from_config_and_audit(x) for x in roles]
    return RoleFilterResponse(
        count=count,
        roles=roles,
    )

# fancyou专用（旧版）
async def old_tab_roles(nsfw: bool,tag:str,sub_tag: str,
     language: str, nickname: str,offset: int,limit: int)->RoleFilterResponse:
    ret_roles: list[RoleRes] = await list_with_popular(
        nickname, nsfw, language
    )
    ret_sub_tags = role_util.list_sub_tags_from_roles(ret_roles)
    # filter sub tags
    ret_roles = [
        x
        for x in ret_roles
        if not sub_tag or sub_tag in x.sub_tags
    ]
    if tag == RoleTag.HOT.translate(language):
        ret_roles.sort(key=lambda x: x.popular_count, reverse=True)
    elif tag == RoleTag.NEW.translate(language):
        ret_roles.sort(key=lambda x: x.id, reverse=True)
    elif tag == RoleTag.CHOSEN.translate(language):
        role_order = await map_role_order(RoleTag.CHOSEN.value)
        ret_roles.sort(key=lambda x: role_order.get(x.id, 999))
    count = len(ret_roles)
    ret_roles = ret_roles[offset : offset + limit]
    user_ids = [x.user_id for x in ret_roles if x.author]
    map_nicknames = await user_service.map_nickname(list(set(user_ids)),language)
    for mid in ret_roles:
        mid.author_name = map_nicknames.get(mid.user_id if mid.author else 0, "")
    return RoleFilterResponse(
        count=count,
        tags=RoleTag.old_online_tags(language),
        current_tag=tag,
        current_sub_tag=sub_tag,
        sub_tags=ret_sub_tags,
        roles=ret_roles,
    )


async def set_nick_name(card_list:list[CardDetail], language:str):
    user_ids = [x.fetch_author_id() for x in card_list]
    map_nicknames = await user_service.map_nickname(list(set(user_ids)),language)
    for card_mid in card_list:
        author_id = card_mid.fetch_author_id()
        nickname = map_nicknames.get(author_id,"")
        nickname = role_loader_service.get_def_author_name(card_mid.mode_type,card_mid.mode_target_id,nickname,language)
        card_mid.update_nickname(nickname)


async def role_bot_next(nsfw:bool, index:int, language:str = Language.ZH.value, 
                        nickname:str = ""):
    ids = await role_loader_service.list_ids_by_user_index(nsfw)
    role_order_map = await tag_service.map_chose_role_order()
    ids.sort(key=lambda x: role_order_map.get(x, 999))
    index = index % len(ids)
    role_config = await role_loader_service.load_translated_role(ids[index], language, nickname)
    return RoleRes.from_model(role_config,False)


async def approved_role(
    audit_info: RoleAudit, tags: str, role_edit_detail: RoleEditDetail
):
    #first init role
    products = await product_service.list_chat_product_new()
    product_ids = [x.mid for x in products]
    if not audit_info.open_role_id:
        open_role = role_edit_detail.to_role_config(product_ids, audit_info.user_id)
        open_role.privacy = True
        open_role.status = True
        open_role.tags = tags
        open_role.def_language = role_util.detect_language(role_edit_detail).value
        if role_edit_detail.role_book:
            new_book = copy.deepcopy(role_edit_detail.role_book)
            new_book.book_id = str(uuid.uuid4())
            new_book = await character_book_service.insert_character_book(new_book)
            open_role.book_id = new_book.book_id
        open_role = await RoleConfigService.add_role_config(open_role)
        return open_role
    
    #add or update role book
    open_role = await role_loader_service.get_by_id(audit_info.open_role_id)
    ## remove
    if not role_edit_detail.role_book and open_role.book_id:
        open_role.book_id = ""
    ## add
    if role_edit_detail.role_book and not open_role.book_id:
        new_book = copy.deepcopy(role_edit_detail.role_book)
        new_book.book_id = str(uuid.uuid4())
        new_book = await character_book_service.insert_character_book(new_book)
        open_role.book_id = new_book.book_id
    ## update
    if role_edit_detail.role_book and open_role.book_id:
        update_book = copy.deepcopy(role_edit_detail.role_book)
        update_book.book_id = open_role.book_id
        await character_book_service.update_character_book(update_book)
    
    
    input_edit_role = role_edit_detail.to_role_config(product_ids)
    open_role.card_name = input_edit_role.card_name
    open_role.role_name = input_edit_role.role_name
    open_role.role_avatar = input_edit_role.role_avatar
    open_role.introduction = input_edit_role.introduction
    open_role.data_config = input_edit_role.data_config
    open_role.tags = tags
    open_role.spec_version = input_edit_role.spec_version
    open_role.speaker_id = input_edit_role.speaker_id
    open_role.sub_tags = input_edit_role.sub_tags
    open_role.nsfw = input_edit_role.nsfw
    #open_role.support_model_config = input_edit_role.support_model_config
    open_role.excluded_product_ids = input_edit_role.excluded_product_ids
    open_role.level_type = input_edit_role.level_type
    open_role.operation_config = input_edit_role.operation_config
    open_role.def_language = role_util.detect_language(role_edit_detail).value
    open_role.real_role = input_edit_role.real_role
    open_role = await RoleConfigService.update_user_role_config(
        open_role.uid, open_role
    )
    return open_role


async def card_list_all()->list[CardDetail]:
    role_config_list = await RoleConfig.filter(status=True,privacy=True).all() 
    role_config_list = [ role_util.format_role_config(x,"") for x in role_config_list]
    author_ids = [x.uid for x in role_config_list]
    nicknames = await user_service.map_nickname_by_admin(author_ids)
    role_list = [UserRoleBrief.from_config_and_audit(x,admin=True) for x in role_config_list]
    for role in role_list:
        role.author_name = nicknames.get(role.author_id,AuthorDefaultName.ANONYMOUS.value)
        
    role_ret = [CardDetail.from_role(x) for x in role_list]
    group_detail_list = await role_group_service.list_public()
    role_ret.extend([CardDetail.from_group(x) for x in group_detail_list])
    return role_ret


async def chosen_card_list():
    ret_list = await card_list_all()
    role_order_list = await RoleOrder.all()
    max_position = 0
    role_order_map = {}
    for role_order in role_order_list:
        key = CardDetail.key(role_order.mode_type, role_order.mode_target_id)
        role_order_map[key] = role_order.position
        max_position = max(max_position, role_order.position)
    ret_list.sort(key=lambda x: role_order_map.get(x.self_key(), max_position))
    ret_list = [AdminCardDetail.from_card_detail(x) for x in ret_list]
    role_video_map = await role_loader_service.map_all_role_video_url()
    for mid in ret_list:
        if mid.mode_type == ChatModeType.SINGLE.value and mid.mode_target_id in role_video_map:
            mid.contains_video = True
    return ret_list


async def chosen_new_order_card_list():
    ret_list = await card_list_all()
    role_order_list = await RoleOrder.all()
    order_map = {CardDetail.key(x.mode_type, x.mode_target_id): x.position_temp for x in role_order_list}
    max_position = max(order_map.values(), default=999999)
    ret_list.sort(key=lambda x: order_map.get(x.self_key(), max_position))
    return ret_list

async def host_card_list():
    ret_list = await card_list_all()
    role_hot = await role_statistic_service.role_hot_all()
    group_hot = await role_statistic_service.group_hot_all()
    role_order_map = {}
    for role_id, hot in role_hot.items():
        role_order_map[CardDetail.key(ChatModeType.SINGLE.value, role_id)] = hot
    for group_id, hot in group_hot.items():
        role_order_map[CardDetail.key(ChatModeType.GROUP.value, group_id)] = hot
    min_position = 0  
    ret_list.sort(
        key=lambda x: role_order_map.get(
            CardDetail.key(x.mode_type, x.mode_target_id), min_position
        ),reverse=True
    )
    return ret_list


async def new_card_list():
    role_ret = await card_list_all()
    role_ret = [x for x in role_ret if x.mode_type == ChatModeType.SINGLE.value]
    role_ret.sort(key=lambda x: x.mode_target_id, reverse=True)
    return role_ret

async def other_tag_card_list(tag:str):
    role_config_list = await role_loader_service.load_by_tags(tag)
    role_config_list = [ role_util.format_role_config(x,"") for x in role_config_list]

    tag_roles_order = await map_role_order(tag)
    role_config_list.sort(key=lambda x: tag_roles_order.get(x.id, 999))

    role_config_list = [CardDetail.from_role(UserRoleBrief.from_config_and_audit(x,admin=True)) for x in role_config_list]
    return role_config_list

async def card_list_by_sub_tags_category(tags:str):
    sub_tags = await tag_service.list_sub_tags_with_enabled()
    sub_tags = [x.tag_name for x in sub_tags if x.category == tags]
    role_configs = await role_loader_service.list_public_by_sub_tags(sub_tags)
    role_configs.sort(key=lambda x: x.updated_at, reverse=True)
    role_config_list = [ role_util.format_role_config(x,"") for x in role_configs]
    role_config_list = [UserRoleBrief.from_config_and_audit(x,admin=True) for x in role_config_list]
    return [CardDetail.from_role(x) for x in role_config_list]


async def user_role_list():
    user_roles = await role_loader_service.list_user_roles_by_admin()
    user_roles.sort(key=lambda x: x.created_at, reverse=True)
    user_roles = [UserRoleBrief.from_config_and_audit(x,admin=True) for x in user_roles]
    for role in user_roles:
        role.author = True
        role.author_name = str(role.author_id)
    return [CardDetail.from_role(x) for x in user_roles]

async def user_group_list():
    user_groups = await role_group_service.list_user_groups_by_admin()
    user_groups.sort(key=lambda x: x.id, reverse=True)
    for group in user_groups:
        group.author = True
        group.author_name = str(group.author_id)
    return [CardDetail.from_group(x) for x in user_groups]

async def user_public_role_list():
    audit_list = await role_audit_service.list_by_public_audit()
    audit_list.sort(key=lambda x: x.updated_at, reverse=True)
    role_ids = [x.open_role_id for x in audit_list if x.open_role_id > 0]
    role_maps = await role_loader_service.map_brief_by_ids_with_admin(role_ids)
    ret = []
    for audit in audit_list:
        if audit.open_role_id > 0 and audit.open_role_id in role_maps:
            ret.append(CardDetail.from_role(role_maps[audit.open_role_id]))
    return ret

async def user_public_group_list():
    audit_list = await role_audit_service.list_by_public_audit()
    audit_list.sort(key=lambda x: x.updated_at, reverse=True)
    group_ids = [x.open_group_id for x in audit_list if x.open_group_id > 0]
    group_maps = await role_group_service.map_detail_by_ids(group_ids)

    ret = []
    for audit in audit_list:
        if audit.open_group_id > 0 and audit.open_group_id in group_maps:
            ret.append(CardDetail.from_group(group_maps[audit.open_group_id]))
    return ret

async def delete_public_role_list():
    user_roles = await role_loader_service.list_deleted_public_roles_by_admin()
    user_roles.sort(key=lambda x: x.updated_at, reverse=True)
    user_roles = [UserRoleBrief.from_config_and_audit(x,admin=True) for x in user_roles]
    for role in user_roles:
        role.author = True
        role.author_name = str(role.author_id)
    return [CardDetail.from_role(x) for x in user_roles]

async def load_role_edit_detail(role_id: int) -> RoleEditDetail:
    role_config = await RoleConfig.get(id=role_id)

    role_book = None
    if role_config.book_id:
        role_book = await character_book_service.get_edit_book_by_id(
            role_config.book_id
        )
    products = await product_service.list_chat_product_new()
    product_ids = [x.mid for x in products]
    role_edit = RoleEditDetail.from_role_config(role_config, product_ids)
    if role_book:
        role_edit.role_book = role_book
        role_edit.book_id = role_book.book_id
    return role_edit



async def user_chosen_roles(filter:RoleFilterRequest):
    role_ids = await role_loader_service.list_ids_by_user_filter(filter)
    card_list = []
    count = 0
    map_orders = await tag_service.map_chose_role_order()
    role_ids.sort(key=lambda x: map_orders.get(x,999999999))
    count = len(role_ids)
    role_ids = role_ids[filter.offset : filter.offset + filter.limit]
    ret_roles = await role_loader_service.load_translated_roles(role_ids,filter.language,"")
    ret_roles = [UserRoleBrief.from_config_and_audit(x) for x in ret_roles]
    card_list = [CardDetail.from_role(x) for x in ret_roles]
    await set_nick_name(card_list,filter.language)
    
    return RoleFilterResponseV2(
        count=count,
        card_list=card_list
    )
async def user_new_roles(filter:RoleFilterRequest):
    role_ids = await role_loader_service.list_ids_by_user_filter(filter)
    role_ids.sort(key=lambda x: x, reverse=True)
    count = len(role_ids)
    role_ids = role_ids[filter.offset : filter.offset + filter.limit]
    ret_roles = await role_loader_service.load_translated_roles(role_ids,filter.language,"")
    ret_roles = [UserRoleBrief.from_config_and_audit(x) for x in ret_roles]
    card_list = [CardDetail.from_role(x) for x in ret_roles]
    await set_nick_name(card_list,filter.language)
    
    latest_role_created_at = await role_loader_service.latest_card_created_at()
    last_group_created_at = await role_group_service.latest_group_created_at()
    
    return RoleFilterResponseV2(
        count=count,
        card_list=card_list,
        latest_card_created_at=max(latest_role_created_at, last_group_created_at)
    )

async def user_hot_roles(filter:RoleFilterRequest):
    role_ids = await role_loader_service.list_ids_by_user_filter(filter)
    popular_map = await role_statistic_service.role_hot_all()
    role_ids.sort(key=lambda x: popular_map.get(x,0), reverse=True)
    count = len(role_ids)
    role_ids = role_ids[filter.offset : filter.offset + filter.limit]
    ret_roles = await role_loader_service.load_translated_roles(role_ids,filter.language,"")
    ret_roles = [UserRoleBrief.from_config_and_audit(x) for x in ret_roles]
    card_list = [CardDetail.from_role(x) for x in ret_roles]
    await set_nick_name(card_list,filter.language)
    return RoleFilterResponseV2(
        count=count,
        card_list=card_list,
    )
async def user_ranking_roles(filter:RoleFilterRequest,sort_type:RoleSortType):
    if sort_type in RoleSortType.summary_rank_types():
        role_filter_tag = sort_type.to_filter_tag()
        if not role_filter_tag:
            raise Exception("invalid role filter tag")
        return await user_summary_ranking_roles(filter,sort_type)
    role_ids = await role_loader_service.list_ids_by_user_filter(filter)
    sort_map = {}
    if sort_type == RoleSortType.FAVORITE:
        sort_map = await role_statistic_service.favorite_count()
    elif sort_type == RoleSortType.LIKE:
        sort_map = await role_statistic_service.like_count()
    elif sort_type == RoleSortType.UPDATE:
        sort_map = await role_statistic_service.role_update_time()
    role_ids.sort(key=lambda x: sort_map.get(x,0), reverse=True)
    count = len(role_ids)
    role_ids = role_ids[filter.offset : filter.offset + filter.limit]
    ret_roles = await role_loader_service.load_translated_roles(role_ids,filter.language,"")
    ret_roles = [UserRoleBrief.from_config_and_audit(x) for x in ret_roles]
    card_list = [CardDetail.from_role(x) for x in ret_roles]
    await set_nick_name(card_list,filter.language)
    return RoleFilterResponseV2(
        count=count,
        card_list=card_list,
    )
async def user_summary_ranking_roles(filter:RoleFilterRequest,sort_type:RoleSortType):
    # role_ids = await role_loader_service.list_ids_by_user_filter(filter)
    user_role_ids = await role_loader_service.list_ids_by_user_filter(filter)
    role_ids = await role_loader_service.list_role_ids_by_rank(sort_type.value)
    role_ids = [x for x in role_ids if x in user_role_ids]
    count = len(role_ids)
    role_ids = role_ids[filter.offset : filter.offset + filter.limit]
    ret_roles = await role_loader_service.load_translated_roles(role_ids,filter.language,"")
    ret_roles = [UserRoleBrief.from_config_and_audit(x) for x in ret_roles]
    card_list = [CardDetail.from_role(x) for x in ret_roles]
    await set_nick_name(card_list,filter.language)
    return RoleFilterResponseV2(
        count=count,
        card_list=card_list,
    )
    
async def user_groups_roles(filter:RoleFilterRequest):
    group_ids = await role_group_service.list_ids_by_user_index(filter.nsfw,filter.sub_tags)
    sort_map = await role_statistic_service.group_hot_all()
    group_ids.sort(key=lambda x: sort_map.get(x,0), reverse=True)
    count = len(group_ids)
    group_ids = group_ids[filter.offset : filter.offset + filter.limit]
    group_list = await role_group_service.list_translate_detail_by_ids(group_ids,filter.language)
    card_list = [CardDetail.from_group(x) for x in group_list]
    await set_nick_name(card_list,filter.language)
    return RoleFilterResponseV2(
        count=count,
        card_list=card_list,
    )


async def role_token_count(data_config:RoleDataConfig,book:Optional[CharacterBookEdit],book_all:bool = False)->dict[str,int]:
    role_token_count = role_util.num_token_dict(data_config)
    role_token_count['role_book'] = char_book_util.sum_token(book,book_all) if book else 0
    return role_token_count


# list for user created roles
async def list_effective_role_by_uid(uid: int, nickname: str) -> list[UserRoleBrief]:
    config = await RoleConfig.filter(uid=uid, status=True, privacy=False).all()
    if config is None:
        return []
    audit_list = await role_audit_service.list_audit(uid, ChatModeType.SINGLE.value)
    role_audit_map = {x.mode_target_id: x for x in audit_list}
    role_public_ids = {
        x.mode_target_id: x.open_role_id for x in role_audit_map.values()
    }
    open_role_hot = await role_statistic_service.user_role_statistic(
        list(role_public_ids.values())
    )
    open_role_like_count = await user_like_service.get_like_count_by_role_ids(
        list(role_public_ids.values())
    )
    open_role_dislike_count = await user_dislike_service.get_dislike_count_by_role_ids(
        list(role_public_ids.values())
    )
    ret_list: list[UserRoleBrief] = [
        UserRoleBrief.from_config_and_audit(x, role_audit_map.get(x.id)) for x in config
    ]
    for mid_ret in ret_list:
        mid_ret.introduction = str_util.format_char_and_user(
            mid_ret.introduction, mid_ret.role_name, nickname
        )
        mid_ret.simple_intro = str_util.format_char_and_user(
            mid_ret.simple_intro, mid_ret.role_name, nickname
        )
        mid_ret.public_role_id = role_public_ids.get(mid_ret.id, 0)
        mid_ret.popular_count = str_util.format_hot(
            open_role_hot.get(mid_ret.public_role_id, 0)
        )
        mid_ret.deducted_popular_count = str_util.format_hot(
            open_role_dislike_count.get(mid_ret.public_role_id, 0) * 10000
        )
        final_popular_count = (
            open_role_hot.get(mid_ret.public_role_id, 0)
            - open_role_dislike_count.get(mid_ret.public_role_id, 0) * 10000
        )
        if final_popular_count < 0:
            final_popular_count = 0
        mid_ret.final_popular_count = str_util.format_hot(final_popular_count)
        mid_ret.like_count = open_role_like_count.get(mid_ret.public_role_id, 0)
        mid_ret.author_name = nickname
    return ret_list

async def user_create_role_count(user_id:int):
    return await RoleConfig.filter(uid=user_id, status=True,privacy=False).count()

async def get_role_config_by_ids(role_ids: list[int]):
    return await RoleConfig.filter(id__in=role_ids).all()