from datetime import UTC, datetime, timedelta
import logging
from aiogram import Bot, html
from aiogram.types import custom
from tortoise.transactions import in_transaction
from aiogram.utils.keyboard import InlineKeyboardBuilder
from common.common_constant import BotCategory, ChatBenefitEnum, GroupCategory
from common.entity import UserBioType
from persistence.models.models import (
    ChatJoinTask,
    CheckInTask,
    RechargeChannelEnum,
    RechargeOrder,
    RechargeStatusEnum,
    User,
    UserRegisterSource,
)
from persistence.chat_history import chat_history_persistence
from services import (
    bot_message_service,
    gift_award_service,
    recharge_service,
    tg_config_service,
    tg_message_service,
)
from services.account_service import AccountService
from services.user import user_benefit_service
from services.user_service import user_service, get_effective_invitations
from services.bot_services import bot, chat_bot
from utils import env_const, user_growth_constants
from common.models.forward_msg import ForwardMsg
from common.bot_common import (
    RECHARGE_PRODUCT_CALLBACK_TIPS,
    Button,
    MessageTemplate,
    charge_url,
)
from tasks import sx_bot_f_msg_task

log = logging.getLogger(__name__)

fc_reward_tips = """⚠️ 钻石见底，你的专属豪华套餐还没领取！
你的新用户<b>专属超级特权</b>即将消失，立即充值可解锁<b>新用户限时超值套餐</b>

<b>📌机会稍纵即逝，错过后悔一年！</b>
⏳ 限时新用户专属，一旦关闭无法再领取！
<b>点击领取你的专属特权 → [立即充值]</b>"""

fc_rewarded_notify = """<b>🎉🎉🎉恭喜您成功领取首充奖励！10,000🟡已到账，畅聊无阻的时刻正式开始！🎉🎉🎉</b>

您的首次充值不仅为您解锁了丰厚的💎奖励，还打开了一个充满无限可能的世界！现在，您可以尽情体验我们的4种独特模式(输入 `/settting` 命令或者在Menu中点击设置即可更改模式)，每种模式都为您带来不同的惊喜与魅力：

<b>🌟 极速模式</b>：速度超快、性价比高，文笔细腻丝滑，无论是甜蜜对话还是快速互动，都能满足您的需求，让每一次交流都如行云流水般顺畅。

<b>🔥 欲望模式</b>：欲望加倍，细节描写引人入胜，让每一次互动都充满激情与挑战！她们会更加懂你、宠你，心跳和悸动在这里从不停歇。

<b>✨ 全能模式</b>：集所有优点于一身的超级模式，智商超高、记忆力超强，复杂角色扮演和宏大世界模拟能力出类拔萃。她们将与你深度交流，时而让你恍若面对真人，绝对是极致体验的不二之选！

<b>🌌 世界卡模式</b>：开放世界的探索之旅，高智商模型加上最佳调优，让您的每一次对话都充满惊喜。这里没有边界，只有无尽的可能，等着您去发掘！

我们的平台上还有无数风格各异的角色等待您的宠幸，无论是温柔体贴的伴侣，还是充满个性的伙伴，他们都渴望被您召唤！<b>每一次互动，都是一场新的冒险与发现，每一颗💎，都能为您带来更多意想不到的惊喜！输入 `/list` 或者在Menu中点击”角色列表”即可查看和切换角色。</b>

<b>立即探索各大模式，选择您心仪的角色，与他们展开100次的亲密对话！</b> 别让这些💎静静躺在账户里，尽情使用它们来体验前所未有的甜蜜和激情吧！您的王国已经开启，无数宠爱在等您收下，<b>现在就是让您的每一天都充满心动与惊艳的最佳时刻！</b>
"""

card_channel_tips_text = """尊贵的用户{username}，我们发现您的充值操作可能遇到了困难，但别担心，我们已经为您准备好了更快捷和稳妥的支付渠道，以及更贴心的优惠活动，请您参考如下说明操作即可。

【限时惊喜福利，充值大优惠，充越多送越多】

点击下方链接（ https://www.sdfkw.xyz/links/1275C4C5 ），进入发卡平台，选择对应套餐，充值即可。
充值成功后，你将收到一串兑换密钥，请复制密钥，回到幻梦AI伴侣bot( https://t.me/FancyTavernBot )
进入充值页面，点击兑换，输入密钥兑换，钻石到账。

⚠️注意：如果发卡平台打开缓慢，可尝试关闭VPN重新加载，付款完成后再重新打开VPN。
如有任何问题，请联系官方客服（ https://t.me/{customer_service_bot} ）。"""


async def card_channel_tips(username: str) -> str:
    customer_service_bot = await tg_config_service.get_main_bot_by_category(
        BotCategory.CUSTOMER
    )
    return card_channel_tips_text.format(
        username=username, customer_service_bot=customer_service_bot.username
    )


async def list_chat_id_by_join_chat_task(user_id: int) -> list[int]:
    tasks = await ChatJoinTask.filter(user_id=user_id)
    return [task.chat_id for task in tasks]


async def check_join_chat_task(user_id: int, chat_id: int) -> bool:
    return await ChatJoinTask.filter(user_id=user_id, chat_id=chat_id).exists()

async def check_join_any_chat_group_task(user_id: int) -> bool:
    group_list = await tg_config_service.list_group(category=GroupCategory.CHAT)
    group_chat_ids = [group.chat_id for group in group_list]
    if not group_chat_ids:
        return False
    return await ChatJoinTask.filter(user_id=user_id, chat_id__in=group_chat_ids).exists()

async def check_check_in_task(user_id: int) -> bool:
    ds = datetime.now().date()
    return await CheckInTask.filter(user_id=user_id, check_in_at=ds).exists()


async def create_join_chat_task(user_id: int, chat_id: int, chat_name: str) -> bool:
    log.info(
        f"create_join_chat_task user_id:{user_id},chat_id:{chat_id},chat_name:{chat_name}"
    )
    async with in_transaction():
        current = await ChatJoinTask.filter(user_id=user_id, chat_id=chat_id).first()
        if current is not None:
            return False
        await ChatJoinTask.create(
            user_id=user_id,
            chat_id=chat_id,
            chat_name=chat_name,
            join_time=datetime.now(UTC),
        )

        await gift_award_service.add_award_balance_with_charge_order(
            user_id=user_id,
            amount=user_growth_constants.JOIN_GROUP_AWARD_AMOUNT,
            channel=RechargeChannelEnum.JOIN_CHAT_GROUP,
            expire_delta=user_growth_constants.JOIN_AWARD_EXPIRES,
            out_order_id=f"join_chat:{user_id}_{chat_id}",
        )
        return True


async def create_check_in_task(
    user_id: int, amount: int = user_growth_constants.GROUP_CHECK_IN_AWARD_AMOUNT
) -> bool:
    async with in_transaction():
        current = await CheckInTask.filter(
            user_id=user_id, check_in_at=datetime.now().date()
        ).first()
        if current is not None:
            return False
        await CheckInTask.create(user_id=user_id, check_in_at=datetime.now().date())

        ds = datetime.now().strftime("%Y-%m-%d")
        await gift_award_service.add_award_balance_with_charge_order(
            user_id=user_id,
            amount=amount,
            channel=RechargeChannelEnum.CHECK_IN,
            expire_delta=user_growth_constants.GROUP_CHECK_IN_AWARD_EXPIRES,
            out_order_id=f"check_in:{user_id}_{ds}",
        )
        return True


async def create_check_in_task_wit_bio(
    user_id: int,
    amount: int = user_growth_constants.GROUP_CHECK_IN_AWARD_AMOUNT,
    bio_type: UserBioType | None = None,
    in_role_channel: bool = False,
) -> bool:
    async with in_transaction():
        current = await CheckInTask.filter(
            user_id=user_id, check_in_at=datetime.now().date()
        ).first()
        if current is not None:
            return False
        await CheckInTask.create(user_id=user_id, check_in_at=datetime.now().date())

        ds = datetime.now().strftime("%Y-%m-%d")
        if bio_type == UserBioType.WITH_COMPETITOR_LINK:
            amount = user_growth_constants.CHECK_IN_COMPETITOR_LINK_AMOUNT
        await gift_award_service.add_award_balance_with_charge_order(
            user_id=user_id,
            amount=amount,
            channel=RechargeChannelEnum.CHECK_IN,
            expire_delta=user_growth_constants.GROUP_CHECK_IN_AWARD_EXPIRES,
            out_order_id=f"check_in:{user_id}_{ds}",
        )

        if bio_type == UserBioType.WITH_OUR_LINK:
            await gift_award_service.add_award_balance_with_charge_order(
                user_id=user_id,
                amount=user_growth_constants.CHECK_IN_WITH_LINK_REWARD_AMOUNT,
                channel=RechargeChannelEnum.CHECK_IN_WITH_LINK,
                expire_delta=user_growth_constants.GROUP_CHECK_IN_AWARD_EXPIRES,
                out_order_id=f"check_in_link:{user_id}_{ds}",
            )
        if in_role_channel:
            await gift_award_service.add_award_balance_with_charge_order(
                user_id=user_id,
                amount=user_growth_constants.CHECK_IN_IN_ROLE_CHANNEL_REWARD_AMOUNT,
                channel=RechargeChannelEnum.CHECK_IN_IN_ROLE_CHANNEL,
                expire_delta=user_growth_constants.GROUP_CHECK_IN_AWARD_EXPIRES,
                out_order_id=f"check_in_role_channel:{user_id}_{ds}",
            )
        return True

async def create_check_in_join_role(user_id:int):
    ds = datetime.now().strftime("%Y-%m-%d")
    await gift_award_service.add_award_balance_with_charge_order(
                user_id=user_id,
                amount=user_growth_constants.CHECK_IN_IN_ROLE_CHANNEL_REWARD_AMOUNT,
                channel=RechargeChannelEnum.CHECK_IN_IN_ROLE_CHANNEL,
                expire_delta=user_growth_constants.GROUP_CHECK_IN_AWARD_EXPIRES,
                out_order_id=f"check_in_role_channel:{user_id}_{ds}",
            )

async def forward_register(user_id: int, user_tg_id: int, channel_id: int | None):
    logging.info(
        f"forward_register user_id:{user_id},user_tg_id:{user_tg_id},channel_id:{channel_id}"
    )
    if channel_id is None:
        channel_id = -1
    await sx_bot_f_msg_task.forward_msg(
        ForwardMsg(
            tg_id=user_tg_id,
            user_id=user_id,
            msg_type="text",
            channel_id=channel_id,
            text=f"新用户注册，渠道id:#{channel_id},uid:#{user_id},tg_id:#{user_tg_id}",
        )
    )

async def add_fc_reward(user: User):
    return
    now = datetime.now(UTC)
    delta = now - user.created_at
    if delta >= timedelta(hours=30):
        return
    has_benefit_recharge = await user_benefit_service.has_benefit_recharge(user.id)
    if has_benefit_recharge:
        return

    rewarded = await RechargeOrder.filter(
        user_id=user.id,
        out_order_id=f"fc_reward:{user.id}",
        status=RechargeStatusEnum.SUCCEED,
    ).exists()
    if rewarded:
        return

    await gift_award_service.add_award_balance_with_charge_order(
        user_id=user.id,
        amount=user_growth_constants.FC_REWARD_AMOUNT,
        channel=RechargeChannelEnum.FC_REWARD,
        expire_delta=user_growth_constants.FC_REWARD_EXPIRES,
        out_order_id=f"fc_reward:{user.id}",
    )
    # await notify_fc_rewarded(user)
    user_tg = await user_service.get_tg_info_by_user_id(user.id)
    if not user_tg:
        return
    # bot = get_bot_by_register_source(user.register_source)
    await bot_message_service.send_user_template_message(
        user, MessageTemplate(tips=fc_rewarded_notify), True
    )


async def notify_fc_reward(user: User):
    builder = InlineKeyboardBuilder()
    target_url = f'{env_const.TMA_DIRECT_URL}?startapp=e_eyJwIjoicGF5In0-u_1999'
    builder.button(text="立即充值", url=target_url)
    user_tg = await user_service.get_tg_info_by_user_id(user.id)
    if not user_tg:
        return
    if user.register_source == UserRegisterSource.CHAT_BOT:
        message = await chat_bot.send_message(
            user_tg.tg_id, fc_reward_tips, reply_markup=builder.as_markup()
        )
        await tg_message_service.add_deleted_message(
            message, "CHAT_BOT", bot_id=chat_bot.id
        )
    else:
        message = await bot.send_message(
            user_tg.tg_id, fc_reward_tips, reply_markup=builder.as_markup()
        )
        await tg_message_service.add_deleted_message(message, bot_id=bot.id)


async def notify_recharge_channels(user: User):
    cnt = await recharge_service.get_unsuccess_recharge_orders(user.id)
    if cnt not in [2, 5, 8, 13, 21]:
        return

    user = await user_service.get_user_by_id(user.id)
    user_tg = await user_service.get_tg_info_by_user_id(user.id)
    if not user_tg:
        return
    builder = InlineKeyboardBuilder()
    builder.button(text="快捷充值", url=charge_url)
    try:
        message = await bot.send_message(
            user_tg.tg_id,
            await card_channel_tips(user.nickname),
            reply_markup=builder.as_markup(),
        )
        await tg_message_service.add_deleted_message(
            message, "TMA", timedelta(hours=1), bot_id=bot.id
        )
    except Exception as e:
        logging.warning(f"send message error: {e}")


async def check_and_notify_fc_reward(user: User):
    now = datetime.now(UTC)
    delta = now - user.created_at
    if delta.days >= 1:
        return
    balance = await AccountService.get_total_balance(user.id)
    if balance > 500:
        return
    has_benefit_recharge = await user_benefit_service.has_benefit_recharge(user.id)
    if has_benefit_recharge:
        return
    await notify_fc_reward(user)


async def after_message_tasks(
    user: User, ai_content: str, human_content: str, inviter_user_id: int | None
):
    await check_and_notify_fc_reward(user)

    if inviter_user_id is not None and inviter_user_id > 100000:
        inviter = await user_service.get_user_by_id(inviter_user_id)
        benefits = await user_benefit_service.get_user_benefits_with_type(inviter.id, ChatBenefitEnum.INVITATION)
        benefit_count = benefits[0].reward_times if benefits else 200

        tips = f"""🎉 恭喜您成功邀请 1 位新用户！获得 1000 金币 + {benefit_count} 次经济模式权益已到账 

请前往账户查收，注意在有效期内使用（金币31天有效，聊天权益7天有效）～

继续邀请好友，福利拿到手软！"""
        await bot_message_service.send_user_template_message(
            inviter, MessageTemplate(tips=tips)
        )

async def new_recharge_product(tg_id: int, bot: Bot):
    try:

        bot_config = await tg_config_service.get_main_bot_by_category(BotCategory.TMA)
        if not bot_config:
            return
        url = f"https://t.me/{bot_config.username}/tavern?startapp=e_eyJwIjoicGF5In0-u_1999"
        template = MessageTemplate(
            tips=RECHARGE_PRODUCT_CALLBACK_TIPS,
            buttons=[Button(text="点此充值领豪礼", url=url)],
        )
        await bot.send_message(
            tg_id, text=template.tips, reply_markup=template.as_markup()
        )
    except Exception as e:
        log.warning(f"send message error: {e}")

