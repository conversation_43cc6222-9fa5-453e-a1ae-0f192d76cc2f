import asyncio
from datetime import datetime, <PERSON><PERSON><PERSON>
import os
from typing import Optional
import logging
import random

from aiogram import Bot, types
import re


# from aiogram import LoggingMiddleware
from aiogram.types import (
    InlineKeyboardButton,
    InlineKeyboardMarkup,
)

from aiogram.utils.keyboard import InlineKeyboardBuilder

import pytz
import requests

from persistence.models.models import Product
from persistence.models.models_bot_image import (
    BotImgGenTask,
    BotImgBasicProfile,
    BotStartImagePool,
    ImgGenStatus,
    ImageBotSettings,
    GroupInviteLink,
    BotGroupInviteCofig,
    BotStartMsgLog,
    BotPromoMsgStageLog,
)

from tortoise.transactions import in_transaction

from common.image_bot_model import (
    GenImageBaseProfileBO,
    GenImageResultBO,
)


from common.image_bot_common import (
    IMAGE_STYLE_DICT,
    IMAGE_RESOLUTION_DICT,
    SHAPE_DICT,
    RESOLUTION_SHAPE_MAP,
)

from services import product_service
# from services.account_service import AccountService
from services.img_bot.bot_img_chekin_service import BotImgGroupCheckinService
from services.img_bot.img_review_service import ImageReviewService


PROMO_MSG = "【幻梦AI】幻梦AI - 效果超级炸裂的无限制成人AI，AI陪聊TG小程序：https://t.me/FancyTavernBot"
PROMO_STAGES = [5, 10, 20]

IMAGE_SERVER_URL = os.getenv("IMAGE_SERVER_URL")

HELP_MESSAGE_TEMPLATE = (
    "AI作图，只需输入图片描述即可完成图片生成。\n\n1.支持中文和英文输入\n\n2.高清模式以上有助于提升复杂图片的生成效果\n\n3. 价格说明：标清500💎，高清1000💎，超清1500💎\n\n4.偷走咒语☔️50💎,隐私模式50💎\n\n\n"
    "清晰度(BOT): {image_res}\n"
    "风格(BOT):{style}\n"
    "长宽比设置: {shape}\n\n\n"
    "🥷 隐私模式:\n"
    "{privacy}\n\n"
    "「幻夢AI」生图官方群:{invite_link}\n"
)

FREE_GEN_IMG_TIP = "剩余免费生图次数({cnt}/2)"


def get_stage(img_count: int) -> int:
    """根据累计生图张数获取推广阶段
    :param img_count: 累计生图张数
    :return: 推广阶段（5/10/20），如果不在阶段内则返回0
    """
    if 5 <= img_count < 10:
        return 5
    elif 10 <= img_count < 20:
        return 10
    elif img_count >= 20:
        return 20
    return 0


logger = logging.getLogger(__name__)


class ImageBotService:

    @classmethod
    async def update_b_profile_resolution(
        cls, tg_id: int, img_def: str
    ) -> GenImageBaseProfileBO:

        bot_img_profile = await BotImgBasicProfile.get_or_none(tg_id=tg_id)
        if not bot_img_profile:
            bot_img_profile = await BotImgBasicProfile.create(
                tg_id=tg_id,
                img_gen_profile={
                    "style": "image_style_1",
                    "resolution": img_def,
                    "privacy": "public",
                },
            )
        else:
            if not isinstance(bot_img_profile.img_gen_profile, dict):
                bot_img_profile.img_gen_profile = {}
            bot_img_profile.img_gen_profile["resolution"] = img_def
            await bot_img_profile.save(update_fields=["img_gen_profile"])

        return GenImageBaseProfileBO.from_model(bot_img_profile)

    @classmethod
    async def update_b_profile_style(
        cls, tg_id: int, style: str
    ) -> GenImageBaseProfileBO:
        bot_img_profile = await BotImgBasicProfile.get_or_none(tg_id=tg_id)
        if not bot_img_profile:
            bot_img_profile = await BotImgBasicProfile.create(
                tg_id=tg_id,
                img_gen_profile={
                    "style": style,
                    "resolution": "img_resolution_low",
                    "privacy": "public",
                },
            )
        else:
            if not isinstance(bot_img_profile.img_gen_profile, dict):
                bot_img_profile.img_gen_profile = {}
            bot_img_profile.img_gen_profile["style"] = style
            await bot_img_profile.save(update_fields=["img_gen_profile"])

        return GenImageBaseProfileBO.from_model(bot_img_profile)

    @classmethod
    async def update_b_profile_privacy(
        cls, tg_id: int, privacy: str
    ) -> GenImageBaseProfileBO:
        bot_img_profile = await BotImgBasicProfile.get_or_none(tg_id=tg_id)
        if not bot_img_profile:
            bot_img_profile = await BotImgBasicProfile.create(
                tg_id=tg_id,
                img_gen_profile={
                    "style": "image_style_1",
                    "resolution": "img_resolution_low",
                    "privacy": privacy,
                },
            )
        else:
            if not isinstance(bot_img_profile.img_gen_profile, dict):
                bot_img_profile.img_gen_profile = {}
            bot_img_profile.img_gen_profile["privacy"] = privacy
            await bot_img_profile.save(update_fields=["img_gen_profile"])

        return GenImageBaseProfileBO.from_model(bot_img_profile)

    @classmethod
    async def update_b_profile_shape(
        cls, tg_id: int, shape: str
    ) -> GenImageBaseProfileBO:
        # 更新用户画图基础信息
        bot_img_profile = await BotImgBasicProfile.get_or_none(tg_id=tg_id)
        if not bot_img_profile:
            # 如果不存在，则创建一个新的
            bot_img_profile = await BotImgBasicProfile.create(
                tg_id=tg_id,
                img_gen_profile={
                    "style": "image_style_1",
                    "resolution": "img_resolution_low",
                    "privacy": "public",
                    "shape": shape,
                },
            )
        else:
            # 如果存在，则更新 shape 字段
            if not isinstance(bot_img_profile.img_gen_profile, dict):
                bot_img_profile.img_gen_profile = {}
            bot_img_profile.img_gen_profile["shape"] = shape
            await bot_img_profile.save(update_fields=["img_gen_profile"])

        return GenImageBaseProfileBO.from_model(bot_img_profile)

    @classmethod
    async def get_basic_profile(cls, tg_id: int) -> GenImageBaseProfileBO:
        # 获取用户画图基础信息
        bot_img_profile = await BotImgBasicProfile.get_or_none(tg_id=tg_id)

        if not bot_img_profile:
            return GenImageBaseProfileBO(
                style="image_style_1", resolution="img_resolution_low"
            )
        return GenImageBaseProfileBO.from_model(bot_img_profile)

    @classmethod
    async def add_img_gen_task(
        cls, tg_id: int, prompt: str, basic_profile: GenImageBaseProfileBO
    ) -> BotImgGenTask:
        # 添加画图任务
        req_json = basic_profile.model_dump()
        req_json["prompt"] = prompt
        img_gen_task = await BotImgGenTask.create(
            tg_id=tg_id,
            prompt=prompt,
            req_json=req_json,
            status=ImgGenStatus.PROCESSING,
        )

        return img_gen_task

    @classmethod
    async def get_img_gen_task_by_id(cls, task_id: int) -> Optional[BotImgGenTask]:
        # 获取画图任务
        img_gen_task = await BotImgGenTask.get_or_none(id=task_id)

        return img_gen_task

    @classmethod
    async def get_img_gen_task_by_tg_id(cls, tg_id: int) -> Optional[BotImgGenTask]:
        # 获取画图任务
        img_gen_task = (
            await BotImgGenTask.filter(tg_id=tg_id)
            .filter(status=ImgGenStatus.PROCESSING)
            .first()
        )

        return img_gen_task

    @classmethod
    async def check_in_progress_img_gen_task(cls, tg_id: int) -> bool:
        # 检查是否有进行中的画图任务
        img_gen_task = (
            await BotImgGenTask.filter(tg_id=tg_id)
            .filter(status=ImgGenStatus.PROCESSING)
            .first()
        )
        if img_gen_task:
            c_time = img_gen_task.created_at
            now = datetime.now(tz=pytz.utc)
            if (now - c_time).total_seconds() > 60 * 2:
                # 超过2分钟没有更新,则认为任务失败
                img_gen_task.status = ImgGenStatus.FAILED
                img_gen_task.completed_at = now
                img_gen_task.error_message = "任务超时"
                await img_gen_task.save(
                    update_fields=["status", "completed_at", "error_message"]
                )
                logger.warning(f"任务超时,更新任务状态为失败,任务ID: {img_gen_task.id}")
                return False
            return True
        return False

    @classmethod
    async def update_img_gen_task(
        cls,
        req_id: int,
        status: ImgGenStatus,
        gen_result: Optional[dict] = None,
        error_message: Optional[str] = None,
    ) -> Optional[BotImgGenTask]:
        # 更新画图任务
        img_gen_task = await BotImgGenTask.get_or_none(id=req_id)
        if not img_gen_task:
            return None

        img_gen_task.status = status
        img_gen_task.completed_at = datetime.now(tz=pytz.utc)
        if gen_result:
            img_gen_task.gen_result = gen_result
        if error_message:
            img_gen_task.error_message = error_message
        await img_gen_task.save(
            update_fields=["status", "gen_result", "error_message", "completed_at"]
        )

    @classmethod
    async def update_sent_msg_id(
        cls, task_id: int, sent_msg_id: int
    ) -> Optional[BotImgGenTask]:
        logger.info(f"update_sent_msg_id: {task_id}, sent_msg_id: {sent_msg_id}")
        img_gen_task = await BotImgGenTask.get_or_none(id=task_id)
        if not img_gen_task:
            logger.error(f"Task with id {task_id} not found.{sent_msg_id}")
            return None
        img_gen_task.sent_msg_id = sent_msg_id
        await img_gen_task.save(update_fields=["sent_msg_id"])

    @classmethod
    async def get_img_bot_start_msg(cls, bot_id: int) -> str:
        # 获取机器人启动消息
        img_bot_settings = await ImageBotSettings.get_or_none(bot_id=bot_id)
        if not img_bot_settings:
            return "欢迎使用机器人"
        return img_bot_settings.start_msg

    @classmethod
    async def get_share_group_topic(cls, bot_id: int, style: str) -> tuple[int, int]:
        """
        style_group_mapping 字段格式:
        {"image_style_1": {"group_id": -1002411997003, "topic_id": 2367},    "image_style_2": {"group_id": -1002411997003, "topic_id": 2368}
        }
        """

        # 获取机器人分享群和话题
        img_bot_settings = await ImageBotSettings.get_or_none(bot_id=bot_id)
        logger.info(
            f"获取机器人分享群和话题,bot_id:{bot_id},style:{style},img_bot_settings:{img_bot_settings}"
        )
        if style not in IMAGE_STYLE_DICT.keys():
            logger.warning(f"Style {style} not found in image_style_dict")
            style = "image_style_1"  # 默认样式
        if img_bot_settings and img_bot_settings.style_group_mapping:
            style_group_mapping = img_bot_settings.style_group_mapping
            if style in style_group_mapping:
                group_id = style_group_mapping.get(style).get("group_id")  # type: ignore
                topic_id = style_group_mapping.get(style).get("topic_id")  # type: ignore
                return group_id, topic_id

        # 如果没有找到对应的映射，返回默认值
        return 0, 0

    @classmethod
    async def get_start_random_image(cls, bot_id: int) -> tuple[str, str]:
        """
        获取机器人启动时的随机图片
        """
        random_image_list = (
            await BotStartImagePool.filter(bot_id=bot_id).order_by("id").all().limit(10)
        )
        if not random_image_list:
            return "", ""
        # 随机选择一张图片
        random_image = random.choice(random_image_list)

        logger.info(
            f"获取机器人启动时的随机图片,bot_id:{bot_id},random_image:{random_image}"
        )
        return random_image.img_url, random_image.img_desc

    @classmethod
    async def add_start_msg_log(
        cls, bot_id: int, tg_id: int, msg_text: str, msg_id: int = 0
    ) -> BotStartMsgLog:
        """
        添加机器人启动消息日志
        """
        start_msg_log = await BotStartMsgLog.create(
            bot_id=bot_id, tg_id=tg_id, msg_text=msg_text, msg_id=msg_id
        )
        logger.info(
            f"添加机器人启动消息日志,bot_id:{bot_id},tg_id:{tg_id},msg_text:{msg_text}"
        )
        return start_msg_log

    @classmethod
    async def check_join_group(cls, bot_id: int, tg_id: int) -> bool:
        """
        检查是否能加入群组
        """
        logger.info(f"检查是否能加入群组,bot_id:{bot_id},tg_id:{tg_id}")
        gen_task = await BotImgGenTask.get_or_none(tg_id=tg_id)

        if gen_task:
            return True

        start_msg_log = await BotStartMsgLog.get_or_none(tg_id=tg_id)
        if start_msg_log:
            return True

        return False

    @classmethod
    async def check_and_send_promo_msg(cls, bot: Bot, bot_id: int, tg_id: int):
        """
        检查用户累计生图张数，达到5/10/20时推送推广消息，并按规则删除历史消息
        :param bot: bot对象
        :param bot_id: 当前bot id
        :param tg_id: 用户tg id
        :param send_func: 发送消息函数，需返回消息对象（含message_id）
        """
        img_count = (
            await BotImgGenTask.filter(tg_id=tg_id)
            .filter(created_at__gt=datetime.now(tz=pytz.utc) - timedelta(days=1))
            .count()
        )
        stage = get_stage(img_count)

        logger.info(
            f"检查用户累计生图张数,tg_id:{tg_id},img_count:{img_count},stage:{stage}"
        )
        if stage not in PROMO_STAGES:
            logger.info(
                f"用户累计生图张数未达到推广阶段,不推送,tg_id:{tg_id},img_count:{img_count},stage:{stage}"
            )
            return

        async with in_transaction():
            # 检查该阶段是否已推送过
            exist = await BotPromoMsgStageLog.filter(
                bot_id=bot_id, tg_id=tg_id, stage=stage
            ).first()
            if exist:
                logger.info(f"用户已在阶段{stage}推送过消息,不再重复推送,tg_id:{tg_id}")
                return

            # 推送消息
            try:
                msg = await bot.send_message(tg_id, PROMO_MSG)
            except Exception as e:
                logger.warning(f"推送消息失败,tg_id:{tg_id},error:{e}")
                return
            # 记录本次推送
            await BotPromoMsgStageLog.create(
                bot_id=bot_id,
                tg_id=tg_id,
                stage=stage,
                msg_id=msg.message_id,
                is_deleted=False,
            )

            # 删除逻辑
            if stage == 10:
                # 删除5阶段的消息
                old = await BotPromoMsgStageLog.filter(
                    bot_id=bot_id, tg_id=tg_id, stage=5, is_deleted=False
                ).first()
                if old:
                    try:
                        await bot.delete_message(tg_id, old.msg_id)
                    except Exception:
                        logger.warning(
                            f"删除5阶段消息失败,tg_id:{tg_id},msg_id:{old.msg_id}"
                        )
                    old.is_deleted = True
                    await old.save()
            elif stage == 20:
                # 删除10阶段的消息
                old = await BotPromoMsgStageLog.filter(
                    bot_id=bot_id, tg_id=tg_id, stage=10, is_deleted=False
                ).first()
                if old:
                    try:
                        await bot.delete_message(tg_id, old.msg_id)
                    except Exception:
                        logger.warning(
                            f"删除10阶段消息失败,tg_id:{tg_id},msg_id:{old.msg_id}"
                        )
                    old.is_deleted = True
                    await old.save()

    @classmethod
    async def get_help_message(
        cls, bot_id: int, tg_id: int, image_base_profile: GenImageBaseProfileBO
    ):

        logger.info(
            f"获取帮助信息,bot_id:{bot_id},image_base_profile:{image_base_profile}"
        )

        small_free_gen_count = await BotImgGroupCheckinService.get_free_gen_img_count(
            tg_id=tg_id
        )

        image_res_str = IMAGE_RESOLUTION_DICT.get(image_base_profile.resolution, "标清")

        if image_base_profile.resolution == "img_resolution_low":
            image_res_str += "（500💎）"
            if small_free_gen_count > 0:
                image_res_str += FREE_GEN_IMG_TIP.format(cnt=small_free_gen_count)
        elif image_base_profile.resolution == "img_resolution_medium":
            image_res_str += "（1000💎)"
        elif image_base_profile.resolution == "img_resolution_high":
            image_res_str += "（1500💎）"

        style_str = IMAGE_STYLE_DICT.get(image_base_profile.style, "【二次元风】")

        shape_str = SHAPE_DICT.get(image_base_profile.shape, "竖图")
        privacy_str = (
            "🔗 自動分享到群"
            if image_base_profile.privacy == "public"
            else "「🥷 隱私模式」不分享到群（+50 💎)"
        )

        _, invite_link = await ImageShareReviewService.get_group_invite_link_txt(
            bot_id=bot_id
        )
        return HELP_MESSAGE_TEMPLATE.format(
            image_res=image_res_str,
            style=style_str,
            privacy=privacy_str,
            invite_link=invite_link,
            shape=shape_str,
        )

    @classmethod
    async def del_msg_delay(
        cls, bot: Bot, chat_id: int, message_id: int, delay: int = 60
    ):
        logger.info(f"del_msg_delay: {chat_id},{message_id},{delay}")
        await asyncio.sleep(delay)
        try:
            await bot.delete_message(chat_id=chat_id, message_id=message_id)
        except Exception as e:
            logger.error(f"del_msg_delay error: {e}", exc_info=True)

    @staticmethod
    def create_copy_prompt_button(
        bot_username: str, copy_prompt_str: str
    ) -> InlineKeyboardMarkup:
        logger.info(f"create_copy_prompt_button: {bot_username},{copy_prompt_str}")
        # 创建键盘并添加按钮
        copy_prompt_keyboard = InlineKeyboardBuilder()
        # 创建一个跳转按钮
        button = InlineKeyboardButton(
            text="偷走咒语☔️",
            url=f"https://t.me/{bot_username}?start={copy_prompt_str}",  # 指定目标 Bot 的链接
        )
        copy_prompt_keyboard.add(button)
        return copy_prompt_keyboard.as_markup()

    @classmethod
    async def send_photo_to_share_group(
        cls, bot: Bot, photo_url: str, style: str, replay_markup: InlineKeyboardMarkup
    ):
        logger.info(f"send_photo_to_share_group: {photo_url},{style}")
        try:
            group_id, topic_id = await ImageBotService.get_share_group_topic(
                bot.id, style
            )

            if group_id == 0 or topic_id == 0:
                logger.warning(f"Group ID or Topic ID not found for style: {style}")
                return
            sent_msg = await bot.send_photo(
                chat_id=group_id,
                message_thread_id=topic_id,
                photo=photo_url,
                protect_content=True,
                reply_markup=replay_markup,
            )

            logger.info(
                f"send_photo_to_share_group: {sent_msg.message_id},{group_id},{topic_id}"
            )
        except Exception as e:
            logger.error(f"send_image error: {e},{group_id},{topic_id}", exc_info=True)

    @staticmethod
    def escape_markdown_v2(text: str) -> str:
        # 转义 MarkdownV2 特殊字符
        return re.sub(r"([_*\[\]()~`>#\+\-=|{}.!])", r"\\\1", text)

    @classmethod
    async def handle_copy_prompt(
        cls,
        copy_prompt_id: int,
        message: types.Message,
        bot: Bot,
        user_id,
        image_bot_name: str,
    ):
        """
        处理复制提示词的逻辑
        :param copy_prompt_id: 提示词 ID
        :param message: 消息对象
        :param bot: Bot 实例
        :param user_id: 用户 ID
        """
        from services.account_service import AccountService
        logger.info(f"handle_copy_prompt:{copy_prompt_id},{message.text}")
        # 处理复制提示词的逻辑
        # 从数据库中获取提示词
        img_task = await cls.get_img_gen_task_by_id(int(copy_prompt_id))
        if img_task:
            prompt = img_task.prompt
            logger.info(f"copy_prompt_id:{copy_prompt_id},prompt:{prompt}")
            # 发送提示词
            prompt_text = f"{prompt}"
            photo_url = img_task.gen_result.get("image_url")  # type: ignore

            balance = await AccountService.get_total_balance(user_id)

            if balance < 50:
                logger.warning(f"余额不足,当前余额:{balance},{message.from_user.id}")
                await bot.send_message(
                    chat_id=message.chat.id,
                    text=f'⚠️⚠️余额不足请充值之后再玩～\n\n点击 <a href="https://t.me/{image_bot_name}?start=recharge">这里进行充值</a>',
                    parse_mode="HTML",
                )

                return

            # 对 prompt_text 加粗和斜体
            tip_txt = f"\n\n(🌟✨偷学咒语成功)"
            escaped_text = f"`{prompt_text}`" + ImageBotService.escape_markdown_v2(
                tip_txt
            )  # 转义MarkdownV2特殊字符

            sent_msg = await bot.send_photo(
                chat_id=message.chat.id,
                photo=photo_url,  # type: ignore
                caption=escaped_text,
                allow_sending_without_reply=True,
                parse_mode="MarkdownV2",  # 使用MarkdownV2格式
            )
            # sent_msg = await message.reply_photo(photo=photo_url, caption=prompt_text, allow_sending_without_reply=True)  # type: ignore

            if sent_msg:
                logger.info(
                    f"copy_prompt_id:{copy_prompt_id},sent_msg:{sent_msg.message_id}"
                )

                try:
                    # 复制提示词，扣除50钻石
                    product = await product_service.get_photo_bot_copy_promt_product()
                    await AccountService.create_pay_order(
                        user_id=user_id,
                        product=product,  # type: ignore
                        role_id=-copy_prompt_id,  # 使用负数来区分角色
                    )  # type: ignore
                except Exception as e:
                    logger.error(f"create_pay_order error: {e}", exc_info=True)
                # # 删除消息
                # asyncio.create_task(
                #     del_msg_delay(bot=bot, chat_id=message.chat.id, message_id=sent_msg.message_id, delay=60)  # type: ignore
                # )
        else:
            logger.info(f"copy_prompt_id:{copy_prompt_id},not found")
            start_msg = await ImageBotService.get_img_bot_start_msg(bot_id=bot.id)
            sent_msg = await message.reply(
                start_msg, parse_mode="html", allow_sending_without_reply=True
            )

            asyncio.create_task(
                del_msg_delay(bot=bot, chat_id=message.chat.id, message_id=sent_msg.message_id, delay=60)  # type: ignore
            )

    @staticmethod
    def create_one_try_button(
        bot_username: str, try_prompt_str: str, style_txt: str
    ) -> InlineKeyboardMarkup:
        """创建一个试用按钮的键盘
        :param bot_username: 目标 Bot 的用户名
        :param try_prompt_str: 试用提示字符串
        :return: InlineKeyboardMarkup 对象
        """

        logger.info(f"create_one_try_button: {bot_username},{try_prompt_str}")
        # 创建键盘并添加按钮
        try_prompt_keyboard = InlineKeyboardBuilder()

        try_txt = ImageBotService.escape_markdown_v2(f"{style_txt}我也试试")
        # 创建一个跳转按钮
        button = InlineKeyboardButton(
            text=try_txt,
            url=f"https://t.me/{bot_username}?start={try_prompt_str}",  # 指定目标 Bot 的链接
        )
        try_prompt_keyboard.add(button)
        return try_prompt_keyboard.as_markup()

    @staticmethod
    def create_share_group_photo_button_markup(
        bot_username: str, task_id: int, style_txt: str
    ) -> InlineKeyboardMarkup:
        """创建一个分享按钮的键盘
        :param bot_username: 目标 Bot 的用户名
        :param task_id: 任务 ID
        :return: InlineKeyboardMarkup 对象
        """
        logger.info(
            f"create_share_group_photo_button: {bot_username},{task_id},style_txt:{style_txt}"
        )
        # 创建键盘并添加按钮
        share_keyboard = InlineKeyboardBuilder()

        one_try_txt = ImageBotService.escape_markdown_v2(f"{style_txt}我也试一试")

        # 创建一个试一试跳转按钮
        one_try_button = InlineKeyboardButton(
            text=one_try_txt,
            url=f"https://t.me/{bot_username}?start=one_try_{task_id}",  # 指定目标 Bot 的链接
        )
        # 创建一个偷走咒语按钮
        copy_prompt_str = f"copy_prompt_{task_id}"
        copy_prompt_button = InlineKeyboardButton(
            text="偷走咒语☔️",
            url=f"https://t.me/{bot_username}?start={copy_prompt_str}",  # 指定目标 Bot 的链接
        )

        share_keyboard.add(copy_prompt_button).add(one_try_button)
        share_keyboard.adjust(1, 1)  # 调整按钮布局
        return share_keyboard.as_markup()

    @classmethod
    async def handle_one_try(
        cls, task_id: int, message: types.Message, bot: Bot, user_id
    ):
        """
        处理试用提示词的逻辑
        :param one_try_task_id: 提示词 ID
        :param message: 消息对象
        :param bot: Bot 实例
        :param user_id: 用户 ID
        """
        logger.info(f"handle_one_try:{task_id}")
        img_task = await ImageBotService.get_img_gen_task_by_id(task_id)

        if img_task:
            req_json = img_task.req_json

            style = req_json.get("style", "image_style_1")  # type: ignore

            await ImageBotService.update_b_profile_style(tg_id=user_id, style=style)
            chat_id = message.chat.id
            try:
                sent_msg = await bot.send_message(
                    chat_id=chat_id,
                    text=f"自动切换至风格: {IMAGE_STYLE_DICT.get(style, IMAGE_STYLE_DICT['image_style_1'])}",
                )

                asyncio.create_task(
                    ImageBotService.del_msg_delay(
                        bot=bot,
                        chat_id=chat_id,
                        message_id=sent_msg.message_id,
                        delay=60,
                    )
                )
            except Exception as e:
                logger.error(
                    f"send message error: {e}, chat_id: {chat_id}", exc_info=True
                )

        # ...existing code...

    @classmethod
    async def send_checkin_group_tip(
        cls, message: types.Message, bot: Bot, invite_link: str
    ):
        tip_text = (
            "「簽到」功能已經轉移至<b>【幻夢AI春宮圖同享】</b>\n"
            f"群組 <a href='{invite_link}'>{invite_link}</a> 簽到，簽到刷新每日的2次免費生圖权益\n\n"
            "生圖群中直接發送「簽到」、「签到」、「打卡」即可獲得生圖次數，點選以下按鈕到群組簽到。"
        )
        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="去【幻夢AI春宮圖同享】群签到", url=f"{invite_link}"
                    )
                ]
            ]
        )
        try:
            sent_msg = await message.reply(
                tip_text, reply_markup=keyboard, parse_mode="HTML"
            )

            # 删除消息 1min 之后
            asyncio.create_task(
                ImageBotService.del_msg_delay(
                    bot=bot,
                    chat_id=message.chat.id,
                    message_id=sent_msg.message_id,
                    delay=60,
                )
            )
        except Exception as e:
            logger.error(f"send_checkin_group_tip error: {e}", exc_info=True)

    @staticmethod
    def create_retry_button(
        task_id: int, prompt: str, lang: str = "zh"
    ) -> InlineKeyboardMarkup:
        logger.info(f"create_retry_button: {task_id},{prompt},{lang}")
        # 创建键盘并添加按钮
        retry_keyboard = InlineKeyboardBuilder()
        if lang == "en":
            txt = "🪄Retry Drawing"
            same_style_txt = "🎨One-Click Same Style"
        else:
            txt = "🎰继续抽卡"
            same_style_txt = "🎨一键同款"

        # 创建一个一键同款按钮
        same_button = InlineKeyboardButton(
            text=same_style_txt,
            switch_inline_query_current_chat=f"{prompt}",
        )

        # 创建一个跳转按钮
        button = InlineKeyboardButton(
            text=txt,
            callback_data=f"retry_task_{task_id}",
        )
        retry_keyboard.add(same_button).add(button)
        retry_keyboard.adjust(1, 1)

        return retry_keyboard.as_markup()


class ImageShareReviewService:
    """
    画图任务审核服务
    """

    @classmethod
    async def get_group_invite_link_txt(
        cls, bot_id: int, creates_join_request: bool = False
    ) -> tuple[str, str]:
        """
        获取群组的邀请链接，支持生成一个30分钟有效的链接，并能够替换现有链接。
        return: (群组邀请文本, 群组邀请链接)
        """
        try:

            bot_config = await BotGroupInviteCofig.filter(
                bot_id=bot_id, is_enable=True
            ).first()
            if not bot_config:
                logger.error(f"Bot with id {bot_id} not found.不启用链接生成")
                return "", ""

            group_id = bot_config.group_id

            # 先查找数据库中是否有有效的邀请链接
            now = datetime.now(tz=pytz.utc) + timedelta(minutes=10)
            invite_link_obj = (
                await GroupInviteLink.filter(chat_id=group_id, expire_date__gt=now)
                .order_by("-expire_date")
                .first()
            )
            if invite_link_obj:
                logger.info(
                    f"从数据库获取有效群组邀请链接,bot_id:{bot_id},invite_link:{invite_link_obj.invite_link}"
                )
                return bot_config.group_invite_text, invite_link_obj.invite_link

            # 如果没有有效的邀请链接，则创建一个新的邀请链接
            logger.info(f"创建新的群组邀请链接,bot_id:{bot_id},group_id:{group_id}")

            if bot_config.creates_join_request:
                member_limit = None  # 如果需要管理员批准，则不设置成员限制
            else:
                member_limit = bot_config.member_limit

            # 创建新的邀请链接
            bot_token = bot_config.bot_token
            # 使用 async with 确保会话正确关闭
            async with Bot(token=bot_token) as bot:
                invite_link = await bot.create_chat_invite_link(
                    chat_id=group_id,  # 群组ID
                    creates_join_request=creates_join_request,  # 创建加入请求
                    name="Group_Invite_Link",  # 邀请链接名称
                    expire_date=datetime.now(tz=pytz.utc)
                    + timedelta(minutes=30),  # 30分钟有效期
                    member_limit=member_limit,  # 限制成员数量
                )
                logger.info(
                    f"获取群组邀请链接成功,bot_id:{bot_id},{group_id},invite_link:{invite_link}"
                )

                await GroupInviteLink.create(
                    chat_id=group_id,
                    invite_link=invite_link.invite_link,
                    name=invite_link.name,
                    creates_join_request=invite_link.creates_join_request,
                    member_limit=invite_link.member_limit,
                    expire_date=invite_link.expire_date,
                )
                return bot_config.group_invite_text, invite_link.invite_link
        except Exception as e:
            logger.error(f"获取群组邀请链接失败,bot_id:{bot_id},error:{e}")
            return "", ""


class ImageGenService:
    """
    画图服务
    """

    @classmethod
    async def gen_image_and_send_bot(
        cls,
        tg_id: int,
        prompt: str,
        message: types.Message,
        user_id: int = 0,
        free_times_pripority: bool = False,
        del_pre_msg_id: int = 0,
        lang: str = "zh",
    ):

        logger.info(f"gen_image_and_send_bot:{tg_id},{prompt},{del_pre_msg_id},{lang}")

        basic_profile = await ImageBotService.get_basic_profile(tg_id)
        style = basic_profile.style
        resolution = basic_profile.resolution
        # check 有无生成任务
        is_have_progress = await ImageBotService.check_in_progress_img_gen_task(tg_id)
        if is_have_progress:
            logger.warning(f"用户 {tg_id} 正在进行中的生成任务")
            await message.reply("上一个图片正在创作中,请等待上一个任务完成...")
            return
        # 生成图片
        logger.info(
            f"gen_image_and_send_bot:{tg_id},{prompt},{basic_profile},{style},{resolution}"
        )

        gen_task = await ImageBotService.add_img_gen_task(tg_id, prompt, basic_profile)

        # image_result = await generate_image(
        #     req_id=gen_task.id, prompt=prompt, style=style, resolution=resolution_v
        # )

        image_result_bo = await ImageGenService._generate_image_v2(
            req_id=gen_task.id,
            prompt=prompt,
            style=style,
            resolution=resolution,
            shape=basic_profile.shape,
            tg_id=tg_id,
            user_id=0,
        )

        logger.info(f"gen_image_and_send_bot:{image_result_bo}")

        await ImageBotService.update_img_gen_task(
            gen_task.id, ImgGenStatus.COMPLETED, image_result_bo.to_dict()
        )

        if image_result_bo and image_result_bo.code == 200:

            image_url = image_result_bo.image_url
            # del_pre_msg
            if del_pre_msg_id:
                try:
                    await message.bot.delete_message(
                        chat_id=message.chat.id, message_id=del_pre_msg_id
                    )
                except Exception as e:
                    logger.error(
                        f"del_pre_msg error: {del_pre_msg_id},{e}", exc_info=True
                    )

            # 发送图片
            sent_id = cls._send_image_to_bot_and_update_task(
                message=message,
                task_id=gen_task.id,
                prompt=prompt,
                image_url=image_url,
                tg_id=tg_id,
            )
            if sent_id:
                logger.info(f"图片发送成功,消息ID: {sent_id}")
                await cls._handle_pay_logic(
                    user_id=user_id,
                    tg_id=tg_id,
                    gen_task_id=gen_task.id,
                    basic_profile=basic_profile,
                )

                # 处理分享q群逻辑
                await cls._handle_share_group_logic(
                    message=message,
                    tg_id=tg_id,
                    user_id=user_id,
                    basic_profile=basic_profile,
                    image_result_bo=image_result_bo,
                )
        # 发送推广消息
        asyncio.create_task(
            ImageBotService.check_and_send_promo_msg(
                bot=message.bot,  # type: ignore
                bot_id=message.bot.id,  # type: ignore
                tg_id=tg_id,
            )
        )

    @staticmethod
    def __normalize_image_params(
        req_id: int,
        style: str,
        resolution: str,
        shape: str,
        prompt: str,
        tg_id: int = 0,
        user_id: int = 0,
    ) -> dict:
        """
        Normalize the image generation parameters.
        :param style: The style of the image
        :param resolution: The resolution of the image
        :param shape: The shape of the image
        :param prompt: The text prompt for image generation
        :return: A dictionary containing normalized parameters
        """
        if style and style.startswith("image_style_"):
            normalized_style = style.replace("image_style_", "")
        else:
            normalized_style = "1"
        normalized_resolution = RESOLUTION_SHAPE_MAP.get(
            (resolution, shape), "normal_portrait"
        )

        return {
            "style": normalized_style,
            "resolution": normalized_resolution,
            "prompt": prompt,
            "request_id": str(req_id),
            "tgid": tg_id,
            "uid": user_id,
            "source": 1,  # 1 for image bot
        }

    @staticmethod
    async def _generate_image_v2(
        req_id: int,
        prompt: str,
        style: str,
        resolution: str,
        shape: str,
        tg_id: int = 0,
        user_id: int = 0,
    ) -> GenImageResultBO:

        logger.info(
            f"generate_image_v2: req_id={req_id},prompt={prompt},style={style},resolution={resolution},shape={shape},tg_id={tg_id},user_id={user_id}"
        )

        req_json = ImageGenService.__normalize_image_params(
            req_id=req_id,
            style=style,
            resolution=resolution,
            shape=shape,
            prompt=prompt,
            tg_id=tg_id,
            user_id=user_id,
        )

        logger.info(f"generate_image_v2 called with: {req_json}")
        #  api 调用 ImageServer

        if not IMAGE_SERVER_URL:
            logger.error("IMAGE_SERVER_URL is not set, cannot generate image.")
            return GenImageResultBO(
                tg_id=tg_id,
                prompt=prompt,
                request_id=req_id,
                code=505,
                msg="error: IMAGE_SERVER_URL is not set",
            )

        try:

            response = requests.post(
                f"{IMAGE_SERVER_URL}",
                json=req_json,
                timeout=60,
            )
            logger.info(
                f"Image_remote_generation_response: {response.status_code}, {response.text}"
            )
            if response.status_code != 200:
                logger.error(
                    f"Image generation failed with status code: {response.status_code}"
                )
                return GenImageResultBO(
                    tg_id=tg_id,
                    prompt=prompt,
                    request_id=req_id,
                    code=response.status_code,
                    msg=f"error: {response.text}",
                )

            else:

                return GenImageResultBO(
                    tg_id=tg_id,
                    prompt=prompt,
                    request_id=req_id,
                    code=response.json().get("code", 501),
                    msg=response.json().get("msg", ""),
                    image_url=response.json().get("image_url", ""),
                    enhanced_prompt=response.json().get("enhanced_prompt", ""),
                )

        except Exception as e:
            logger.error(f"Request error during image generation: {e}", exc_info=True)
            return GenImageResultBO(
                tg_id=tg_id,
                prompt=prompt,
                request_id=req_id,
                code=500,
                msg=f"exception: {str(e)}",
            )

    @classmethod
    async def _send_image_to_bot_and_update_task(
        cls,
        message: types.Message,
        tg_id: int,
        image_url: str,
        prompt: str,
        task_id: int,
        lang: str = "zh",
    ) -> int:
        """
        发送生成的图片到用户
        :param bot: Bot对象
        :param tg_id: 用户的Telegram ID
        :param image_url: 图片的URL
        :param prompt: 生成图片的提示词
        :param task_id: 任务ID
        :param lang: 语言选项，默认为中文
        """

        try:
            # 发送图片
            reply_markup = ImageBotService.create_retry_button(
                task_id=task_id, prompt=prompt, lang=lang
            )
            sent_msg = await message.reply_photo(
                photo=image_url, reply_markup=reply_markup
            )
            if sent_msg:
                logger.info(f"send_image success: {sent_msg.message_id}")
                # 更新任务的 sent_msg_id
                await ImageBotService.update_sent_msg_id(
                    task_id=task_id, sent_msg_id=sent_msg.message_id
                )
                return sent_msg.message_id
        except Exception as e:
            logger.error(f"send_image error: {task_id}, {e}", exc_info=True)

        return 0

    @classmethod
    async def _handle_pay_logic(
        cls,
        user_id: int,
        tg_id: int,
        gen_task_id: int,
        basic_profile: GenImageBaseProfileBO,
    ):
        from services.account_service import AccountService
        # 求一个id 的负数来区分role
        role_id = -gen_task_id
        pay_order_flag = True

        # 检查用户是否有免费生成次数
        if basic_profile.resolution == "img_resolution_low":
            # 低清免费生成次数
            free_cnt = await BotImgGroupCheckinService.get_free_gen_img_count(tg_id)

        free_times_priority = free_cnt > 0

        if free_times_priority:
            # 如果有免费次数，优先使用免费次数
            try:
                free_suc = await BotImgGroupCheckinService.decrease_free_gen_img_count(
                    user_id=user_id, task_id=role_id
                )
                if free_suc:
                    logging.info(f"用户免费生成次数扣除成功{user_id}, {role_id}")
                    pay_order_flag = False
            except Exception as e:
                logging.error(f"扣除免费生成次数失败: {user_id},{e}", exc_info=True)

        if pay_order_flag:
            # 获取支付产品
            photo_product = await ImageGenService._get_pay_product(
                user_id=user_id, basic_profile=basic_profile
            )

            try:
                logging.info(
                    f"用户没有免费生成次数,扣除钻石{user_id}, {role_id},phto_product:{photo_product}"
                )
                await AccountService.create_pay_order(
                    user_id=user_id, product=photo_product, role_id=role_id
                )
            except Exception as e:
                logging.error(f"create_pay_order error: {user_id},{e}", exc_info=True)

    @classmethod
    async def _handle_share_group_logic(
        cls,
        user_id: int,
        tg_id: int,
        message: types.Message,
        basic_profile: GenImageBaseProfileBO,
        image_result_bo: GenImageResultBO,
        lang: str = "zh",
    ):
        if lang == "zh" and basic_profile.privacy == "public":
            logger.info(
                f"用户隐私设置为公开,转发图片到群里的审核任务:{image_result_bo}"
            )
            try:
                # 添加审核任务
                await ImageReviewService.create_review_task(
                    tg_id=tg_id,
                    user_id=user_id,
                    task_id=image_result_bo.request_id,
                    prompt=image_result_bo.prompt,
                    nai_prompt=image_result_bo.enhanced_prompt,
                    image_url=image_result_bo.image_url,
                )
            except Exception as e:
                logger.error(f"_handle_share_group_logic error: {e}", exc_info=True)

    @classmethod
    async def _get_pay_product(
        cls, user_id: int, basic_profile: GenImageBaseProfileBO
    ) -> Product:
        """获取支付产品
        :param user_id: 用户ID
        :param basic_profile: 用户的基本画图配置
        :return: 支付产品对象
        """

        mid = basic_profile.resolution
        if basic_profile.privacy == "private":
            mid = basic_profile.resolution + "_private"

        logger.info(f"获取支付产品, user_id: {user_id}, mid: {mid}")
        return await product_service.get_online_photo_bot_product(mid=mid)  # type: ignore
