from datetime import UTC, datetime, timedelta
import logging
import os
from dotenv import load_dotenv
from fastapi import Response
import firebase_admin
from firebase_admin import credentials, auth
from tortoise.transactions import atomic, in_transaction


from controllers.user_check import create_access_token
from persistence.models.models import OauthUser, User, UserRegisterSource
from services.account_service import AccountService


load_dotenv()

certificate_file = os.getenv("FIREBASE_CERTIFICATE_FILE")
cred = credentials.Certificate(certificate_file)
app = firebase_admin.initialize_app(cred)
log = logging.getLogger(__name__)


async def login_or_register_by_firebase(
    id_token: str, source: UserRegisterSource
) -> User | None:
    decoded_token = {}
    try:
        decoded_token = auth.verify_id_token(id_token)
    except Exception as e:
        log.error("analysis firebase token error, e: %s", e, exc_info=True)
        return None
    log.info("analysis firebase token decoded_token: %s", decoded_token)

    if decoded_token is None or "uid" not in decoded_token:
        log.error("analysis firebase token error, decoded_token: %s", decoded_token)
        return None

    # Use the oauth_login function to handle the authentication
    return await oauth_login("firebase", decoded_token, source)


async def add_login_token(response: Response, user: User):
    access_token_expires = timedelta(days=30)
    access_token = create_access_token(
        data={"sub": str(user.id)}, expires_delta=access_token_expires
    )
    response.set_cookie(
        "token",
        access_token,
        samesite="none",
        secure=True,
        expires=datetime.now(UTC) + timedelta(days=30),
    )


async def gmail_login(email, name, user_info, source):
    # For backward compatibility, ensure email is in user_info
    if email and "email" not in user_info:
        user_info["email"] = email

    # For backward compatibility, ensure name is in user_info
    if name and "name" not in user_info:
        user_info["name"] = name

    # Use the oauth_login function to handle the authentication
    return await oauth_login("google", user_info, source)


async def oauth_login(provider_name: str, user_info: dict, source: UserRegisterSource) -> User | None:
    """
    Generic OAuth login function that works with various providers including those that don't provide email

    Args:
        provider_name: Name of the provider (e.g., 'twitter', 'facebook')
        user_info: User information from the provider
        source: Registration source

    Returns:
        User object
    """
    # Get the provider's unique ID for this user
    provider_uid = user_info.get("id", "") or user_info.get("uid", "")

    if not provider_uid:
        log.error(f"No unique ID found in {provider_name} user info: {user_info}")
        return None

    # Check if user already exists with this provider uid
    oauth_user = await OauthUser.filter(
        provider_user_id=provider_uid, provider_name=provider_name
    ).first()

    if oauth_user:
        return await User.get(id=oauth_user.uid)

    # Get email if available, otherwise generate a synthetic one
    email = user_info.get("email", "")
    if not email:
        email = f"{provider_name}_{provider_uid}@firebase.auth"
        log.info(f"No email provided by {provider_name}, using synthetic email: {email}")

    # Get or generate a nickname
    nickname = user_info.get("name", "") or user_info.get("displayName", "")
    if not nickname:
        # Try to extract from other common fields based on the provider
        if provider_name == "twitter":
            nickname = user_info.get("screen_name", "") or f"Twitter_{provider_uid[:8]}"
        elif provider_name == "facebook":
            nickname = f"{user_info.get('first_name', '')} {user_info.get('last_name', '')}".strip()
            if not nickname:
                nickname = f"Facebook_{provider_uid[:8]}"
        else:
            nickname = f"{provider_name.capitalize()}_User_{provider_uid[:8]}"

    # Get avatar if available
    avatar = ""
    if provider_name == "twitter":
        avatar = user_info.get("profile_image_url_https", "")
    elif provider_name == "facebook":
        avatar = user_info.get("picture", {}).get("data", {}).get("url", "")
    else:
        avatar = user_info.get("picture", "") or user_info.get("avatar", "")

    enable_nsfw = False if source == UserRegisterSource.USA_WEB.value else True
    show_nsfw = False if source == UserRegisterSource.USA_WEB.value else True

    async with in_transaction():
        user = await User.create(
            email=email,
            password="xx0001",
            nickname=nickname,
            register_source=source,
            enable_nsfw=enable_nsfw,
            show_nsfw=show_nsfw,
            avatar=avatar,
        )
        await OauthUser.create(
            uid=user.id,
            provider_name=provider_name,
            provider_user_id=provider_uid,
            provider_user_email=email,
            user_info=user_info,
        )
        await AccountService.create_account(user.id)
        return user
