import logging
import os

from dotenv import load_dotenv
from common.common_constant import LlmModel, LlmRequestCluster
from common.models.chat_model import ModelStatus
from persistence.models.models import LlmModelConfig, ModelWaterConfig
from utils import tg_util


load_dotenv()

DEFAULT_LLM_MODEL = LlmModel.CLAUDE_3_HAIKU.value

log = logging.getLogger(__name__)

BASE_URL = os.getenv("ai_base_url", "")
PRO_BASE_URL = os.getenv("ai_base_url", "")
FREE_BASE_URL = os.getenv("free_ai_base_url", "")


def get_lite_llm_base_url(request_cluster: str) -> str:
    if not PRO_BASE_URL and not FREE_BASE_URL:
        return BASE_URL
    if request_cluster == LlmRequestCluster.PRO.value and PRO_BASE_URL:
        return PRO_BASE_URL
    elif request_cluster == LlmRequestCluster.FREE.value and FREE_BASE_URL:
        return FREE_BASE_URL
    return BASE_URL


async def get_water_config_by_id(id: int) -> ModelWaterConfig | None:
    water_config = await ModelWaterConfig.filter(id=id).first()
    if not water_config:
        return None
    return water_config


async def get_water_config(
    llm_model: str, to_llm_model: str, enabled: bool = True
) -> ModelWaterConfig | None:
    water_config = await ModelWaterConfig.filter(
        llm_model=llm_model, to_llm_model=to_llm_model, enabled=enabled
    ).first()
    return water_config


async def get_water_config_v1(
    llm_model: str, use_filter: str, to_llm_model: str
) -> ModelWaterConfig | None:
    water_config = await ModelWaterConfig.filter(
        llm_model=llm_model, to_llm_model=to_llm_model, use_filter=use_filter
    ).first()
    return water_config


async def list_water_config_by_model(
    llm_model: str, enabled: bool = True
) -> list[ModelWaterConfig]:
    water_config = await ModelWaterConfig.filter(
        llm_model=llm_model, enabled=enabled
    ).all()
    return water_config


async def list_water_config_by_model_and_use_filter(
    llm_model: str, use_filter: str, enabled: bool = True, sort: bool = True
) -> list[ModelWaterConfig]:
    water_config = await ModelWaterConfig.filter(
        llm_model=llm_model, use_filter=use_filter, enabled=enabled
    ).all()
    if sort:
        water_config.sort(key=lambda x: x.num * 100000 - x.sort_order, reverse=True)
    return water_config


async def list_model_water_config(enabled: bool = True) -> list[ModelWaterConfig]:
    return await ModelWaterConfig.filter(enabled=enabled).all()


async def list_llm_model_config(enabled: bool = True) -> list[LlmModelConfig]:
    return await LlmModelConfig.filter(enabled=enabled).all()


async def get_llm_model_config_by_model(model: str) -> LlmModelConfig | None:
    llm_model_config = await LlmModelConfig.filter(llm_model=model).first()
    return llm_model_config


async def map_llm_model_to_int() -> dict[str, int]:
    llm_model_list = await LlmModelConfig.filter(enabled=True).all()
    ret = {}
    for llm_model in llm_model_list:
        ret[llm_model.llm_model] = llm_model.model_int
    return ret


async def map_int_to_llm_model() -> dict[int, str]:
    llm_model_list = await LlmModelConfig.filter(enabled=True).all()
    ret = {}
    for llm_model in llm_model_list:
        ret[llm_model.model_int] = llm_model.llm_model
    return ret
