from hashlib import md5
import json
import logging
import os
from common.common_constant import Language
from common.role_card import CharacterBook
from common.role_model import ProductResponse
from common.translate_model import (
    TranslateBook,
    TranslateGroup,
    TranslateRole,
    TranslateRoleDescription,
    TranslateSubTag,
    TranslateTaskStatus,
    TranslateTaskType,
)
from persistence.models.models import TranslateResource, TranslateTask
from properties import prop_util
from utils import date_util, file_util, json_util, model_util, translate_util
from utils.exception_util import async_ignore_catch_exception
from tortoise.contrib.pydantic import pydantic_model_creator

log = logging.getLogger(__name__)


async def update_task(task: TranslateTask):
    await task.save()


async def upsert_task(type: str, task_key: str, source: dict, languages: list[str]):
    log.info(f"upsert task type:{type},task_key:{task_key}")
    task = await TranslateTask.filter(type=type, task_key=task_key).first()
    if task is None:
        task = TranslateTask(
            type=type,
            task_key=task_key,
            source=json.dumps(source),
            languages=json.dumps(languages),
            status=TranslateTaskStatus.PENDING.value,
            retry_times=0,
        )
        await task.save()
        return task

    task.status = TranslateTaskStatus.PENDING.value
    task.source = json.dumps(source)
    task.languages = json.dumps(languages)
    task.retry_times = 0
    await task.save()
    return task

async def list_task_keys(task_type:TranslateTaskType):
    tasks =  await TranslateTask.filter(type=task_type.value).only("task_key").all()
    return [task.task_key for task in tasks]


async def get_task(type: str, task_key: str):
    return await TranslateTask.filter(type=type, task_key=task_key).first()

async def get_task_by_id(id: int):
    return await TranslateTask.filter(id=id).first()

async def finish_task(id: int, target: dict):
    return (
        await TranslateTask.select_for_update()
        .filter(id=id, status=TranslateTaskStatus.PROCESSING.value)
        .update(
            status=TranslateTaskStatus.FINISHED.value,
            target=json.dumps(target),
            retry_times=0,
        )
    )


async def reset_task(id: int):
    return (
        await TranslateTask.select_for_update()
        .filter(id=id, status=TranslateTaskStatus.PROCESSING.value)
        .update(status=TranslateTaskStatus.PENDING.value)
    )


async def error_task(id: int):
    return (
        await TranslateTask.select_for_update()
        .filter(id=id, status=TranslateTaskStatus.PROCESSING.value)
        .update(status=TranslateTaskStatus.ERROR.value)
    )


async def list_tasks_by_type(type: str):
    return await TranslateTask.filter(type=type).all()


async def list_tasks(type: str, status: str):
    return await TranslateTask.filter(type=type, status=status).all()


async def list_tasks_by_status(status: str):
    return await TranslateTask.filter(status=status).all()

async def update_status_by_id(id: int, status: str) -> bool:
    return (
        await TranslateTask.select_for_update()
        .filter(id=id)
        .update(status=status)
    ) > 0

async def update_processing(id: int, retry_times: int) -> bool:
    return (
        await TranslateTask.select_for_update()
        .filter(id=id, status=TranslateTaskStatus.PENDING.value)
        .update(
            status=TranslateTaskStatus.PROCESSING.value, retry_times=retry_times + 1
        )
    ) > 0


# async def get_role_task(lang: str = None, role_id: int = None) -> TranslateRole:
#     if lang is None or role_id is None:
#         return None
#     task = await get_task(TranslateTaskType.ROLE.value, str(role_id))
#     if not task:
#         return None
#     if lang not in task.target:
#         return None
#     return TranslateRole(**task.target[lang])


# async def map_all_sub_tag(lang: str = Language.ZH.value) -> dict[str, str]:
#     tasks = await list_tasks_by_type(TranslateTaskType.SUB_TAG.value)
#     if len(tasks) == 0 or lang is None or len(lang) == 0:
#         return {}
#     ret = {}
#     for task in tasks:
#         source = TranslateSubTag(**task.source)
#         if lang in task.target:
#             trans_sub_tag = TranslateSubTag(**task.target.get(lang))
#             ret[source.tag_name] = trans_sub_tag.tag_name
#             continue
#         ret[source.tag_name] = source.tag_name
#     return ret


async def map_role_tasks_by_ids(
    lang: str, role_ids: list[int]
) -> dict[int, TranslateRole]:
    tasks = await TranslateTask.filter(
        type=TranslateTaskType.ROLE.value, task_key__in=[str(x) for x in role_ids]
    ).all()
    ret: dict[int, TranslateRole] = {}
    for task in tasks:
        if lang not in task.target:
            continue
        trans_role = TranslateRole(**task.target[lang])
        ret[int(task.task_key)] = trans_role
    return ret


async def map_group_task_by_ids(
    group_ids: list[int], lang: str
) -> dict[int, TranslateGroup]:
    tasks = await TranslateTask.filter(
        type=TranslateTaskType.GROUP.value, task_key__in=[str(x) for x in group_ids]
    ).all()
    ret = {}
    for task in tasks:
        if lang not in task.target:
            continue
        target = json_util.convert_to_dict(task.target)
        if not target or lang not in target:
            continue
        task_target = target.get(lang, {})
        ret[int(task.task_key)] = TranslateGroup(**task_target)
    return ret


# async def translate_sub_tags(language: str, sub_tags: list[str]):
#     map_sub_tag = await map_all_sub_tag(language)
#     return [map_sub_tag.get(sub_tag, sub_tag) for sub_tag in sub_tags]


# async def map_all_roles(lang: str = Language.ZH.value) -> dict[int, TranslateRole]:
#     tasks: list[TranslateTask] = await TranslateTask.filter(
#         type=TranslateTaskType.ROLE.value
#     ).all()
#     ret = {}
#     for task in tasks:
#         if not task.target or lang not in task.target:
#             continue
#         trans_role = TranslateRole(**task.target[lang])
#         ret[int(task.task_key)] = trans_role

#     return ret


# async def update_sub_tags_by_file():
#     sub_tags: list[str] = prop_util.read_line_sub_tags()
#     source_sub_tag_map = {x[0]: x for x in sub_tags}

#     task_list = await list_tasks_by_type(TranslateTaskType.SUB_TAG.value)
#     for task in task_list:
#         if task.status != TranslateTaskStatus.FINISHED.value:
#             continue
#         source = TranslateSubTag(**task.source)
#         if source.tag_name not in source_sub_tag_map:
#             continue
#         source_sub_tag = source_sub_tag_map[source.tag_name]
#         if len(source_sub_tag) < 3:
#             continue

#         en_source = None
#         tw_source = None
#         if Language.EN.value in task.target:
#             en_source = TranslateSubTag(**task.target[Language.EN.value])
#         if Language.ZH_TW.value in task.target:
#             tw_source = TranslateSubTag(**task.target[Language.ZH_TW.value])
#         if en_source is None or tw_source is None:
#             continue
#         if (
#             en_source.tag_name == source_sub_tag[1]
#             and tw_source.tag_name == source_sub_tag[2]
#         ):
#             continue
#         en_source.tag_name = source_sub_tag[1]
#         tw_source.tag_name = source_sub_tag[2]
#         task.target[Language.EN.value] = en_source.model_dump()
#         task.target[Language.ZH_TW.value] = tw_source.model_dump()
#         log.info(
#             f"update sub tag {source.tag_name} to en:{en_source.tag_name},zh_tw:{tw_source.tag_name}"
#         )
#         await update_task(task)


# async def format_product_res(
#     products: list[ProductResponse], langage: str = Language.ZH.value
# ) -> list[ProductResponse]:
#     for product in products:

#         product.model_name = translate_util.translate_common(
#             product.model_name, langage
#         )
#         product.desc = translate_util.translate_common(product.desc, langage)
#     return products


async def format_character_book(
    character_book: CharacterBook, lang: str = Language.ZH.value
) -> CharacterBook:
    if character_book is None or character_book.entries is None:
        return character_book
    translate_task = await get_task(
        TranslateTaskType.CHAR_BOOK_KEYS.value, str(character_book.book_id)
    )
    if (
        translate_task is None
        or translate_task.target is None
        or translate_task.target == []
        or lang not in translate_task.target
    ):
        return character_book
    target = json_util.convert_to_dict(translate_task.target).get(lang)
    if not target:
        return character_book
    mid_book = TranslateBook(**json_util.convert_to_dict(target))
    for index, entry in enumerate(character_book.entries):
        if index >= len(mid_book.entries):
            break
        mid_entry = mid_book.entries[index]
        if len(mid_entry.keys) != len(entry.keys):
            #     or len(
            #     mid_entry.secondary_keys
            # ) != len(entry.secondary_keys):
            continue
        entry.keys = mid_entry.keys
        # entry.secondary_keys = mid_entry.secondary_keys
    return character_book


async def get_role_description(role_id: int, language: str) -> str:
    translate_task = await get_task(
        TranslateTaskType.ROLE_DESCRIPTION.value, str(role_id)
    )
    if (
        translate_task is None
        or translate_task.target is None
        or translate_task.target == []
        or Language.EN.value not in translate_task.target
    ):
        return ""
    target = json_util.convert_to_dict(translate_task.target).get(Language.EN.value)
    if not target:
        return ""
    target_task = TranslateRoleDescription(**target)
    description = target_task.description
    if language == Language.EN:
        return description
    terms = target_task.parse_terms()
    if not terms:
        return description
    for term in terms.keys():
        term_map = terms.get(term, {})
        zh_term = term_map.get(Language.ZH.value, "")
        zh_tw_term = term_map.get(Language.ZH_TW.value, "")
        en_term = term_map.get(Language.EN.value, "")
        if language == Language.ZH.value and zh_term and en_term:
            description = description.replace(en_term, zh_term)
        if language == Language.ZH_TW.value and zh_tw_term and en_term:
            description = description.replace(en_term, zh_tw_term)
    return description


max_ut_local = 0

@async_ignore_catch_exception
async def refresh_cache():
    tr_pydantic = pydantic_model_creator(TranslateResource)
    queue_set = set(translate_util.uncache_text_queue)
    for category, text in queue_set:
        key = md5(str(text).encode()).hexdigest() if len(text) > 32 else text
        resource = await TranslateResource.filter(
            category=category,
            key=key,
        ).first()
        if not resource:
            resource = TranslateResource(
                category=category,
                key=key,
                text=text,
            )
            await resource.save()
        if not resource:
            continue
        resource_pydantic = await tr_pydantic.from_tortoise_orm(resource)
        resource_dict = resource_pydantic.dict()
        cache_data = {
            x.value: resource_dict.get(x.fetch_field_name(), "")
            for x in Language
        }
        translate_util.add_cache(text, category, cache_data)
    translate_util.uncache_text_queue.clear()
    max_ut = await max_resource_updated_at()
    global max_ut_local
    if max_ut <= max_ut_local:
        return
    filter_updated = date_util.timestamp2datetime(max_ut_local)
    filter_updated = filter_updated.replace(tzinfo=None)
    translate_list = await TranslateResource.filter(
        updated_at__gt=filter_updated,
    ).all()
    for resource in translate_list:
        resource_pydantic = await tr_pydantic.from_tortoise_orm(resource)
        resource_dict = resource_pydantic.dict()
        cache_data = {
            x.value: resource_dict.get(x.fetch_field_name(), "")
            for x in Language
        }
        translate_util.add_cache(resource.key, resource.category, cache_data)
        max_ut = max(max_ut, int(resource.updated_at.timestamp()))
    max_ut_local = max_ut
    ids = [x.id for x in translate_list]
    log.info(f"refresh cache,success,ids: {ids}")


async def max_resource_updated_at():
    resource = await TranslateResource.all().order_by("-updated_at").first()
    if not resource:
        return 0
    return int(resource.updated_at.timestamp())

@async_ignore_catch_exception
async def refresh_all_cache():
    log.info("start refresh all cache")
    max_ut = 0
    translate_list = await TranslateResource.filter().all()
    tr_pydantic = pydantic_model_creator(TranslateResource)
    for resource in translate_list:
        resource_pydantic = await tr_pydantic.from_tortoise_orm(resource)
        resource_dict = resource_pydantic.dict()
        cache_data = {
            x.value: resource_dict.get(x.fetch_field_name(), "")
            for x in Language
        }
        translate_util.add_cache(resource.key, resource.category, cache_data)
        max_ut = max(max_ut, int(resource.updated_at.timestamp()))
    global max_ut_local
    max_ut_local = max_ut
    log.info("refresh all cache,success,refresh count: %s", len(translate_list))

async def refresh_local_resource_to_db():
    log.info("start refresh local resource to db")
    resources = await TranslateResource.filter().all()
    for resource in resources:
        if not resource.key or not resource.category:
            log.warning(f"resource {resource.id} has no key or category, skip")
            continue
        update_dict = {}
        resource_dict = await model_util.model_to_dict(TranslateResource, resource)
        for lang in Language:
            field_name = lang.fetch_field_name()
            trans_text = prop_util.load_translate(resource.key, lang.value)
            original_trans_text = resource_dict.get(field_name, "")
            if trans_text and field_name and original_trans_text != trans_text:
                update_dict[field_name] = trans_text
        if update_dict:
            await TranslateResource.filter(id=resource.id).update(**update_dict)
            log.info(f"update resource {resource.id} with {update_dict}")
    log.info("refresh local resource to db,success")
    return True

