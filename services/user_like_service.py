import logging
from common.common_constant import ChatModeType
from persistence.models.models import LikeStats, UserLikeRecord
from tortoise.expressions import F

log = logging.getLogger(__name__)

# 点赞
async def add_like_role(user_id: int, mode_type: str, mode_target_id: int,count:int):
    record = await UserLikeRecord.filter(
        user_id=user_id, mode_type=mode_type, mode_target_id=mode_target_id
    ).first()
    if record:
        return None
    try:
        return await UserLikeRecord.create(
            user_id=user_id, mode_type=mode_type, mode_target_id=mode_target_id,count=count
        )
    # 捕获uk异常
    except Exception as e:
        log.warning("add_like_role error: %s", e)
        return None

# 动态加赞
async def add_like_count_randomly(mode_type: str, mode_target_id: int, count: int):
    try:
        like_stats = await LikeStats.filter(mode_type=mode_type, mode_target_id=mode_target_id).first()
        if like_stats is None:
            await LikeStats.create(
                mode_type=mode_type, mode_target_id=mode_target_id, like_count=count
            )
        else:
           await LikeStats.filter(mode_type=mode_type, mode_target_id=mode_target_id).update(
            like_count=F('like_count') + count
        )
    except Exception as e:
        log.warning("add_like_count_randomly error: %s", e)

# 获取角色卡的点赞数量
async def get_like_count_by_role_ids(role_ids: list[int]):
    like_stats = await LikeStats.filter(mode_type=ChatModeType.SINGLE.value, mode_target_id__in=role_ids).all()
    return {x.mode_target_id: x.like_count for x in like_stats} 
