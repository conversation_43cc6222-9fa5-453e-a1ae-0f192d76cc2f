import asyncio
from datetime import datetime, timedelta
import logging
import uuid
from common.common_constant import (
    BenefitUsageScopeType,
    GenImagePrivacy,
    ImageAspectRatio,
    ImageQuality,
    ImageRecordStatus,
    ImageStyle,
    ProductType,
)
from persistence.models.models import UserGenerateImageRecord
from services import account_service, product_service, user_service
from services.third import image_server_service
from services.user import user_benefit_service
from utils import date_util, exception_util, request_util, tg_util
from persistence.models.models_bot_image import BotImgGenTaskReview, BotImgGenTask,ImgGenStatus, ImgReviewStatus


async def user_generate_image(
    user_id: int,
    prompt: str,
    image_style: ImageStyle,
    image_quality: ImageQuality,
    image_aspect_ratio: ImageAspectRatio,
    privacy: GenImagePrivacy = GenImagePrivacy.PUBLIC
) -> int:

    record = UserGenerateImageRecord(
        user_id=user_id,
        prompt=prompt,
        image_style=image_style.value,
        image_quality=image_quality.value,
        image_aspect_ratio=image_aspect_ratio.value,
        commit_id=uuid.uuid4().hex,
        status=ImageRecordStatus.SUBMIT.value,
        image_privacy=privacy.value,
        generate_start_time=int(datetime.now().timestamp()),
    )
    await record.save()
    asyncio.create_task(run_generate_image(user_id, record.id, record.commit_id, 1))
    return record.id


async def run_generate_image(
    user_id: int, record_id: int, commit_id: str, retry_count: int = 0
):
    logging.info(
        f"StartRunImage: user_id={user_id}, record_id={record_id}, commit_id={commit_id}, retry_count={retry_count}"
    )
    user = await user_service.get_user_by_id(user_id)
    record = await UserGenerateImageRecord.filter(id=record_id).first()
    if not record:
        raise exception_util.verify_exception(message="记录不存在")
    logging.info(f"RunGenerateImage: record_id={record_id}, commit_id={commit_id}")
    update = (
        await UserGenerateImageRecord.select_for_update()
        .filter(
            id=record_id,
            commit_id=commit_id,
            deleted=False,
            status=ImageRecordStatus.SUBMIT.value,
        )
        .update(
            status=ImageRecordStatus.GENERATING.value,
            retry_count=record.retry_count + 1,
        )
    )
    if not update or not record:
        logging.error(
            f"RunGenerateImage: user_id={user_id}, record_id={record_id} not found or already processed"
        )
        raise exception_util.verify_exception(message="记录不存在")
    image_quality = ImageQuality(record.image_quality)
    image_privacy = GenImagePrivacy(record.image_privacy)
    mid = image_quality.value
    if image_privacy == GenImagePrivacy.PRIVATE:
        mid = f"{mid}_private"
    product = await product_service.get_online_product_by_type_and_mid(
        ProductType.GENERATE_IMAGE, mid
    )
    if not product:
        logging.error(
            f"RunGenerateImage: Product not found for quality user_id={user_id}, record_id={record_id}, mid={mid}"
        )
        raise exception_util.verify_exception(message="未找到对应的产品")
    benefit_count = await user_benefit_service.count_remain_benefit(
        record.user_id, BenefitUsageScopeType.GENERATOR_IMAGE, image_quality.value
    )
    tg_info = await user_service.get_tg_user_by_id(record.user_id)
    resolution = f"{record.image_quality}_{record.image_aspect_ratio}"
    response = await image_server_service.generate_image_request(
        user_id=record.user_id,
        tg_id=tg_info.tg_id if tg_info else 0,
        style=record.image_style,
        prompt=record.prompt,
        resolution=resolution,
        request_id=record.commit_id,
    )
    if not response.success() and retry_count > 0:
        await UserGenerateImageRecord.filter(id=record_id).update(
            status=ImageRecordStatus.SUBMIT.value,
            error_message=response.message,
        )
        await asyncio.sleep(2)  # 等待2秒后重试
        return await run_generate_image(
            user_id=user_id,
            record_id=record_id,
            commit_id=commit_id,
            retry_count=retry_count - 1,
        )
    if not response.success():
        await UserGenerateImageRecord.filter(id=record_id).update(
            status=ImageRecordStatus.FAILED.value, error_message=response.message
        )
        return False
    await UserGenerateImageRecord.filter(id=record_id).update(
        status=ImageRecordStatus.SUCCESS.value,
        image_url=response.image_url,
        generate_end_time=int(datetime.now().timestamp()),
        image_width=response.width if response.width else 0,
        image_height=response.height if response.height else 0,
    )
    if record.image_privacy == GenImagePrivacy.PUBLIC and response.success() and response.image_url:
        logging.info(
            f"Image generated successfully: user_id={user_id}, record_id={record_id}, image_url={response.image_url}, privacy={record.image_privacy}"
        )
        await add_image_to_review_task(
            request_id=response.request_id,
            user_id=record.user_id,
            tg_id=tg_info.tg_id if tg_info else 0,
            prompt=record.prompt,
            nai_prompt=response.nai_prompt if response.nai_prompt else "",
            image_url=response.image_url, # type: ignore
            style= ImageStyle(record.image_style),
            image_aspect_ratio=ImageAspectRatio(record.image_aspect_ratio),
            image_quality=image_quality
        )
    
    if benefit_count > 0:
        deduct_benefit_ret = await user_benefit_service.deduct_benefit(
            user,
            BenefitUsageScopeType.GENERATOR_IMAGE,
            image_quality.value,
        )
        if deduct_benefit_ret:
            return True
    order = await account_service.AccountService.create_pay_order(
        user_id=record.user_id, product=product
    )
    return bool(order)


async def check_generate_image_status(
    user_id: int, record_id: int
) -> UserGenerateImageRecord:
    record = await UserGenerateImageRecord.filter(id=record_id, user_id=user_id).first()
    if not record or record.deleted:
        raise exception_util.verify_exception(message="记录不存在")
    return record


async def user_records(user_id: int, offset: int, limit: int):
    records = (
        await UserGenerateImageRecord.filter(user_id=user_id, deleted=False)
        .order_by("-id")
        .only("id")
        .all()
    )
    ids = [record.id for record in records]
    count = len(ids)
    if offset >= count:
        return count, []
    if offset + limit > count:
        ids = ids[offset:]
    else:
        ids = ids[offset : offset + limit]
    records = await UserGenerateImageRecord.filter(id__in=ids).order_by("-id").all()
    now_time = int(datetime.now().timestamp())
    for record in records:
        if record.status != ImageRecordStatus.GENERATING.value:
            continue
        diff_time = now_time - record.generate_start_time
        if diff_time > 60 * 2:
            # 超过2分钟未完成,则认为任务失败
            await UserGenerateImageRecord.filter(id=record.id).update(
                status=ImageRecordStatus.FAILED.value,
                error_message="任务超时",
                generate_end_time=now_time,
            )
            record.status = ImageRecordStatus.FAILED.value
            record.error_message = "任务超时"
    return count, records


async def delete_user_generate_image_record(user_id: int, record_id: int):
    record = await UserGenerateImageRecord.filter(id=record_id, user_id=user_id).first()
    if not record or record.deleted:
        raise exception_util.verify_exception(message="记录不存在")
    if record.status == ImageRecordStatus.GENERATING.value:
        raise exception_util.verify_exception(
            message="正在生成中的记录不能删除,请稍后再试"
        )
    await UserGenerateImageRecord.filter(id=record_id).update(deleted=True)


async def update_like_status(record_id: int, like_status: int, user_id: int):
    record = await UserGenerateImageRecord.filter(id=record_id, user_id=user_id).first()
    if not record or record.deleted:
        raise exception_util.verify_exception(message="记录不存在")

    await UserGenerateImageRecord.filter(id=record_id).update(like_status=like_status)


async def exist_generating_image(user_id: int) -> bool:
    record = await UserGenerateImageRecord.filter(
        user_id=user_id,
        status=ImageRecordStatus.GENERATING.value,
        deleted=False,
    ).first()
    return bool(record)


@exception_util.async_ignore_catch_exception
async def check_generate_image():
    now_time = date_util.now() - timedelta(minutes=10)
    now_time = now_time.replace(tzinfo=None)
    records = (
        await UserGenerateImageRecord.filter(created_at__gte=now_time)
        .only("id", "status")
        .all()
    )
    sum = len(records)
    error_records = [
        record for record in records if record.status == ImageRecordStatus.FAILED.value
    ]
    if sum <= 2 or not error_records:
        return
    failed_count = len(error_records)
    error_records.sort(key=lambda x: x.created_at, reverse=True)
    last_id = error_records[0].id
    last_record = await UserGenerateImageRecord.filter(id=last_id).first()
    if not last_record:
        return
    if failed_count * 1.0 / sum < 0.1:
        return
    logging.warning(
        f"CheckGenerateImage: {sum} records found, {failed_count} failed, more than 10% failed, need to check"
    )
    data = {
        "title": "图片生成异常",
        "content": f"最近10分钟内有{sum}条生成图片记录，其中{failed_count}条失败，失败率超过10%，请检查相关服务是否正常。",
        "error": last_record.error_message if last_record else "未知错误",
        "error_record_ids": [record.id for record in error_records],
    }
    await tg_util.sm(data)


# 增加小程序生图的图片到审核任务
async def add_image_to_review_task(
    request_id: str,
    user_id: int,
    tg_id: int,
    prompt: str,
    nai_prompt: str,
    image_url: str,
    style: ImageStyle,
    image_aspect_ratio: ImageAspectRatio,
    image_quality: ImageQuality
):
    """
    {"shape": "shape_portrait", "style": "image_style_3", "prompt": "d.va，喷出", "privacy": "private", "resolution": "img_resolution_high", "small_free_gen_str": "", "small_free_gen_count": 0}
    """

    logging.info(
        f"add_image_to_review_task: request_id={request_id}, user_id={user_id}, tg_id={tg_id}, prompt={prompt}, image_url={image_url}"
    )

    if not image_url:
        logging.error("Invalid parameters for add_image_to_review_task,image_url is empty")
        return

    task_id = 0
    req_dict = {
        "req_source_id": request_id,
        "user_id": user_id,
        "tg_id": tg_id,
        "prompt": prompt,
        "image_url": image_url,
        "style": style.to_image_bot_style(),
        "shape": image_aspect_ratio.to_image_bot_aspect_ratio(),
        "resolution": image_quality.to_image_bot_quality(),
        "privacy": "public",
        "req_source": "fancy_little",
    }

    """
    {"msg": "success", "code": 200, "prompt": "mature woman, BBW, curvaceous figure, G-cup enormous breasts exposed, large nipples lactating, black glossy transparent tight pantyhose, visible feet, legs spread in V-shape, legs lifted up, voluptuous buttocks, red crotchless lace panties, explicit vaginal details, clitoris and labia visible, anus exposed, orgasmic squirting, rolling eyes, tears streaming, unfocused gaze, flushed face, being held up, penetrated vagina, intense pleasure expression, intimate close-up, soft lighting, high detail photography", "image_url": "https://s3.ap-southeast-1.amazonaws.com/group-image.424224.xyz/20250811/191896_e39d0960a4144b02b4409c78dc1f05f5.png", "request_id": 191896, "spent_time_s": "8.93s"}"""
    gen_result = {
        "request_id": task_id,
        "req_source_id": request_id,
        "user_id": user_id,
        "tg_id": tg_id,
        "prompt": prompt,
        "image_url": image_url,
        "code": 200,
        "msg": "success",
    }

    gen_task = await BotImgGenTask.create(
        tg_id=tg_id,
        user_id=user_id,
        req_json=req_dict,
        prompt=prompt,
        gen_result=gen_result,
        status=ImgGenStatus.COMPLETED
    )

    await BotImgGenTaskReview.create(
        tg_id=tg_id,
        user_id=user_id,
        task_id=gen_task.id,
        prompt=prompt,
        nai_prompt=nai_prompt,
        image_url=image_url,
        review_status=ImgReviewStatus.INIT,
        tags=["fancy_little"]
    )
