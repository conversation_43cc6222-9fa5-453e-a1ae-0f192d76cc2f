# 记录模型 请求数据
from datetime import datetime, timedelta
import logging
from typing import Optional
from common.models.chat_model import ModelLlmRequestStat
from persistence import redis_client
from services import config_service
from utils import date_util

log = logging.getLogger(__name__)

CACHE_KEY = "chat:model:req:{hour}:{minute}"
CACHE_MAP_KEY = "{model}:{success}"


async def log_model_request(model: str, success: int):
    try:
        now = date_util.utc_now()
        cache_key = CACHE_KEY.format(hour=now.hour, minute=now.minute)
        map_key = CACHE_MAP_KEY.format(model=model, success=success)
        redis_client.redis_client.hincrby(cache_key, map_key, 1)
        redis_client.redis_client.expire(cache_key, 60 * 60)
    except Exception as e:
        log.error(f"log_model_request error:{e}")
        pass


async def map_model_request(hour: int = 0, minute: int = 0)->dict[str, ModelLlmRequestStat]:
    cache_key = CACHE_KEY.format(hour=hour, minute=minute)
    cache_val = redis_client.map_get_all(cache_key)
    llm_configs = await config_service.list_llm_model_config()
    log.info("map_model_request cache_key:%s,cache_val:%s", cache_key, cache_val)
    if not cache_val:
        return {}
    ret = {}
    for model in llm_configs:
        success_key = CACHE_MAP_KEY.format(model=model.llm_model, success=1)
        fail_key = CACHE_MAP_KEY.format(model=model.llm_model, success=0)
        success = cache_val.get(success_key, 0)
        fail = cache_val.get(fail_key, 0)
        ret[model.llm_model] = ModelLlmRequestStat(
            model=model.llm_model,
            success_count=int(success),
            fail_count=int(fail),
            total_count=int(success) + int(fail),
        )

    return ret


async def map_model_request_by_summary(last_minute: int = 0):
    ret = {}

    def add(a: Optional[ModelLlmRequestStat], b: Optional[ModelLlmRequestStat]):
        if not a and not b:
            return ModelLlmRequestStat()
        if not a or not b:
            return a if a else b
        return ModelLlmRequestStat(
            model=a.model,
            success_count=a.success_count + b.success_count,
            fail_count=a.fail_count + b.fail_count,
            total_count=a.total_count + b.total_count,
        )

    now = date_util.utc_now()
    for minute in range(last_minute):
        mid_time = now - timedelta(minutes=minute)
        mid_ret = await map_model_request(mid_time.hour, mid_time.minute)
        if not mid_ret:
            continue
        for model, stat in mid_ret.items():
            ret[model] = add(ret.get(model), stat)
    return ret


async def map_model_request_by_time_range(
    start_time: datetime, end_time: datetime
) -> dict[str, ModelLlmRequestStat]:
    ret = {}

    def add(a: Optional[ModelLlmRequestStat], b: Optional[ModelLlmRequestStat]):
        if not a and not b:
            return ModelLlmRequestStat()
        if not a or not b:
            return a if a else b
        return ModelLlmRequestStat(
            model=a.model,
            success_count=a.success_count + b.success_count,
            fail_count=a.fail_count + b.fail_count,
            total_count=a.total_count + b.total_count,
        )

    while start_time <= end_time:
        mid_ret = await map_model_request(start_time.hour, start_time.minute)
        for model, stat in mid_ret.items():
            ret[model] = add(ret.get(model), stat)
        start_time = start_time + timedelta(minutes=1)
    return ret