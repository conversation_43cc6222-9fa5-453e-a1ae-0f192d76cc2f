from datetime import UTC, datetime, timedelta
import logging
from common.common_constant import ChatModeType
from persistence.models.models import UserDisLikeRecord
from persistence.redis_client import redis_client
from tortoise.functions import Count

log = logging.getLogger(__name__)



# 踩
async def add_dislike_role(user_id: int, mode_type: str, mode_target_id: int):
    current_count = _get_current_dislike_count(user_id)
    if current_count > 5:
        return None, "超出今天的踩的数量限制"
    record = await UserDisLikeRecord.filter(
        user_id=user_id, mode_type=mode_type, mode_target_id=mode_target_id
    ).first()
    if record:
        return None, "重复操作"
    try:
        result = await UserDisLikeRecord.create(
            user_id=user_id, mode_type=mode_type, mode_target_id=mode_target_id
        )
        _increase_dislike_count(user_id)
        return result, ""
    # 捕获uk异常
    except Exception as e:
        log.warning("add_dislike_role error: %s", e)
        return None, "操作失败"

def _increase_dislike_count(user_id: int) -> int:
    today = datetime.now(UTC) + timedelta(hours=8)
    today_date = today.strftime("%Y-%m-%d")
    redis_key = f"user:{user_id}:dislike_count:{today_date}"
    count = redis_client.incr(redis_key)
    # expire in 24 hours
    redis_client.expire(redis_key, 3600 * 24)
    return int(str(count))

def _get_current_dislike_count(user_id: int) -> int:
    today = datetime.now(UTC) + timedelta(hours=8)
    today_date = today.strftime("%Y-%m-%d")
    redis_key = f"user:{user_id}:dislike_count:{today_date}"
    count = redis_client.get(redis_key)
    if count is not None:
        # 将字节字符串解码为普通字符串，然后转换为整数
        count = int(count.decode("utf-8"))
    else:
        count = 0
    return count


# 获取角色卡的踩的数量
async def get_dislike_count_by_role_id(role_id: int):
    count = await UserDisLikeRecord.filter(mode_type=ChatModeType.SINGLE.value, mode_target_id=role_id).count()
    return count

async def get_dislike_count_by_role_ids(role_ids: list[int]):
    dislike_stats = await UserDisLikeRecord.filter(mode_type=ChatModeType.SINGLE.value, mode_target_id__in=role_ids).annotate(count=Count('id')).group_by('mode_target_id').values('mode_target_id', 'count')
    log.info(f"get_like_count_by_role_ids: {dislike_stats}")
    return {x['mode_target_id']: x['count'] for x in dislike_stats}
