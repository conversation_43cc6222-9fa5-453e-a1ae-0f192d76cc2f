import logging
from datetime import UTC, datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass

from persistence.models.models import (
    RechargeOrder, 
    RechargeChannelStats, 
    RechargeChannelControl,
    RechargeChannelAlert,
    RechargeStatusEnum,
)
from utils import tg_util

logger = logging.getLogger(__name__)

monitor_chat_id = tg_util.channel_monitor_chat_id

@dataclass
class ChannelPerformanceData:
    channel: str
    pay_type: str
    success_rate: float
    total_orders: int
    success_orders: int
    time_window_orders: int
    is_newbie_protected: bool
    current_weight: int


@dataclass
class MonitoringResult:
    channels_processed: int
    channels_adjusted: int
    alerts_created: int
    errors: List[str]


class ChannelMonitorService:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.decision_logs = []  # 收集监控决策过程中的日志信息
        
    async def run_monitoring_cycle(self) -> MonitoringResult:
        self.decision_logs = []  # 清空上次的决策日志
        self.logger.info("开始执行渠道监控周期")
        self.decision_logs.append("🔍 开始执行渠道监控周期")
        
        result = MonitoringResult(
            channels_processed=0,
            channels_adjusted=0, 
            alerts_created=0,
            errors=[]
        )
        
        try:
            # 按新策略：先获取所有支付类型，然后逐个处理
            pay_types = await self._get_all_pay_types()
            self.decision_logs.append(f"📊 获取到 {len(pay_types)} 个支付类型: {', '.join(pay_types)}")
            
            for pay_type in pay_types:
                try:
                    self.logger.info(f"处理支付类型: {pay_type}")
                    self.decision_logs.append(f"\n📈 开始处理支付类型: {pay_type}")
                    
                    # 获取该支付类型下的所有活跃渠道
                    channels_data = []
                    active_channels = await self._get_active_channels_by_pay_type(pay_type)
                    self.decision_logs.append(f"  📋 发现 {len(active_channels)} 个活跃渠道: {', '.join(active_channels)}")
                    
                    for channel in active_channels:
                        performance_data = await self._calculate_time_window_success_rate(channel, pay_type)
                        if performance_data:
                            # 检查新手保护期和最小订单量
                            if performance_data.is_newbie_protected and performance_data.total_orders < 7:
                                self.decision_logs.append(f"    ⚠️ {channel}: 新手保护期且订单量<7，跳过")
                                continue
                            if not await self._check_minimum_orders(performance_data):
                                self.decision_logs.append(f"    ⚠️ {channel}: 订单量不足，创建低量告警")
                                await self._create_low_volume_alert(performance_data, result)
                                continue
                                
                            self.decision_logs.append(f"    ✅ {channel}: 成功率 {performance_data.success_rate:.2%}, 订单量 {performance_data.total_orders}, 权重 {performance_data.current_weight}%")
                            channels_data.append(performance_data)
                            await self._update_channel_stats(performance_data)
                            result.channels_processed += 1
                    
                    # 对该支付类型的所有渠道进行整体处理
                    if channels_data:
                        await self._process_channels_by_pay_type(channels_data, result)
                        
                except Exception as e:
                    error_msg = f"处理支付类型 {pay_type} 时出错: {str(e)}"
                    self.logger.exception(error_msg, stack_info=True)
                    result.errors.append(error_msg)
                    
        except Exception as e:
            error_msg = f"监控周期执行失败: {str(e)}"
            self.logger.exception(error_msg, stack_info=True)
            result.errors.append(error_msg)
            
        self.logger.info(f"监控周期完成: 处理 {result.channels_processed} 个渠道, "
                        f"调整 {result.channels_adjusted} 个, 创建 {result.alerts_created} 个告警")
        
        # 发送监控决策日志通知
        if self.decision_logs:
            await self._send_monitoring_decision_logs(result)
        
        return result
    
    async def _get_active_channels(self) -> List[Tuple[str, str]]:
        controls = await RechargeChannelControl.filter(enabled=True).all()
        return [(control.channel.value, control.pay_type) for control in controls]
        
    async def _get_all_pay_types(self) -> List[str]:
        return ['wechat', 'alipay']

    async def _get_active_channels_by_pay_type(self, pay_type: str) -> List[str]:
        controls = await RechargeChannelControl.filter(enabled=True, pay_type=pay_type, ratio__gt=0).all()
        return [control.channel.value for control in controls]
    
    async def _process_channel(self, channel: str, pay_type: str, result: MonitoringResult):
        self.logger.info(f"处理渠道: {channel}-{pay_type}")
        
        # 1. 计算时间窗口成功率
        performance_data = await self._calculate_time_window_success_rate(channel, pay_type)
        
        if not performance_data:
            self.logger.warning(f"渠道 {channel}-{pay_type} 无性能数据")
            return
            
        # 2. 检查新手保护期（3小时内才生效的渠道）
        if performance_data.is_newbie_protected:
            # 新手保护期的渠道需要等7单后才参与计算
            if performance_data.total_orders < 7:
                self.logger.info(f"渠道 {channel}-{pay_type} 处于新手保护期，且订单量<7，跳过调整")
                return
            
        # 3. 检查最小订单量要求
        if not await self._check_minimum_orders(performance_data):
            await self._create_low_volume_alert(performance_data, result)
            return
            
        # 4. 检查FAILED订单并单独报警
        await self._check_failed_orders_alert(performance_data, result)
        
        # 5. 更新统计信息
        await self._update_channel_stats(performance_data)
        
        # 6. 根据成功率调整权重
        await self._adjust_channel_weight(performance_data, result)
    
    async def _calculate_time_window_success_rate(self, channel: str, pay_type: str) -> Optional[ChannelPerformanceData]:
        """计算渠道在时间窗口内的成功率（按新策略）"""
        now = datetime.now(UTC)
        
        # 获取渠道配置检查新手保护
        stats, _ = await RechargeChannelStats.get_or_create(
            channel=channel, pay_type=pay_type,
            defaults={
                'total_orders': 0,
                'success_orders': 0,
                'success_rate': 0.0,
                'current_weight': 50  # 默认权重
            }
        )
        
        # 检查新手保护期（最近3小时内才生效的渠道）
        is_newbie_protected = False
        if stats.newbie_protection_until:
            is_newbie_protected = now < stats.newbie_protection_until
        elif stats.created_at and (now - stats.created_at) < timedelta(hours=3):
            is_newbie_protected = True
            # 设置新手保护期到创建后3小时
            stats.newbie_protection_until = stats.created_at + timedelta(hours=3)
            await stats.save()
        
        # 按新策略：以半小时为单位，划分过去3个小时，共6个时间单位
        total_orders = 0
        success_orders = 0
        
        # 逐步扩展时间窗口直到满足10单或取满6个时间单位且有5单
        for time_units in range(1, 7):  # 1到6个时间单位（30分钟、1小时、1.5小时...3小时）
            # 计算时间范围
            start_time = now - timedelta(minutes=30 * time_units)
            end_time = now
            
            # 获取这个时间范围内的订单
            window_orders = await self._get_window_orders(channel, pay_type, start_time, end_time)
            
            total_orders = len(window_orders)
            success_orders = sum(1 for order in window_orders if order.status == RechargeStatusEnum.SUCCEED)
            
            self.logger.info(f"渠道 {channel}-{pay_type} 时间单位{time_units}({time_units*30}分钟): {total_orders}单, 成功{success_orders}单")
            
            # 策略要求：满足10单则不再取更多时间单位
            if total_orders >= 10:
                self.logger.info(f"渠道 {channel}-{pay_type} 达到10单，停止扩展时间窗口")
                break
                
            # 如果已经取满6个时间单位且有至少5单，可以进行计算
            if time_units == 6 and total_orders >= 5:
                self.logger.info(f"渠道 {channel}-{pay_type} 取满6个时间单位且>=5单，开始计算")
                break
                
            # 如果取满6个时间单位但不足5单，需要报警（在调用处处理）
            if time_units == 6 and total_orders < 5:
                self.logger.warning(f"渠道 {channel}-{pay_type} 3小时内仅{total_orders}单，低于5单要求")
                break
        
        # 计算成功率
        success_rate = success_orders / total_orders if total_orders > 0 else 0.0
        
        return ChannelPerformanceData(
            channel=channel,
            pay_type=pay_type,
            success_rate=success_rate,
            total_orders=total_orders,
            success_orders=success_orders,
            time_window_orders=total_orders,
            is_newbie_protected=is_newbie_protected,
            current_weight=stats.current_weight or 50
        )
    
    async def _get_window_orders(self, channel: str, pay_type: str, start_time: datetime, end_time: datetime) -> List[RechargeOrder]:
        query = RechargeOrder.filter(
            recharge_channel=channel,
            created_at__gte=start_time.replace(tzinfo=None),
            created_at__lt=end_time.replace(tzinfo=None)
        )
        orders = await query.order_by('created_at').all()

        # 按用户分组应用排除规则
        filtered_orders = []
        
        # 按用户ID分组订单
        orders_by_user = {}
        for order in orders:
            user_id = order.user_id
            if user_id not in orders_by_user:
                orders_by_user[user_id] = []
            orders_by_user[user_id].append(order)
        
        # 对每个用户的订单单独应用排除规则
        for user_id, user_orders in orders_by_user.items():
            user_success_orders = [order for order in user_orders if order.status == RechargeStatusEnum.SUCCEED]
            
            # 按时间排序的成功订单，用于规则1b
            user_success_orders_by_time = sorted(user_success_orders, key=lambda x: x.created_at)
            
            user_excluded_combinations = set()  # 记录该用户已排除的组合(支付方式+金额)
            user_excluded_before_success = set()  # 记录该用户成功订单前需排除的不同金额订单
            
            # 先处理该用户的成功订单，确定排除规则
            for success_order in user_success_orders_by_time:
                # 规则1a: 同用户、同支付方式、同金额的后续订单需排除
                combination_key = (success_order.pay_type, str(success_order.amount))
                user_excluded_combinations.add(combination_key)
                
                # 规则1b: 该用户成功订单前的不同金额订单需排除
                for prior_order in user_orders:
                    if (prior_order.created_at < success_order.created_at and 
                        prior_order.pay_type == success_order.pay_type and
                        str(prior_order.amount) != str(success_order.amount)):
                        user_excluded_before_success.add(prior_order.id)
            
            # 应用排除规则过滤该用户的订单
            for order in user_orders:
                should_exclude = False
                
                # 排除规则1a: 支付成功后，同用户、同支付方式、同金额的后续订单（保留第一个成功的）
                if order.status == RechargeStatusEnum.SUCCEED:
                    combination_key = (order.pay_type, str(order.amount))
                    # 检查是否是该用户该组合的第一个成功订单
                    first_success_order = next(
                        (o for o in user_success_orders_by_time 
                         if o.pay_type == order.pay_type and str(o.amount) == str(order.amount)), 
                        None
                    )
                    if first_success_order and order.id != first_success_order.id:
                        should_exclude = True
                
                # 排除规则1b: 该用户支付成功的订单前，不同金额的支付订单
                if order.id in user_excluded_before_success:
                    should_exclude = True
                
                if not should_exclude:
                    filtered_orders.append(order)
        
        return filtered_orders
    
    async def _check_minimum_orders(self, performance_data: ChannelPerformanceData) -> bool:
        # 新手保护期需要7单，其他情况至少5单
        min_orders = 7 if performance_data.is_newbie_protected else 5
        return performance_data.total_orders >= min_orders
    
    async def _update_channel_stats(self, performance_data: ChannelPerformanceData):
        stats, _ = await RechargeChannelStats.get_or_create(
            channel=performance_data.channel,
            pay_type=performance_data.pay_type
        )
        
        stats.time_window_success_rate = performance_data.success_rate
        stats.window_total_orders = performance_data.total_orders
        stats.window_success_orders = performance_data.success_orders
        stats.last_calculation_at = datetime.now(UTC)
        
        await stats.save()
    
    async def _adjust_channel_weight(self, performance_data: ChannelPerformanceData, result: MonitoringResult):
        # 获取同支付方式下的所有渠道
        same_type_channels = await self._get_same_pay_type_channels(performance_data.pay_type)
        
        # 策略5: 在相同的支付方式下进行处理
        await self._process_channels_by_pay_type(same_type_channels, result)
    
    async def _process_channels_by_pay_type(self, channels: List[ChannelPerformanceData], result: MonitoringResult):
        if not channels:
            return
            
        pay_type = channels[0].pay_type
        self.logger.info(f"处理支付类型 {pay_type} 的 {len(channels)} 个渠道")
        self.decision_logs.append(f"\n📊 处理支付类型 {pay_type} 的 {len(channels)} 个渠道:")
        for ch in channels:
            self.decision_logs.append(f"   - {ch.channel}: 成功率 {ch.success_rate:.2%}, 权重 {ch.current_weight}%, 订单量 {ch.total_orders}")
        
        # 策略4: 检测成功率最低要求（45%紧急模式）
        all_channels_below_45 = all(ch.success_rate < 0.45 for ch in channels)
        if all_channels_below_45:
            self.decision_logs.append(f"  🚨 紧急情况：所有渠道成功率都低于45%，启用紧急模式")
            await self._handle_all_channels_below_45_emergency(channels, result)
            return
        
        # 策略5: 处理与最高成功率差距大的渠道（>=25%）
        self.decision_logs.append(f"  🔍 检查与最高成功率差距>=25%的渠道")
        await self._handle_large_success_rate_gap(channels, result)
        
        # 重新获取仍然活跃的渠道
        active_channels = [ch for ch in channels if ch.current_weight > 0]
        if len(active_channels) <= 1:
            return
        
        # 策略6: 处理成功率低于35%的渠道  
        channels_below_35 = [ch for ch in active_channels if ch.success_rate < 0.35]
        remaining_channels = [ch for ch in active_channels if ch.success_rate >= 0.35]
        
        if channels_below_35:
            self.decision_logs.append(f"  🔄 将 {len(channels_below_35)} 个成功率<35%的渠道权重设为5%:")
            for ch in channels_below_35:
                self.decision_logs.append(f"     - {ch.channel}: {ch.success_rate:.2%}")
        
        for channel in channels_below_35:
            await self._set_channel_weight(channel, 5, "成功率低于35%", "WARNING", result)
        
        # 检查是否仅剩一个渠道
        if len(remaining_channels) == 1:
            self.decision_logs.append(f"  🚨 仅剩一个35%以上渠道: {remaining_channels[0].channel}")
            await self._handle_only_one_channel_remaining(remaining_channels[0], "仅剩1个35%以上渠道", "EMERGENCY", result)
            return
        
        # 策略7: 处理成功率低于45%但高于35%的渠道
        channels_35_to_45 = [ch for ch in remaining_channels if ch.success_rate < 0.45]
        channels_above_45 = [ch for ch in remaining_channels if ch.success_rate >= 0.45]
        
        if channels_above_45:  # 还有高于45%的渠道
            if channels_35_to_45:
                self.decision_logs.append(f"  🔄 将 {len(channels_35_to_45)} 个35-45%范围渠道权重设为10%:")
                for ch in channels_35_to_45:
                    self.decision_logs.append(f"     - {ch.channel}: {ch.success_rate:.2%}")
            
            for channel in channels_35_to_45:
                await self._set_channel_weight(channel, 10, "成功率低于45%", "INFO", result)
            
            remaining_channels = channels_above_45
            
            # 检查是否仅剩一个渠道
            if len(remaining_channels) == 1:
                self.decision_logs.append(f"  🚨 仅剩一个45%以上渠道: {remaining_channels[0].channel}")
                await self._handle_only_one_channel_remaining(remaining_channels[0], "仅剩1个45%以上渠道", "EMERGENCY", result)
                return
        
        # 策略8: 处理剩余的渠道权重分配
        if remaining_channels:
            self.decision_logs.append(f"  📊 处理剩余 {len(remaining_channels)} 个渠道的权重分配")
        await self._distribute_remaining_weights(remaining_channels, result)
    
    async def _handle_all_channels_below_45_emergency(self, channels: List[ChannelPerformanceData], result: MonitoringResult):
        """处理所有渠道都低于45%的紧急情况"""
        # 选择成功率最高的渠道
        highest_channel = max(channels, key=lambda x: x.success_rate)
        
        # 将最高成功率渠道设为100%权重
        await self._set_channel_weight(
            highest_channel, 100, 
            f"所有渠道低于45%紧急模式，成功率最高({highest_channel.success_rate:.2%})", 
            "EMERGENCY", result
        )
        
        # 其他渠道设为0权重
        for channel in channels:
            if channel.channel != highest_channel.channel:
                await self._set_channel_weight(channel, 0, "所有渠道低于45%紧急模式", "EMERGENCY", result)
        
        # 创建紧急告警（尝试电话报警）
        await self._create_emergency_phone_alert(
            channels[0].pay_type,
            "ALL_CHANNELS_BELOW_45_PERCENT",
            f"支付类型 {channels[0].pay_type} 所有渠道成功率都低于45%，已启用紧急模式"
        )
        
        self.logger.warning(f"支付类型 {channels[0].pay_type} 所有渠道成功率都低于45%，启用紧急模式")
    
    async def _handle_large_success_rate_gap(self, channels: List[ChannelPerformanceData], result: MonitoringResult):
        """处理与最高成功率差距>=25%的渠道"""
        if len(channels) < 2:
            return
            
        # 按成功率排序
        sorted_channels = sorted(channels, key=lambda x: x.success_rate, reverse=True)
        highest_channel = sorted_channels[0]
        highest_rate = highest_channel.success_rate
        
        # 找出差距>=25%的渠道
        gap_25_percent_channels = [
            ch for ch in channels 
            if ch.channel != highest_channel.channel and (highest_rate - ch.success_rate) >= 0.25
        ]
        
        if not gap_25_percent_channels:
            return
            
        # 将差距>=25%的渠道权重设为5%
        for channel in gap_25_percent_channels:
            await self._set_channel_weight(channel, 5, f"与最高成功率差距 {highest_rate - channel.success_rate:.2%} >=25%", "WARNING", result)
        
        # 检查处理后是否所有其他渠道都与最高成功率差>=25%
        other_channels = [ch for ch in channels if ch.channel != highest_channel.channel]
        remaining_active_channels = [ch for ch in other_channels if ch.current_weight > 5]  # 排除已设为5%的
        
        if not remaining_active_channels:
            # 仅剩最高成功率渠道，剩余权重都给它
            total_low_weight = len(gap_25_percent_channels) * 5
            remaining_weight = 100 - total_low_weight
            
            await self._set_channel_weight(
                highest_channel, remaining_weight,
                "成功率差距大且仅剩1个有效渠道", "EMERGENCY", result
            )
            
            # 创建紧急告警（尝试电话报警）
            await self._create_emergency_phone_alert(
                channels[0].pay_type,
                "SUCCESS_RATE_GAP_LARGE_ONLY_ONE_REMAINING",
                f"支付类型 {channels[0].pay_type} 成功率差距大且仅剩1个有效渠道"
            )
    
    async def _handle_only_one_channel_remaining(self, channel: ChannelPerformanceData, reason: str, alert_level: str, result: MonitoringResult):
        """处理仅剩一个渠道的情况"""
        await self._set_channel_weight(channel, 100, reason, alert_level, result)
    
    async def _distribute_remaining_weights(self, channels: List[ChannelPerformanceData], result: MonitoringResult):
        """策略8: 按步骤执行处理剩余的支付渠道"""
        if len(channels) < 2:
            return
        
        # 按成功率排序
        sorted_channels = sorted(channels, key=lambda x: x.success_rate, reverse=True)
        highest = sorted_channels[0]
        second_highest = sorted_channels[1] if len(sorted_channels) > 1 else None
        
        if not second_highest:
            return
            
        # 计算成功率差距
        rate_diff = highest.success_rate - second_highest.success_rate
        
        # 8a. 如果最高成功率与第二成功率相差15%及以上
        if rate_diff >= 0.15:
            await self._set_channel_weight(highest, 60, f"最高成功率相差>=15% ({rate_diff:.2%})", "INFO", result)
            
            # 剩余的第2、3、4……等渠道均分剩余权重
            remaining_weight = 40
            other_channels = sorted_channels[1:]
            if other_channels:
                weight_per_channel = remaining_weight // len(other_channels)
                for channel in other_channels:
                    await self._set_channel_weight(channel, weight_per_channel, "均分剩余权重", "INFO", result)
            return  # 终止后续处理
        
        # 8b. 处理与最高成功率相差15%以上的渠道
        gap_15_percent_channels = [
            ch for ch in sorted_channels[1:] 
            if (highest.success_rate - ch.success_rate) >= 0.15
        ]
        
        for channel in gap_15_percent_channels:
            await self._set_channel_weight(channel, 10, f"与最高成功率相差>=15% ({highest.success_rate - channel.success_rate:.2%})", "INFO", result)
        
        # 重新计算剩余渠道（排除已设为10%的）
        remaining_channels = [ch for ch in sorted_channels if ch not in gap_15_percent_channels]
        
        if len(remaining_channels) < 2:
            return
            
        # 重新计算第二成功率（排除已处理的渠道）
        second_highest = remaining_channels[1] if len(remaining_channels) > 1 else None
        if not second_highest:
            return
            
        rate_diff = highest.success_rate - second_highest.success_rate
        
        # 8c. 如果最高成功率与第二成功率相差10%以上
        if rate_diff >= 0.10:
            await self._set_channel_weight(highest, 50, f"最高成功率相差>=10% ({rate_diff:.2%})", "INFO", result)
            
            # 剩余的第2、3、4……等渠道均分剩余权重（扣除已设为10%的权重）
            fixed_10_percent_weight = len(gap_15_percent_channels) * 10
            available_weight = 50 - fixed_10_percent_weight
            other_channels = remaining_channels[1:]
            
            if other_channels and available_weight > 0:
                weight_per_channel = available_weight // len(other_channels)
                for channel in other_channels:
                    await self._set_channel_weight(channel, weight_per_channel, "均分剩余权重", "INFO", result)
            return  # 终止后续处理
        
        # 8d. 处理与最高成功率相差10%以上的渠道
        gap_10_percent_channels = [
            ch for ch in remaining_channels[1:] 
            if (highest.success_rate - ch.success_rate) >= 0.10
        ]
        
        for channel in gap_10_percent_channels:
            await self._set_channel_weight(channel, 15, f"与最高成功率相差>=10% ({highest.success_rate - channel.success_rate:.2%})", "INFO", result)
        
        # 最终剩余渠道均分剩余权重
        final_remaining_channels = [ch for ch in remaining_channels if ch not in gap_10_percent_channels]
        
        if final_remaining_channels:
            # 计算已分配的权重
            fixed_weight_total = len(gap_15_percent_channels) * 10 + len(gap_10_percent_channels) * 15
            available_weight = 100 - fixed_weight_total
            
            if available_weight > 0:
                weight_per_channel = available_weight // len(final_remaining_channels)
                for channel in final_remaining_channels:
                    await self._set_channel_weight(channel, weight_per_channel, "最终均分剩余权重", "INFO", result)
    
    async def _set_channel_weight(self, channel: ChannelPerformanceData, new_weight: int, reason: str, alert_level: str, result: MonitoringResult):
        """设置渠道权重并创建告警"""
        if new_weight != channel.current_weight:
            await self._update_channel_weight_in_db(channel.channel, channel.pay_type, new_weight)
            result.channels_adjusted += 1
            # tg_util.sm(channel.channel, new_weight)
            # 创建告警
            if alert_level in ["WARNING", "EMERGENCY"]:
                await self._create_alert(
                    channel,
                    "WEIGHT_ADJUSTED",
                    alert_level,
                    f"{reason}: 权重调整为{new_weight}%（成功率{channel.success_rate:.2%}）"
                )
                result.alerts_created += 1
    
    async def _get_same_pay_type_channels(self, pay_type: str) -> List[ChannelPerformanceData]:
        stats = await RechargeChannelStats.filter(pay_type=pay_type).all()
        
        channels = []
        for stat in stats:
            channels.append(ChannelPerformanceData(
                channel=stat.channel.value,
                pay_type=stat.pay_type,
                success_rate=stat.time_window_success_rate,
                total_orders=stat.window_total_orders,
                success_orders=stat.window_success_orders,
                time_window_orders=stat.window_total_orders,
                is_newbie_protected=False,
                current_weight=stat.current_weight
            ))
        
        return channels
    
    async def _update_channel_weight_in_db(self, channel: str, pay_type: str, new_weight: int):
        # 更新统计表
        await RechargeChannelStats.filter(
            channel=channel, pay_type=pay_type
        ).update(
            current_weight=new_weight,
            auto_adjusted_weight=new_weight
        )
        
        # 更新控制表
        # await RechargeChannelControl.filter(
        #     channel=channel, pay_type=pay_type
        # ).update(ratio=new_weight)
        
        self.logger.info(f"更新渠道权重: {channel}-{pay_type} -> {new_weight}%")
    
    async def _create_low_volume_alert(self, performance_data: ChannelPerformanceData, result: MonitoringResult):
        """创建低订单量告警"""
        min_orders = 7 if performance_data.is_newbie_protected else 5
        await self._create_alert(
            performance_data,
            "LOW_VOLUME",
            "WARNING", 
            f"3小时及6个时间单位内订单量过低: {performance_data.total_orders}单，低于{min_orders}单最低要求"
        )
        result.alerts_created += 1
    
    async def _create_alert(self, performance_data: ChannelPerformanceData, alert_type: str, 
                          alert_level: str, message: str):
        """创建告警记录"""
        try:
            await RechargeChannelAlert.create(
                channel=performance_data.channel,
                pay_type=performance_data.pay_type,
                alert_type=alert_type,
                alert_level=alert_level,
                success_rate=performance_data.success_rate,
                order_count=performance_data.total_orders,
                message=message,
                phone_notified=alert_level == "EMERGENCY"  # 紧急情况需要电话通知
            )
            
            self.logger.warning(f"创建告警 [{alert_level}] {performance_data.channel}-{performance_data.pay_type}: {message}")
            
            # TODO: 实现电话/短信通知逻辑
            if alert_level == "EMERGENCY":
                await self._send_emergency_notification(performance_data, message)
                
        except Exception as e:
            self.logger.error(f"创建告警失败: {str(e)}")
    
    async def _create_emergency_phone_alert(self, pay_type: str, alert_type: str, message: str):
        """创建紧急电话告警"""
        try:
            self.logger.warning(f"创建紧急电话告警 [{alert_type}] {pay_type}: {message}")
            await self._send_emergency_phone_notification(pay_type, message)
        except Exception as e:
            self.logger.error(f"创建紧急电话告警失败: {str(e)}")
    
    async def _send_emergency_phone_notification(self, pay_type: str, message: str):
        self.logger.warning(f"【紧急电话通知】支付类型 {pay_type}: {message}")
        pass

    async def _check_failed_orders_alert(self, performance_data: ChannelPerformanceData, result: MonitoringResult):
        try:
            # 获取最近30分钟的FAILED订单
            now = datetime.now(UTC)
            start_time = now - timedelta(minutes=30)
            
            failed_orders = await RechargeOrder.filter(
                recharge_channel=performance_data.channel,
                pay_type=performance_data.pay_type,
                status=RechargeStatusEnum.FAILED,
                created_at__gte=start_time.replace(tzinfo=UTC),
            ).count()
            
            # 如果FAILED订单过多，创建告警
            if failed_orders >= 5:  # 30分钟内>=5个FAILED订单
                await self._create_alert(
                    performance_data,
                    "HIGH_FAILED_ORDERS",
                    "WARNING",
                    f"最近30分钟内有{failed_orders}个FAILED订单，需要检查渠道状态"
                )
                result.alerts_created += 1
                
        except Exception as e:
            self.logger.exception(f"检查FAILED订单时出错: {str(e)}", stack_info=True)
    
    async def _send_emergency_notification(self, performance_data: ChannelPerformanceData, message: str):
        self.logger.warning(f"紧急通知: {performance_data.channel}-{performance_data.pay_type} {message}")
        pass

    async def _send_monitoring_decision_logs(self, result: MonitoringResult):
        try:
            messages = []
            
            # 添加决策过程日志
            if self.decision_logs:
                decision_summary = '\n'.join(self.decision_logs)
                messages.append(f"🔍 监控决策过程:\n{decision_summary}")
                messages.append("\n" + "="*50 + "\n")
            
            # 添加最终统计结果
            messages.append(f"📊 监控周期统计:")
            messages.append(f"  - 处理渠道数: {result.channels_processed}")
            messages.append(f"  - 调整渠道数: {result.channels_adjusted}")
            messages.append(f"  - 创建告警数: {result.alerts_created}")
            if result.errors:
                messages.append(f"  - 错误数量: {len(result.errors)}")
                messages.append("⚠️ 错误详情:")
                for error in result.errors:
                    messages.append(f"    - {error}")

            content = '\n'.join(messages)
            # 如果内容过长，分割为多个消息发送
            max_length = 4000
            if len(content) <= max_length:
                await tg_util.sm({
                    "title": "渠道监控决策日志",
                    "内容": content
                }, chat_id=monitor_chat_id)
            else:
                # 分割内容为多个消息
                chunks = []
                current_chunk = ""
                
                for line in content.split('\n'):
                    if len(current_chunk + line + '\n') > max_length:
                        if current_chunk:
                            chunks.append(current_chunk.rstrip())
                            current_chunk = line + '\n'
                        else:
                            # 单行就超过限制，强制分割
                            chunks.append(line[:max_length])
                            current_chunk = line[max_length:] + '\n'
                    else:
                        current_chunk += line + '\n'
                
                if current_chunk:
                    chunks.append(current_chunk.rstrip())
                
                # 发送多个消息
                for i, chunk in enumerate(chunks):
                    title = f"渠道监控决策日志 ({i+1}/{len(chunks)})"
                    await tg_util.sm({
                        "title": title,
                        "内容": chunk
                    }, chat_id=monitor_chat_id)

            await tg_util.sm({
                "title": "渠道监控决策日志",
                "内容": content
            }, chat_id=monitor_chat_id)
            
        except Exception as e:
            self.logger.error(f"发送监控决策日志失败: {str(e)}")

channel_monitor_service = ChannelMonitorService()