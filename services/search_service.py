import logging
import os

from pydantic import BaseModel
import requests

from common.common_constant import Language
from utils import request_util


log = logging.getLogger(__name__)

_SEARCH_API = os.getenv("SEARCH_API", "")


async def list_roles(
    nsfw: bool, keyword: str, offset: int, limit: int, current_language: str
):
    post_data = {
        "q": keyword,
        "from": offset,
        "size": limit,
        "lang": current_language.lower().replace("-", "_"),
    }
    if not nsfw:
        post_data["nsfw"] = 0
    ret = await request_util.post_json(_SEARCH_API, post_data)
    # res = requests.post(_SEARCH_API, json=post_data)
    # ret = res.json()
    if not ret:
        return 0, []
    total = ret.get("total", 0)
    ids = []
    if "taverns" in ret:
        ids = [x["id"] for x in ret["taverns"]]
    log.info(f"list_roles: {post_data},res:{ids}")
    return total, ids
