from datetime import datetime, timedelta, UTC
from tortoise.transactions import in_transaction
from persistence.models.models import (
    ExpirableAward,
    ExpirableStatusEnum,
    RechargeChannelEnum,
    RechargeOrder,
    RechargeProduct,
    RechargeStatusEnum,
    USDTRechargeOrder,
)
from services import gift_award_service
from utils import user_growth_constants

async def get_usdt_order(order_id: int) -> USDTRechargeOrder | None:
    order = await USDTRechargeOrder.filter(id=order_id).first()
    return order

async def get_usdt_order_by_tx_id(tx_id: str) -> USDTRechargeOrder | None:
    return await USDTRechargeOrder.filter(tx_id=tx_id).first()

async def get_usdt_orders_by_user_id(user_id: int) -> list[USDTRechargeOrder]:
    return await USDTRechargeOrder.filter(user_id=user_id).order_by('-id').limit(100).all()

async def reconcile_usdt_order(usdt_order_id: int, network: str, tx_id: str,
                               payment_address: str, fc_reward: bool) -> RechargeOrder:
    async with in_transaction():
        usdt_order: USDTRechargeOrder = await USDTRechargeOrder.filter(id=usdt_order_id).select_for_update().first() # type: ignore
    
        usdt_order.status = RechargeStatusEnum.SUCCEED
        usdt_order.tx_id = tx_id
        usdt_order.payment_address = payment_address
        usdt_order.chain = network
        usdt_order.platform = network
        usdt_order.out_order_id = tx_id
        usdt_order.finished_at = datetime.now(UTC) #datetime.fromtimestamp(int(evm_transaction.ts)/1000)
        await usdt_order.save()

        recharge: RechargeProduct = await RechargeProduct.filter(recharge_product_id=usdt_order.recharge_product_id).first() # type: ignore

        recharge_order = RechargeOrder(
            user_id=usdt_order.user_id,
            amount=recharge.amount + recharge.reward_amount,
            pay_fee=recharge.cny_price,
            pay_currency='CNY',
            status=RechargeStatusEnum.SUCCEED,
            recharge_channel=RechargeChannelEnum.USDT,
            out_order_id=tx_id,
            finished_at=datetime.now(UTC),
            recharge_product_id=usdt_order.recharge_product_id
        )
        await recharge_order.save()

        now = datetime.now(UTC)
        # payed amount
        payed_award = ExpirableAward(user_id=usdt_order.user_id,
                                     out_order_id = recharge_order.recharge_order_id,
                                     total_amount=recharge.amount,
                                     spend_amount=0,
                                     balance=recharge.amount,
                                     status=ExpirableStatusEnum.NORMAL,
                                     expires_at=datetime(2200, 1, 1),
                                     claim_at=now,
                                     from_type='PAYED')
        await payed_award.save()
        if recharge.reward_amount > 0:
            reward_award = ExpirableAward(user_id=usdt_order.user_id,
                                          out_order_id = recharge_order.recharge_order_id,
                                          total_amount=recharge.reward_amount,
                                          spend_amount=0,
                                          balance=recharge.reward_amount,
                                          status=ExpirableStatusEnum.NORMAL,
                                          expires_at=now + timedelta(days=recharge.charged_expire_delta),
                                          claim_at=now)
            await reward_award.save()
        if fc_reward:
            await gift_award_service.add_award_balance_with_charge_order(
                user_id=usdt_order.user_id,
                amount=user_growth_constants.FC_REWARD_AMOUNT,
                channel=RechargeChannelEnum.FC_REWARD,
                expire_delta=user_growth_constants.FC_REWARD_EXPIRES,
                out_order_id=f"fc_reward:{usdt_order.user_id}")
        
        return recharge_order
