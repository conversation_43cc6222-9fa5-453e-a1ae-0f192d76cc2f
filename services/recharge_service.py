from decimal import Decimal
import logging
import os, stripe
import uuid
import random
from datetime import datetime, timedelta, UTC
from dotenv import load_dotenv
from tortoise.transactions import in_transaction
from common.common_constant import SKIP_QUEUE_RECHARGE_PRODUCT_ID, RpDisplayFilter
from common.entity import EvmTransaction, OkxDeposit, RechargeSetting
from persistence.models.models import (
    Account,
    ChargeAmountGenerator,
    ExpirableAward,
    ExpirableStatusEnum,
    LedgerEntry,
    RechargeChannelEnum,
    RechargeOrder,
    RechargeProduct,
    RechargeProductTypeEnum,
    RechargeStatusEnum,
    TransactionTypeEnum,
    TransactionSourceEnum,
    USDTRechargeOrder,
)
from services.reward_calculation_service import RewardCalculationService
from persistence.redis_client import redis_client
from utils import env_const


load_dotenv()

stripe.set_app_info("TgChatBot", version="0.0.1", url="https://myapp.com")
stripe.api_version = "2023-10-16"
stripe.api_key = os.environ.get("STRIPE_PRIVATE_KEY")


async def create_checkout_session(
    user_id: int, setting: RechargeProduct, return_host: str, from_bot: int = 0
) -> str:
    recharge_id = setting.recharge_product_id
    quantity = 1
    stripe_success_url = f"{return_host}/pay?paystatus=1"
    stripe_failure_url = f"{return_host}/pay?paystatus=2"
    try:
        currency = "usd"
        checkout_session = stripe.checkout.Session.create(
            payment_method_types=["card"],
            # payment_method_options={"wechat_pay": {"client": "web"}},
            line_items=[
                {
                    "price_data": {
                        "currency": f"{currency}",  # "usd",
                        "product_data": {
                            "name": f"{setting.amount}钻石",
                        },
                        "unit_amount": int(setting.price / 1000),
                    },
                    "quantity": quantity,
                },
            ],
            mode="payment",
            success_url=stripe_success_url,
            cancel_url=stripe_failure_url,
            metadata={
                "recharge_id": str(recharge_id),
            },
        )
        await create_recharge_order(
            user_id,
            checkout_session.id,
            setting.amount,
            setting.cny_price,
            'CNY',
            str(recharge_id),
            from_bot,
        )
        return checkout_session.url
    except Exception as e:
        logging.exception(e)
        return ""


async def create_recharge_order(
    user_id: int,
    payment_id: str,
    amount: int,
    pay_fee: int,
    pay_currency: str,
    recharge_product_id: str,
    from_bot: int = 0,
):
    order = RechargeOrder(
        user_id=user_id,
        out_order_id=payment_id,
        amount=amount,
        pay_fee=pay_fee,
        pay_currency=pay_currency,
        recharge_channel=RechargeChannelEnum.STRIPE,
        status=RechargeStatusEnum.INIT,
        recharge_product_id=recharge_product_id,
        from_bot_id=from_bot,
    )
    await order.save()
    return order


async def pay_success(out_order_id: str, pay_time: int, raw_data: str) -> RechargeOrder:
    async with in_transaction():
        order: RechargeOrder = await RechargeOrder.get(out_order_id=out_order_id)
        if order.status == RechargeStatusEnum.SUCCEED:
            return order

        recharge_product: RechargeProduct = await RechargeProduct.filter(recharge_product_id=order.recharge_product_id).first()  # type: ignore

        calculated_reward = await RewardCalculationService.calculate_reward_amount(
            user_id=order.user_id,
            recharge_product=recharge_product
        )
        recharge_product.reward_amount = calculated_reward

        order.status = RechargeStatusEnum.SUCCEED
        order.finished_at = datetime.fromtimestamp(pay_time).astimezone(UTC)
        order.raw_response = raw_data
        await order.save()

        now = datetime.now(UTC)
        order.out_order_id = out_order_id
        order.status = RechargeStatusEnum.SUCCEED
        order.finished_at = now
        order.raw_response = raw_data
        await order.save()

        # payed amount
        payed_award = ExpirableAward(
            user_id=order.user_id,
            out_order_id=order.recharge_order_id,
            total_amount=recharge_product.amount,
            spend_amount=0,
            balance=recharge_product.amount,
            status=ExpirableStatusEnum.NORMAL,
            expires_at=datetime(2500, 1, 1),
            claim_at=now,
            from_type="PAYED",
        )
        await payed_award.save()
        if recharge_product.reward_amount > 0:
            reward_award = ExpirableAward(
                user_id=order.user_id,
                out_order_id=order.recharge_order_id,
                total_amount=recharge_product.reward_amount,
                spend_amount=0,
                balance=recharge_product.reward_amount,
                status=ExpirableStatusEnum.NORMAL,
                expires_at=now + timedelta(days=recharge_product.charged_expire_delta),
                claim_at=now,
            )
            await reward_award.save()

        try:
            from services.recharge_marketing_service import RechargeMarketingService
            await RechargeMarketingService.handle_user_recharged(order.user_id)
        except Exception as e:
            logging.warning(f"Failed to handle marketing notifications for user {order.user_id}: {e}")
        
        return order


async def airdrop_to_user_balance(user_id: int, amount: int) -> Account:
    now = datetime.now(UTC)
    async with in_transaction():
        account = await Account.filter(user_id=user_id).select_for_update().first()
        if account is None:
            raise ValueError("Account not found.")

        out_order_id = str(uuid.uuid4())
        charge_order = RechargeOrder(
            user_id=user_id,
            amount=amount,
            pay_fee=0,
            status=RechargeStatusEnum.SUCCEED,
            recharge_channel=RechargeChannelEnum.MANUAL_AIRDROP,
            out_order_id=f"airdrop:{user_id}:{out_order_id}",
            finished_at=now,
        )
        await charge_order.save()

        leger_entry = LedgerEntry(
            user_id=account.user_id,
            transaction_id=charge_order.recharge_order_id,
            amount=amount,
            type=TransactionTypeEnum.DEBIT,
            before_balance=account.total_balance,
            after_balance=account.total_balance + amount,
            source=TransactionSourceEnum.RE_CHARGE,
            description="airdrop user amount",
        )
        await leger_entry.save()

        account.total_balance += amount
        account.award_balance += amount
        await account.save()

        return account


async def airdrop_as_expirable(
    user_id: int, amount: int, expire_days: int
) -> ExpirableAward | None:
    now = datetime.now(UTC)
    async with in_transaction():
        user = await Account.filter(user_id=user_id).first()
        if user is None:
            raise ValueError("Account not found.")
        out_order_id = str(uuid.uuid4())
        recharge_order = RechargeOrder(
            user_id=user_id,
            amount=amount,
            pay_fee=0,
            status=RechargeStatusEnum.SUCCEED,
            recharge_channel=RechargeChannelEnum.MANUAL_AIRDROP,
            out_order_id=f"airdrop:{user_id}:{out_order_id}",
            finished_at=now,
        )
        await recharge_order.save()
        if expire_days <= 0:
            expire = datetime(2200, 1, 1)
        else:
            expire = now + timedelta(days=expire_days)
        award = ExpirableAward(
            user_id=user_id,
            out_order_id=recharge_order.recharge_order_id,
            total_amount=recharge_order.amount,
            spend_amount=0,
            balance=recharge_order.amount,
            status=ExpirableStatusEnum.NORMAL,
            expires_at=expire,
            claim_at=now,
            from_type="REWARD",
        )
        await award.save()
        return award


async def create_usdt_recharge_order(
    recipient: str, user_id: int, recharge_id: str, chain: str
) -> USDTRechargeOrder:
    recharge_product = await RechargeProduct.filter(
        recharge_product_id=recharge_id, expired_at__gte=datetime.now(UTC).replace(tzinfo=None), enabled=True
    ).first()
    if recharge_product is None:
        raise ValueError("Recharge product not found.")
    if recharge_product.max_charge_times > 0:
        charged_times = await RechargeOrder.filter(
            user_id=user_id, recharge_product_id=recharge_product.recharge_product_id
        ).count()
        if charged_times >= recharge_product.max_charge_times:
            raise ValueError("Exceed max charge times.")

    pay_fee = recharge_product.price
    async with in_transaction():
        amount_gen = (
            await ChargeAmountGenerator.filter(
                recipient_address=recipient, original_fee=pay_fee
            )
            .select_for_update()
            .first()
        )
        if amount_gen is None:
            amount_gen = ChargeAmountGenerator(
                recipient_address=recipient, original_fee=pay_fee, delta=0
            )
            delta = 0
            await amount_gen.save()
        else:
            delta = amount_gen.delta + 1
            if delta >= 100:
                delta = delta - 100
            amount_gen.delta = delta
            await amount_gen.save()

        final_amount = int(pay_fee * 0.99) + delta * 10
        current_order = await USDTRechargeOrder.filter(
            recipient_address=recipient, original_fee=final_amount
        ).first()
        if (
            current_order
            and current_order.status == RechargeStatusEnum.INIT
            and current_order.expired_at > datetime.now(UTC)
        ):
            raise ValueError("Order already exists.")

        order = USDTRechargeOrder(
            user_id=user_id,
            recharge_id=recharge_id,
            recipient_address=recipient,
            original_fee=pay_fee,
            final_fee=final_amount,
            platform=chain,
            status=RechargeStatusEnum.INIT,
            ccy="USDT",
            chain=chain,
            expired_at=datetime.now(UTC) + timedelta(minutes=30),
            recharge_product_id=recharge_product.recharge_product_id,
        )
        await order.save()
        return order


async def usdt_recharge_success(okx_deposit: OkxDeposit) -> Account:
    async with in_transaction():
        amt = int(Decimal(okx_deposit.amt) * 1000 * 100)
        usdt_order: USDTRechargeOrder = (
            await USDTRechargeOrder.filter(
                recipient_address=okx_deposit.to, final_fee=amt, ccy=okx_deposit.ccy
            )
            .order_by("-id")
            .select_for_update()
            .first()
        )
        if (
            usdt_order is None
            or usdt_order.status != RechargeStatusEnum.INIT
            or usdt_order.expired_at < datetime.now(UTC)
        ):
            raise ValueError("Order not found or expired.")

        payment_address: str = okx_deposit.fromWdId
        out_order_id: str = okx_deposit.depId
        tx_id = okx_deposit.txId
        if usdt_order.status == RechargeStatusEnum.SUCCEED:
            return await Account.get(user_id=usdt_order.user_id)

        usdt_order.status = RechargeStatusEnum.SUCCEED
        usdt_order.tx_id = tx_id
        usdt_order.payment_address = payment_address
        usdt_order.chain = okx_deposit.chain
        usdt_order.out_order_id = out_order_id
        usdt_order.finished_at = datetime.fromtimestamp(int(okx_deposit.ts) / 1000)
        await usdt_order.save()

        account = (
            await Account.filter(user_id=usdt_order.user_id).select_for_update().first()
        )
        recharge = await RechargeProduct.filter(
            recharge_product_id=usdt_order.recharge_product_id
        ).first()
        recharge_order = RechargeOrder(
            user_id=account.user_id,
            amount=recharge.amount,
            pay_fee=usdt_order.final_fee,
            status=RechargeStatusEnum.SUCCEED,
            recharge_channel=RechargeChannelEnum.USDT,
            out_order_id=out_order_id,
            finished_at=datetime.now(UTC),
        )

        await recharge_order.save()

        leger_entry = LedgerEntry(
            user_id=account.user_id,
            transaction_id=recharge_order.recharge_order_id,
            amount=recharge_order.amount,
            type=TransactionTypeEnum.CREDIT,
            before_balance=account.total_balance,
            after_balance=account.total_balance + usdt_order.final_fee,
            source=TransactionSourceEnum.RE_CHARGE,
            description=f"USDT充值{usdt_order.final_fee}",
        )
        await leger_entry.save()

        account.total_balance += usdt_order.final_fee
        account.charge_balance += usdt_order.final_fee
        await account.save()
        return account


async def alchemy_usdt_recharge_success(
    evm_transaction: EvmTransaction, network: str
) -> RechargeOrder | None:
    async with in_transaction():
        amt = int(Decimal(str(evm_transaction.value)) * 1000 * 100)
        usdt_order = (
            await USDTRechargeOrder.filter(
                recipient_address=evm_transaction.toAddress,
                final_fee=amt,
                ccy=evm_transaction.asset,
            )
            .order_by("-id")
            .select_for_update()
            .first()
        )
        if usdt_order is None:
            logging.warning(f"USDTOrder not found: {amt}")
            return None
        if usdt_order.status == RechargeStatusEnum.SUCCEED:
            logging.info(f"USDTOrder {usdt_order.order_id} already succeeded.")
            return await RechargeOrder.filter(out_order_id=usdt_order.tx_id).first()
        if usdt_order.status != RechargeStatusEnum.INIT:
            logging.warning(f"USDTOrder {usdt_order.order_id} status is not INIT.")
            return None
        if usdt_order.expired_at < datetime.now(UTC):
            logging.warning(f"USDTOrder {usdt_order.order_id} expired.")
            return None

        payment_address: str = evm_transaction.fromAddress
        out_order_id: str = evm_transaction.log.transactionHash
        tx_id = evm_transaction.log.transactionHash
        if usdt_order.status == RechargeStatusEnum.SUCCEED:
            return await RechargeOrder.filter(out_order_id=usdt_order.tx_id).first()

        usdt_order.status = RechargeStatusEnum.SUCCEED
        usdt_order.tx_id = tx_id
        usdt_order.payment_address = payment_address
        usdt_order.chain = network
        usdt_order.out_order_id = out_order_id
        usdt_order.finished_at=datetime.now(
            UTC
        )  # datetime.fromtimestamp(int(evm_transaction.ts)/1000)
        await usdt_order.save()

        account: Account = (
            await Account.filter(user_id=usdt_order.user_id).select_for_update().first()
        )  # type: ignore
        recharge: RechargeProduct = await RechargeProduct.filter(recharge_product_id=usdt_order.recharge_product_id).first()  # type: ignore

        calculated_reward = await RewardCalculationService.calculate_reward_amount(
            user_id=account.user_id,
            recharge_product=recharge
        )
        recharge.reward_amount = calculated_reward

        recharge_order = RechargeOrder(
            user_id=account.user_id,
            amount=recharge.amount + recharge.reward_amount,
            pay_fee=recharge.cny_price,
            pay_currency="CNY",
            status=RechargeStatusEnum.SUCCEED,
            recharge_channel=RechargeChannelEnum.USDT,
            out_order_id=out_order_id,
            finished_at=datetime.now(UTC),
            recharge_product_id=usdt_order.recharge_product_id,
        )

        await recharge_order.save()

        if recharge.charged_expire_delta <= 0:
            leger_entry = LedgerEntry(
                user_id=account.user_id,
                transaction_id=recharge_order.recharge_order_id,
                amount=recharge_order.amount,
                type=TransactionTypeEnum.CREDIT,
                before_balance=account.total_balance,
                after_balance=account.total_balance + recharge_order.amount,
                source=TransactionSourceEnum.RE_CHARGE,
                description=f"USDT充值{usdt_order.final_fee}",
            )
            await leger_entry.save()

            account.total_balance += recharge_order.amount
            account.charge_balance += recharge_order.amount
            await account.save()
        else:
            now = datetime.now(UTC)
            # payed amount
            payed_award = ExpirableAward(
                user_id=account.user_id,
                out_order_id=recharge_order.recharge_order_id,
                total_amount=recharge.amount,
                spend_amount=0,
                balance=recharge.amount,
                status=ExpirableStatusEnum.NORMAL,
                expires_at=datetime(2500, 1, 1),
                claim_at=now,
                from_type="PAYED",
            )
            await payed_award.save()
            if recharge.reward_amount > 0:
                reward_award = ExpirableAward(
                    user_id=account.user_id,
                    out_order_id=recharge_order.recharge_order_id,
                    total_amount=recharge.reward_amount,
                    spend_amount=0,
                    balance=recharge.reward_amount,
                    status=ExpirableStatusEnum.NORMAL,
                    expires_at=now + timedelta(days=recharge.charged_expire_delta),
                    claim_at=now,
                )
                await reward_award.save()

        try:
            from services.recharge_marketing_service import RechargeMarketingService
            await RechargeMarketingService.handle_user_recharged(account.user_id)
        except Exception as e:
            logging.warning(f"Failed to handle marketing notifications for user {account.user_id}: {e}")

        return recharge_order


async def get_usdt_order(order_id: str) -> USDTRechargeOrder | None:
    order = await USDTRechargeOrder.filter(order_id=order_id).first()
    return order


async def get_recharge_orders(user_id: int, limit: int) -> list[RechargeOrder]:
    return (
        await RechargeOrder.filter(user_id=user_id, status=RechargeStatusEnum.SUCCEED)
        .order_by("-id")
        .limit(limit)
        .all()
    )


async def get_recharge_orders_paging(
    user_id: int,
    offset: int,
    limit: int,
    only_recharge: bool,
    # start_time: datetime,
    # end_time: datetime,
):
    filter = {}
    filter["user_id"] = user_id
    filter["status"] = RechargeStatusEnum.SUCCEED
    # filter["created_at__gte"] = start_time
    # filter["created_at__lte"] = end_time
    if only_recharge:
        filter["pay_fee__gt"] = 0
    result: list[RechargeOrder] = (
        await RechargeOrder.filter(**filter)
        .order_by("-id")
        # .offset(offset)
        # .limit(limit)
        .all()
    )
    total = await RechargeOrder.filter(**filter).count()
    return result, total


async def get_recharge_orders_by_id(
    user_id: int, order_ids: list[str]
) -> list[RechargeOrder]:
    return await RechargeOrder.filter(
        user_id=user_id, recharge_order_id__in=order_ids
    ).all()


async def get_recharge_products(payed_user: bool = False) -> list[RechargeProduct]:
    ret = await RechargeProduct.filter(
        expired_at__gte=datetime.now(UTC).replace(tzinfo=None), enabled=True, show_for_user=True
    ).all()
    if payed_user and ret:
        ret = [product for product in ret if product.price != 2680000]
    return ret

async def get_image_bot_recharge_products() -> list[RechargeProduct]:
    products = await RechargeProduct.filter(enabled=True, show_for_user=True
    ).all()
    result = []
    for p in products:
        if p.display_filters is None:
            continue
        if RpDisplayFilter.IMAGE_BOT_ONLY.value in p.display_filters:
            result.append(p)
    return result

async def list_user_products(user_id: int, target_product_id: str = "") -> list[RechargeProduct]:
    products = await RechargeProduct.filter(
        expired_at__gte=datetime.now(UTC).replace(tzinfo=None), enabled=True, show_for_user=True
    ).all()
    if target_product_id:
        target_product = next((p for p in products if p.recharge_product_id == target_product_id), None)
        if not target_product:
            target_product = await RechargeProduct.filter(recharge_product_id=target_product_id, expired_at__gte=datetime.now(UTC).replace(tzinfo=None), enabled=True).first()
            if target_product:
                products.append(target_product)

    # 获取用户充值记录
    orders = await RechargeOrder.filter(
        user_id=user_id, status=RechargeStatusEnum.SUCCEED, pay_fee__gt=0
    ).all()

    # 获取用户已购买的产品ID集合
    order_product_ids = {order.recharge_product_id for order in orders}

    # 计算用户总充值金额（人民币）
    total_paid_fee = sum([o.pay_fee if o.pay_currency == 'CNY' else o.pay_fee * 7.5 for o in orders])
    
    # 确定用户类型
    is_new_user = len(orders) == 0
    is_high_recharge_user = total_paid_fee >= 300 * 100000
    
    def filter_products(product: RechargeProduct) -> bool:
        if not product.display_filters:
            return True  # 没有过滤条件，对所有用户显示

        if RpDisplayFilter.IMAGE_BOT_ONLY.value in product.display_filters:
            return False

        product_id = str(product.recharge_product_id)

        # 购买后隐藏
        if (RpDisplayFilter.HIDE_AFTER_PURCHASE.value in product.display_filters
                and product_id in order_product_ids):
            return False

        if (is_new_user and RpDisplayFilter.DISABLED_AFTER_PURCHASE.value in product.display_filters):
            product.display_filters.remove(RpDisplayFilter.DISABLED_AFTER_PURCHASE.value)
            return True

        # 新用户专享
        if RpDisplayFilter.NEW_USER_ONLY.value in product.display_filters and not is_new_user:
            return False

        # 高充值用户专享
        if RpDisplayFilter.HIGH_RECHARGE_USER_ONLY.value in product.display_filters and not is_high_recharge_user:
            return False

        # 非首充用户专享
        if RpDisplayFilter.NON_FIRST_RECHARGE_USER.value in product.display_filters and is_new_user:
            return False

        # 检查是否是常驻套餐
        if RpDisplayFilter.PERMANENT_RETAIN.value in product.display_filters:
            return True

        return True
    
    filtered_products = [p for p in products if filter_products(p)]

    # 处理特殊产品的显示逻辑
    for product in filtered_products:
        product_id = str(product.recharge_product_id)

        if is_new_user and product_id != SKIP_QUEUE_RECHARGE_PRODUCT_ID:
            product.corner_title = product.fc_corner_title
            product.corner_tip = ""
            product.reward_amount = product.fc_reward_amount
            product.promotion_desc = f'充值{product.amount}💎，送{product.fc_reward_amount}🟡，到账{product.amount+product.fc_reward_amount}，仅首充'
            product.corner_tip = product.fc_corner_tip

        # 高充值用户（≥300元）
        elif is_high_recharge_user:
            if (product_id == env_const.DECAY_RECHARGE_PRODUCT_1 
                or product_id == env_const.DECAY_RECHARGE_PRODUCT_2):
                now = datetime.now()
                date_str = now.strftime('%Y-%m-%d')
                rkey = f'decay_{product_id}_{date_str}'
                decays = get_product_decays(rkey)
                decay_count = sum([int(dc) for dc in decays])
                if product_id == env_const.DECAY_RECHARGE_PRODUCT_1:
                    remaining = 300 - decay_count
                    product.corner_title = f"限量{remaining}/300"
                else:
                    remaining = 100 - decay_count
                    product.corner_title = f"限量{remaining}/100"

    return filtered_products


async def get_enabled_recharge_products() -> list[RechargeProduct]:
    return await RechargeProduct.filter(
        expired_at__gte=datetime.now(UTC), show_for_user=True
    ).all()


async def get_all_recharge_products() -> list[RechargeProduct]:
    return await RechargeProduct.filter().all()


async def get_user_recharge_orders_by_recharge(
    user_id: int, recharge_product_ids: list[str]
) -> list[RechargeOrder]:
    return await RechargeOrder.filter(
        user_id=user_id,
        recharge_product_id__in=recharge_product_ids,
        status=RechargeStatusEnum.SUCCEED,
    )


async def get_unsuccess_recharge_orders(user_id: int) -> int:
    cnt = await RechargeOrder.filter(
        user_id=user_id, status=RechargeStatusEnum.INIT
    ).count()
    return cnt


async def check_recharge_user(user_id: int) -> bool:
    return await RechargeOrder.filter(
        user_id=user_id, status=RechargeStatusEnum.SUCCEED, pay_fee__gt=0
    ).exists()


async def get_recharge_product(recharge_product_id: str) -> RechargeProduct | None:
    return await RechargeProduct.filter(recharge_product_id=recharge_product_id).first()


async def get_recent_init_orders(user_id: int) -> list[RechargeOrder]:
    one_minute_ago = datetime.now(UTC) - timedelta(minutes=1)
    return await RechargeOrder.filter(
        user_id=user_id,
        status=RechargeStatusEnum.INIT,
        pay_fee__gt=0,
        created_at__gte=one_minute_ago.replace(tzinfo=None),
    ).all()


async def get_user_unpaid_channels_in_last_10_minutes(user_id: int) -> set[str]:
    """获取用户在过去10分钟内未支付的渠道列表"""
    ten_minutes_ago = datetime.now(UTC) - timedelta(minutes=10)
    unpaid_orders = await RechargeOrder.filter(
        user_id=user_id,
        status=RechargeStatusEnum.INIT,
        pay_fee__gt=0,
        created_at__gte=ten_minutes_ago.replace(tzinfo=None),
    ).all()

    return {order.recharge_channel.value for order in unpaid_orders}

async def get_last_recharge_channel(channels: list[str]) -> str:
    unpaid_orders = await RechargeOrder.filter(
        recharge_channel__in=channels,
        pay_fee__gt=0
    ).order_by('-id').limit(1).first()

    return unpaid_orders.recharge_channel.value if unpaid_orders else ""

async def get_user_payed_fee(user_id: int) -> int:
    orders = await RechargeOrder.filter(
        user_id=user_id, status=RechargeStatusEnum.SUCCEED, pay_fee__gt=0
    ).all()
    return sum([order.pay_fee for order in orders])

async def get_user_rmb_payed_fee(user_id: int) -> float:
    orders = await RechargeOrder.filter(
        user_id=user_id, status=RechargeStatusEnum.SUCCEED, pay_fee__gt=0
    ).all()
    ret_sum = 0
    for order in orders:
        if order.pay_currency == "CNY":
            ret_sum += order.pay_fee
        elif order.pay_currency in ["USD","usd"]:
            ret_sum += order.pay_fee * 7.5
    return ret_sum

async def get_order_by_id(order_id: str) -> RechargeOrder | None:
    return await RechargeOrder.filter(recharge_order_id=order_id).first()


async def get_order_by_out_order_id(out_order_id: str) -> RechargeOrder | None:
    return await RechargeOrder.filter(out_order_id=out_order_id).first()

def get_product_decays(rkey: str) -> list[str]:
    current_decay = redis_client.get(rkey)
    if current_decay:
        decays = current_decay.split(",")
        return decays
    return []

async def cancel_recharge_order(order_id: str) -> bool:
    order = await get_order_by_id(order_id)
    if not order:
        return False
    order.status = RechargeStatusEnum.USER_CANCELED
    await order.save()
    return True