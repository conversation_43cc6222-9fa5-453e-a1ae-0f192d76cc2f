from tortoise.transactions import in_transaction
from datetime import UTC, datetime
from persistence.models.models import ChatHistoryStatistic, ChatBotTask, RechargeChannelEnum
from services import gift_award_service
from utils import user_growth_constants

async def add_user_task(user_id: int, task_key: str, task_type) -> ChatBotTask:
    return await ChatBotTask.create(user_id=user_id, task_key=task_key, task_type=task_type, finished_at=datetime.now(UTC))

async def get_user_task(user_id: int, task_key: str) -> ChatBotTask | None:
    return await ChatBotTask.filter(user_id=user_id, task_key=task_key).first()

async def get_user_chat_count(user_id: int) -> int:
    return await ChatHistoryStatistic.filter(user_id=user_id).count()

async def get_user_chat_roles(user_id: int) -> list[int]:
    return await ChatHistoryStatistic.filter(user_id=user_id).distinct().values_list('role_id', flat=True)

async def roles_chat_satisfied(user_id: int, current_role_id: int = None) -> bool:
    roles = await get_user_chat_roles(user_id)
    if current_role_id is not None:
        roles.append(current_role_id)
    roles = list(set(roles))
    if len(roles) < 5:
        return False
    return await ChatBotTask.filter(user_id=user_id, task_key='ROLES_CHAT_TO_5', task_type='role_chat_count').count() <= 0

async def task_finished(user_id: int, task_key: str) -> bool:
    return await ChatBotTask.filter(user_id=user_id, task_key=task_key).count() > 0

async def can_do_c3_task(user_id: int) -> bool:
    finished = await ChatBotTask.filter(user_id=user_id, task_key='CHAT_COUNT_3', task_type='chat_count').count() > 0
    if finished:
        return False
    chat_count = await get_user_chat_count(user_id)
    if chat_count >= 3:
        return True
    
async def can_do_c5_task(user_id: int) -> bool:
    finished = await ChatBotTask.filter(user_id=user_id, task_key='CHAT_COUNT_5', task_type='chat_count').count() > 0
    if finished:
        return False
    chat_count = await get_user_chat_count(user_id)
    if chat_count >= 5:
        return True

async def daily_check_in_count(user_id: int) -> int:
    count = await ChatBotTask.filter(user_id=user_id,
                                    task_type='check_in').count()
    return count

async def daily_check_in_finished(user_id: int) -> bool:
    ds = datetime.now().strftime('%Y-%m-%d')
    task_key = f'CHECK_IN:{ds}'
    finished = await ChatBotTask.filter(user_id=user_id,
                                        task_key=task_key, task_type='check_in').count() > 0
    return finished

async def add_user_check_in_task(user_id: int) -> ChatBotTask:
    ds = datetime.now().strftime('%Y-%m-%d')
    task_key = f'CHECK_IN:{ds}'
    async with in_transaction():
        await gift_award_service.add_award_balance_with_charge_order(
            user_id=user_id,
            amount=user_growth_constants.GROUP_CHECK_IN_AWARD_AMOUNT,
            channel=RechargeChannelEnum.CHECK_IN,
            expire_delta=user_growth_constants.GROUP_CHECK_IN_AWARD_EXPIRES,
            out_order_id=f"chat_bot_check_in:{user_id}_{ds}")

        return await ChatBotTask.create(user_id=user_id,
                                        task_key=task_key, 
                                        task_type='check_in',
                                        finished_at=datetime.now(UTC))
    
async def add_user_roles_chat_task(user_id: int) -> ChatBotTask:
    count = await get_user_chat_roles(user_id)
    if len(count) < 5:
        return None
    async with in_transaction():
        await gift_award_service.add_award_balance_with_charge_order(
            user_id=user_id,
            amount=user_growth_constants.ROLE_CHAT_AWARD_AMOUNT,
            channel=RechargeChannelEnum.CHAT_BOT_TASKS,
            expire_delta=user_growth_constants.ROLE_CHAT_AWARD_EXPIRES,
            out_order_id=f"chat_bot_roles_chat_5:{user_id}")
        return await ChatBotTask.create(user_id=user_id,
                                        task_key='ROLES_CHAT_TO_5', 
                                        task_type='role_chat_count',
                                        finished_at=datetime.now(UTC))