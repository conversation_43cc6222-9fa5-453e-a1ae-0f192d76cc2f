# from tortoise.transactions import in_transaction
# from tortoise.functions import Sum
# from common.Invitation import InvitationSummary
# from persistence.models.models import Account, Invitation, LedgerEntry, RechargeChannelEnum, RechargeOrder, RechargeStatusEnum, TransactionSourceEnum, TransactionTypeEnum
# from services.account_service import INVITEE_RECHARGE_REWARD_RATE

# class InvitationService():
#     @classmethod
#     async def get_invitation_summary(cls, tg_user_id: str) -> InvitationSummary:
#         inviter = await Account.get(tg_user_id=tg_user_id)
#         inviter_id = str(inviter.account_id)
#         invitees = await Invitation.filter(inviter_id=inviter_id).all()
#         invite_rewards = await RechargeOrder.filter(account_id=inviter_id, recharge_channel=RechargeChannelEnum.INVITATION, status=RechargeStatusEnum.SUCCEED).all()
#         invite_reward_amount = sum([r.amount for r in invite_rewards])

#         inviter_recharge_reward_amount = (await RechargeOrder.annotate(sum=Sum('amount')).filter(account_id=inviter_id, recharge_channel=RechargeChannelEnum.INVITEE_RECHARGE_REWARD, status=RechargeStatusEnum.SUCCEED).values('sum'))[0]['sum']

#         invitee_recharge_amount = (await RechargeOrder.annotate(sum=Sum('amount')).filter(account_id__in=[i.account_id for i in invitees], recharge_channel=RechargeChannelEnum.STRIPE, status=RechargeStatusEnum.SUCCEED).values('sum'))[0]['sum']

#         return InvitationSummary(inviter_id=inviter_id, invitee_count=len(invitees), invite_reward_amount=invite_reward_amount, invitee_recharge_reward_amount=inviter_recharge_reward_amount if inviter_recharge_reward_amount is not None else 0, invitee_recharge_amount=invitee_recharge_amount if invitee_recharge_amount is not None else 0)

#     @classmethod
#     async def add_invitee_recharge_reward(cls, order: RechargeOrder) -> Account | None:
#         if order.recharge_channel != RechargeChannelEnum.STRIPE:
#             return
#         invitation = await Invitation.filter(account_id=order.account_id).first()
#         if invitation is None:
#             return
#         async with in_transaction():
#             inviter = await Account.filter(account_id=invitation.inviter_id).select_for_update().first()
#             if inviter is None:
#                 return

#             reward_amount = int(order.amount * INVITEE_RECHARGE_REWARD_RATE)
#             recharge = RechargeOrder(account_id = inviter.account_id,
#                                     amount = reward_amount,
#                                     status = RechargeStatusEnum.SUCCEED,
#                                     recharge_channel = RechargeChannelEnum.INVITEE_RECHARGE_REWARD,
#                                     out_order_id = f'invitee_recharge_reward:{order.recharge_order_id}',
#                                     finished_at = order.finished_at)
#             await recharge.save()

#             leger_entry = LedgerEntry(account_id=inviter.account_id, transaction_id=recharge.recharge_order_id,
#                                     amount=reward_amount, type=TransactionTypeEnum.CREDIT, before_balance=inviter.balance,
#                                     after_balance=inviter.balance + reward_amount,
#                                     source=TransactionSourceEnum.RE_CHARGE,
#                                     description=f"邀请用户充值奖励")
#             await leger_entry.save()

#             inviter.balance += reward_amount
#             await inviter.save()
#             return inviter