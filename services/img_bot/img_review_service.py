import logging

from persistence.models.models_bot_image import (
    ImgReviewStatus,
    BotImgGenTaskReview,
)

from utils.str_util import is_prohibited_words


logger = logging.getLogger(__name__)


class ImageReviewService:

    @classmethod
    async def create_review_task(
        cls,
        task_id: int,
        tg_id: int,
        user_id: int,
        prompt: str,
        nai_prompt: str,
        image_url: str,
    ) -> BotImgGenTaskReview:
        """
        创建一个新的审核任务
        """
        logger.info(f"Creating review task for task_id: {task_id}, user_id: {user_id}")
        phobiden_words = is_prohibited_words(prompt)
        
        review_status = ImgReviewStatus.INIT
        # if phobiden_words and len(phobiden_words) > 0:
        #     logger.warning(
        #         f"Prohibited words found in prompt: {phobiden_words}, task_id: {task_id}, user_id: {user_id}"
        #     )
        #     review_status = ImgReviewStatus.REJECTED
        # else:
        #     review_status = ImgReviewStatus.INIT
        
        try:
            review = BotImgGenTaskReview(
                task_id=task_id,
                tg_id=tg_id,
                user_id=user_id,
                prompt=prompt,
                nai_prompt=nai_prompt,
                image_url=image_url,
                review_status=review_status,
            )
            await review.save()
            return review
        except Exception as e:
            logger.error(f"Error creating review task: {e}")
            raise e

    @classmethod
    async def update_review_status_batch(
        cls, review_ids: list[int], new_status: ImgReviewStatus
    ) -> None:
        """
        批量更新审核任务的状态
        """
        await BotImgGenTaskReview.filter(id__in=review_ids).update(
            review_status=new_status
        )

    @classmethod
    async def get_review_tasks_page(
        cls, review_status: ImgReviewStatus, last_id: int = 0, page_size: int = 20
    ) -> tuple[list[BotImgGenTaskReview], int, int]:
        """
        按照id倒序游标分页获取最新的指定审核状态的审核任务,total_pages, total_count
        """
        logger.info(
            f"get_review_tasks_page: {review_status}, last_id: {last_id}, page_size: {page_size}"
        )
        # 查询符合条件的总记录数
        total_count = await BotImgGenTaskReview.filter(
            review_status=review_status
        ).count()
        total_pages = (total_count + page_size - 1) // page_size  # 计算总页数

        # 分页查询
        query = BotImgGenTaskReview.filter(review_status=review_status)
        if last_id != 0:
            query = query.filter(id__lt=last_id)
        reviews = await query.order_by("-id").limit(page_size)
        return reviews, total_pages, total_count

    @classmethod
    async def get_review_task_by_offset_page(
        cls, review_status: ImgReviewStatus, page: int = 1, page_size: int = 20
    ) -> tuple[list[BotImgGenTaskReview], int, int]:
        """
        分页获取指定审核状态的审核任务，返回任务列表、total_pages、total_count
        """
        logger.info(
            f"get_review_task_by_offset_page: {review_status}, page: {page}, page_size: {page_size}"
        )
        offset = (page - 1) * page_size if page > 1 else 0

        reviews = (
            await BotImgGenTaskReview.filter(review_status=review_status)
            .offset(offset)
            .limit(page_size)
        )
        total_count = await BotImgGenTaskReview.filter(
            review_status=review_status
        ).count()
        total_pages = (total_count + page_size - 1) // page_size

        return reviews, total_pages, total_count

    @classmethod
    async def get_review_tasks_by_status(
        cls, review_status: ImgReviewStatus, page: int = 1, page_size: int = 20
    ) -> list[BotImgGenTaskReview]:
        """
        分页获取指定审核状态的审核任务
        """
        offset = (page - 1) * page_size
        reviews = (
            await BotImgGenTaskReview.filter(review_status=review_status)
            .offset(offset)
            .limit(page_size)
        )
        return reviews

    @classmethod
    async def get_approved_to_send_review_tasks(
        cls, limit: int = 20
    ) -> list[BotImgGenTaskReview]:
        """
        获取已通过审核的任务最新的20条
        """

        reviews = (
            await BotImgGenTaskReview.filter(review_status=ImgReviewStatus.APPROVED)
            .filter(send_ack=False)
            .limit(limit)
        )

        return reviews

    @classmethod
    async def update_review_task_send_ack(cls, review_id: int, send_id: int = 0):
        logger.info(f"update_review_task_send_ack: {review_id}")

        if send_id != 0:
            await BotImgGenTaskReview.filter(id=review_id).update(
                send_ack=True, send_msg_id=send_id
            )
