import asyncio
import logging
import os

from aiogram import Bo<PERSON>

from common.common_constant import BenefitChannel, BenefitUsageScopeType
from common.image_bot_common import (
    IMAGE_BOT_CHECKIN_TEMPLATE,
    IMAGE_BOT_REPET_CHECKIN_TEMPLATE,
)
from common.image_bot_model import BotImgUserCheckinBO
from persistence.models.models import User, UserRegisterSource
from persistence.models.models_bot_image import (
    UserCheckinGroupMsgLog,
    BotDailyCheckinCount,
    BotSentGroupMsgLog,
    BotGroupCheckinLastMsg,
    BotImgCheckinBotConfig,
)

from services.user_service import UserService
from services.user.user_benefit_service import (
    reward_benefit,
    deduct_benefit,
    count_remain_benefit,
)


from datetime import datetime, timedelta, date
from pytz import timezone
from tortoise.transactions import in_transaction
import random


BEIJING_TZ = timezone("Asia/Shanghai")


def get_today_date():
    now = datetime.now(BEIJING_TZ)
    return now.date()


logger = logging.getLogger(__name__)

user_service = UserService()

IMAGE_BOT_NAME = os.getenv("IMAG_BOT_NAME", "AITupian_Bot")

# 获取环境变量中的免费生图权益ID
CHAT_BENEFIT_ID = int(os.getenv("CHAT_BENEFIT_ID", 0))


class BotImgGroupCheckinService:

    @classmethod
    async def user_checkin(cls, user_checkin_data: BotImgUserCheckinBO):
        """用户签到处理
        :param user_checkin_data: BotImgUserCheckinBO 包含用户签到信息
        :return: "success" - 签到成功, "repeat" - 重复签到, "limit" - 超过签到次数限制
        """
        logger.info(f"User checkin data: {user_checkin_data}")

        today = get_today_date()
        bot_id = user_checkin_data.bot_id
        group_id = user_checkin_data.group_id
        tg_id = user_checkin_data.tg_id
        msg_id = user_checkin_data.msg_id
        send_msg_txt = user_checkin_data.msg_txt
        tg_nickname = user_checkin_data.tg_nickname or "用户"

        checkin_status = "initial"

        async with in_transaction():

            checkin_log = await cls.save_group_checkin_msg(
                bot_id=bot_id,
                group_id=group_id,
                tg_id=tg_id,
                msg_id=msg_id,
                msg_text=send_msg_txt,
                checkin_date=today,
            )
            # 查询今日签到记录
            log = await BotDailyCheckinCount.get_or_none(
                tg_id=tg_id, checkin_date=today
            )
            if not log:
                # 首次签到
                log = await BotDailyCheckinCount.create(
                    bot_id=bot_id,
                    tg_id=tg_id,
                    group_id=group_id,
                    checkin_date=today,
                    checkin_count=1,
                )

                await log.save()

                checkin_status = "success"
            else:
                # 已签到，最多回复2次
                if log.checkin_count < 2:
                    log.checkin_count += 1
                    await log.save()

                    checkin_status = "repeat"
                else:
                    # 超过2次不再回复
                    checkin_status = "limit"

        if checkin_status == "success":

            tip_text = IMAGE_BOT_CHECKIN_TEMPLATE.format(
                tg_nickname=tg_nickname,
                bot_name=IMAGE_BOT_NAME,
            )

            # 增加权益
            await cls.add_benefit_to_user(tg_id, bot_id, group_id)
            # 发送成功消息
            sent_log_id = await cls.send_msg_func(
                tg_id,
                group_id,
                tip_text,
                message_thread_id=user_checkin_data.message_thread_id,
            )

            await cls.process_group_checkin_msg_history(
                group_id, checkin_log.id, sent_log_id
            )
        elif checkin_status == "repeat":
            # Do something for repeat checkin
            tip_text = IMAGE_BOT_REPET_CHECKIN_TEMPLATE
            sent_log_id = await cls.send_msg_func(
                tg_id,
                group_id,
                tip_text,
                message_thread_id=user_checkin_data.message_thread_id,
            )
            await cls.delete_telegram_history_msg(
                group_id,
                checkin_log.id,
                sent_log_id,
                delay_sec=6,
            )
        elif checkin_status == "limit":

            asyncio.create_task(
                cls.delete_telegram_history_msg(
                    group_id,
                    checkin_log.id,
                    0,  # No sent message log to delete
                    delay_sec=3,  # Delay before deletion
                )
            )

        else:
            logger.error(f"Unexpected checkin status: {checkin_status}")

        return

    @classmethod
    async def add_benefit_to_user(cls, tg_id: int, bot_id: int, group_id: int):
        """
        给用户添加签到奖励
        :param tg_id: 用户的Telegram ID
        :param bot_id: 机器人ID
        :param group_id: 群组ID
        """
        logger.info(
            f"Adding benefit to user: tg_id={tg_id}, bot_id={bot_id}, group_id={group_id}"
        )

        user = await user_service.get_user_by_tg_id(tg_id)
        if not user:
            logger.warning(f"User not found for tg_id: {tg_id}")
            user = await user_service.register_by_tg_with_start_role(
                tg_id,
                "",
                "",
                "",
                group_id,
                UserRegisterSource.IMAGE_BOT,
            )
        # 添加签到奖励逻辑
        benift = await reward_benefit(
            user=user,
            chat_benefit_id=CHAT_BENEFIT_ID,
            benefit_channel=BenefitChannel.SIGN_IN,
        )
        logger.info(f"User {tg_id} has been granted sign-in benefits: {benift}")

    @classmethod
    async def get_free_gen_img_count(cls, tg_id: int) -> int:
        """
        获取用户的免费生成图像次数
        :param tg_id: 用户的Telegram ID
        :return: 用户的免费生成图像次数
        """

        user = await user_service.get_user_by_tg_id(tg_id)
        if not user:
            logger.warning(f"User not found for tg_id: {tg_id}")
            return 0

        cnt = await count_remain_benefit(
            user_id=user.id,
            usage_scope_type=BenefitUsageScopeType.GENERATOR_IMAGE,
            usage_scope_id="small",
        )

        logger.info(
            f"User {tg_id} {user.id} has {cnt} free image generation counts left."
        )

        return cnt

    @classmethod
    async def save_group_checkin_msg(
        cls,
        bot_id: int,
        group_id: int,
        tg_id: int,
        msg_id: int,
        msg_text: str,
        checkin_date: date,
    ) -> UserCheckinGroupMsgLog:

        logger.info(
            f"Saving checkin message: bot_id={bot_id}, group_id={group_id}, tg_id={tg_id}, msg_id={msg_id}, checkin_date={checkin_date}"
        )
        # 插入签到消息
        return await UserCheckinGroupMsgLog.create(
            bot_id=bot_id,
            group_id=group_id,
            tg_id=tg_id,
            msg_id=msg_id,
            checkin_date=checkin_date,
            msg_text=msg_text,
        )
        # 查询当天该群签到消息，按时间排序，只保留最新2条
        # msgs = await UserCheckinGroupMsgLog.filter(
        #     group_id=group_id, checkin_date=checkin_date, is_deleted=False
        # ).order_by("-created_at")
        # if len(msgs) > 2:
        #     # 删除多余消息
        #     for msg in msgs[2:]:
        #         # 这里调用你的Telegram删除消息接口
        #         await cls.delete_telegram_msg(group_id, msg.msg_id)
        #         msg.is_deleted = True
        #         await msg.save()

    @classmethod
    async def delete_telegram_history_msg(
        cls,
        group_id: int,
        last_user_log_id: int,
        last_sent_msg_log_id: int,
        delay_sec: int = 0,
    ):
        # 这里实现你的消息删除逻辑
        logger.info(
            f"Deleting Telegram history messages for group {group_id}, last_user_log_id={last_user_log_id}, last_sent_msg_log_id={last_sent_msg_log_id}, delay_sec={delay_sec}"
        )

        if delay_sec > 0:
            await asyncio.sleep(delay_sec)
        bot_id = 0

        user_log = await UserCheckinGroupMsgLog.filter(
            id=last_user_log_id, is_deleted=False
        ).first()

        if user_log:
            # 删除用户签到消息,任何bot都可以删除
            bot_id = 0
            del_r = await cls.del_msg_func(
                bot_id=bot_id, group_id=group_id, msg_id=user_log.msg_id
            )
            if del_r:
                logger.info(
                    f"User checkin message {user_log.msg_id} deleted successfully."
                )
                await UserCheckinGroupMsgLog.filter(id=last_user_log_id).update(
                    is_deleted=True
                )

        # 获取bot最后发送的消息日志
        sent_msg_log = await BotSentGroupMsgLog.filter(
            id=last_sent_msg_log_id, is_deleted=False
        ).first()

        if sent_msg_log:
            bot_id = sent_msg_log.bot_id
            del_r = await cls.del_msg_func(
                bot_id=bot_id, group_id=group_id, msg_id=sent_msg_log.sent_msg_id
            )
            if del_r:
                logger.info(
                    f"Sent message {sent_msg_log.sent_msg_id} deleted successfully."
                )
                await BotSentGroupMsgLog.filter(id=last_sent_msg_log_id).update(
                    is_deleted=True
                )

    @classmethod
    async def del_msg_func(cls, bot_id: int, group_id: int, msg_id: int) -> bool:
        """
        删除指定群组的消息
        :param bot_id: 机器人ID
        :param group_id: 群组ID
        :param msg_id: 消息ID
        :return: 是否删除成功
        """
        logger.info(f"Deleting message {msg_id} in group {group_id} with bot {bot_id}")
        
        if bot_id == 0:
            bot_config_list = await BotImgCheckinBotConfig.filter(group_id=group_id).all()
            if not bot_config_list:
                logger.error(f"No bot config_list found for group {group_id}")
                return False
            
            # 随机选择一个bot进行删除
            bot_config = random.choice(bot_config_list)
        else:
            # 根据bot_id获取配置
            bot_config = await BotImgCheckinBotConfig.filter(
                bot_id=bot_id, group_id=group_id
            ).first()

        if not bot_config:
            logger.error(
                f"No bot config found for group {group_id} and bot_id {bot_id}"
            )
            return False

        async with Bot(bot_config.bot_token) as bot:
            try:
                logger.info(f"Deleting message {msg_id} in group {group_id} by bot {bot_config.bot_id},{bot_config.description}")
                return await bot.delete_message(chat_id=group_id, message_id=msg_id)
            except Exception as e:
                logger.error(
                    f"Failed to delete message {msg_id} in group {group_id}: {e}"
                )
                return False

    @classmethod
    async def send_msg_func(
        cls, tg_id: int, group_id: int, text: str, message_thread_id: int = 0
    ) -> int:
        """
        发送消息到指定群组
        """
        # 这里实现你的发送消息逻辑
        # 返回发送的消息对象
        logger.info(
            f"Sending message to group {tg_id} {group_id}: {text} (thread_id: {message_thread_id})"
        )

        bot_list = await BotImgCheckinBotConfig.filter(group_id=group_id).all()
        if not bot_list:
            logger.error(f"No bot found for group {group_id}")
            return 0

        # random_bot = bot_list[0]  # 选择第一个bot进行发送
        random_bot = random.choice(bot_list)
        try:
            async with Bot(random_bot.bot_token) as bot:
                msg = await bot.send_message(
                    group_id,
                    text,
                    message_thread_id=message_thread_id,
                    parse_mode="HTML",
                )
                logger.info(
                    f"Message sent to group {group_id},{msg.message_thread_id}: {msg.message_id}"
                )
                sent_group_msg = await BotSentGroupMsgLog.create(
                    bot_id=random_bot.bot_id,
                    group_id=group_id,
                    tg_id=tg_id,
                    sent_msg_id=msg.message_id,
                    msg_text=text,
                )
                return sent_group_msg.id
        except Exception as e:
            logger.error(f"Failed to send message to group {group_id}: {e}")
            return 0

    @classmethod
    async def process_group_checkin_msg_history(
        cls, group_id: int, last_user_log_id: int, last_sent_msg_log_id: int
    ):
        """
        处理群组签到消息历史
        :param group_id: 群组ID
        :param last_user_log_id: 最后处理的用户签到日志ID
        :param last_sent_msg_log_id: 最后处理的发送消息日志ID
        """
        logger.info(
            f"Processing group checkin message history for group {group_id}, last_user_log_id={last_user_log_id}, last_sent_msg_log_id={last_sent_msg_log_id}"
        )
        need_del = False
        del_last_sent_msg_log_id = 0
        del_last_user_log_id = 0

        async with in_transaction():
            last_history_msg = await BotGroupCheckinLastMsg.get_or_none(
                group_id=group_id
            )

            if not last_history_msg:
                last_history_msg = await BotGroupCheckinLastMsg.create(
                    group_id=group_id,
                    last_checkin_log_id=last_user_log_id,
                    last_sent_msg_log_id=last_sent_msg_log_id,
                )
            else:
                need_del = True
                del_last_user_log_id = last_history_msg.last_checkin_log_id
                del_last_sent_msg_log_id = last_history_msg.last_sent_msg_log_id
                last_history_msg.last_checkin_log_id = last_user_log_id
                last_history_msg.last_sent_msg_log_id = last_sent_msg_log_id
                await last_history_msg.save()

        if need_del:
            asyncio.create_task(
                cls.delete_telegram_history_msg(
                    group_id, del_last_user_log_id, del_last_sent_msg_log_id
                )
            )

    @classmethod
    async def decrease_free_gen_img_count(cls, user_id: int, task_id: int) -> bool:
        """
        减少用户的免费生成次数
        """
        logger.info(f"Decreasing free generation image count for user {user_id}")
        user = await user_service.get_user_by_id(user_id)
        if not user:
            logger.warning(f"User not found for user_id: {user_id}")
            return False

        return await deduct_benefit(
            user=user,
            usage_scope_type=BenefitUsageScopeType.GENERATOR_IMAGE,
            usage_scope_id="small",
            log_product_id=str(task_id),
        )
