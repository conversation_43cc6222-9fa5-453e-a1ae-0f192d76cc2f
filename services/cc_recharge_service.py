from decimal import Decimal
import logging
import os, stripe
import uuid
from datetime import datetime, timedelta, UTC
from dotenv import load_dotenv
from tortoise.transactions import in_transaction
from common.entity import EvmTransaction, OkxDeposit, RechargeSetting
from persistence.models.models import (
    Account,
    ChargeAmountGenerator,
    ExpirableAward,
    ExpirableStatusEnum,
    LedgerEntry,
    RechargeChannelEnum,
    RechargeOrder,
    RechargeProduct,
    RechargeProductTypeEnum,
    RechargeStatusEnum,
    TransactionTypeEnum,
    TransactionSourceEnum,
    USDTRechargeOrder,
    WithdrawChannelEnum,
    WithdrawOrder,
)
from services import gift_award_service

async def create_deposit_order(user_id: int, recharge_product_id: str) -> RechargeOrder:
    recharge_product = await RechargeProduct.filter(recharge_product_id=recharge_product_id).first()
    if recharge_product is None:
        return None

    try:
        order = RechargeOrder(
            user_id=user_id,
            out_order_id=str(uuid.uuid4()),
            amount=recharge_product.amount + recharge_product.reward_amount,
            pay_fee=recharge_product.cny_price,
            pay_currency='CNY',
            recharge_channel=RechargeChannelEnum.CC,
            status=RechargeStatusEnum.INIT,
            recharge_product_id=recharge_product.recharge_product_id
        )
        await order.save()
        return order
    except Exception as e:
        logging.exception(e)
        return None

async def pay_success(recharge_order_id: str,
                      pay_time: int,
                      out_order_id: str,
                      raw_data: str) -> RechargeOrder:
    async with in_transaction():
        order: RechargeOrder = await RechargeOrder.get(recharge_order_id=recharge_order_id)
        if order.status == RechargeStatusEnum.SUCCEED:
            return order
        recharge_product = await RechargeProduct.filter(recharge_product_id=order.recharge_product_id).first()

        order.out_order_id = out_order_id
        order.status = RechargeStatusEnum.SUCCEED
        order.finished_at = datetime.fromtimestamp(pay_time)
        order.raw_response = raw_data
        await order.save()

        total_amount = recharge_product.amount + recharge_product.reward_amount
        expirable_award = ExpirableAward(user_id=order.user_id,
                                        out_order_id = order.recharge_order_id,
                                        total_amount=total_amount,
                                        spend_amount=0,
                                        balance=total_amount,
                                        status=ExpirableStatusEnum.NORMAL,
                                        expires_at=datetime.now() + timedelta(31),
                                        claim_at=datetime.now())
        await expirable_award.save()

        return order

async def create_withdraw_order(fee: int, currency: str, card_name: str, card_no: str, bank_id: str, bank_province: str, bank_city: str, bank_branch_name: str, user_id: int) -> WithdrawOrder:
    try:
        order = WithdrawOrder(
            user_id=user_id,
            out_order_id=str(uuid.uuid4()),
            fee=fee,
            currency=currency,
            card_name=card_name,
            card_no=card_no,
            bank_id=bank_id,
            bank_province=bank_province,
            bank_city=bank_city,
            bank_branch_name=bank_branch_name,
            status=RechargeStatusEnum.INIT,
            withdraw_channel=WithdrawChannelEnum.CC
        )
        await order.save()
        return order
    except Exception as e:
        logging.exception(e)
        return None

async def withdraw_success(order_id: str, pay_time: datetime, out_order_id: str, raw_data: str) -> WithdrawOrder | None:
    order = await WithdrawOrder.filter(withdraw_order_id=order_id).first()
    if order is None:
        return None
    if order.status == RechargeStatusEnum.SUCCEED:
        return order

    try:
        order.out_order_id = out_order_id
        order.status = RechargeStatusEnum.SUCCEED
        order.finished_at = pay_time
        order.raw_response = raw_data
        await order.save()
        return order
    except Exception as e:
        logging.exception(e)
        return None
