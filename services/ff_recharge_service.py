from decimal import Decimal
import hashlib
import logging
import requests
import os
import uuid
from datetime import datetime, timedelta, UTC
from tortoise.transactions import in_transaction
from persistence.models.models import (
    ExpirableAward,
    ExpirableStatusEnum,
    RechargeChannelEnum,
    RechargeOrder,
    RechargeProduct,
    RechargeStatusEnum,
)
from utils import env_const
from services import out_recharge_common

ffpay_recharge_notify_url = f'{os.environ['TG_WEBHOOK_URL']}/ffpay/notify'
ffpay_recharge_return_url = f'{os.environ['TG_WEBHOOK_URL']}/ffpay/result'

async def create_recharge_order_with_product(user_id: int, recharge_product: RechargeProduct, bot_id: int = 0) -> RechargeOrder | None:
    return await out_recharge_common.create_recharge_order_with_product(user_id, recharge_product, RechargeChannelEnum.FFPAY, bot_id)

async def update_out_order_id(recharge_order_id: str, out_order_id: str, pay_url: str | None = None):
    return await out_recharge_common.update_out_order_id(recharge_order_id, out_order_id, pay_url)

async def pay_success(recharge_order_id: str,
                      out_order_id: str,
                      raw_data: str) -> RechargeOrder:
    return await out_recharge_common.pay_success(recharge_order_id, out_order_id, raw_data)

def create_ffpay_order(order: RechargeOrder, type: str, client_ip: str):
    params = {
        'pid': env_const.FFPAY_APP_ID,
        'out_trade_no': order.recharge_order_id,
        'money': f'{(Decimal(order.pay_fee) / 1000 / 100):.2f}',
        'type': type,
        'notify_url': ffpay_recharge_notify_url,
        'return_url': ffpay_recharge_return_url,
        'clientip': client_ip,
        'name': '充值',
    }
    sorted_params = sorted(params.items(), key=lambda x: x[0])
    sign_str = '&'.join(f'{k}={v}' for k, v in sorted_params) + env_const.FFPAY_APP_KEY
    sign = hashlib.md5(sign_str.encode()).hexdigest()
    params['sign'] = sign.lower()
    params['sign_type'] = 'MD5'

    # post params as application/x-www-form-urlencoded
    resp = requests.post(env_const.FFPAY_HOST, data=params, proxies=env_const.RECHARGE_PROXY)
    logging.info(f'ffpay_recharge_response: {resp.text}')
    return resp.json()