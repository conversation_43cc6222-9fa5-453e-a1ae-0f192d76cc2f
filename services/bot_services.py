import logging
import os
from typing import Optional
from datetime import timedelta
import urllib.parse
from dotenv import load_dotenv
from aiogram.types import Message, CopyTextButton
from aiogram import Bot, types
from aiogram.client.default import DefaultBotProperties
from aiogram.enums import ParseMode
from aiogram.exceptions import TelegramForbiddenError
from aiogram.client.session.aiohttp import AiohttpSession
from aiogram.utils.keyboard import InlineKeyboardBuilder
from pydantic import BaseModel
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from common import bot_common, copywriting_templates
from common.common_constant import (
    BotCategory,
    BotReplace,
    BotType,
    ChatBenefitEnum,
    GroupCategory,
)
from persistence.models.models import User, UserRegisterSource, UserStatus
from services import tg_config_service, tg_message_service
from services.bot_message_service import format_content_replace
from services.user import user_benefit_service
from services.user_service import user_service, get_effective_invitations
from utils import env_const
from utils.exception_util import async_ignore_catch_tg_exception
from persistence.redis_client import redis_client

log = logging.getLogger(__name__)
load_dotenv()

TOKEN = os.environ["TMA_BOT_TOKEN"]
CHAT_BOT_TOKEN = os.environ["CHAT_BOT_TOKEN"]
HELPER_BOT_TOKENS = os.environ["HELPER_BOT_TOKENS"].split(",")
ADV_ROLE_CHANNEL_BOT_TOKEN = os.environ["ROLE_ADV_BOT_TOKEN"]

CHAT_BOT_NAMES = os.environ["ROLE_BOTS"].split(",")
CHAT_BOT_TOKENS = os.environ["ROLE_BOT_TOKENS"].split(",")
CHAT_BOT_IDS = os.environ["ROLE_BOT_IDS"].split(",")
CHAT_BOT_USERNAMES = os.environ["ROLE_BOT_USERNAMES"].split(",")

TMA_BOT_NAMES = os.environ["TMA_BOTS"].split(",")
TMA_TOKENS = os.environ["TMA_BOT_TOKENS"].split(",")
TMA_BOT_IDS = os.environ["TMA_BOT_IDS"].split(",")
TMA_BOT_USERNAMES = os.environ["TMA_BOT_USERNAMES"].split(",")

DIAMOND_SEASON_CHANNEL_BOT_TOKEN = os.environ["DIAMOND_SEASON_CHANNEL_BOT_TOKEN"]


class BotSendResult(BaseModel):
    tma_success: bool = False
    chat_bot_success: bool = False
    tma_blocked: bool = False
    chat_bot_blocked: bool = False
    tma_uninitiated: bool = False
    chat_bot_uninitiated: bool = False
    ignored: bool = False
    tma_error: str = ""
    chat_bot_error: str = ""


proxy = os.environ.get("ALL_PROXY")
if proxy:
    session = AiohttpSession(proxy=os.environ.get("ALL_PROXY"))
    helper_bots = [
        Bot(
            token=x,
            default=DefaultBotProperties(parse_mode=ParseMode.HTML),
            session=session,
        )
        for x in HELPER_BOT_TOKENS
    ]
    chat_bots = [
        Bot(
            token=x,
            default=DefaultBotProperties(parse_mode=ParseMode.HTML),
            session=session,
        )
        for x in CHAT_BOT_TOKENS
    ]
    tma_bots = [
        Bot(
            token=x,
            default=DefaultBotProperties(parse_mode=ParseMode.HTML),
            session=session,
        )
        for x in TMA_TOKENS
    ]
    adv_role_channel_bot = Bot(
        token=ADV_ROLE_CHANNEL_BOT_TOKEN,
        default=DefaultBotProperties(parse_mode=ParseMode.HTML),
        session=session,
    )
    diamond_season_channel_bot = Bot(
        token=DIAMOND_SEASON_CHANNEL_BOT_TOKEN,
        default=DefaultBotProperties(parse_mode=ParseMode.HTML),
        session=session,
    )
else:
    helper_bots = [
        Bot(token=x, default=DefaultBotProperties(parse_mode=ParseMode.HTML))
        for x in HELPER_BOT_TOKENS
    ]
    chat_bots = [
        Bot(token=x, default=DefaultBotProperties(parse_mode=ParseMode.HTML))
        for x in CHAT_BOT_TOKENS
    ]
    tma_bots = [
        Bot(token=x, default=DefaultBotProperties(parse_mode=ParseMode.HTML))
        for x in TMA_TOKENS
    ]
    adv_role_channel_bot = Bot(
        token=ADV_ROLE_CHANNEL_BOT_TOKEN,
        default=DefaultBotProperties(parse_mode=ParseMode.HTML),
    )
    diamond_season_channel_bot = Bot(
        token=DIAMOND_SEASON_CHANNEL_BOT_TOKEN,
        default=DefaultBotProperties(parse_mode=ParseMode.HTML),
    )

tma_bots_map = {
    TMA_BOT_NAMES[i].upper(): tma_bots[i] for i in range(len(TMA_BOT_NAMES))
}
tma_bots_id2name_map = {
    TMA_BOT_IDS[i]: TMA_BOT_NAMES[i].upper() for i in range(len(TMA_BOT_IDS))
}
chat_bots_map = {
    CHAT_BOT_NAMES[i].upper(): chat_bots[i] for i in range(len(CHAT_BOT_NAMES))
}
chat_bots_id2name_map = {
    CHAT_BOT_IDS[i]: CHAT_BOT_NAMES[i].upper() for i in range(len(CHAT_BOT_IDS))
}
chat_bots_id_username_map = {
    CHAT_BOT_IDS[i]: CHAT_BOT_USERNAMES[i] for i in range(len(CHAT_BOT_IDS))
}
tma_bots_id_username_map = {
    TMA_BOT_IDS[i]: TMA_BOT_USERNAMES[i] for i in range(len(TMA_BOT_IDS))
}
tg_chat_id_bot_map = {bot.id: bot for bot in tma_bots + chat_bots}

bot = tma_bots[0]
chat_bot = chat_bots[0]
helper_bot = helper_bots[0]


def get_bot_by_bot_id(bot_id: int) -> Bot | None:
    return tg_chat_id_bot_map.get(bot_id)


def get_bot_name(bot: Bot) -> str:
    tma_name = tma_bots_id2name_map.get(str(bot.id))
    if tma_name:
        return tma_name.lower()
    chat_name = chat_bots_id2name_map[str(bot.id)]
    return chat_name.lower()


async def get_bot_username(bot: Bot) -> str:
    bot_id = str(bot.id)
    if bot_id in tma_bots_id_username_map:
        return tma_bots_id_username_map[bot_id]
    if bot_id in chat_bots_id_username_map:
        return chat_bots_id_username_map[bot_id]
    me = await bot.get_me()
    return me.username


def get_bot_and_register_source_by_id(
    bot_id: str | None,
) -> tuple[UserRegisterSource, Bot]:
    if not bot_id:
        return UserRegisterSource.TMA, bot
    tbot = tma_bots_map.get(bot_id.upper())
    if tbot:
        return get_register_source_by_bot(tbot), tbot
    cbot = chat_bots_map.get(bot_id.upper())
    if cbot:
        return get_register_source_by_bot(cbot), cbot
    return UserRegisterSource.TMA, bot


async def find_tma_link_by_bot(bot: Bot) -> str:
    for i, v in enumerate(tma_bots):
        if v.id == bot.id or bot.id == chat_bots[i].id:
            tma_bot = tma_bots[i]
            me = await tma_bot.get_me()
            return f"https://t.me/{me.username}/tavern"
    return env_const.TMA_DIRECT_URL


@async_ignore_catch_tg_exception
async def send_go_tma_tip(bot: Bot, chat_id: int):
    tma_url = await find_tma_link_by_bot(bot)
    content = await copywriting_templates.goto_tma_tip_template(tma_url)
    message = await bot.send_message(chat_id, content)
    reg_source = get_register_source_by_bot(bot)
    await tg_message_service.add_deleted_message(message, reg_source, bot_id=bot.id)


def get_register_source_by_bot(target_bot: Bot) -> UserRegisterSource:
    if target_bot.id == chat_bot.id:
        return UserRegisterSource.CHAT_BOT
    if target_bot.id == bot.id:
        return UserRegisterSource.TMA

    bot_id = str(target_bot.id)
    tma_bot = tma_bots_id2name_map.get(bot_id)
    rs = UserRegisterSource.of(tma_bot)
    if rs:
        return rs
    rs = UserRegisterSource.of(chat_bots_id2name_map.get(bot_id))
    if rs:
        return rs
    return UserRegisterSource.TMA


def get_bot_by_register_source(register_source: UserRegisterSource) -> Bot:
    if register_source in tma_bots_map:
        return tma_bots_map[register_source]
    if register_source in chat_bots_map:
        return chat_bots_map[register_source]
    if register_source.is_tma():
        return tma_bots[0]
    return chat_bot


# async def send_tma_message(text: str, user: User, chat_id: int | None = None,
#                            parse_mode: str = ParseMode.HTML):
#     if not chat_id:
#         tg_user = await user_service.get_tg_info_by_user_id(user.id)
#         if not tg_user:
#             logging.warning(f'chat id not found for user: {user.id}')
#             return
#         chat_id = tg_user.tg_id
#     bot = get_bot_by_register_source(user.register_source)
#     try:
#         await bot.send_message(chat_id=chat_id, text=text, parse_mode=parse_mode)
#     except Exception as e:
#         logging.warning(f'chat id {chat_id} send message failed: {e}')

# async def send_message(text: str, user: User,
#                        parse_mode: str = ParseMode.HTML,
#                        chat_id: int | None = None,
#                        reply_markup: InlineKeyboardMarkup | None = None,
#                        deleted: bool = False) -> types.Message | None:
#     if not chat_id:
#         tg_user = await user_service.get_tg_info_by_user_id(user.id)
#         if not tg_user:
#             return
#         chat_id = tg_user.tg_id

#     try:
#         bot = get_bot_by_register_source(user.register_source)
#         log.info(f'chat id {chat_id} send message by {user.register_source}')
#         ret =  await bot.send_message(chat_id=chat_id, text=text, parse_mode=parse_mode,reply_markup=reply_markup)
#         if deleted and ret:
#             await tg_message_service.add_deleted_message(ret,bot_id=bot.id)
#     except Exception as e:
#         logging.warning(f'chat id {chat_id} send message failed: {e}')
#     return None


async def safe_send_message_to_all(
    text: str,
    user: User,
    parse_mode: str = ParseMode.HTML,
    chat_id: int | None = None,
    tma_markup: InlineKeyboardMarkup | None = None,
    chat_markup: InlineKeyboardMarkup | None = None,
) -> BotSendResult:
    if not chat_id:
        tg_user = await user_service.get_tg_info_by_user_id(user.id)
        if not tg_user:
            return BotSendResult(ignored=True)
        chat_id = tg_user.tg_id

    result = BotSendResult()
    if (
        user.register_source == UserRegisterSource.CHAT_BOT
        or user.register_source == UserRegisterSource.TMA
    ):
        tma_bot = bot
        l_chat_bot = chat_bot
    else:
        tma_bot = tma_bots[1]
        l_chat_bot = chat_bots[1]

    try:
        await tma_bot.send_message(
            chat_id=chat_id, text=text, parse_mode=parse_mode, reply_markup=tma_markup
        )
        result.tma_success = True
    except Exception as e:
        logging.warning(f"chat id {chat_id} send message failed: {e}")
        error_msg = getattr(e, "message", None)
        if error_msg:
            result.tma_error = error_msg
        if isinstance(e, TelegramForbiddenError):
            if e.message.find("Forbidden: bot was blocked by the user") != -1:
                result.tma_blocked = True
            elif e.message.find("bot can't initiate conversation with a user") != -1:
                result.tma_uninitiated = True

    try:
        await l_chat_bot.send_message(
            chat_id=chat_id, text=text, parse_mode=parse_mode, reply_markup=chat_markup
        )
        result.chat_bot_success = True
    except Exception as e:
        logging.warning(f"CHAT_BOT chat id {chat_id} send message failed: {e}")
        error_msg = getattr(e, "message", None)
        if error_msg:
            result.chat_bot_error = error_msg
        if isinstance(e, TelegramForbiddenError):
            if e.message.find("Forbidden: bot was blocked by the user") != -1:
                result.chat_bot_blocked = True
            elif e.message.find("bot can't initiate conversation with a user") != -1:
                result.chat_bot_uninitiated = True

    return result


# async def send_message_replay_markup(
#     text: str,
#     user: User,
#     parse_mode: str,
#     reply_markup: InlineKeyboardMarkup,
#     chat_id: int | None = None,
# ):
#     if not chat_id:
#         tg_user = await user_service.get_tg_info_by_user_id(user.id)
#         if not tg_user:
#             return
#         chat_id = tg_user.tg_id

#     try:
#         bot = get_bot_by_register_source(user.register_source)
#         log.info(f"chat id {chat_id} send message by {user.register_source}")
#         await chat_bot.send_message(
#             chat_id=chat_id, text=text, parse_mode=parse_mode, reply_markup=reply_markup
#         )
#     except Exception as e:
#         logging.warning(f"chat id {chat_id} send message failed: {e}")


# async def send_message_replay_markup_by_chat_bot(
#     text: str,
#     user: User,
#     parse_mode: str,
#     reply_markup: InlineKeyboardMarkup,
#     chat_id: int | None = None,
# ):
#     if not chat_id:
#         tg_user = await user_service.get_tg_info_by_user_id(user.id)
#         if not tg_user:
#             return
#         chat_id = tg_user.tg_id
#     if user.register_source == UserRegisterSource.CHAT_BOT:
#         l_chat_bot = chat_bots[0]
#     elif user.register_source == UserRegisterSource.CHAT_BOT_1:
#         l_chat_bot = chat_bots[1]
#     else:
#         log.warning(
#             f"chat id {chat_id} send message failed: user register source error"
#         )
#         return
#     try:
#         log.info(f"chat id {chat_id} send message by {user.register_source}")
#         await l_chat_bot.send_message(
#             chat_id=chat_id, text=text, parse_mode=parse_mode, reply_markup=reply_markup
#         )
#     except Exception as e:
#         logging.warning(f"chat id {chat_id} send message failed: {e}")


# def is_special_seo_bot(bot: Bot) -> bool:
#     return bot.id == 8196109543


# def is_sfw_bot(bot: Bot) -> bool:
#     return bot.id in env_const.SFW_BOT_IDS


unlock_tip = """如果你进入某个群或频道遇到如下提示：
<code>This channel can't displayed because it was used to spread pornographic content.</code>
原因：
有人在群/频道里发了色情内容,  被 Telegram 官方限制了;

✅解决办法：
登录Telegram Web网页版链接： https://web.telegram.org
（复制到浏览器打开）
⚡️操作： 登录网页版后
➊点击「Settings/设置」
➋点击「Privacy and Security/隐私和安全」
➌找到「Sensitive content/敏感内容」并勾选「Disable filtering/禁用过滤」
➍ 重启 iOS 客户端即可正常访问，

❓评论区问题汇总:
找不到「Disable filtering」选项；
用伊斯兰国家的电话号码注册的电报都无法禁用过滤，只能换个手机号码，从新注册尝试
登录网页版时收不到验证码；
因为你正在使用盗版的电报应用。为了避免这个问题，强烈建议你卸载非官方版本，并前往官方网站 (https://telegram.org/)下载正版电报。

本教程来自 <a href="https://t.me/jisou?start=a_7100170922">极搜</a>
"""

unlock_video_url = (
    "https://ai-data.424224.xyz/dev/video/7ca384a3515140be85ca07f3bffaa4ea.mp4"
)


async def send_unlock_tip(bot: Bot, message: types.Message):
    await bot.send_video(
        message.chat.id,
        video=unlock_video_url,
        caption=unlock_tip,
        parse_mode=ParseMode.HTML,
    )


async def get_start_markup(tbot: Bot, text: str = "开始陪聊") -> InlineKeyboardMarkup:
    me = await tbot.get_me()
    url = f"https://t.me/{me.username}/tavern"
    return InlineKeyboardMarkup(
        inline_keyboard=[[InlineKeyboardButton(text=text, url=url)]]
    )


async def get_tma_start_markup(text: str = "开始陪聊") -> InlineKeyboardMarkup:
    me = await bot.get_me()
    url = f"https://t.me/{me.username}/tavern"
    return InlineKeyboardMarkup(
        inline_keyboard=[[InlineKeyboardButton(text=text, url=url)]]
    )


async def check_in_chat(tg_user_id: int, chat_id: int | str, bot: Bot = helper_bot):
    try:
        logging.info(f"check_in_chat: tg_id: {tg_user_id}, chat_id: {chat_id}")
        member = await bot.get_chat_member(chat_id, tg_user_id)
        if not member:
            return False
        if (
            isinstance(member, types.ChatMemberOwner)
            or isinstance(member, types.ChatMemberMember)
            or isinstance(member, types.ChatMemberAdministrator)
            or isinstance(member, types.ChatMemberRestricted)
        ):
            return True
    except Exception as e:
        logging.warning(
            f"check_in_chat failed, tg_id: {tg_user_id}, chat_id: {chat_id}, error: {e}"
        )
    return False


async def check_if_banned_in_chat(
    tg_user_id: int, chat_id: int | str, bot: Bot = helper_bot
):
    try:
        logging.info(
            f"check_if_banned_in_chat: tg_id: {tg_user_id}, chat_id: {chat_id}"
        )
        member = await bot.get_chat_member(chat_id, tg_user_id)
        if not member:
            return False
        if isinstance(member, types.ChatMemberBanned):
            return True
    except Exception as e:
        logging.warning(
            f"check_if_banned_in_chat failed, tg_id: {tg_user_id}, chat_id: {chat_id}, error: {e}"
        )
    return False


async def ban_in_chat(tg_user_id: int, chat_id: int | str, bot: Bot = helper_bot):
    try:
        logging.info(f"ban_in_chat: tg_id: {tg_user_id}, chat_id: {chat_id}")
        return await bot.ban_chat_member(chat_id, tg_user_id)
    except Exception as e:
        logging.warning(
            f"ban_in_chat failed, tg_id: {tg_user_id}, chat_id: {chat_id}, error: {e}"
        )
    return False


async def unban_in_chat(tg_user_id: int, chat_id: int | str, bot: Bot = helper_bot):
    try:
        logging.info(f"unban_in_chat: tg_id: {tg_user_id}, chat_id: {chat_id}")
        return await bot.unban_chat_member(chat_id, tg_user_id, True)
    except Exception as e:
        logging.warning(
            f"unban_in_chat failed, tg_id: {tg_user_id}, chat_id: {chat_id}, error: {e}"
        )
    return False


async def send_transfer_message(tg_id: int, bot: Bot):
    if bot.id not in env_const.BOT_IDS_TO_TRANSFER:
        return

    tma_bot = await tg_config_service.get_main_bot_by_category(BotCategory.TMA)
    chat_bot = await tg_config_service.get_main_bot_by_category(BotCategory.CHAT_BOT)
    builder = InlineKeyboardBuilder()
    builder.button(text="小程序", url=f"{tma_bot.url}/tavern")
    builder.button(text="TG 直聊", url=chat_bot.url)
    content = f"""⚠️ 紧急通知 ⚠️

亲爱的幻梦用户：

近期我们持续被友商恶意举报，为防止走丢，请尽快更新到我们的新机器人
小程序：@{tma_bot.username}
TG 直聊：@{chat_bot.username}

🔔 温馨提示：
• 强烈建议立即全部添加，以免失联
• 新bot主要功能与当前完全一致，点开即玩
• 账号数据及钻石互通，无需重新设置

📢 更多福利：
• 加入幻梦AI官方福利频道，获取最新羊毛与福利：@huanmeng_ai
• 牢记幻梦AI唯一官方网址，永远记得回家的路：huanmeng.ai (http://huanmeng.ai/)

幻梦AI祝您生活愉快！"""
    try:
        await bot.send_message(
            tg_id,
            content,
            reply_markup=builder.as_markup(),
            parse_mode=ParseMode.HTML,
        )
    except Exception as e:
        logging.warning(f"send_transfer_message failed: {e}")


# async def create_invite_link(user_id: int, bot: Bot) -> str:
#     me = await bot.get_me()
#     username = me.username
#     hu = f"u_{hex(user_id)}"
#     url = f"https://t.me/{username}/?start={hu}"
#     return url


# async def create_share_link(user_id: int, lbot: Bot | None = None) -> str:
#     bot = lbot or tma_bots[0]
#     share_link = await create_invite_link(user_id, bot)
#     text = urllib.parse.quote(
#         "👆点我体验幻梦AI，一款AI角色扮演聊天产品，满足你的一切幸福幻想，分享赚🟡无上限"
#     )
#     return f"tg://msg_url?url={share_link}&text={text}"


async def create_web_share_link(user_id: int) -> str:
    share_link = f"{env_const.WEB_INVITE_HOST}/?aff=u_{hex(user_id)}"
    text = urllib.parse.quote(
        """👆点我体验幻梦AI，电报最好的成人AI伴侣，满足你的一切XP与幻想。

现在注册免费玩，分享还能赚🟡，不设上限，先到先得！"""
    )
    return f"https://t.me/share/url?url={share_link}&text={text}"


async def send_invitation_message(message: Message, user: User, bot: Bot):
    if user.status == UserStatus.CHAT_BLACK.value:
        logging.info(f"user in black list, ignore invite message, uid: {user.id}")
        return
    if not user.invitation_privilege:
        logging.info(
            f"user has no invitation privilege, ignore invite message, uid: {user.id}"
        )
        await message.answer("您的邀请行为存在异常，如有疑问请联系管理员 @ai_x01_bot")
        return

    benefits = await user_benefit_service.get_user_benefits_with_type(
        user.id, ChatBenefitEnum.INVITATION
    )
    benefit_count = sum([b.reward_times for b in benefits])
    invited_count = await get_effective_invitations(user.id)
    share_link = await bot_common.create_invite_link(user.id, bot)
    web_share_link = f"{env_const.WEB_INVITE_HOST}/?aff=u_{hex(user.id)}"
    content = f"""🌟 您已成功邀请 {invited_count} 个有效中文用户。

累计获得金币数：{invited_count * 1000} 金币；

累计获得聊天权益次数：{benefit_count} 次；

奖励已全部到账，具体权益细节，可在小程序\-我的中查看。

已为您生成专属邀请链接，点击下方任意链接即可复制您的邀请链接。（轻触点击，不要长按）

为了减少tg群封禁，建议您优先选取第一个链接分享到telegram群聊或频道。第二个链接用于分享给tg联系人。

`赛博文援，免费开玩 {web_share_link}`

`文爱秒回，文笔超棒 {share_link}`

【温馨提示】金币和聊天权益会在邀请成功后24小时内自动到账。您邀请的用户必须为真实、活跃的中文用户才会计入有效邀请。平台有严格的反作弊算法识别作弊行为，任何作弊行为（包括但不限于恶意刷量、虚假注册、重复设备等行为）一旦发现，将会冻结账号资产，情节严重永久封禁账号。"""

    text = urllib.parse.quote(
        "👆点我体验幻梦AI，电报最好的成人AI伴侣，满足你的一切XP与幻想。\n 现在注册免费玩，分享还能赚金币，不设上限，先到先得！"
    )
    share_url = f"tg://msg_url?url={share_link}&text={text}"
    web_share_url = f"tg://msg_url?url={web_share_link}&text={text}"
    builder = InlineKeyboardBuilder()
    builder.button(text="复制邀请链接", copy_text=CopyTextButton(text=share_link))
    builder.button(text="分享到群组", url=web_share_url)
    builder.button(text="分享给好友", url=share_url)
    builder.adjust(1)

    answer_message = await message.answer(
        text=content, parse_mode=ParseMode.MARKDOWN_V2, reply_markup=builder.as_markup()
    )
    await tg_message_service.add_check_in_deleted_message(
        message, answer_message, user.id, timedelta(minutes=2), bot_id=bot.id
    )


async def send_invite_notice(message: Message, start_uid: int):
    is_member = redis_client.sismember("invite_notify_uids", str(start_uid))
    if not is_member:
        return
    await message.answer(
        """🔥限时活动🔥你邀请，我免单，幻梦AI免费玩！

即日起宠粉大促，活动期间邀请新用户，每成功邀请1人获得1000金币，加送50次聊天权益，上不封顶先到先得，真正实现聊天自由，不花钱也能随便玩！

赶快输入 /invite_link ，邀请好友，享受免费聊天吧！""",
        parse_mode=ParseMode.HTML,
    )


async def check_in_any_chat_group(tg_id: int) -> bool:
    groups = await tg_config_service.list_group(category=GroupCategory.CHAT)
    for group in groups:
        try:
            helper_bot = await tg_config_service.get_group_sender(group.chat_id)
            check = await check_in_chat(tg_id, group.chat_id, helper_bot)
            if check:
                return True
        except Exception as e:
            logging.warning(f"join_chat failed: {e}")
    return False
