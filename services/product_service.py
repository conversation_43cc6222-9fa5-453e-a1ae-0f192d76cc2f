import logging
import os
from typing import Optional

from common.common_constant import ApiSource, Language, ProductPermission, ProductType
from common.role_model import ProductResponse
from persistence.models.models import Product, User, UserRegisterSource
from utils.translate_util import _tl

log = logging.getLogger(__name__)


async def get_default_chat_product() -> Product:
    products = await Product.filter(type=ProductType.CHAT.value, online=True).all()
    product = [x for x in products if x.user_default]
    if not product:
        raise ValueError("No default chat product found")
    return product[0]


async def get_user_chat_product(
    user: User, return_default: bool = True
) -> Product | None:
    online_list = await Product.filter(type=ProductType.CHAT.value, online=True).all()
    online_map = {product.mid: product for product in online_list}
    if user.chat_product_mid and user.chat_product_mid in online_map:
        return online_map[user.chat_product_mid]
    if user.chat_product_mid and user.chat_product_mid not in online_map:
        return None
    if not return_default:
        return None
    product = [x for x in online_list if x.user_default]
    return product[0] if product else None


async def get_original_chat_product_by_mid(mid: str) -> Product | None:
    product = await Product.filter(mid=mid, type=ProductType.CHAT.value).first()
    if not product or not product.online:
        return None
    return product


async def get_translated_chat_product_by_mid(mid: str, language: str) -> Product | None:
    product = await Product.filter(mid=mid, type=ProductType.CHAT.value).first()
    if not product or not product.online:
        return None
    product.name = _tl(product.name, language, Product.__name__)
    product.short_name = _tl(product.short_name, language, Product.__name__)
    product.display_name = _tl(product.display_name, language, Product.__name__)
    product.desc = _tl(product.desc, language, Product.__name__)
    product.short_name = _tl(product.short_name, language, Product.__name__)
    return product


async def get_original_chat_product_by_mid_with_def(mid: str) -> Product:
    product = await Product.filter(mid=mid, type=ProductType.CHAT.value).first()
    if not product:
        return await get_default_chat_product()
    return product


async def get_chat_product_by_mid(mid: str) -> ProductResponse | None:
    product = await Product.filter(mid=mid, type=ProductType.CHAT.value).first()
    if not product or not product.online:
        return None
    return ProductResponse.from_product(product)


async def get_chat_product_by_model(model: str) -> ProductResponse | None:
    product = await Product.filter(model=model, type=ProductType.CHAT.value).first()
    if not product or not product.online:
        return None
    return ProductResponse.from_product(product)


async def get_by_chat(product_type: str, model: str) -> Product | None:
    return await Product.filter(type=product_type, model=model).first()


# new version
async def list_chat_product_new(
    source: Optional[UserRegisterSource] = None,
) -> list[ProductResponse]:
    list_products = await Product.filter(online=True, type=ProductType.CHAT.value).all()

    ret_products = [ProductResponse.from_product(x) for x in list_products]
    ret_products.sort(key=lambda x: x.price)

    if source and source == UserRegisterSource.USA_WEB.value:
        usa_support_mids = ["m1", "m2"]
        ret_products = [x for x in ret_products if x.mid in usa_support_mids]
    return ret_products


async def list_display_chat_product(
    language: str = Language.ZH.value, source: Optional[UserRegisterSource] = None
) -> list[ProductResponse]:
    list_products = await Product.filter(online=True, type=ProductType.CHAT.value).all()
    ret_products = [ProductResponse.from_product(x) for x in list_products]
    ret_products.sort(key=lambda x: x.price)
    if source and source == UserRegisterSource.USA_WEB.value:
        usa_support_mids = ["m1", "m2"]
        ret_products = [x for x in ret_products if x.mid in usa_support_mids]
    for product in ret_products:
        product.model_name = _tl(product.model_name, language, Product.__name__)
        product.desc = _tl(product.desc, language, Product.__name__)
        product.short_name = _tl(product.short_name, language, Product.__name__)
    return ret_products


async def list_display_chat_products_v1(
    language: str = Language.ZH.value, api_source: ApiSource = ApiSource.TMA
) -> list[ProductResponse]:
    list_products = await Product.filter(online=True, type=ProductType.CHAT.value).all()
    ret_products = [ProductResponse.from_product(x) for x in list_products]
    ret_products.sort(key=lambda x: x.price)
    if api_source and api_source == ApiSource.US_WEB.value:
        usa_support_mids = ["m1", "m2"]
        ret_products = [x for x in ret_products if x.mid in usa_support_mids]
    for product in ret_products:
        product.model_name = _tl(product.model_name, language, Product.__name__)
        product.desc = _tl(product.desc, language, Product.__name__)
        product.short_name = _tl(product.short_name, language, Product.__name__)
    return ret_products


async def get_most_expensive_all_user_model() -> ProductResponse | None:
    products = await Product.filter(online=True, type=ProductType.CHAT.value).all()
    # 按价格降序排列
    products.sort(key=lambda x: x.price, reverse=True)
    for product in products:
        if product.permission == ProductPermission.ALL_USER.value:
            return ProductResponse.from_product(product)
    return None


async def list_original_products(
    product_type: ProductType, online: bool = True
) -> list[Product]:
    list_products = await Product.filter(type=product_type.value, online=online).all()
    return list_products


async def map_mid_chat_product(online: bool = True) -> dict[str, Product]:
    list_products = await Product.filter(
        online=online, type=ProductType.CHAT.value
    ).all()
    return {x.mid: x for x in list_products}


async def map_chat_display_name_by_mid(
    online: bool = True,
) -> dict[str, str]:
    list_products = await Product.filter(
        online=online, type=ProductType.CHAT.value
    ).all()
    return {x.mid: x.display_name for x in list_products}


async def get_by_type_and_mid(type: str, mid: str) -> Product | None:
    return await Product.filter(type=type, mid=mid, online=1).first()


async def get_online_by_type_first(type: str) -> Product | None:
    return await Product.filter(type=type, online=True).first()


async def get_display_by_type_first(type: str) -> ProductResponse | None:
    product = await Product.filter(type=type, online=True).first()
    if not product:
        return None
    return ProductResponse.from_product(product)


async def map_by_product_id() -> dict[str, Product]:
    products = await Product.all()
    return {str(product.product_id): product for product in products}


async def get_online_tts_product_res() -> ProductResponse:
    tts_product = await Product.filter(type=ProductType.TTS.value, online=True).first()
    if not tts_product:
        raise ValueError("No online TTS product found")
    tts = ProductResponse(
        mid="tts",
        product_type="tts",
        price=tts_product.price if tts_product else 0,
        model_name="tts",
        desc="文字转语音",
        permission=(
            tts_product.permission if tts_product else ProductPermission.ALL_USER.value
        ),
    )
    return tts


async def get_online_photo_bot_product(mid: str) -> Product | None:
    log.info(f"get_online_photo_bot_product: {mid}")
    product = await Product.filter(
        mid=mid, type=ProductType.PHOTO_BOT.value, online=True
    ).first()
    if not product:
        return await Product.filter(
            type=ProductType.PHOTO_BOT.value, online=True
        ).first()
    return product


async def get_online_product_by_type_and_mid(
    product_type: ProductType, mid: str
) -> Product | None:
    product = await Product.filter(
        type=product_type.value, mid=mid, online=True
    ).first()
    if not product:
        return await Product.filter(
            type=ProductType.PHOTO_BOT.value, online=True
        ).first()
    return product


async def get_photo_bot_share_product() -> Product | None:
    log.info(f"get_photo_bot_share_product:")
    product = await Product.filter(
        mid="img_bot_share", type=ProductType.PHOTO_BOT.value, online=True
    ).first()

    return product


async def get_photo_bot_copy_promt_product() -> Product | None:
    log.info(f"get_photo_bot_share_product:")
    product = await Product.filter(
        mid="img_bot_copy_prompt", type=ProductType.PHOTO_BOT.value, online=True
    ).first()

    return product
