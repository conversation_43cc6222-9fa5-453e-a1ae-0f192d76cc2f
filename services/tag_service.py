import logging
from typing import Optional
from common.common_constant import ChatModeType, Language, RoleGenderType
from common.role_model import CardDetail
from persistence.models.models import (
    RoleOrder,
    SubTag,
    TagListOrder,
)
from properties import prop_util
from tortoise.expressions import F

from services import translate_service
from utils import json_util
from utils.translate_util import _tl


log = logging.getLogger(__name__)

# sub tag interface


async def get_sub_tag_by_name(tag_name: str):
    return await SubTag.filter(tag_name=tag_name).first()


async def list_sub_tag_by_ids(sub_tag_ids: list[int]):
    return await SubTag.filter(id__in=sub_tag_ids).all()


async def list_sub_tags_with_enabled():
    return await SubTag.filter(enabled=True).all().order_by("order")


async def list_sub_tag_names_with_enabled():
    return [tag.tag_name for tag in await list_sub_tags_with_enabled()]


async def list_all_sub_tags_with_deleted() -> list[SubTag]:
    return await SubTag.all()


async def list_ids_by_sub_tag_names(tag_names: list[str], language: str) -> list[int]:
    if not tag_names:
        return []
    sub_tags = await list_sub_tag_by_names(tag_names, language)
    return [x.id for x in sub_tags]


async def list_sub_tag_by_names(tag_names: list[str], language: str) -> list[SubTag]:
    if not tag_names:
        return []
    sub_list = await list_all_sub_tag(language)
    sub_list = [x for x in sub_list if x.tag_name in tag_names]
    ids = [x.id for x in sub_list]
    if not ids:
        return []
    return await SubTag.filter(id__in=ids).all()


async def list_sub_tag_names(tag_names: list[str], language: str) -> list[str]:
    if not tag_names:
        return []
    if language == Language.ZH.value:
        return tag_names
    return [_tl(x, language, SubTag.__name__) for x in tag_names]


async def map_all_tag_names(language: str) -> dict[str, str]:
    sub_tag_list = await list_sub_tags_with_enabled()
    if language == Language.ZH.value:
        return {tag.tag_name: tag.tag_name for tag in sub_tag_list}
    return {
        x.tag_name: _tl(x.tag_name, language, SubTag.__name__) for x in sub_tag_list
    }


async def list_all_sub_tag(language: str) -> list[SubTag]:
    sub_tags_list = await list_sub_tags_with_enabled()
    if language == Language.ZH.value:
        return sub_tags_list
    for sub_tag in sub_tags_list:
        sub_tag.tag_name = _tl(sub_tag.tag_name, language, SubTag.__name__)
        sub_tag.category = _tl(sub_tag.category, language, SubTag.__name__)
    return sub_tags_list


async def update_or_create_sub_tag(tag_name: str, category: str) -> SubTag:
    # 获取 max order
    max_sub_tag = await SubTag.filter(enabled=True).all().order_by("-order").first()
    order = max_sub_tag.order + 1 if max_sub_tag else 0
    cur_tag = await SubTag.filter(tag_name=tag_name).first()
    if cur_tag:
        cur_tag.category = category
        cur_tag.enabled = True
        cur_tag.order = order
        await cur_tag.save()
        return cur_tag
    cur_tag = await SubTag.create(
        tag_name=tag_name, category=category, order=order, enabled=True
    )
    return cur_tag


async def delete_sub_tag(sub_tag: SubTag) -> None:
    sub_tag.enabled = False
    await sub_tag.save()


async def update_sub_tag_order(sub_tag_name: str, order: int):
    sub_tag = await SubTag.filter(tag_name=sub_tag_name).first()
    if not sub_tag:
        return False
    if sub_tag.order == order:
        return False
    if order < sub_tag.order:
        await SubTag.filter(order__gte=order, order__lt=sub_tag.order).update(
            order=F("order") + 1
        )
    if order > sub_tag.order:
        await SubTag.filter(order__gt=sub_tag.order, order__lte=order).update(
            order=F("order") - 1
        )
    sub_tag.order = order
    await sub_tag.save()
    return True


async def update_tag_order(mode_type: str, mode_target_id: int, position: int):
    role_order = await RoleOrder.filter(
        mode_type=mode_type, mode_target_id=mode_target_id
    ).first()
    if not role_order:
        # 自身的position+1
        await RoleOrder.filter(position__gte=position).update(
            position=F("position") + 1
        )
        await RoleOrder.create(
            mode_type=mode_type, mode_target_id=mode_target_id, position=position
        )
        return True

    if position > role_order.position:
        await RoleOrder.filter(
            position__gt=role_order.position, position__lte=position
        ).update(position=F("position") - 1)
        role_order.position = position
        await role_order.save()
        return True
    if position < role_order.position:
        await RoleOrder.filter(
            position__gte=position, position__lt=role_order.position
        ).update(position=F("position") + 1)
        role_order.position = position
        await role_order.save()
        return True
    return False


async def list_tags_with_orders():
    tags_order = await TagListOrder.all().order_by("-id").first()
    if tags_order is not None:
        return json_util.convert_to_list(tags_order.tags_order)
    else:
        return []


async def add_update_tag_list(tags_order: list[str]):
    tag_order_list = await TagListOrder.create(tags_order=tags_order)
    return tag_order_list


async def map_chosen_order():
    ret_list = await RoleOrder.filter().all()
    return {CardDetail.key(x.mode_type, x.mode_target_id): x.position for x in ret_list}


async def map_chose_role_order():
    ret_list = await RoleOrder.filter().all()
    ret_list = [x for x in ret_list if x.mode_type == ChatModeType.SINGLE.value]
    return {x.mode_target_id: x.position for x in ret_list}


async def role_hot_sub_tags(limit: int = 20):
    return await SubTag.filter(enabled=True).all().order_by("-role_count").limit(limit)


async def list_sub_tag_category(language: str = Language.ZH.value) -> list[str]:
    category_list = [
        "互动方式",
        "性取向",
        "职业/身份",
        "关系/身份",
        "题材",
        "玩法/属性",
        "性格/心理特征/属性",
        "角色类型/属性",
    ]
    if language != Language.ZH.value:
        category_list = [_tl(x, language, SubTag.__name__) for x in category_list]
    return category_list


async def list_category_group_sub_tags(language: str = Language.ZH.value):
    category_list = await list_sub_tag_category(language)
    all_tags = await list_all_sub_tag(language)
    all_tags.sort(key=lambda x: x.order)
    category_groups = []
    for category in category_list:
        sub_tags = [tag.tag_name for tag in all_tags if tag.category == category]
        category_groups.append({"category": category, "sub_tags": sub_tags})
    return category_groups


async def format_filter_sub_tags(
    request_sub_tags: str, gender_type: Optional[RoleGenderType], language: str
) -> list[str]:
    if not request_sub_tags and not gender_type:
        return []
    if not request_sub_tags and gender_type:
        return [gender_type.to_tag_name()]
    split_sub_tags = request_sub_tags.split(",")
    sub_tags_ids = [int(x) for x in split_sub_tags if x.isdigit()]
    sub_tag_names = [x for x in split_sub_tags if not x.isdigit()]
    ret_list = []
    if split_sub_tags and sub_tags_ids:
        tag_list = await list_sub_tag_by_ids(sub_tags_ids)
        ret_list = [tag.tag_name for tag in tag_list]
    elif sub_tag_names:
        tag_list = await list_sub_tag_by_names(sub_tag_names, language=language)
        ret_list.extend([tag.tag_name for tag in tag_list])
    if gender_type:
        ret_list.append(gender_type.to_tag_name())
    return ret_list
