from datetime import datetime, UTC
import logging
from typing import Optional, Dict, Any
from persistence.redis_client import redis_client
from persistence.models.models import RechargeProduct, RechargeOrder, RechargeStatusEnum, RechargeActivityReward

class RewardCalculationService:
    @staticmethod
    async def calculate_reward_amount(
        user_id: int, 
        recharge_product: RechargeProduct,
    ) -> int:
        try:
            # Get all applicable activity rewards and filter by user eligibility
            all_activity_rewards = await RewardCalculationService.get_applicable_activities(
                str(recharge_product.recharge_product_id))

            ar_reward = 0
            if all_activity_rewards:
                # Filter activities by user eligibility and get the highest reward
                eligible_rewards = []
                for activity_reward in all_activity_rewards:
                    # Check both marketing eligibility and activity conditions
                    marketing_eligible = RewardCalculationService.is_user_eligible(user_id, activity_reward.activity_name)
                    conditions_eligible = await RewardCalculationService._check_activity_conditions(user_id, activity_reward)

                    if marketing_eligible and conditions_eligible:
                        eligible_rewards.append(activity_reward.reward_amount)

                # Use the highest reward amount from eligible activities
                if eligible_rewards:
                    ar_reward = max(eligible_rewards)

            is_first_charge = await RewardCalculationService._is_first_charge(user_id)

            if is_first_charge:
                return max(recharge_product.fc_reward_amount, ar_reward)

            return max(recharge_product.reward_amount, ar_reward)
        except Exception as e:
            logging.exception(f"Error calculating reward amount for user {user_id}: {e}", stack_info=True)
            return recharge_product.reward_amount

    @staticmethod
    async def _is_first_charge(user_id: int) -> bool:
        history_orders = await RechargeOrder.filter(
            user_id=user_id, 
            status=RechargeStatusEnum.SUCCEED, 
            pay_fee__gt=0
        ).all()
        return len(history_orders) == 0
    
    @staticmethod
    async def _get_activity_reward(
        recharge_product_id: str, 
    ) -> Optional[RechargeActivityReward]:
        now = datetime.now(UTC)

        activity_reward = await RechargeActivityReward.filter(
            recharge_product_id=recharge_product_id,
            enabled=True,
            start_at__lte=now,
            end_at__gte=now
        ).order_by('-priority').first()

        return activity_reward

    @staticmethod
    async def _check_activity_conditions(user_id: int, activity_reward: RechargeActivityReward) -> bool:
        """检查活动条件是否满足"""
        conditions = activity_reward.conditions or {}
        if not isinstance(conditions, dict):
            conditions = {}
        
        if not conditions:
            return True
        
        # 检查用户类型条件
        if 'user_type' in conditions:
            user_type = conditions.get('user_type')
            if user_type == 'new_user':
                is_first_charge = await RewardCalculationService._is_first_charge(user_id)
                if not is_first_charge:
                    return False
            elif user_type == 'old_user':
                is_first_charge = await RewardCalculationService._is_first_charge(user_id)
                if is_first_charge:
                    return False

        # 检查充值次数条件
        if 'max_charge_times' in conditions:
            max_times = conditions.get('max_charge_times')
            if max_times is not None:
                charge_count = await RechargeOrder.filter(
                    user_id=user_id,
                    status=RechargeStatusEnum.SUCCEED,
                    pay_fee__gt=0
                ).count()
                if charge_count >= max_times:
                    return False
        
        # 检查最小充值金额条件
        if 'min_charge_amount' in conditions:
            min_amount = conditions.get('min_charge_amount')
            if min_amount is not None:
                total_charged = await RewardCalculationService._get_user_total_charged(user_id)
                if total_charged < min_amount:
                    return False
        
        return True
    
    @staticmethod
    async def _get_user_total_charged(user_id: int) -> int:
        """获取用户总充值金额"""
        orders = await RechargeOrder.filter(
            user_id=user_id,
            status=RechargeStatusEnum.SUCCEED,
            pay_fee__gt=0
        ).all()
        
        total = 0.0
        for order in orders:
            if order.pay_currency == 'CNY':
                total += order.pay_fee
            elif order.pay_currency in ['USD', 'usd']:
                total += order.pay_fee * 7.5  # 汇率转换
        
        return int(total)
    
    @staticmethod
    async def get_applicable_activities(recharge_product_id: str) -> list[RechargeActivityReward]:
        """获取当前可用的活动列表"""
        now = datetime.now(UTC)
        
        return await RechargeActivityReward.filter(
            recharge_product_id=recharge_product_id,
            enabled=True,
            start_at__lte=now,
            end_at__gte=now
        ).order_by('-priority').all()
    
    @staticmethod
    def is_user_eligible(user_id: int, user_type: str) -> bool:
        """检查用户是否符合营销推送条件"""
        redis_key = f"recharge_marketing:{user_type}:{user_id}:eligible"
        eligible = redis_client.get(redis_key)
        return eligible is not None
