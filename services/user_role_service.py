import logging

from pydantic import BaseModel
from common.common_constant import (
    OFFICIAL_BACKGROUND,
    ApiSource,
    AuthorDefaultName,
    ChatModeType,
    Language,
    ProductPermission,
    ProductType,
    RoleFilterTag,
    RoleLevelType,
    WelfareTaskType,
)
from common.entity import UserRegexRuleResponse
from common.models.chat_model import UcbBrief
from common.models.common_res_model import SpeakerR<PERSON>
from common.role_model import (
    CardDetail,
    ProductResponse,
    RoleDataConfig,
    RoleDetail,
    UserChatRoleRes,
)
from persistence import chat_history_dao
from persistence.models.models import (
    LikeStats,
    PersonalBg,
    RegexOption,
    User,
    UserDisLikeRecord,
    UserFavoriteRecord,
    UserLikeRecord,
    UserRoleConfig,
    UserRolePhotoRecord,
)
from services import (
    product_service,
    regex_service,
    tag_service,
    user_service,
    welfare_service,
)
from services import role_config_service
from services.role import (
    character_book_service,
    role_group_service,
    role_loader_service,
    role_verify_service,
)
from services.role_config_service import RoleConfigService
from tortoise.expressions import F
from services.user import user_benefit_service
from services.voice_speaker_service import VoiceSpeakerService
from utils import date_util, json_util, role_util, str_util
from utils.translate_util import _tl

log = logging.getLogger(__name__)


async def recent_chat_list(user: User, language: str, offset: int = 0, limit: int = 15):
    recent_roles_groups = await chat_history_dao.get_recent_roles_and_group(user.id)
    recent_roles_groups.sort(key=lambda x: x.timestamp, reverse=True)
    count = len(recent_roles_groups)
    if offset >= count:
        return count, []
    if offset + limit > count:
        limit = count - offset
    recent_roles_groups = recent_roles_groups[offset : offset + limit]

    role_ids = [
        mid.mode_target_id
        for mid in recent_roles_groups
        if mid.mode_type == ChatModeType.SINGLE.value
    ]
    group_ids = [
        mid.mode_target_id
        for mid in recent_roles_groups
        if mid.mode_type == ChatModeType.GROUP.value
    ]
    recent_role_map = await role_loader_service.map_user_brief_by_filter(
        ids=role_ids, language=language, nickname=user.nickname, audit=False
    )
    recent_group_map = await role_group_service.map_detail_by_ids(
        group_ids=group_ids, audit=False
    )
    for mid_rg in recent_roles_groups:
        mid_rg.role = (
            recent_role_map.get(mid_rg.mode_target_id)
            if mid_rg.mode_type == ChatModeType.SINGLE.value
            else None
        )
        mid_rg.group = (
            recent_group_map.get(mid_rg.mode_target_id)
            if mid_rg.mode_type == ChatModeType.GROUP.value
            else None
        )
    return count, recent_roles_groups


async def recent_single_chat_list(
    user: User, offset: int = 0, limit: int = 12, language: str = Language.ZH.value
) -> tuple[int, list]:
    recent_roles_groups = await chat_history_dao.get_recent_roles_and_group(user.id)
    recent_roles_groups.sort(key=lambda x: x.timestamp, reverse=True)
    recent_roles = [
        mid for mid in recent_roles_groups if mid.mode_type == ChatModeType.SINGLE.value
    ]
    count = len(recent_roles)
    if offset >= count:
        return count, []
    if offset + limit > count:
        limit = count - offset
    target_roles = recent_roles[offset : offset + limit]
    target_role_ids = [mid.mode_target_id for mid in target_roles]

    role_map = await role_loader_service.map_user_brief_by_filter(
        ids=target_role_ids, language=language, nickname=user.nickname, audit=False
    )
    for mid_rg in target_roles:
        mid_rg.role = (
            role_map.get(mid_rg.mode_target_id)
            if mid_rg.mode_type == ChatModeType.SINGLE.value
            else None
        )
        mid_rg.group = None
    return count, target_roles


async def create_roles(user: User, language: str):

    create_group = await role_group_service.list_by_user_id(user.id)
    create_group.sort(key=lambda x: x.id, reverse=True)
    create_roles = await RoleConfigService.list_effective_role_by_uid(
        user.id, user.nickname
    )
    create_roles.sort(key=lambda x: x.id, reverse=True)
    reward = bool([x for x in create_roles if x.receive_reward])
    publish_card_tips = await welfare_service.user_publish_task_tips(
        user, reward, language
    )
    return UserChatRoleRes(
        create_roles=create_roles,
        create_groups=create_group,
        publish_card_tips=publish_card_tips,
    )


async def add_favorite_role(user: User, mode_type: str, mode_target_id: int):
    record = await UserFavoriteRecord.filter(
        user_id=user.id, mode_type=mode_type, mode_target_id=mode_target_id
    ).first()
    if not record:
        try:
            return await UserFavoriteRecord.create(
                user_id=user.id, mode_type=mode_type, mode_target_id=mode_target_id
            )
        # 捕获uk异常
        except Exception as e:
            log.warning("add_favorite_role error: %s", e)
            return None
    record.status = True
    await record.save()
    return record


async def del_favorite_role(user: User, mode_type: str, mode_target_id: int):
    record = await UserFavoriteRecord.filter(
        user_id=user.id, mode_type=mode_type, mode_target_id=mode_target_id
    ).first()
    if not record:
        return False

    record.status = False
    await record.save()
    return True


async def favorite_roles(user: User, language: str):
    records = await UserFavoriteRecord.filter(user_id=user.id, status=True).all()
    records.sort(key=lambda x: x.updated_at, reverse=True)
    role_ids = [
        mid.mode_target_id
        for mid in records
        if mid.mode_type == ChatModeType.SINGLE.value
    ]
    group_ids = [
        mid.mode_target_id
        for mid in records
        if mid.mode_type == ChatModeType.GROUP.value
    ]
    recent_role_map = await role_loader_service.map_user_brief_by_filter(
        ids=role_ids, language=language, nickname=user.nickname, audit=False
    )
    recent_group_list = await role_group_service.list_translate_detail_by_ids(
        group_ids, language
    )
    recent_group_map = {rg.id: rg for rg in recent_group_list}
    ret_list = []
    for mid_rg in records:
        role = (
            recent_role_map.get(mid_rg.mode_target_id)
            if mid_rg.mode_type == ChatModeType.SINGLE.value
            else None
        )
        group = (
            recent_group_map.get(mid_rg.mode_target_id)
            if mid_rg.mode_type == ChatModeType.GROUP.value
            else None
        )
        ret_list.append(
            CardDetail(
                mode_type=mid_rg.mode_type,
                mode_target_id=mid_rg.mode_target_id,
                role=role,
                group=group,
            )
        )
    return ret_list


async def get_role_detail(user: User | None, role_id: int, language: str):
    nickname = user.nickname if user else ""
    role_config = await role_loader_service.load_translated_role(
        role_id, language, nickname
    )
    if not role_config:
        return None
    if not user and not role_config.privacy:
        return None
    if not role_config.privacy and user and role_config.uid != user.id:
        return None
    # if not role_config or not role_config.status or not role_config.privacy:
    #     return None
    role_config = role_util.format_role_config(role_config, nickname)
    products = await product_service.list_chat_product_new()
    product_ids = [x.mid for x in products]
    role_detail = RoleDetail.from_config(role_config, product_ids)

    # author name
    map_nicknames = await user_service.map_nickname(
        [role_detail.author_id], language=language
    )
    author_name = map_nicknames.get(role_detail.author_id, "")
    role_detail.author_name = role_loader_service.get_def_author_name(
        ChatModeType.SINGLE.value, role_id, author_name, language
    )
    # token
    book = None
    if role_config.book_id:
        book = await character_book_service.get_edit_book_by_id(role_config.book_id)
    data_config = RoleDataConfig(**json_util.convert_to_dict(role_config.data_config))
    token_map = await role_config_service.role_token_count(data_config, book, True)
    role_detail.sum_token = sum(token_map.values())
    role_detail.token_map = token_map
    if user:
        role_detail.favorite = await UserFavoriteRecord.filter(
            user_id=user.id,
            mode_type=ChatModeType.SINGLE.value,
            mode_target_id=role_id,
            status=True,
        ).exists()
        # like info
        await _set_like_info(user.id, role_id, role_detail)
        # dislike info
        await _set_dislike_info(user.id, role_id, role_detail)
    return role_detail


async def _set_like_info(user_id: int, role_id: int, role_detail: RoleDetail):
    record = await UserLikeRecord.filter(
        user_id=user_id, mode_type=ChatModeType.SINGLE.value, mode_target_id=role_id
    ).first()
    if record is not None:
        role_detail.like = True

    like_stats = await LikeStats.filter(
        mode_type=ChatModeType.SINGLE.value, mode_target_id=role_id
    ).first()
    if like_stats is not None:
        role_detail.like_count = like_stats.like_count


async def _set_dislike_info(user_id: int, role_id: int, role_detail: RoleDetail):
    record = await UserDisLikeRecord.filter(
        mode_type=ChatModeType.SINGLE.value, mode_target_id=role_id, user_id=user_id
    ).first()
    if record is not None:
        role_detail.dislike = True
    count = await UserDisLikeRecord.filter(
        mode_type=ChatModeType.SINGLE.value, mode_target_id=role_id
    ).count()
    role_detail.dislike_count = count


async def get_group_detail(user: User, group_id: int, language: str):
    group = await role_group_service.get_translate_detail_by_id(group_id, language)
    if group is None:
        return None
    exist = await UserFavoriteRecord.filter(
        user_id=user.id,
        mode_type=ChatModeType.GROUP.value,
        mode_target_id=group_id,
        status=True,
    ).exists()
    group.favorite = exist
    return group


class UserProfileRes(BaseModel):
    id: int
    nickname: str
    enable_nsfw: bool
    show_nsfw: bool
    roles: list = []
    create_roles: list = []
    user_model: str
    recent_roles: list = []
    speaker_list: list = []
    avatar: str = ""
    sub_tags: list[str] = []
    regex_rules: list[UserRegexRuleResponse] = []
    chat_products: list[ProductResponse] = []
    tts_products: list[ProductResponse] = []
    language_list: list[dict] = []
    replay_token_max_config: dict = {}
    payed_user: bool = False
    show_nsfw_image: bool = True
    show_chat_tips: bool = True
    max_role_count: int = 0

    ## 个人聊天背景
    use_personal_bg: bool = False
    image_bgs: list[str] = []
    selected_bg_index: int = 0
    opacity: int = 85
    # 官方背景
    official_bgs: list[str] = []

    display_summary_rank_tag: str = ""
    initial_active_tag: str = ""
    voice_content_type: str = "INSIDE_ONLY"
    chat_channel: str = ""
    benefits: dict[str, UcbBrief] = {}
    publish_task_switch: bool = False
    status_block_switch: bool = True
    language: str = ""


async def get_user_detail_info(
    user: User, language: str, api_source: ApiSource
) -> UserProfileRes:
    user_id = user.id
    # get speaker list
    speaker_list = await VoiceSpeakerService.get_active_speakers()
    speaker_list = [SpeakerRes.from_model(speaker) for speaker in speaker_list]
    languages = Language.fetch_config(api_source)

    sub_tags = await tag_service.list_sub_tag_names_with_enabled()
    tts_product = await product_service.get_online_tts_product_res()
    chat_products = await product_service.list_display_chat_product(
        language, user.register_source
    )
    payed_user = await user_service.is_payed_user(user_id)
    max_role_count = await role_verify_service.get_max_role_count(user_id)
    publish_task_switch = await welfare_service.user_publish_task_switch(user)

    user_model_product = await product_service.get_user_chat_product(user)
    personal_bg = PersonalBg(**json_util.convert_to_dict(user.personal_bg))

    rank_tag = await user_service.get_user_rank_type(user.id)
    benefits = await user_benefit_service.map_valid_by_all_product_mids(user)
    regex_rules = await regex_service.list_res_by_option(RegexOption.FORMAT_DISPLAY)
    user_res = UserProfileRes(
        id=user_id,
        nickname=user.nickname,
        enable_nsfw=user.enable_nsfw,
        show_nsfw=user.show_nsfw,
        speaker_list=speaker_list,
        avatar=str_util.format_avatar(user.avatar),
        user_model=user_model_product.mid if user_model_product else "",
        sub_tags=sub_tags,
        regex_rules=regex_rules,
        chat_products=chat_products,
        tts_products=[tts_product],
        language_list=languages,
        replay_token_max_config=RoleLevelType.replayMaxTokenConfig(),
        payed_user=payed_user,
        show_nsfw_image=user.show_nsfw_image,
        max_role_count=max_role_count,
        show_chat_tips=user.show_chat_tips,
        use_personal_bg=user.use_personal_bg,
        image_bgs=personal_bg.image_bgs,
        selected_bg_index=personal_bg.selected_bg_index,
        opacity=personal_bg.opacity,
        official_bgs=OFFICIAL_BACKGROUND,
        display_summary_rank_tag=rank_tag,
        initial_active_tag = rank_tag,
        voice_content_type=user.voice_content_type,
        chat_channel=user.chat_channel,
        benefits=benefits,
        publish_task_switch=publish_task_switch,
        status_block_switch=user.status_block_switch,
        language=user.language if user.language else ""
    )
    return user_res


async def get_user_role_config(user_id: int, role_id: int):
    return await UserRoleConfig.filter(user_id=user_id, role_id=role_id).first()


async def set_user_role_config(user_id: int, role_id: int, speaker_id: str):
    config = await UserRoleConfig.filter(user_id=user_id, role_id=role_id).first()
    if config:
        config.speaker_id = speaker_id
        await config.save()
    else:
        config = UserRoleConfig(user_id=user_id, role_id=role_id, speaker_id=speaker_id)
        await config.save()
    return config


async def get_user_role_configs(user_id: int, role_ids: list[int]):
    return await UserRoleConfig.filter(user_id=user_id, role_id__in=role_ids).all()


async def get_user_role_photo_record(
    user_id: int,
    role_id: int,
    conversation_id: str,
    message_id: str,
    version: int,
    photo_id: str,
):
    return await UserRolePhotoRecord.filter(
        user_id=user_id,
        role_id=role_id,
        conversation_id=conversation_id,
        message_id=message_id,
        version=version,
        photo_id=photo_id,
    ).first()


async def get_photo_likes_map_by_conversation_id(
    user_id: int, role_id: int, conversation_id: str
):
    records = await UserRolePhotoRecord.filter(
        user_id=user_id, role_id=role_id, conversation_id=conversation_id
    ).all()
    return {record.photo_id: record.like_status for record in records}


async def get_user_role_photo_record_by_conversation_id(
    user_id: int, role_id: int, conversation_id: str
):
    return await UserRolePhotoRecord.filter(
        user_id=user_id, role_id=role_id, conversation_id=conversation_id
    ).all()


async def add_user_role_photo_record(
    user_id: int,
    role_id: int,
    conversation_id: str,
    message_id: str,
    version: int,
    photo_id: str,
    photo_url: str,
):
    return await UserRolePhotoRecord.create(
        user_id=user_id,
        role_id=role_id,
        conversation_id=conversation_id,
        message_id=message_id,
        version=version,
        photo_id=photo_id,
        photo_url=photo_url,
    )
