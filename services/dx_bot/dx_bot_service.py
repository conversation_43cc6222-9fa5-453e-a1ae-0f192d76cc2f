

from persistence.models.models_dx_bot import DxBotConfig


class DxBotConfigService:
    @classmethod
    async def get_bot_config_by_name(cls, bot_name: str):
        return await DxBotConfig.filter(bot_name=bot_name).first()
    
    @classmethod
    async def get_bot_config_by_token(cls, bot_token: str):
        return await DxBotConfig.filter(bot_token=bot_token).first()
    
    @classmethod
    async def add_bot_config(cls, bot_config: DxBotConfig) -> DxBotConfig:
        existing_config = await DxBotConfig.filter(bot_name=bot_config.bot_name).first()
        if existing_config:
            return existing_config
        else:
            return await DxBotConfig.create(bot_name=bot_config.bot_name, bot_token=bot_config.bot_token, bot_config=bot_config.bot_config)
    
    @classmethod
    async def update_bot_config(cls, bot_name: str, bot_config: DxBotConfig)-> DxBotConfig:
        existing_config = await DxBotConfig.filter(bot_name=bot_name).first()
        if existing_config:
            existing_config.bot_token = bot_config.bot_token
            existing_config.bot_config = bot_config.bot_config
            await existing_config.save()
            return existing_config
        else:
            return await DxBotConfig.create(bot_name=bot_config.bot_name, bot_token=bot_config.bot_token, bot_config=bot_config.bot_config)
    
    @classmethod
    async def delete_bot_config_by_name(cls, bot_name: str) -> bool:
        existing_config = await DxBotConfig.filter(bot_name=bot_name).first()
        if existing_config:
            existing_config.deleted = True
            await existing_config.save()
            return True
        else:
            return False

    @classmethod
    async def get_all_bot_config(cls) -> list[DxBotConfig]:
        return await DxBotConfig.all()

    @classmethod
    async def get_all_active_bot_config(cls) -> list[DxBotConfig]:
        return await DxBotConfig.filter(deleted = False).all()
