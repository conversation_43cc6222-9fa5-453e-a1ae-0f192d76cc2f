

from persistence.models.models_dx_bot import Media<PERSON>roup<PERSON>esssa<PERSON>, FormnStatus, MessageMap

class MessageService:
    
    @classmethod
    async def add_media_group_message(cls, media_group_message: MediaGroupMesssage):
        existing_message = await MediaGroupMesssage.filter(media_group_id=media_group_message.media_group_id,message_id=media_group_message.message_id).first()
        if existing_message:
            return existing_message
        else:
            return await MediaGroupMesssage.create(chat_id=media_group_message.chat_id, message_id=media_group_message.message_id, media_group_id=media_group_message.media_group_id, is_header=media_group_message.is_header, caption_html=media_group_message.caption_html)
    
    @classmethod
    async def get_all_media_group_message(cls, media_group_id: int):
        return await MediaGroupMesssage.filter(media_group_id=media_group_id).all()
    
    
    @classmethod
    async def add_formn_status(cls, formn_status: FormnStatus):
        existing_status = await FormnStatus.filter(message_thread_id=formn_status.message_thread_id).first()
        if existing_status:
            return existing_status
        else:
            return await FormnStatus.create(message_thread_id=formn_status.message_thread_id, status=formn_status.status)
    
    @classmethod
    async def get_formn_status(cls, message_thread_id: int):
        return await FormnStatus.filter(message_thread_id=message_thread_id).first()

    
    
    @classmethod
    async def add_message_map(cls, message_map: MessageMap):
        return await MessageMap.create(user_chat_message_id=message_map.user_chat_message_id, group_chat_message_id=message_map.group_chat_message_id, user_id=message_map.user_id)
    
    @classmethod
    async def get_message_map(cls, user_chat_message_id: int,user_id:int):
        return await MessageMap.filter(user_chat_message_id=user_chat_message_id,user_id=user_id).first()
    
    @classmethod
    async def get_message_map_by_group_chat_message_id(cls, group_chat_message_id: int,user_id:int):
        return await MessageMap.filter(group_chat_message_id=group_chat_message_id,user_id=user_id).first()
    
    @classmethod
    async def get_all_messages_by_user_id(cls, user_id: int):
        return await MessageMap.filter(user_id=user_id).all()
    