
import datetime
import logging
import os
from dotenv import load_dotenv
from typing import Optional, <PERSON><PERSON>
import random

from asyncio import sleep
from aiogram import Bo<PERSON>, Dispatcher
from aiogram.types import ForumTopic
import asyncio
# from aiogram.utils.
from tenacity import retry, stop_after_attempt, wait_random, retry_if_exception_type, after_log
from aiogram import Bo<PERSON>
from aiogram.exceptions import TelegramAPIError
from cachetools import TTLCache
from asyncache import cached

from queue_msg.redis_queue import RedisQueue
from persistence.models.models import SXUser
from services.recharge_service import check_recharge_user
from services.account_service import AccountService
from services.user_service import UserService
from services.chat_bot_task_service  import get_user_chat_count

log = logging.getLogger(__name__)
load_dotenv()

user_service = UserService()



API_TOKEN = os.getenv("TMA_BOT_TOKEN_FF","")
FORWAD_GROUP_ID = int(os.getenv("FORWARD_GROUP_ID",0))

# 初始化 Bot 和 Dispatcher
bot = Bot(token=API_TOKEN)
dp = Dispatcher(bot=bot)

MAX_MESSAGE_LENGTH = 4096
MAX_RETRIES = 3 # 最大重试次数
REQUEST_TIMEOUT = 10 # 10s

MIN_RETRY_WAIT = 5 # 最小重试等待时间s
MAX_RETRY_WAIT = 10 # 最大重试等待时间s

# 创建一个带有过期时间的缓存，最大容量为 2048，过期时间为 60*60*24 秒
cache = TTLCache(maxsize=2048, ttl=60*60*24)

#发送到tg的延迟时间
TG_SEND_DEALY = int(os.getenv("TG_SEND_DEALY",4))

CHAT_CNT_MIN = 50

# 定义一个发送消息的函数
@retry(
    stop=stop_after_attempt(MAX_RETRIES),
    wait=wait_random(min=MIN_RETRY_WAIT, max=MAX_RETRY_WAIT),
    retry=retry_if_exception_type(TelegramAPIError),
    after =after_log(log, logging.WARNING),
    reraise=True
)
async def send_message_safe(chat_id: int, text: str,message_thread_id:int):
    await bot.send_message(chat_id=chat_id, text=text,message_thread_id=message_thread_id,parse_mode='HTML',request_timeout=REQUEST_TIMEOUT)
    await asyncio.sleep(random.uniform(1, 5))
    
async def send_message(chat_id: int, text: str,message_thread_id:int):
    try:
        await send_message_safe(chat_id=chat_id, text=text,message_thread_id=message_thread_id)
    except Exception as e:
        log.warning(f'send_message error: {e},chat_id={chat_id},text={text},message_thread_id={message_thread_id}')
        return None
    

async def create_forum_topic(group_id: int, name: str) -> ForumTopic:
    return await bot.create_forum_topic(chat_id=group_id, name=name)

# 更新论坛主题的名字
async def update_forum_topic(group_id: int, message_thread_id:int, name: str) -> bool:
    return await bot.edit_forum_topic(chat_id=group_id, message_thread_id=message_thread_id,name=name)




@cached(cache)
async def get_u_all_name(user_id):
    name = "nickname"
    user = await user_service.get_user_by_id(user_id)
    tg_user = await user_service.get_tg_user_by_id(user_id)

    if user and user.nickname:
        name = f"{name}:#{user.nickname}"

    if tg_user:
        name = f"{name},tg_user_name:#{tg_user.user_name},tg_first_name:#{tg_user.first_name},tg_last_name:#{tg_user.last_name}"
    return name


class SXUserService:
    @classmethod
    async def get_sx_user(cls, tg_id: int) -> Optional[SXUser]:
        return await SXUser.filter(tg_id=tg_id).first()
    
    @classmethod
    async def create_sx_user(cls, tg_id: int,user_id:int,group_id:int,channel_id:int,is_vip:bool =False) -> Optional[SXUser]:
        
        topic_name = f'用户c_{channel_id}:{tg_id}'
        if is_vip:
            topic_name = f'VIP_{topic_name}'
        forum_topic = await create_forum_topic(group_id=group_id, name=topic_name)
        
        if not forum_topic:
            log.error(f'create forum topic failed for user {tg_id}')
            return None
        return await SXUser.create(tg_id=tg_id,message_thread_id=forum_topic.message_thread_id,group_id=group_id,forum_topic_name=forum_topic.name,bot_id=1)
    
    @classmethod
    async def update_sx_user_topic_name(cls, sx_user: SXUser) -> Optional[SXUser]:
    
        try:
            update_topic = await update_forum_topic(group_id=sx_user.group_id, message_thread_id=sx_user.message_thread_id,name=sx_user.forum_topic_name)
        except Exception as e:
            log.error(f'update forum topic failed for user {sx_user.tg_id}, error:{e}')
        return await sx_user.save()

class MessageSender:
    def __init__(self, bot_token:str, queue :RedisQueue, name:str):
        self.bot = Bot(token=bot_token)
        self.queue = queue
        self.name = name

    async def combine_message(self, message: dict) -> Tuple[int, str, int]:
        
        msg_type = message['msg_type']
        tg_id = message['tg_id']
        user_id = message['user_id']
        channel_id = message['channel_id']
        message_text = message['text']
        sx_user = await SXUserService.get_sx_user(tg_id)
        is_vip = await check_recharge_user(user_id=user_id)
        chat_cnt = await get_user_chat_count(user_id)
        if not sx_user:
            if is_vip:
                log.info(f"forward_msg: sx_user not found for tg_id={tg_id}，is_vip:{is_vip},chat_cnt:{chat_cnt},create new sx_user")
                sx_user = await SXUserService.create_sx_user(tg_id=tg_id,user_id=user_id,group_id=FORWAD_GROUP_ID,channel_id=channel_id,is_vip=is_vip)
            else:
                log.info(f"forward_msg: sx_user not found for tg_id={tg_id}，is_vip:{is_vip},chat_cnt:{chat_cnt},not create new sx_user,disable forward")
                return 0,"",0
        if is_vip and sx_user and sx_user.forum_topic_name.find("VIP用户") == -1:
            sx_user.forum_topic_name = f"VIP用户-用户c_{channel_id}:{tg_id}"
            sx_user = await SXUserService.update_sx_user_topic_name(sx_user)
        if sx_user and msg_type == 'text':
            if 't_balance' not in message:
                t_balance = await AccountService.get_total_balance(user_id)
            else:
                t_balance = message['t_balance']
            name = await get_u_all_name(user_id)
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            #发送用户状态信息
            u_s_msg = f"\n\n####{current_time}:用户状态信息: uid#{user_id},tg_id#{tg_id},channel_id#{channel_id},\n用户名:{name}\n💎 当前余额:{t_balance}"
                   # 拼接消息并截取
            full_message = message_text + u_s_msg
            if len(full_message) > MAX_MESSAGE_LENGTH:
                log.warning(f"forward_msg: message too long, cut it, len={len(full_message)}, message={full_message}")
                full_message = full_message[-MAX_MESSAGE_LENGTH:]
            return FORWAD_GROUP_ID, full_message, sx_user.message_thread_id
        else:
            log.warning(f"forward_msg error: sx_user {sx_user},not found for {tg_id},{user_id} message={message}")
            # raise Exception("Error in combine_message:{user_id},{tg_id},{channel_id}")
            return 0,"",0
          
    async def process_queue(self):
        while True:
            msg_queue = await self.queue.dequeue()
            if msg_queue and "message" in msg_queue:
                try:
                    log.info(f"Bot {self.name} got message: {msg_queue}")
                    chat_id,message,message_thread_id = await self.combine_message(msg_queue["message"])
                    # 如果chat_id为0，说明不需要发送消息,直接跳过
                    if chat_id == 0:
                        continue
                    await send_message(chat_id, message, message_thread_id)
                    await asyncio.sleep(TG_SEND_DEALY)  # 限制发送速率
                except Exception as e:
                    print(f"Error sending message with Bot {self.name}: {msg_queue},{e}")
            else:
                log.debug(f"Bot {self.queue} queue is empty")
                await asyncio.sleep(10)




