import logging
from persistence import redis_client
from persistence.models.models import User, UserAltProfiles, UserAltConfigStatus
from tortoise.expressions import F
from tortoise.transactions import in_transaction

log = logging.getLogger(__name__)


async def create_user_alt_profile(user_id: int, avatar: str, nickname: str, persona_setting: str):
    # 加锁，防止重复创建或者创建超过3个
    lock = redis_client.acquire_lock("create_new_user_alt_profile", str(user_id))
    if not lock:
        return None
    try:
        undeleted_profile_list = await UserAltProfiles.filter(
            user_id=user_id, status__not=UserAltConfigStatus.DELETED.value
        ).all()
        # 为空说明数据有问题，也直接返回None
        # 最多3个
        if not undeleted_profile_list or len(undeleted_profile_list) >= 3:
            return None
        
        # 默认未激活
        return await UserAltProfiles.create(
            user_id=user_id, avatar=avatar, nickname=nickname, persona_setting=persona_setting, status=UserAltConfigStatus.INACTIVE.value
        )
    except Exception as e:
        log.warning(f"create_new_user_alt_profile error: {e}")
        return None
    finally:
        redis_client.release_lock("create_new_user_alt_profile", str(user_id))


async def delete_user_alt_profile(user_id: int, alt_profile_id: int):
    try:
        undeleted_profile_list = await UserAltProfiles.filter(
            user_id=user_id, status__not=UserAltConfigStatus.DELETED.value
        ).order_by("id").all()
        if not undeleted_profile_list or len(undeleted_profile_list) <= 1:
            return False
        # id最小的不能删除(默认profile), 当前active状态的不能删除
        target_profile = None
        for index, profile in enumerate(undeleted_profile_list):
            if profile.id == alt_profile_id:
                if index == 0:
                    return False
                elif profile.status == UserAltConfigStatus.ACTIVE.value:
                    return False
                else:
                    target_profile = profile
                    break
        if not target_profile:
            return False
        target_profile.status = UserAltConfigStatus.DELETED.value
        await target_profile.save()
        return True
    except Exception as e:
        log.warning(f"delete_user_alt_profile error: {e}")
        return False

# 更新profile（不更改状态）
async def update_user_alt_profile(
    user_id: int, alt_profile_id: int, nickname: str | None, avatar: str | None, persona_setting: str | None
):
    try:
        target_profile = await UserAltProfiles.filter(
            id=alt_profile_id, user_id=user_id, status__not=UserAltConfigStatus.DELETED.value
        ).first()
        if not target_profile:
            return False
        if nickname:
            target_profile.nickname = nickname
        if avatar:
            target_profile.avatar = avatar
        if persona_setting:
            target_profile.persona_setting = persona_setting
        
        if target_profile.status == UserAltConfigStatus.ACTIVE.value:
            async with in_transaction():
                user = await User.filter(id=user_id).first()
                if not user:
                    return False
                if avatar:
                    user.avatar = avatar
                if nickname:
                    user.nickname = nickname
                await user.save()
                await target_profile.save()
        else:
            await target_profile.save()
        return True
    except Exception as e:
        log.warning(f"update_user_alt_profile error: {e}")
        return False

# 切换profile
async def active_user_alt_profile(user_id: int, alt_profile_id: int):
    try:
        profile_list = await UserAltProfiles.filter(
            user_id=user_id, status__not=UserAltConfigStatus.DELETED.value
        ).all()
        if not profile_list:
            return False
        target_profile = None
        current_activie_profile = None
        for profile in profile_list:
            if profile.id == alt_profile_id:
                target_profile = profile
            if profile.status == UserAltConfigStatus.ACTIVE.value:
                current_activie_profile = profile
        if not target_profile or not current_activie_profile:
            return False
        if target_profile.status == UserAltConfigStatus.ACTIVE.value:
            return True
        async with in_transaction():
            await User.filter(id=user_id).update(
                avatar=target_profile.avatar, nickname=target_profile.nickname
            )
            target_profile.status = UserAltConfigStatus.ACTIVE.value
            current_activie_profile.status = UserAltConfigStatus.INACTIVE.value
            await target_profile.save()
            await current_activie_profile.save()
            return True
    except Exception as e:
        log.warning(f"active_user_alt_profile error: {e}")
        return False

async def get_user_history_nicknames(user_id: int):
    profile_list = await UserAltProfiles.filter(user_id=user_id).all()
    if not profile_list:
        return []
    return [ profile.nickname for profile in profile_list ]

async def get_user_all_alt_profile(user_id: int):
    undeleted_profile_list = await UserAltProfiles.filter(user_id=user_id, status__not=UserAltConfigStatus.DELETED.value).order_by("id").all()
    if undeleted_profile_list:
        return undeleted_profile_list
    # 上线后第一次请求时，会走到下面的逻辑：初始化用户在该表的数据（把user表中该用户的nickname和avatar拿过来初始化）
    # 需要加锁，防止多次初始化
    lock = redis_client.acquire_lock("user_alt_profile", str(user_id))
    if not lock:
        return []
    try:
        user = await User.filter(id=user_id).first()
        if not user:
            return []  
        user_profile = await UserAltProfiles.create(
            user_id=user.id, avatar=user.avatar, nickname=user.nickname, status=UserAltConfigStatus.ACTIVE.value
        )
        undeleted_profile_list = [user_profile]
    except Exception as e:
        log.warning(f"create_user_default_alt_profile error: {e}")
        return []
    finally:
        redis_client.release_lock("user_alt_profile", str(user_id))
    return undeleted_profile_list

async def get_user_active_profile(user_id: int):
    active_profile = await UserAltProfiles.filter(
        user_id=user_id, status=UserAltConfigStatus.ACTIVE.value
    ).first()
    if not active_profile:
        return None
    return active_profile
    
