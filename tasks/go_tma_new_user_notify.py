import asyncio
from datetime import timedelta
import logging
import os
import mysql.connector
from mysql.connector.abstracts import MySQLConnectionAbstract
from mysql.connector.pooling import PooledMySQLConnection
from common import copywriting_templates
from services import tg_message_service
from utils import utils
from services.bot_services import chat_bot, send_go_tma_tip
from persistence.redis_client import redis_client

def get_chatbot_only_users(conn: PooledMySQLConnection | MySQLConnectionAbstract ) -> list[int]:
    cursor = conn.cursor()
    cursor.execute("SELECT uid, tg_id FROM tg_user WHERE created_at>(UTC_TIMESTAMP() - INTERVAL 1 HOUR) and created_at<(UTC_TIMESTAMP() - INTERVAL 15 MINUTE)")
    items = cursor.fetchall()
    res = {int(x[0]): int(x[1]) for x in items} # type: ignore
    uids = [str(x) for x in res.keys()]
    if len(uids) <= 0:
        logging.info('no chatbot only users found')
        return []
    uidsStr = f'({",".join(uids)})'
    cursor.execute("SELECT user_id FROM chat_history_statistic WHERE user_id in %s GROUP BY user_id HAVING COUNT(DISTINCT platform) = 1 AND MAX(platform) = 'CHAT_BOT'" % uidsStr)
    items = cursor.fetchall()
    bot_only_uids = [int(x[0]) for x in items] # type: ignore
    result = [res[x] for x in bot_only_uids]
    cursor.close()
    return result

async def send_messages(tg_ids: list[int]):
    for tid in tg_ids:
        key = f'go_tma_notify:{tid}'
        sent = redis_client.get(key)
        logging.info(f'sent {key}: {sent}')
        if sent is not None:
            continue
        await send_go_tma_tip(chat_bot, tid)
        redis_client.set(key, 'true')
        await asyncio.sleep(0.2)

async def tma_new_user_notify():
    connection_string = os.environ['MYSQL_SLAVE_URL']
    conn_params = utils.parse_mysql_connection_string(connection_string)
    with mysql.connector.connect(**conn_params) as conn:
        tg_ids = get_chatbot_only_users(conn)
        await send_messages(tg_ids)
