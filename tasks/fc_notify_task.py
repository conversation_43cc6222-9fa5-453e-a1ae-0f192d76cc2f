import asyncio
from datetime import UTC, datetime, timedelta
import logging
import os

from redis import Redis

from common.bot_common import Button, MessageTemplate
from persistence.models.models import User, UserBotActive
from services import bot_message_service, tg_config_service
from services.user_service import user_service
from utils import env_const

log = logging.getLogger(__name__)
redis_client = Redis.from_url(os.environ.get('REDIS_URL'))

f1_tip = '''你的专属 限时豪华奖励 还没领取？


还在犹豫？你的特权正在悄悄流失……💔

<b>⚠️ 仅限新用户！机会稍纵即逝，错过后悔一年！</b>
<b>💥 领取你的专属大礼！→ [立即充值]</b>
'''
f3_tip = '''你的幸运时刻正倒计时，千万别错过！ 3小时过去了，你还没有领取你的VIP级宠爱？💖


<b>小姐姐们等你宠爱，解锁属于你的专属特权！</b>

<b>⚠️ 机会只有一次，错过不再重来！</b>
<b>💥 点击领取专属特权！→ [立即充值]</b>
'''
f6_tip = '''倒计时加速！福利即将溜走！你知道吗？已经有超过78% 的用户在6小时内领取了他们的专属奖励！💎


<b>你是少数还没领取的幸运用户！千万别浪费这次机会！</b>

<b>⚠️ 再不领取，你的特权将彻底消失！</b>
<b>💥 [立即充值，解锁专属特权]</b>
'''
f12_tip = '''你的专属尊贵礼包，只剩最后12小时！你的VIP级豪华畅聊套餐还没领取？💎💬


<b>时间已过半！你的宠爱特权即将关闭！</b>

<b>⏳ 限时12小时，错过不再有！
💥 [立即充值，抢占专属名额]</b>
'''
f20_tip = '''> 最后冲刺！你的VIP级特权即将彻底消失！你还没有领取属于你的专属畅聊特权？小姐姐们等你太久了！💖

<b>📌 超值福利仅限新用户，错过真的等一年！
💥 [立即充值，解锁尊贵特权]</b>
'''
f23_tip = '''⚠️ 仅剩1小时！最后的专属福利倒计时！你的尊贵特权 即将彻底消失，充值立享聊天权益！💖

<b>💥 这可是史无前例的专属限时优惠！不充值 = 损失一个亿！
💥 点击领取最后的宠爱 → [立即充值]</b>
'''

async def process_user(user: User):
    user_active_list = await UserBotActive.filter(user_id=user.id).all()
    bot_ids = [b.bot_id for b in user_active_list]
    langs = await tg_config_service.get_bot_langs(bot_ids)
    if len([b for b in langs if b.startswith('en')]) > 0:
        return
    delta = datetime.now(UTC) - user.created_at
    hours = delta.total_seconds() / 3600
    hm = None
    tip = None
    match hours:
        case h if 1 <= h <= 3:
            hm = 1
            tip = f1_tip
        case h if 3 < h <= 6:
            hm = 3
            tip = f3_tip
        case h if 6 < h <= 12:
            hm = 6
            tip = f6_tip
        case h if 12 < h <= 20:
            hm = 12
            tip = f12_tip
        case h if 20 < h <= 23:
            hm = 20
            tip = f20_tip
        case h if 23 < h <= 24:
            hm = 23
            tip = f23_tip
        case _:
            return
    if hm is None:
        return
    notified = redis_client.smismember(f'fc_reward_notify_{hm}', str(user.id))[0]
    if notified:
        return
    charge_url = f'{env_const.TMA_DIRECT_URL}?startapp=e_eyJwIjoicGF5In0-u_1999'
    button = [Button(text = '立即充值', url=charge_url)]
    await bot_message_service.send_user_template_message(
        user,MessageTemplate(tips=tip, buttons=button),True
    )
    # message = await bot_services.send_message(text=tip, user=user,deleted=True)
    redis_client.sadd(f'fc_reward_notify_{hm}', str(user.id))

async def send_fc_reward_notify():
    users = await user_service.get_users_in_one_day()
    for user in users:
        try:
            await process_user(user)
        except Exception as e:
            logging.warning(f"send_fc_reward_notify failed, user_id={user.id}, error={e}")
        await asyncio.sleep(1)
