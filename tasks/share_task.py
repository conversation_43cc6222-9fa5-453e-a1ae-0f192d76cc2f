import logging
from common.common_constant import BotCategory
from persistence.models.models import UserRoleShare
from services import tg_config_service, user_share_service
from utils import env_const, tg_util


log = logging.getLogger(__name__)


async def check_content():

    # filter check_result 不为空或者为{}的数据
    check_list = (
        await UserRoleShare.filter()
        .only("id", "check_result", "title", "user_id")
        .all()
    )
    main_tmp = await tg_config_service.get_main_bot_by_category(
        category=BotCategory.TMA
    )
    for check in check_list:
        if check.check_result:
            continue
        try:
            log.info(
                f"check_content,share_id:{check.id},share_title:{check.title},user_id:{check.user_id}"
            )
            # 检查分享内容
            ret = await user_share_service.check_share_content(check.id)
            tg_util.send_message(
                {
                    "text": f"分享内容检查完成,分享ID:{check.id}",
                    "result": ret.model_dump(),
                    "url": f"https://t.me/{main_tmp.username}/tavern?startapp=sd_{check.id}",
                }
            )
        except Exception as e:
            log.error(f"check_content error,share_id:{check.id}: {e}")
            tg_util.send_message(
                {
                    "text": f"分享内容检查失败,分享ID:{check.id}",
                    "error": str(e),
                    "url": f"https://t.me/{main_tmp.username}/tavern?startapp=sd_{check.id}",
                }
            )
            continue
