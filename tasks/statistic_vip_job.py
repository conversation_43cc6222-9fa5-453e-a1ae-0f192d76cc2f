
from datetime import datetime, timedelta


import asyncio
import logging
from typing import List
from apscheduler.schedulers.blocking import BlockingScheduler
from apscheduler.schedulers.background import BackgroundScheduler
from dotenv import load_dotenv

from redis import Redis


logger = logging.getLogger(__name__)


from asyncio import sleep
from aiogram import Bo<PERSON>, Dispatcher, types
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

from tortoise.exceptions import DoesNotExist


from aiogram.utils.keyboard import InlineKeyboardBuilder
from telegram.helpers import mention_html
import asyncio
import csv
import os

from persistence.models.models import RechargeOrder,User,UserRegisterChannel,RechargeStatusEnum,USDTRechargeOrder,TelegramUser

from utils.redis_lock import RedisLock


load_dotenv()
#正式
C_VIP_JOB_BOT_TOKEN = os.environ.get('C_VIP_JOB_BOT_TOKEN')
C_VIP_CHAT_ID = int(os.environ.get('C_VIP_CHAT_ID'))
C_VIP_MESSAGE_THREAD_ID = int(os.environ.get('C_VIP_MESSAGE_THREAD_ID'))

# 初始化 Bot 和 Dispatcher
bot = Bot(token=C_VIP_JOB_BOT_TOKEN)
dp = Dispatcher(bot=bot)

#初始化redis
redis_client = Redis.from_url(os.environ.get('REDIS_URL'))
lock_key = "s_lock_vip_job"

logger.info(f"init vip job bot:{C_VIP_JOB_BOT_TOKEN},{C_VIP_CHAT_ID},{C_VIP_MESSAGE_THREAD_ID},{os.environ.get('REDIS_URL')}")
class StatisticVipInfo:
    def __init__(self, id, created_at, updated_at, uid, nick_name, tg_id, first_name, last_name, user_name, channel_id, inviter_user_id, joined_chat_type, start_role):
        self.id = id
        self.created_at = created_at
        self.updated_at = updated_at
        self.uid = uid
        self.nick_name = nick_name
        self.tg_id = tg_id
        self.first_name = first_name
        self.last_name = last_name
        self.user_name = user_name
        self.channel_id = channel_id
        self.inviter_user_id = inviter_user_id
        self.joined_chat_type = joined_chat_type
        self.start_role = start_role
        
    @staticmethod
    def get_field_names():
        return [
            'id',
            'created_at',
            'updated_at',
            'uid',
            'nick_name',
            'tg_id',
            'first_name',
            'last_name',
            'user_name',
            'channel_id',
            'inviter_user_id',
            'joined_chat_type',
            'start_role'
        ]

    def __str__(self):
        return f"id:{self.id}, created_at:{self.created_at}, updated_at:{self.updated_at}, uid:{self.uid}, nick_name:{self.nick_name}, tg_id:{self.tg_id}, first_name:{self.first_name}, last_name:{self.last_name}, user_name:{self.user_name}, channel_id:{self.channel_id}, inviter_user_id:{self.inviter_user_id}, joined_chat_type:{self.joined_chat_type}, start_role:{self.start_role}"


# 定义一个发送消息的函数
async def send_message(chat_id: int, text: str,reply_markup ,message_thread_id:int):
    await bot.send_message(chat_id=chat_id, text=text,reply_markup=reply_markup,message_thread_id=message_thread_id,parse_mode='HTML')
    await asyncio.sleep(3)  # 添加延迟以避免触发速率限制

async def send_message_block(chat_id: int, text: str):
    await bot.send_message(chat_id=chat_id, text=text)

async def get_vip_users(created_time:datetime)->List[StatisticVipInfo]:
    
    end_time = created_time + timedelta(days=1)
    orders = await RechargeOrder.filter(status = RechargeStatusEnum.SUCCEED,created_at__gte=created_time,created_at__lt=end_time,pay_fee__gt=0).values_list('user_id',flat=True)
    # usdt_orders = await USDTRechargeOrder.filter(status = RechargeStatusEnum.SUCCEED,created_at__gt=created_time,created_at__lt=end_time).values_list('user_id',flat=True)
    
    u_ids = set()
    if orders and len(orders)> 0:
        u_ids.update(orders)
    # if usdt_orders and len(usdt_orders) > 0:
    #     u_ids.update(usdt_orders)
    vip_user_list = []
    for user_id in u_ids:
        try:
            user = await User.get(id = user_id)
            tg_user = await TelegramUser.get(uid = user_id)
            user_register_channel = await UserRegisterChannel.get(user_id = user_id)
        except DoesNotExist as e:
            logger.error(f"get user error:{user_id} -- {e}")
            continue
        
        sta_info =  StatisticVipInfo(
            id=user.id,
            created_at=user.created_at,
            updated_at=user.updated_at,
            uid=user.id,
            nick_name=user.nickname,
            tg_id=tg_user.tg_id,
            first_name=tg_user.first_name,
            last_name=tg_user.last_name,
            user_name=tg_user.user_name,
            channel_id=user_register_channel.channel_id,
            inviter_user_id=user_register_channel.inviter_user_id,
            joined_chat_type=user_register_channel.joined_chat_type,
            start_role=user_register_channel.start_role
            
        )
        vip_user_list.append(sta_info)

    return vip_user_list
    
    
    
# 从数据库读取数据并生成 CSV 文件
async def generate_vip_csv():
    pass


def get_yesterday_str():
    # 获取当前日期
    today = datetime.now()
    yesterday = today - timedelta(days=1)

    # 将日期格式化为字符串
    yesterday_str = yesterday.strftime('%Y-%m-%d')
    
    return yesterday_str


# 任务的入口create_date是，%Y-%m-%d' 是日期格式字符串
async def cal_vip_job_daily(create_date):
    logger.info(f"start cal_vip_job_daily job:{C_VIP_CHAT_ID},{C_VIP_MESSAGE_THREAD_ID},{create_date}")
    
    create_date_time = datetime.strptime(create_date, '%Y-%m-%d')
    vip_users = await get_vip_users(create_date_time)
    # 生成 CSV 文件
    # await generate_vip_csv(vip_users)
    # 发送消息
    await send_message(C_VIP_CHAT_ID, f'{create_date}的付费用户信息：',None,C_VIP_MESSAGE_THREAD_ID)
    
    for vip_user in vip_users:
        platform_msg = f"用户id:#{vip_user.uid},channel_id:#{vip_user.channel_id},nickname:#{vip_user.nick_name},tg_id:#{vip_user.tg_id},tg_username:#{vip_user.user_name},f_l_name:#{vip_user.first_name} {vip_user.last_name},{mention_html(vip_user.tg_id, 'tg_id联系用户')}"
        message_text = platform_msg
        builder = InlineKeyboardBuilder()
        if vip_user.user_name:
            builder.add(InlineKeyboardButton(text=f"👤 直接联络:{vip_user.first_name} {vip_user.last_name}", url=f"https://t.me/{vip_user.user_name}"))
        await send_message(C_VIP_CHAT_ID, message_text, builder.as_markup(),C_VIP_MESSAGE_THREAD_ID)


async def run_cal_vip_job_daily():
    logger.info("run_cal_vip_job_daily,before lock")
    try:
        with RedisLock(redis_client, lock_key):
            logger.info("run_cal_vip_job_daily,get lock")
            create_date = get_yesterday_str()
            await cal_vip_job_daily(create_date)
    except TimeoutError as e:
        logger.warning(f"run_cal_vip_job_daily, get lock error:{e}")
        return
    except Exception as e:
        logger.error(f"run_cal_vip_job_daily, error:{e}")
        return
    
    

