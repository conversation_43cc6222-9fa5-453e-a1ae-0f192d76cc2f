from datetime import UTC, datetime
from itertools import groupby
import json
import logging
import os
import mysql.connector
from mysql.connector.abstracts import MySQLConnectionAbstract
from mysql.connector.pooling import PooledMySQLConnection
from pydantic import BaseModel
import pytz
from datetime import timezone
from persistence.models.models import RecallMessageHistory
from services.user_service import user_service
from services.bot_services import safe_send_message_to_all
from utils import utils

day7_tip = '您的金币还有7天到期，AI妹妹茶不思饭不香，一直在等你哦'

day3_tip = '您的金币还有3天到期，AI妹妹每日郁郁寡欢，就等你回来宠信啦'

class BalanceExpireInfo(BaseModel):
    user_id: int
    expires_at: datetime
    balance: int

def get_users_balance_expire(conn: PooledMySQLConnection | MySQLConnectionAbstract ) -> list[BalanceExpireInfo]:
    cursor = conn.cursor()
    cursor.execute("select user_id, expires_at, balance from expirable_award where expires_at>date_add(utc_timestamp(), INTERVAL 2 DAY) and expires_at<date_add(utc_timestamp(), INTERVAL 8 DAY) and balance>0 order by id desc")
    items = cursor.fetchall()
    res = [BalanceExpireInfo(user_id=x[0], expires_at=x[1].astimezone(UTC), balance=x[2]) for x in items] # type: ignore
    cursor.close()
    return res

async def check_and_send_messages(items: list[BalanceExpireInfo]):
    grouped_items = groupby(items, key=lambda x: x.user_id)
    for user_id, gitems in grouped_items:
        deltas = [(item.expires_at - datetime.now(UTC)).days for item in gitems]
        user = await user_service.safe_get_user(user_id)
        if not user:
            continue
        tip = None
        if 3 in deltas:
            tip = day3_tip
        elif 7 in deltas:
            tip = day7_tip

        if tip:
            send_result = await safe_send_message_to_all(tip, user)
            h = RecallMessageHistory(
                user_id=user.id,
                send_to_tma_success=send_result.tma_success,
                send_to_chatbot_success=send_result.chat_bot_success,
                tma_blocked=send_result.tma_blocked,
                chat_bot_blocked=send_result.chat_bot_blocked,
                tma_uninitiated=send_result.tma_uninitiated,
                chat_bot_uninitiated=send_result.chat_bot_uninitiated,
                content=tip,
                type='BALANCE',
                chatbot_error_msg=send_result.chat_bot_error,
                tma_error_msg=send_result.tma_error,
            )
            await h.save()

async def recall_expires():
    connection_string = os.environ['MYSQL_SLAVE_URL']
    conn_params = utils.parse_mysql_connection_string(connection_string)
    with mysql.connector.connect(**conn_params) as conn:
        items = get_users_balance_expire(conn)
        await check_and_send_messages(items)
