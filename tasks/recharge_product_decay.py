from datetime import datetime, <PERSON><PERSON>ta
import random
from typing import Literal
from persistence.redis_client import redis_client
from services import recharge_service
from utils import env_const

def get_decay_options_p1(current_hour: int):
    if 3 <= current_hour < 20:  # 凌晨3:00-19:59
        # 衰减6-9（6、7各40%，8、9各10%）
        decay_options = [6, 6, 6, 6, 7, 7, 7, 7, 8, 9]
    else:  # 20:00-凌晨02:59
        # 衰减6-9（6、7各10%，8、9各40%）
        decay_options = [6, 7, 8, 8, 8, 8, 9, 9, 9, 9]

    # 随机选择衰减值
    decay = random.choice(decay_options)
    return decay

def get_decay_options_p2(current_hour: int):
    # 根据时间段选择不同的衰减逻辑
    if 3 <= current_hour < 20:  # 凌晨3:00-19:59
        # 衰减1-3（1、2各45%，3概率10%）
        decay_options = [1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3]
    else:  # 20:00-凌晨02:59
        # 衰减1-3个（1、2各15%，3概率70%）
        decay_options = [1, 1, 1, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]

    # 随机选择衰减值
    decay = random.choice(decay_options)
    return decay

def do_decay(pid: str, type: Literal['P1', 'P2']):
    now = datetime.now()
    current_hour = now.hour
    date_str = now.strftime('%Y-%m-%d')
    rkey = f'decay_{pid}_{date_str}'
    decays = recharge_service.get_product_decays(pid)
    if len(decays) >= current_hour:
        return

    decay = get_decay_options_p1(current_hour) if type == 'P1' else get_decay_options_p2(current_hour)
    decays.append(str(decay))
    redis_client.set(rkey, ','.join(decays), ex=timedelta(days=2).seconds)

async def start_decay():
    recharge_products = await recharge_service.get_enabled_recharge_products()
    for p in recharge_products:
        pid = str(p.recharge_product_id)
        if pid == env_const.DECAY_RECHARGE_PRODUCT_1:
            do_decay(pid, 'P1')
        elif pid == env_const.DECAY_RECHARGE_PRODUCT_2:
            do_decay(pid, 'P2')
