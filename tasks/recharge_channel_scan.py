import os
import pandas as pd
import logging
from typing import List, Dict
import mysql.connector
from persistence.models.models import RechargeOrder
from utils import utils

connection_string = os.environ['MYSQL_SLAVE_URL']
conn_params = utils.parse_mysql_connection_string(connection_string)

class PaymentOrderMarker:
    def __init__(self, time_window_minutes: int = 15):
        """
        初始化订单标记器
        
        Args:
            time_window_minutes: 同一支付会话的时间窗口（分钟）
        """
        self.time_window_minutes = time_window_minutes
        
    def mark_orders(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        对订单数据进行标记
        
        Args:
            df: 包含订单数据的DataFrame
            
        Returns:
            标记后的DataFrame，新增以下列：
            - is_counted: True=统计项, False=排除项
            - exclude_reason: 排除原因
            - session_group: 会话分组ID
        """
        # 预处理数据
        df = self._preprocess_data(df.copy())
        
        # 初始化标记列
        df['is_counted'] = True  # 默认都计入统计
        df['exclude_reason'] = ''  # 排除原因
        df['session_group'] = ''  # 会话分组
        
        # 按用户分组处理
        for user_id in df['user_id'].unique():
            user_orders = df[df['user_id'] == user_id].copy()
            sessions = self._group_user_sessions(user_orders)
            
            for session_id, session_orders in enumerate(sessions):
                session_group_id = f"{user_id}_{session_id}"
                
                # 更新会话分组ID
                for order_id in session_orders['order_id']:
                    df.loc[df['order_id'] == order_id, 'session_group'] = session_group_id
                
                # 分析会话并标记订单
                marked_orders = self._analyze_and_mark_session(session_orders)
                
                # 更新主DataFrame
                for order_id, mark_info in marked_orders.items():
                    mask = df['order_id'] == order_id
                    df.loc[mask, 'is_counted'] = mark_info['is_counted']
                    df.loc[mask, 'exclude_reason'] = mark_info['exclude_reason']
        
        return df.sort_values('order_id', ascending=False)
    
    def _preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """预处理数据"""
        # 确保时间列是datetime类型
        if 'order_time' in df.columns:
            df['order_time'] = pd.to_datetime(df['order_time'])
        if 'success_time' in df.columns:
            df['success_time'] = pd.to_datetime(df['success_time'])
            
        # 按用户和时间排序
        df = df.sort_values(['user_id', 'order_time']).reset_index(drop=True)
        return df

    def _group_user_sessions(self, user_orders: pd.DataFrame) -> List[pd.DataFrame]:
        """将用户订单按时间窗口分组为不同的支付会话"""
        sessions = []
        current_session = []
        
        for idx, row in user_orders.iterrows():
            if not current_session:
                current_session = [row]
            else:
                # 检查与上一个订单的时间间隔
                last_order_time = pd.to_datetime(current_session[-1]['order_time'])
                current_order_time = pd.to_datetime(row['order_time'])
                time_diff_minutes = (current_order_time - last_order_time).total_seconds() / 60
                
                if time_diff_minutes <= self.time_window_minutes:
                    current_session.append(row)
                else:
                    # 开始新的会话
                    sessions.append(pd.DataFrame(current_session))
                    current_session = [row]
        
        if current_session:
            sessions.append(pd.DataFrame(current_session))
            
        return sessions

    def _analyze_and_mark_session(self, session_df: pd.DataFrame) -> Dict:
        """
        分析单个支付会话并标记每个订单
        
        Returns:
            Dict[order_id, {'is_counted': bool, 'exclude_reason': str}]
        """
        marked_orders = {}
        
        # 初始化所有订单为统计项
        for _, order in session_df.iterrows():
            marked_orders[order['order_id']] = {
                'is_counted': True,
                'exclude_reason': ''
            }

        # 获取成功订单
        success_orders = session_df[session_df['status'] == 'SUCCEED']

        if len(success_orders) > 0:
            # 有成功订单的情况
            final_success = success_orders.iloc[-1]  # 最后一个成功订单
            
            # 规则1：换支付方式成功 - 过滤成功前的失败订单
            self._mark_switched_payment_method(session_df, final_success, marked_orders)
            
            # 规则2：换金额后成功 - 过滤不同金额的失败订单  
            self._mark_changed_amount_orders(session_df, final_success, marked_orders)
            
            # 规则3：同渠道重复尝试 - 过滤同渠道之前的失败尝试
            self._mark_same_channel_retry(session_df, final_success, marked_orders)

            # 规则4：成功之后的尝试全部排除
            self._mark_attempts_after_success(session_df, final_success, marked_orders)

            # 规则6：标记渠道轮一圈的尝试
            self._mark_round_trip_attempts(session_df, final_success, marked_orders)

        else:
            # 无成功订单的情况
            # 规则5：所有渠道都失败 - 全部排除
            self._mark_all_channels_failed(session_df, marked_orders)

        self._mark_just_failed(session_df, marked_orders)

        return marked_orders

    def _mark_switched_payment_method(self, session_df: pd.DataFrame, final_success: pd.Series, marked_orders: Dict):
        """标记换支付方式的订单"""
        for _, order in session_df.iterrows():
            if (order['order_time'] < final_success['order_time'] 
                and order['status'] != 'SUCCEED' 
                and order['payment_method'] != final_success['payment_method']):
                marked_orders[order['order_id']] = {
                    'is_counted': False,
                    'exclude_reason': '换支付方式前的失败尝试'
                }

    def _mark_changed_amount_orders(self, session_df: pd.DataFrame, final_success: pd.Series, marked_orders: Dict):
        """标记换金额的订单"""
        if len(session_df['amount'].unique()) > 1:
            final_success_amount = final_success['amount']
            
            for _, order in session_df.iterrows():
                if (order['amount'] != final_success_amount and 
                    order['status'] != 'SUCCEED' and
                    marked_orders[order['order_id']]['is_counted']):  # 只处理还未被排除的订单
                    marked_orders[order['order_id']] = {
                        'is_counted': False,
                        'exclude_reason': '换金额后的无效尝试'
                    }
    
    def _mark_same_channel_retry(self, session_df: pd.DataFrame, final_success: pd.Series, marked_orders: Dict):
        """标记同渠道重复尝试的订单"""
        final_success_channel = final_success['channel']

        # 找到该渠道在成功前的失败尝试
        same_channel_failed = session_df[
            (session_df['channel'] == final_success_channel) &
            (session_df['order_time'] < final_success['order_time']) &
            (session_df['status'] != 'SUCCEED')
        ]

        for _, order in same_channel_failed.iterrows():
            if marked_orders[order['order_id']]['is_counted']:  # 只处理还未被排除的订单
                marked_orders[order['order_id']] = {
                    'is_counted': False,
                    'exclude_reason': '同渠道重复尝试'
                }

    def _mark_attempts_after_success(self, session_df: pd.DataFrame, final_success: pd.Series, marked_orders: Dict):
        """标记成功之后的所有尝试"""
        # 找到成功订单之后的所有订单
        attempts_after_success = session_df[
            session_df['order_time'] > final_success['order_time']
        ]

        for _, order in attempts_after_success.iterrows():
            if marked_orders[order['order_id']]['is_counted']:  # 只处理还未被排除的订单
                marked_orders[order['order_id']] = {
                    'is_counted': False,
                    'exclude_reason': '成功之后的无效尝试'
                }
    
    def _mark_all_channels_failed(self, session_df: pd.DataFrame, marked_orders: Dict):
        """标记所有渠道都失败的情况 - 针对不同支付方式单独处理"""
        # 按支付方式分组处理
        payment_methods = session_df['payment_method'].unique()

        for payment_method in payment_methods:
            payment_orders = session_df[session_df['payment_method'] == payment_method]
            channels_tried = payment_orders['channel'].unique()

            # 针对每种支付方式单独判断
            should_exclude_all = False
            exclude_reason = ''

            if len(channels_tried) >= 2:  # 该支付方式尝试了2个或更多渠道
                should_exclude_all = True
                exclude_reason = f'多渠道测试全部排除'

            if should_exclude_all:
                for _, order in payment_orders.iterrows():
                    marked_orders[order['order_id']] = {
                        'is_counted': False,
                        'exclude_reason': exclude_reason
                    }

    def _mark_just_failed(self, session_df: pd.DataFrame, marked_orders: Dict):
        """直接 FAILED 的订单单独报警"""
        for _, order in session_df.iterrows():
            if (order['status'] == 'FAILED'):
                marked_orders[order['order_id']] = {
                    'is_counted': False,
                    'exclude_reason': 'FAILED 订单单独报警处理'
                }

    def _mark_round_trip_attempts(self, session_df: pd.DataFrame, final_success: pd.Series, marked_orders: Dict):
        """
        规则6：标记渠道轮一圈的尝试
        
        当用户在多个渠道间尝试后，最终在某个渠道成功时，
        排除该渠道最后一次未成功尝试之前的所有尝试
        """
        final_success_channel = final_success['channel']
        final_success_payment_method = final_success['payment_method']
        
        # 找到最终成功渠道的所有尝试（按时间排序）
        same_channel_attempts = session_df[
            (session_df['channel'] == final_success_channel) &
            (session_df['payment_method'] == final_success_payment_method) &
            (session_df['order_time'] < final_success['order_time'])
        ].sort_values('order_time')
        
        # 如果该渠道之前有失败尝试，说明可能是轮一圈回来的
        if len(same_channel_attempts) > 0:
            # 找到该渠道最后一次未成功尝试的时间
            last_failed_time = same_channel_attempts.iloc[-1]['order_time']
            
            # 检查是否真的是轮一圈的场景：
            # 1. 在该渠道最后一次失败后，用户尝试了其他渠道
            # 2. 然后又回到这个渠道成功了
            other_channel_attempts = session_df[
                (session_df['channel'] != final_success_channel) &
                (session_df['order_time'] > last_failed_time) &
                (session_df['order_time'] < final_success['order_time'])
            ]
            
            # 如果确实有其他渠道的尝试，说明是轮一圈的场景
            if len(other_channel_attempts) > 0:
                # 排除该渠道最后一次失败之前的所有尝试
                for _, order in session_df.iterrows():
                    if (order['order_time'] < last_failed_time and 
                        marked_orders[order['order_id']]['is_counted']):  # 只处理还未被排除的订单
                        marked_orders[order['order_id']] = {
                            'is_counted': False,
                            'exclude_reason': '渠道一次循环前的尝试'
                        }

    def get_marking_summary(self, df: pd.DataFrame) -> Dict:
        """获取标记结果摘要"""
        marked_df = self.mark_orders(df) if 'is_counted' not in df.columns else df
        
        total_orders = len(marked_df)
        counted_orders = len(marked_df[marked_df['is_counted'] == True])
        excluded_orders = len(marked_df[marked_df['is_counted'] == False])
        
        # 按排除原因统计
        exclude_reasons = marked_df[marked_df['is_counted'] == False]['exclude_reason'].value_counts().to_dict()
        
        # 按渠道统计
        channel_stats = {}
        for channel in marked_df['channel'].unique():
            channel_data = marked_df[marked_df['channel'] == channel]
            channel_stats[channel] = {
                'total': len(channel_data),
                'counted': len(channel_data[channel_data['is_counted'] == True]),
                'excluded': len(channel_data[channel_data['is_counted'] == False])
            }
        
        return {
            'total_orders': total_orders,
            'counted_orders': counted_orders,
            'excluded_orders': excluded_orders,
            'exclude_reasons': exclude_reasons,
            'channel_stats': channel_stats
        }

async def start_scan():
    with mysql.connector.connect(**conn_params) as conn:
        sql = "SELECT id as order_id, user_id, amount, recharge_channel as channel, pay_type as payment_method, status, created_at as order_time FROM recharge_order where pay_fee>0 and recharge_channel not in('STAR_PAYMENT', 'USDT', 'STRIPE', 'PACKAGE_VOUCHER') AND created_at >= UTC_TIMESTAMP - INTERVAL 60 MINUTE and created_at <= UTC_TIMESTAMP - INTERVAL 5 MINUTE order by order_id desc"
        df = pd.read_sql(sql, conn)

    marker = PaymentOrderMarker(time_window_minutes=15)
    marked_df = marker.mark_orders(df)
    excludes = marked_df[marked_df['is_counted'] == False]

    for index, row in excludes.iterrows():
        logging.info(f"Order {row['order_id']} excluded from stat: {row['exclude_reason']}")
        order = await RechargeOrder.filter(id=row['order_id']).first()
        if order and order.status != 'SUCCEED' and order.exclude_reason != '渠道白名单':
            logging.info(f"Order {row['order_id']} update to db")
            order.exclude_from_stat = True
            order.exclude_reason = row['exclude_reason']
            await order.save()
