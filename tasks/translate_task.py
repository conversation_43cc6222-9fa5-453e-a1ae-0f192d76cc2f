import json
import logging
import os
import re
from common.common_constant import Language
from common.role_card import CharacterBook
from common.role_model import RoleDataConfig, SceneConfig
from common.translate_model import (
    TranslateTaskStatus,
    TranslateTaskType,
)
from persistence.models.models import TranslateTask
from services import translate_service
from tasks.translate import (
    trans_charbook_task,
    trans_group_task,
    trans_role_desc_task,
    trans_role_task,
    trans_sub_tag_task,
)
from utils import json_util, tg_util

log = logging.getLogger(__name__)


task_method_map = {
    TranslateTaskType.SUB_TAG.value: trans_sub_tag_task.run_sub_tag_task,
    TranslateTaskType.ROLE.value: trans_role_task.run_role_task,
    TranslateTaskType.CHAR_BOOK_KEYS.value: trans_charbook_task.run_char_book_keys_task,
    TranslateTaskType.GROUP.value: trans_group_task.run_group_task,
    TranslateTaskType.ROLE_DESCRIPTION.value: trans_role_desc_task.run_task,
}
init_task_list = [
    trans_sub_tag_task.init_sub_tags_task,
    trans_role_task.init_role_task,
    trans_charbook_task.init_role_book_task,
    trans_group_task.init_group_task,
    trans_role_desc_task.init_task,
]

async def run_retry_translate_task_by_id(task_id: int):
    task = await translate_service.get_task_by_id(task_id)
    if not task:
        log.error(f"task {task_id} not found")
        return False
    await translate_service.update_status_by_id(
        task.id, TranslateTaskStatus.PENDING.value
    )
    update_ret = await translate_service.update_processing(task.id, task.retry_times)
    if not update_ret:
        return False
    func = task_method_map[task.type](task)
    async def run_task():
        return await func
    target = await run_task()
    if not target:
        log.error(f"task {task.id} ran failed")
        await translate_service.reset_task(task.id)
        return False
    await translate_service.finish_task(task.id, target)
    return True

async def run_retry_task_by_language(task_id:int,language: str,all_language: bool = False):
    task = await translate_service.get_task_by_id(task_id)
    if not task:
        log.error(f"task {task_id} not found")
        return False
    await translate_service.update_status_by_id(
        task.id, TranslateTaskStatus.PENDING.value
    )
    task_languages = json_util.convert_to_list(task.languages)
    if language and language not in task_languages:
        task_languages.append(language)
        task.languages = task_languages
        await TranslateTask.filter(id=task.id).update(
            languages=task.languages
        )
    if all_language:
        task.languages = [lang.value for lang in Language]
        await TranslateTask.filter(id=task.id).update(
            languages=task.languages
        )
    update_ret = await translate_service.update_processing(task.id, task.retry_times)
    if not update_ret:
        return False
    func = task_method_map[task.type](task, language)
    async def run_task():
        return await func
    target = await run_task()
    if not target:
        log.error(f"task {task.id} ran failed")
        await translate_service.reset_task(task.id)
        return False
    await translate_service.finish_task(task.id, target)
    return True


async def run_translate_task():
    # init task
    for init_task in init_task_list:
        try:
            await init_task()
        except Exception as e:
            log.error(f"init task failed, error", e)
    # run task
    task_list: list[TranslateTask] = await translate_service.list_tasks_by_status(
        TranslateTaskStatus.PENDING.value
    )
    for task in task_list:
        if task.retry_times >= 3:
            log.error(f"task {task.id} ran failed")
            continue
        update_ret = await translate_service.update_processing(
            task.id, task.retry_times
        )
        if not update_ret:
            continue
        try:
            target = await task_method_map[task.type](task)
            if not target:
                log.error(f"task {task.id} ran failed")
                await translate_service.reset_task(task.id)
                continue
            await translate_service.finish_task(task.id, target)
        except Exception as e:
            log.error(f"task {task.id} ran failed, error: {e}")
            if task.retry_times < 2:
                await translate_service.reset_task(task.id)
            else:
                tg_util.send_message(
                    {
                        "title": "Translate Task Failed",
                        "content": f"task {task.id} ran failed",
                        "error": str(e),
                    }
                )
            await translate_service.error_task(task.id)
