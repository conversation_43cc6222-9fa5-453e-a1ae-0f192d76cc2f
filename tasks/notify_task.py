from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorCollection, AsyncIOMotorDatabase
from pymongo.errors import CollectionInvalid
import os, logging
from dotenv import load_dotenv
import telegram

load_dotenv()

logger = logging.getLogger(__name__)

mongo_url = os.getenv('MONGO_URL')

client = AsyncIOMotorClient(mongo_url)
db = client['tg_bot']

async def notify_user(chat_id: int, message: str, bot: telegram.Bot):
    await bot.send_message(chat_id, text=message)

async def find_inactive_users(role: str) -> list[int]:
    ...