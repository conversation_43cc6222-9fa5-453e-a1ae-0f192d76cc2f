import json
import logging
from persistence.models.models import ChannelDeliveryPlan, ChannelDeliveryPlanRelation
from properties import prop_util
from utils import json_util

log = logging.getLogger(__name__)


async def start():
    channel_plans = None
    new_relations = None
    try:
        channel_plans, new_relations = prop_util.load_channel_config_by_url()
    except Exception as e:
        log.error(f"load channel config error {e}")
    if not channel_plans or not new_relations:
        return

    # 更新channel_plan
    plan_map = {x.plan_id: x for x in await ChannelDeliveryPlan.all()}
    for channel_plan in channel_plans:
        if channel_plan.plan_id in plan_map:
            update_channel_plan = plan_map[channel_plan.plan_id]
            update_channel_plan.channel = channel_plan.channel
            update_channel_plan.ad_type = channel_plan.ad_type
            update_channel_plan.ad_position = channel_plan.ad_position
            update_channel_plan.start_at = channel_plan.start_at
            update_channel_plan.end_at = channel_plan.end_at
            update_channel_plan.sum_days = channel_plan.sum_days
            update_channel_plan.complete_days = channel_plan.complete_days
            update_channel_plan.pay_fee_usdt = channel_plan.pay_fee_usdt
            update_channel_plan.pay_fee = channel_plan.pay_fee
            update_channel_plan.pay_fee_day = channel_plan.pay_fee_day
            update_channel_plan.complete_pay_fee = channel_plan.complete_pay_fee
            update_channel_plan.remarks = channel_plan.remarks
            update_channel_plan.from_user_ids = channel_plan.from_user_ids
            await update_channel_plan.save()
            log.info(f"update channel plan plan_id:{channel_plan.plan_id}")
        else:
            await channel_plan.save()
            json_data = await json_util.model_to_json(channel_plan, ChannelDeliveryPlan)
            log.info(f"add channel plan {json_data}")

    # 更新channel_plan_relation
    old_relation_list = await ChannelDeliveryPlanRelation.all()
    old_relation_map = {}
    for relation in old_relation_list:
        if relation.plan_id not in old_relation_map:
            old_relation_map[relation.plan_id] = {}
        old_relation_map[relation.plan_id][relation.from_user_id] = relation

    new_relation_map = {}
    for relation in new_relations:
        if relation.plan_id not in new_relation_map:
            new_relation_map[relation.plan_id] = {}
        new_relation_map[relation.plan_id][relation.from_user_id] = relation

    # 删除不存在的relation
    for relation in old_relation_list:
        if (
            relation.plan_id not in new_relation_map
            or relation.from_user_id not in new_relation_map[relation.plan_id]
        ):
            log.info(f"delete channel plan relation plan_id:{relation.plan_id},from_user_id:{relation.from_user_id}")
            await relation.delete()
    # 新增
    for plan_id, mid_relation_map in new_relation_map.items():
        for from_user_id, relation in mid_relation_map.items():
            if plan_id in old_relation_map and from_user_id in old_relation_map[plan_id]:
                continue
            else:
                log.info(f"create channel plan relation {relation.plan_id},{relation.from_user_id}")
                await relation.save()
