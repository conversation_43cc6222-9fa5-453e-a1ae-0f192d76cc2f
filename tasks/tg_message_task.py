import asyncio
import logging
from aiogram.exceptions import TelegramAP<PERSON>rror,TelegramForbiddenError
from persistence.models.models import TgDeletedMessage
from services import bot_config_service, tg_config_service, tg_message_service
from services.bot_services import bot, chat_bot, helper_bots

log = logging.getLogger(__name__)


async def delete_tg_message():
    list_msg: list[TgDeletedMessage] = await tg_message_service.list_expired_message(100)
    for msg in list_msg:
        log.info(f"delete_tg_message: id={msg.id},user_id={msg.user_id},message_id={msg.message_id}")
        try:
            await tg_message_service.flag_deleted_message(msg.id)
            if msg.bot_id:
                sender_bot = await tg_config_service.get_sender_bot_by_id(msg.bot_id)
                await sender_bot.delete_message(msg.chat_id, msg.message_id)
        except Exception as e:
            log.warning(f"delete_tg_message {msg.id} failed: {e}")
        await asyncio.sleep(0.2)
