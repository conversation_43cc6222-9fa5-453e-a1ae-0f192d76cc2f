import asyncio
from datetime import UTC, datetime
import logging
import os
from urllib.parse import urlparse
from typing import Dict, <PERSON><PERSON>, Set
import mysql.connector
import pandas as pd
import json

from persistence.redis_client import redis_client
from utils import tg_util, utils

log = logging.getLogger(__name__)

monitor_chat_id = '-1002550423097'

connection_string = os.environ["MYSQL_SLAVE_URL"]
conn_params = utils.parse_mysql_connection_string(connection_string)

REDIS_DOMAIN_PREFIX = "recharge_domains"


def extract_domain(url: str) -> str:
    """从URL中提取域名"""
    if not url:
        return ""
    try:
        parsed = urlparse(url)
        return parsed.netloc.lower()
    except Exception as e:
        log.warning(f"Failed to parse URL {url}: {e}")
        return ""


def get_recent_orders() -> pd.DataFrame:
    """获取最近一小时内的alipay和wechat订单"""
    with mysql.connector.connect(**conn_params) as conn:
        sql = """
        SELECT 
            id,
            recharge_order_id,
            user_id,
            recharge_channel,
            pay_type,
            pay_url,
            pay_redirect_url,
            created_at
        FROM recharge_order 
        WHERE created_at >= UTC_TIMESTAMP() - INTERVAL 1 HOUR
            AND pay_type IN ('alipay', 'wechat')
        ORDER BY id DESC
        """

        df = pd.read_sql(sql, conn)
        df['created_at'] = pd.to_datetime(df['created_at']).dt.tz_localize('UTC').dt.tz_convert('Asia/Shanghai')
        return df

def group_orders_by_channel_and_pay_type(
    orders_df: pd.DataFrame,
) -> Dict[Tuple[str, str], pd.DataFrame]:
    """按channel和pay_type分组订单"""
    if orders_df.empty:
        return {}

    grouped = {}
    for (channel, pay_type), group_df in orders_df.groupby(
        ["recharge_channel", "pay_type"]
    ):
        grouped[(channel, pay_type)] = group_df.sort_values(
            "id", ascending=False
        )

    return grouped


def extract_domains_from_order_row(order_row) -> Tuple[str, str]:
    """从订单行中提取pay_url和pay_redirect_url的域名"""
    pay_url_domain = extract_domain(order_row["pay_url"])
    pay_redirect_url_domain = extract_domain(order_row["pay_redirect_url"])
    return pay_url_domain, pay_redirect_url_domain


def get_all_domains_in_group(orders_df: pd.DataFrame, url_type: str) -> Set[str]:
    """获取同一组订单中所有的域名"""
    domains = set()
    for _, order_row in orders_df.iterrows():
        if url_type == "pay_url":
            domain = extract_domain(order_row["pay_url"])
        else:  # pay_redirect_url
            domain = extract_domain(order_row["pay_redirect_url"])

        if domain:
            domains.add(domain)

    return domains

def get_redis_key(channel: str, pay_type: str, url_type: str) -> str:
    return f"{REDIS_DOMAIN_PREFIX}:{channel}:{pay_type}:{url_type}"

def get_historical_domains_from_redis(channel: str, pay_type: str, url_type: str) -> Set[str]:
    redis_key = get_redis_key(channel, pay_type, url_type)
    try:
        domains_json = redis_client.get(redis_key)
        if domains_json:
            if isinstance(domains_json, bytes):
                domains_json = domains_json.decode('utf-8')
            elif not isinstance(domains_json, str):
                domains_json = str(domains_json)
            domains_list = json.loads(domains_json)
            return set(domains_list)
        return set()
    except Exception as e:
        log.warning(f"Failed to get historical domains from Redis for key {redis_key}: {e}")
        return set()

def save_domains_to_redis(channel: str, pay_type: str, url_type: str, domains: Set[str]):
    if not domains:
        return

    redis_key = get_redis_key(channel, pay_type, url_type)
    try:
        existing_domains = get_historical_domains_from_redis(channel, pay_type, url_type)
        all_domains = existing_domains.union(domains)
        domains_json = json.dumps(list(all_domains))
        redis_client.setex(redis_key, 30 * 24 * 60 * 60, domains_json)
        log.info(f"Saved {len(all_domains)} domains to Redis for {channel}:{pay_type}:{url_type}")
    except Exception as e:
        log.error(f"Failed to save domains to Redis for key {redis_key}: {e}")

def add_domain_to_redis(channel: str, pay_type: str, url_type: str, domain: str):
    if not domain:
        return
    save_domains_to_redis(channel, pay_type, url_type, {domain})

async def check_domain_changes():
    log.info("Starting recharge domain monitoring task")

    try:
        # 获取最近一小时的订单
        orders_df = get_recent_orders()
        log.info(f"Found {len(orders_df)} orders in the last hour")

        if orders_df.empty:
            log.info("No orders found, skipping domain check")
            return

        # 按channel和pay_type分组
        grouped_orders = group_orders_by_channel_and_pay_type(orders_df)

        alerts = []

        for (channel, pay_type), channel_orders_df in grouped_orders.items():
            current_pay_url_domains = get_all_domains_in_group(channel_orders_df, "pay_url")
            current_pay_redirect_url_domains = get_all_domains_in_group(channel_orders_df, "pay_redirect_url")

            historical_pay_url_domains = get_historical_domains_from_redis(channel, pay_type, "pay_url")
            historical_pay_redirect_url_domains = get_historical_domains_from_redis(channel, pay_type, "pay_redirect_url")

            latest_order = channel_orders_df.iloc[0]
            latest_pay_url_domain, latest_pay_redirect_url_domain = extract_domains_from_order_row(latest_order)

            if latest_pay_url_domain:
                add_domain_to_redis(channel, pay_type, "pay_url", latest_pay_url_domain)

                if historical_pay_url_domains and latest_pay_url_domain not in historical_pay_url_domains:
                    alerts.append(
                        {
                            "类型": "支付URL域名变化",
                            "渠道": channel,
                            "支付方式": pay_type,
                            "新域名": latest_pay_url_domain,
                            "历史域名": ", ".join(sorted(historical_pay_url_domains)),
                            "订单ID": str(latest_order["recharge_order_id"]),
                            "订单时间": latest_order["created_at"].strftime(
                                "%Y-%m-%d %H:%M:%S"
                            ),
                            "URL类型": "pay_url",
                            "当前组订单数量": len(channel_orders_df),
                            "历史域名数量": len(historical_pay_url_domains),
                        }
                    )

            # 检查pay_redirect_url域名变化
            if latest_pay_redirect_url_domain:
                # 将当前域名保存到Redis
                add_domain_to_redis(channel, pay_type, "pay_redirect_url", latest_pay_redirect_url_domain)
                
                # 如果有历史域名且当前域名不在历史域名中，则报警
                if historical_pay_redirect_url_domains and latest_pay_redirect_url_domain not in historical_pay_redirect_url_domains:
                    alerts.append(
                        {
                            "类型": "重定向URL域名变化",
                            "渠道": channel,
                            "支付方式": pay_type,
                            "新域名": latest_pay_redirect_url_domain,
                            "历史域名": ", ".join(sorted(historical_pay_redirect_url_domains)),
                            "订单ID": str(latest_order["recharge_order_id"]),
                            "订单时间": latest_order["created_at"].strftime(
                                "%Y-%m-%d %H:%M:%S"
                            ),
                            "URL类型": "pay_redirect_url",
                            "当前组订单数量": len(channel_orders_df),
                            "历史域名数量": len(historical_pay_redirect_url_domains),
                        }
                    )

            if current_pay_url_domains:
                save_domains_to_redis(channel, pay_type, "pay_url", current_pay_url_domains)
            if current_pay_redirect_url_domains:
                save_domains_to_redis(channel, pay_type, "pay_redirect_url", current_pay_redirect_url_domains)

        if alerts:
            log.warning(f"Found {len(alerts)} domain changes")
            for alert in alerts:
                notification_key = f"domain_alert:{alert['订单ID']}"
                if not redis_client.exists(notification_key):
                    await tg_util.sm(alert, monitor_chat_id)
                    redis_client.setex(notification_key, 30 * 60, '1')
                    log.info(f"Sent domain change alert for order {alert['订单ID']}")
                else:
                    log.info(f"Domain change alert for order {alert['订单ID']} already sent, skipping")
        else:
            log.info("No domain changes detected")

        log.info("Recharge domain monitoring task completed")

    except Exception as e:
        log.error(f"Error in recharge domain monitoring task: {e}")

async def recharge_domain_monitor():
    """主任务函数"""
    await check_domain_changes()


if __name__ == "__main__":
    # 用于测试
    asyncio.run(recharge_domain_monitor())
