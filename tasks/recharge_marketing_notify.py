import asyncio
from datetime import UTC, datetime, timedelta
import logging

from common.bot_common import But<PERSON>, MessageTemplate
from common.common_constant import BotCategory
from persistence.models.models import TgBotConfig, User, UserBotActive
from services import bot_message_service, tg_config_service, recharge_service
from services.user_service import user_service
from services.recharge_marketing_service import RechargeMarketingService
from utils import env_const

log = logging.getLogger(__name__)

async def get_eligible_new_users() -> list[User]:
    """获取符合条件的新用户（注册≥24小时，充值=0）"""
    now = datetime.now(UTC)
    start_time = now - timedelta(days=5)  # 查询最近5天注册的用户
    end_time = now - timedelta(hours=24)  # 注册超过24小时
    
    # 获取注册时间符合条件的用户
    users = await User.filter(
        created_at__gte=start_time,
        created_at__lte=end_time
    ).all()
    
    # 过滤出未充值的用户
    eligible_users = []
    for user in users:
        has_recharged = await recharge_service.check_recharge_user(user.id)
        if not has_recharged:
            eligible_users.append(user)
    
    return eligible_users

async def get_eligible_low_recharge_users() -> list[User]:
    """获取符合条件的低充值用户（注册>72小时，0<充值<100元）"""
    now = datetime.now(UTC)
    start_time = now - timedelta(days=30)  # 查询最近30天注册的用户
    end_time = now - timedelta(hours=72)   # 注册超过72小时
    
    # 获取注册时间符合条件的用户
    users = await User.filter(
        created_at__gte=start_time,
        created_at__lte=end_time
    ).all()
    
    # 过滤出充值金额在0-100元之间的用户
    eligible_users = []
    for user in users:
        total_paid = await recharge_service.get_user_rmb_payed_fee(user.id)
        if 0 < total_paid < 100 * 100000:  # 转换为分
            eligible_users.append(user)
    
    return eligible_users

async def process_new_user(user: User):
    if await RechargeMarketingService.is_user_stopped(user.id, "new_user"):
        return

    # 检查用户是否已充值，如果已充值则停止推送
    has_recharged = await recharge_service.check_recharge_user(user.id)
    if has_recharged:
        await RechargeMarketingService.stop_user_notifications(user.id, "new_user")
        return

    day = await RechargeMarketingService.get_user_notification_day(user.id, "new_user")
    if day > 4:  # 超过4天不再推送
        return

    if not await RechargeMarketingService.should_send_notification(user.id, "new_user", day):
        return

    # 检查用户语言，跳过英文用户
    user_active_list = await UserBotActive.filter(user_id=user.id).all()
    bot_ids = [b.bot_id for b in user_active_list]
    langs = await tg_config_service.get_bot_langs(bot_ids)
    if len([b for b in langs if b.startswith('en')]) > 0:
        return

    if len(bot_ids) > 0:
        bot_config: TgBotConfig = await tg_config_service.get_bot_config_by_id(bot_ids[-1]) # type: ignore
    else:
        bot_config = await tg_config_service.get_main_bot_by_category(BotCategory.TMA)
    message_content = RechargeMarketingService.get_random_message("new_user")
    charge_url = f'{bot_config.url}/tavern?startapp=e_eyJwIjoicGF5In0'
    button = [Button(text='立即充值', url=charge_url)]

    try:
        await bot_message_service.send_user_template_message(
            user, MessageTemplate(tips=message_content, buttons=button), True
        )
        await RechargeMarketingService.mark_notification_sent(user.id, "new_user", day)
        RechargeMarketingService.increment_notification_count("new_user", datetime.now(UTC))
        log.info(f"Sent new user marketing message to user {user.id}, day {day}")
    except Exception as e:
        log.error(f"Failed to send new user marketing message to user {user.id}: {e}")

async def process_low_recharge_user(user: User):
    if await RechargeMarketingService.is_user_stopped(user.id, "low_recharge"):
        return

    # 检查用户充值金额是否仍在范围内
    total_paid = await recharge_service.get_user_rmb_payed_fee(user.id)
    if total_paid >= 100 * 100000:  # 如果充值超过100元，停止推送
        await RechargeMarketingService.stop_user_notifications(user.id, "low_recharge")
        return

    day = await RechargeMarketingService.get_user_notification_day(user.id, "low_recharge")
    if day > 3:  # 超过3天不再推送
        return

    if not await RechargeMarketingService.should_send_notification(user.id, "low_recharge", day):
        return

    # 检查用户语言，跳过英文用户
    user_active_list = await UserBotActive.filter(user_id=user.id).all()
    bot_ids = [b.bot_id for b in user_active_list]
    langs = await tg_config_service.get_bot_langs(bot_ids)
    if len([b for b in langs if b.startswith('en')]) > 0:
        return
    if len(bot_ids) > 0:
        bot_config: TgBotConfig = await tg_config_service.get_bot_config_by_id(bot_ids[-1])  # type: ignore
    else:
        bot_config = await tg_config_service.get_main_bot_by_category(BotCategory.TMA)
    message_content = RechargeMarketingService.get_random_message("low_recharge")
    charge_url = f'{bot_config.url}/tavern?startapp=e_eyJwIjoicGF5In0'
    button = [Button(text='立即充值', url=charge_url)]

    try:
        await bot_message_service.send_user_template_message(
            user, MessageTemplate(tips=message_content, buttons=button), True
        )
        await RechargeMarketingService.mark_notification_sent(user.id, "low_recharge", day)
        RechargeMarketingService.increment_notification_count("low_recharge", datetime.now(UTC))
        log.info(f"Sent low recharge user marketing message to user {user.id}, day {day}")
    except Exception as e:
        log.error(f"Failed to send low recharge user marketing message to user {user.id}: {e}")

async def start_recharge_marketing_notifications():
    log.info("Starting recharge marketing notifications task")

    try:
        new_users = await get_eligible_new_users()
        log.info(f"Found {len(new_users)} eligible new users")

        for user in new_users:
            try:
                await process_new_user(user)
                await asyncio.sleep(1)  # 避免发送过快
            except Exception as e:
                log.exception(f"Error processing new user {user.id}: {e}", stack_info=True)
    except Exception as e:
        log.error(f"Error getting eligible new users: {e}")

    try:
        low_recharge_users = await get_eligible_low_recharge_users()
        log.info(f"Found {len(low_recharge_users)} eligible low recharge users")

        for user in low_recharge_users:
            try:
                await process_low_recharge_user(user)
                await asyncio.sleep(1)  # 避免发送过快
            except Exception as e:
                log.exception(f"Error processing low recharge user {user.id}: {e}", stack_info=True)
    except Exception as e:
        log.error(f"Error getting eligible low recharge users: {e}")

    log.info("Completed recharge marketing notifications task")