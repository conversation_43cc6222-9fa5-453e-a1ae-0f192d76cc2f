import asyncio
from datetime import UTC, datetime, timedelta
import logging
import os
import mysql.connector
import pandas as pd

from common.bot_common import MessageTemplate
from persistence.models.models import User
from persistence.redis_client import redis_client
from services import bot_message_service
from services.user_service import user_service
from utils import utils

log = logging.getLogger(__name__)

connection_string = os.environ['MYSQL_SLAVE_URL']
conn_params = utils.parse_mysql_connection_string(connection_string)

RECHARGE_REMINDER_MESSAGE = '''您上一次的充值未完成，为了保持您继续聊天的体验，特此提醒：充值可在机器人bot完成 

https://t.me/hmcardbot?start=YLID26K9Z4CBV1PGQ0X7ES5TUWORJAFHN83MC_116
'''

def get_users_with_multiple_unpaid_orders():
    with mysql.connector.connect(**conn_params) as conn:
        sql = """
        SELECT 
            user_id,
            COUNT(*) as order_count,
            MIN(created_at) as first_order_time,
            MAX(created_at) as last_order_time
        FROM recharge_order 
        WHERE created_at >= UTC_TIMESTAMP() - INTERVAL 7 MINUTE
            AND status='INIT' AND pay_fee>0
        GROUP BY user_id
        HAVING COUNT(*) >= 2
        """
        
        df = pd.read_sql(sql, conn)
        delta = (datetime.now(UTC) - timedelta(minutes=25)).replace(tzinfo=None)
        return df[df['last_order_time'] >= delta]       

def has_been_notified(user_id: int) -> bool:
    redis_key = f"fkw_recharge_notify:{user_id}"
    return redis_client.exists(redis_key)

def mark_user_notified(user_id: int):
    redis_key = f"fkw_recharge_notify:{user_id}"
    redis_client.setex(redis_key, 30 * 60, "notified")

async def send_recharge_reminder(user: User):
    try:
        if has_been_notified(user.id):
            log.info(f"User {user.id} already notified, skipping")
            return False
        
        await bot_message_service.send_user_template_message(
            user, 
            MessageTemplate(tips=RECHARGE_REMINDER_MESSAGE), 
            True
        )
        
        mark_user_notified(user.id)
        
        log.info(f"Sent recharge reminder to user {user.id}")
        return True
        
    except Exception as e:
        log.error(f"Failed to send recharge reminder to user {user.id}: {e}")
        return False

async def fkw_recharge_notify():
    log.info("Starting FKW recharge notification task")
    
    try:
        eligible_df = get_users_with_multiple_unpaid_orders()
        log.info(f"Found {len(eligible_df)} users with multiple unpaid orders meeting criteria")
        
        success_count = 0
        for _, row in eligible_df.iterrows():
            try:
                user_id = int(row['user_id'])

                user = await user_service.get_user_by_id(user_id)
                if not user:
                    log.warning(f"User {user_id} not found")
                    continue

                success = await send_recharge_reminder(user)
                if success:
                    success_count += 1

                await asyncio.sleep(0.5)

            except Exception as e:
                log.exception(f"Error processing user {row['user_id']}: {e}")
        
        log.info(f"FKW recharge notification task completed. Sent {success_count}/{len(eligible_df)} notifications")
        
    except Exception as e:
        log.error(f"Error in FKW recharge notification task: {e}")
