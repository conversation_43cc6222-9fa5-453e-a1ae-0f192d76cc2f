import logging
import random
from common.common_constant import ChatModeType
from services import role_statistic_service, user_like_service
from services.role import role_loader_service

log = logging.getLogger(__name__)

# 定时任务动态加赞（目前不支持群聊卡）
async def incr_role_like_count():
    role_list = await role_loader_service.list_public_with_fields(["id"])
    role_hot_map: dict[int, int] = await role_statistic_service.role_hot_all()
    sorted_roles = sorted(role_hot_map.items(), key=lambda item: item[1], reverse=True) 
    role_rank_map = {item[0]: index for index, item in enumerate(sorted_roles)}
    for role in role_list:
        role_id = role.id
        rank = role_rank_map.get(role_id, -1)
        if rank >= 0 and rank < 20:
            count = random.randint(450, 500)
        elif rank < 50:
            count = random.randint(350, 400)
        elif rank < 100:
            count = random.randint(250, 300)
        elif rank < 200:
            count = random.randint(150, 200)
        elif rank < 300:
            count = random.randint(80, 100)
        else:
            count = random.randint(20, 50)    
        await user_like_service.add_like_count_randomly(ChatModeType.SINGLE.value, role_id, count)
       


