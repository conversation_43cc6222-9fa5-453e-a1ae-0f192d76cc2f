
from datetime import datetime, timedelta
import logging
from services.bot_group.biz.tg_group_user_msg_biz import GroupMsgUserStatBiz,UserInviteShareStatBiz

from persistence.models.models_bot_group import BotGroupMap

from services.bot_group.group_msg_stat_service import GroupMsgStatService

CAL_GROUP_ID_LIST = [-1002380176119,-1002357871790, -1002411997003]

log = logging.getLogger(__name__)


async def cal_g_user_hours_msg_task():
    log.info("cal_g_user_hours_msg_task start")
    
    group_list = await BotGroupMap.filter(bot_type="group_help").all()
    
    for group in group_list:
        await GroupMsgUserStatBiz.cal_user_last_hour_msg_stat(group.group_id)
    
    log.info("cal_g_user_hours_msg_task end")

async def cal_user_invite_share_task():

    date_now = datetime.now()
    log.info("cal_g_user_invite_share_task start, date_now: %s", date_now)
    await UserInviteShareStatBiz.cal_user_invite_cnt(date_now)
    
    log.info("cal_g_user_invite_share_task end")

async def cal_user_share_stat_task():
    date_now = datetime.now()
    log.info("cal_user_share_stat_task start, date: %s", date_now)
    await UserInviteShareStatBiz.cal_user_share_cnt(date_now)
    log.info("cal_user_share_stat_task end")

#发送分享统计
async def send_last_day_share_stat_task():
    date_yesterday = datetime.now().date() - timedelta(days=1)
    log.info("send_share_stat_task start, date: %s", date_yesterday)
    await GroupMsgStatService.send_share_stat_to_bot(date_yesterday)
    log.info("send_share_stat_task end")

async def send_last_day_invite_stat_task():
    date_yesterday = datetime.now().date() - timedelta(days=1)
    log.info("send_last_day_invite_stat_task start, date: %s", date_yesterday)
    await GroupMsgStatService.send_invite_stat_to_bot(date_yesterday)
    log.info("send_last_day_invite_stat_task end")


#
async def send_last_day_msg_stat_task():
    date_last_day = datetime.now().date() - timedelta(days=1)
    log.info("send_today_msg_stat_task start, date: %s", date_last_day)
    group_list = await BotGroupMap.filter(bot_type="group_help").all()
    for group in group_list:
        await GroupMsgStatService.send_group_msg_stat_to_bot(group.group_id,date_last_day)
    log.info("send_today_msg_stat_task end")

async def send_last_hour_msg_stat_task():
    date_cal = datetime.now().date()
    date_hour = datetime.now().hour
    
    if date_hour == 0:
        date_cal = date_cal - timedelta(days=1)
        date_hour = 23
    else:
        date_hour = date_hour - 1
    log.info(f"send_last_hour_msg_stat_task start, {date_cal} {date_hour}")
    
    group_list = await BotGroupMap.filter(bot_type="group_help").all()
    for group in group_list:
        await GroupMsgStatService.send_group_hour_msg_stat_to_bot(group.group_id,date_cal,date_hour)
    log.info("send_last_hour_msg_stat_task end")

