import asyncio
import logging
import random
from typing import Op<PERSON>, <PERSON><PERSON>

from aiogram import Bo<PERSON>

# from aiogram import LoggingMiddleware
from aiogram.types import (
    InlineKeyboardButton,
    InlineKeyboardMarkup,
)
from aiogram.utils.keyboard import Inline<PERSON>eyboardBuilder

from services.img_service import ImageBotService


from services.img_bot.img_review_service import ImageReviewService
from persistence.models.models_bot_image import ForwardImageBot

from aibot_handlers.image_server import image_style_dict

CAL_GROUP_ID_LIST = [-1002380176119, -1002357871790, -1002411997003]

log = logging.getLogger(__name__)


def create_copy_prompt_button(bot_username: str, task_id: int) -> InlineKeyboardMarkup:

    copy_prompt_str = f"copy_prompt_{task_id}"
    log.info(f"create_copy_prompt_button: {bot_username},{copy_prompt_str}")
    # 创建键盘并添加按钮
    copy_prompt_keyboard = InlineKeyboardBuilder()
    # 创建一个跳转按钮
    button = InlineKeyboardButton(
        text="偷走咒语☔️",
        url=f"https://t.me/{bot_username}?start={copy_prompt_str}",  # 指定目标 Bot 的链接
    )
    copy_prompt_keyboard.add(button)
    return copy_prompt_keyboard.as_markup()


async def send_photo_to_share_group(
    bot: Bot, photo_url: str, style: str, task_id: int, bot_username: str
) -> int:
    log.info(
        f"send_photo_to_share_group: {bot_username}, {photo_url}, {style}, {task_id}, {bot.id}"
    )
    sent_id = 0
    try:

        style_txt = image_style_dict.get(style, "未知风格")
        
        replay_markup = ImageBotService.create_share_group_photo_button_markup(
            bot_username=bot_username, style_txt=style_txt, task_id=task_id
        )
        
        # 获取群组和话题ID

        group_id, topic_id = await ImageBotService.get_share_group_topic(bot.id, style)
        sent_msg = await bot.send_photo(
            chat_id=group_id,
            message_thread_id=topic_id,
            photo=photo_url,
            protect_content=True,
            reply_markup=replay_markup,
        )

        if sent_msg:
            sent_id = sent_msg.message_id
            log.info(
                f"send_photo_to_share_group: {sent_msg.message_id},{group_id},{topic_id}"
            )
        sleep_time = random.randint(3, 7)
        log.info(f"Sleeping for {sleep_time} seconds before next operation")
        await asyncio.sleep(sleep_time)
        return sent_id
    except Exception as e:
        log.error(f"send_image error: {e},{group_id},{topic_id}", exc_info=True)

        return 0


async def get_ff_bot_random() -> Tuple[Optional[Bot], str]:
    f_img_bot_list = await ForwardImageBot.all()
    if not f_img_bot_list:
        log.warning("No ForwardImageBot available")
        return None, ""
    selected_bot = random.choice(f_img_bot_list)
    bot = Bot(token=selected_bot.bot_token)
    log.info(f"Selected bot: {selected_bot.img_bot_username}")
    return bot, selected_bot.img_bot_username


# 定时发送审核图片到群里
async def send_photo_to_share_group_task():
    log.info("send_photo_to_share_group_task start")

    send_tasks = await ImageReviewService.get_approved_to_send_review_tasks(limit=10)
    if not send_tasks:
        log.info("No approved review tasks found")
        return

    bot, bot_username = await get_ff_bot_random()
    if bot is None:
        log.error("No available bot found for sending images")
        return
    try:
        for task in send_tasks:

            log.info(f"Processing task: {task.id}, {task.task_id}, {task.image_url}")
            img_task = await ImageBotService.get_img_gen_task_by_id(task.task_id)
            if not img_task:
                log.warning(
                    f"Image generation task not found for task_id: {task.task_id}"
                )
                continue

            style = img_task.req_json.get("style", "image_style_1")  # type: ignore
            image_url = img_task.gen_result.get("image_url", "")  # type: ignore

            send_ack = await send_photo_to_share_group(
                bot=bot,
                photo_url=image_url,
                style=style,
                task_id=task.task_id,
                bot_username=bot_username,
            )

            if send_ack > 0:
                # 更新审核任务状态为已发送
                await ImageReviewService.update_review_task_send_ack(
                    review_id=task.id,
                    send_id=send_ack,
                )
            log.info(f"Task {task.id} sent successfully{send_ack}")

    finally:
        await bot.session.close()
        log.info("Bot session closed successfully")
    log.info("send_photo_to_share_group_task end")
