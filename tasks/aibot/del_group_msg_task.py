
from datetime import datetime, timedelta, timezone
import logging
from services.bot_group.biz.tg_group_user_msg_biz import GroupMsgUserStatBiz,UserInviteShareStatBiz

from persistence.models.models_bot_group import TgGroupMessage,BotMessageTypeEnum,DeleteGroupMsgImageCfg,DeleteGroupMsgDetail

from services.bot_group.group_msg_stat_service import GroupMsgStatService

from services.bot_group.biz.tg_group_user_msg_biz import FFBotMsgBiz

DEL_GROUP_ID_LIST = [-1002380176119,-1002357871790, -1002411997003]

log = logging.getLogger(__name__)

PHOTO_TYPE = [BotMessageTypeEnum.PHOTO,BotMessageTypeEnum.VIDEO,BotMessageTypeEnum.ANIMATION,BotMessageTypeEnum.STICKER]

async def load_group_photo_msg_task():
    log.info("load_group_photo_msg_task start")
    
    cfg_list = await DeleteGroupMsgImageCfg.filter(is_enable=True).all()
    # 获取当前时间的 UTC 时间戳
    
    for image_cfg in cfg_list:
        group_id = image_cfg.group_id
        skip_tg_ids = image_cfg.skip_tg_ids
        c_utc_time_last_5min = datetime.now(timezone.utc).replace(tzinfo=None) - timedelta(minutes=5)

        log.info(f"load_group_photo_msg_task: {image_cfg},{c_utc_time_last_5min}")
        photo_msg_list = await TgGroupMessage.filter(group_id=group_id).filter(created_at__gt=c_utc_time_last_5min).filter(message_type__in=PHOTO_TYPE,is_deleted=False).all()
        
        for photo_msg in photo_msg_list:
            group_id = photo_msg.group_id
            message_id = photo_msg.message_id
            uid = photo_msg.user_id
            d_utc_time = photo_msg.created_at + timedelta(minutes=image_cfg.delete_time)
            log.info(f"load_group_photo_msg_task: {group_id},{message_id},{d_utc_time}")
            if uid in skip_tg_ids:
                log.info(f"load_group_photo_msg_task: skip {group_id},{message_id},{uid}")
                continue
            await DeleteGroupMsgDetail.update_or_create(group_id=group_id,msg_id=message_id,defaults={"delete_time":d_utc_time})
            # del_r = await FFBotMsgBiz.del_group_msg(group_id,message_id)
        
    log.info("load_group_photo_msg_task end")

async def del_group_photo_msg_task():
    log.info("del_group_photo_msg_task start")
    
    c_utc_time = datetime.now(timezone.utc).replace(tzinfo=None)
    photo_msg_list = await DeleteGroupMsgDetail.filter(delete_time__lt=c_utc_time).filter(delete_result=False).filter(exe_cnt__lt=2).all()
    
    for photo_msg in photo_msg_list:
        group_id = photo_msg.group_id
        message_id = photo_msg.msg_id
        log.info(f"del_group_photo_msg_task: {group_id},{message_id}")
        del_r = await FFBotMsgBiz.del_group_msg(group_id,message_id)
        if del_r:
            await DeleteGroupMsgDetail.filter(group_id=group_id,msg_id=message_id).update(delete_result=True)
            await TgGroupMessage.filter(group_id=group_id,message_id=message_id).update(is_deleted=True)
        else:
            await DeleteGroupMsgDetail.filter(group_id=group_id,msg_id=message_id).update(exe_cnt=photo_msg.exe_cnt+1)
        
        log.info(f"del_group_photo_msg_task: {group_id},{message_id},{del_r}")