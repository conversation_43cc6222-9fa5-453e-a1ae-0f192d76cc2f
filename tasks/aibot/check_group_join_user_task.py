
from datetime import datetime, timedelta
import logging

from services.bot_group.bot_group_service import BgGroupUserSevrvice

from persistence.models.models_bot_group import BotGroupFeatureConfig

GROUP_JOIN_CHECK_REG = "group_join_check_reg"


log = logging.getLogger(__name__)

CAL_GROUP_ID_LIST = [-1002380176119,-1002357871790, -1002411997003, -1002410058975]

async def check_group_join_user_task():
    log.info("check_group_join_user_task start")
    
    # 获取所有的群组
    for group_id in CAL_GROUP_ID_LIST:
        log.info(f"Processing group_id: {group_id}")
        # 检查群组是否开启了新用户加入检测的功能
        if not await _check_group_join_feature_config(group_id):
            log.info(f"Group {group_id} does not have join user check feature enabled, skipping.")
            continue
        await BgGroupUserSevrvice.process_join_group_user(group_id)
    log.info("check_group_join_user_task end")

async def _check_group_join_feature_config(group_id: int)-> bool:
    """
    检查群组是否开启了新用户加入检测的功能
    """
    feature_config = await BotGroupFeatureConfig.get_or_none(group_id=group_id,key_name=GROUP_JOIN_CHECK_REG)
    if not feature_config:
        return False
    return feature_config.enabled
