from datetime import UTC, datetime, timedelta
import logging
from services import out_recharge_common
from services.recharge_channel_service import (
    all_channels,
    CHANNEL_STATUS_AVAILABLE,
    CHANNEL_STATUS_RETRIABLE,
    CHANNEL_STATUS_UNAVAILABLE,
    get_channel_retry_time,
    get_channel_status,
    set_channel_retry_time,
    set_channel_status,
    update_channel_success_rate,
)
from persistence.models.models import RechargeChannelEnum
from utils import tg_util

async def process_channel(channel: str):
    now = datetime.now(UTC)
    orders = await out_recharge_common.get_recent_channel_orders(channel, 15)
    succeed_orders = [
        order for order in orders if order.status == out_recharge_common.RechargeStatusEnum.SUCCEED
    ]

    if len(succeed_orders) >= 4:
        set_channel_status(channel, CHANNEL_STATUS_AVAILABLE)
        return

    current_status = get_channel_status(channel)

    # 检查通道是否需要重试
    if current_status == CHANNEL_STATUS_RETRIABLE:
        # 重试成功 (有至少一个成功订单)，标记为可用
        if len(succeed_orders) > 0:
            set_channel_status(channel, CHANNEL_STATUS_AVAILABLE)
            return
        retry_time = get_channel_retry_time(channel)
        if retry_time and len(orders) > 0 and orders[0].created_at > retry_time:
            # 重试失败，标记为不可用
            set_channel_status(channel, CHANNEL_STATUS_UNAVAILABLE)
        return

    r5_orders = orders[:5]
    # 检查失败订单
    failed_orders = [
        order for order in r5_orders if order.status == out_recharge_common.RechargeStatusEnum.FAILED
    ]
    if len(failed_orders) >= 4 and current_status == CHANNEL_STATUS_AVAILABLE:
        # 4/5订单失败，标记为不可用
        set_channel_status(channel, CHANNEL_STATUS_UNAVAILABLE)
        # 设置5分钟后重试
        retry_time = now + timedelta(minutes=5)
        set_channel_retry_time(channel, retry_time)
        await tg_util.send_monitor({'渠道变更': channel, '状态': '不可用', '类型': '最近 5 个订单 4 个失败'}, prod_only=True)
        return

    # 检查初始化订单
    init_orders = [
        order for order in orders if order.status == out_recharge_common.RechargeStatusEnum.INIT
    ]
    if len(init_orders) >= 12 and current_status == CHANNEL_STATUS_AVAILABLE:
        # 12/15订单处于初始状态，标记为不可用
        set_channel_status(channel, CHANNEL_STATUS_UNAVAILABLE)
        # 设置30分钟后重试
        retry_time = now + timedelta(minutes=30)
        set_channel_retry_time(channel, retry_time)
        await tg_util.send_monitor({'渠道变更': channel, '状态': '不可用', '类型': '最近 15 个订单 12 个未支付'}, prod_only=True)
        return

async def check_channels():
    channels = all_channels
    now = datetime.now(UTC)

    for channel in channels:
        status = get_channel_status(channel)

        # 如果通道状态为不可用，检查是否达到重试时间
        if status == CHANNEL_STATUS_UNAVAILABLE:
            retry_time = get_channel_retry_time(channel)
            if retry_time and now >= retry_time:
                # 设置为可重试状态
                set_channel_status(channel, CHANNEL_STATUS_RETRIABLE)
                continue

        # 处理每个通道
        await process_channel(channel)

        # 更新渠道成功率统计
        try:
            await update_channel_success_rate(channel.value)
        except Exception as e:
            logging.exception(e)
            continue