from datetime import datetime
import json
import logging
import re
from ai import inner_bot, new_chat_bot
from common.common_constant import Dfy<PERSON><PERSON><PERSON><PERSON>, Language
from common.role_model import RoleDataConfig
from common.translate_model import (
    TranslateRoleDescription,
    TranslateTaskStatus,
    TranslateTaskType,
)
from persistence.models.models import TranslateTask
from services import translate_service
from services.role import role_loader_service
from utils import date_util, env_util, json_util, token_util, translate_util

log = logging.getLogger(__name__)


async def init_task():
    # init roles
    role_task_list = await translate_service.list_tasks_by_type(
        TranslateTaskType.ROLE_DESCRIPTION.value
    )
    role_task_map = {x.task_key: x for x in role_task_list}
    role_configs = await role_loader_service.list_public()
    for role in role_configs:
        if not role.switch_en_desc:
            continue
        role_task = role_task_map.get(str(role.id), None)
        if role_task and role_task.updated_at > role.updated_at:
            continue
        role_data_config = RoleDataConfig(**json_util.convert_to_dict(role.data_config))
        if len(role_data_config.description) <= 10:
            continue
        desc_lang = translate_util.detect_language(role_data_config.description)
        if desc_lang == Language.EN:
            continue
        role_desc = TranslateRoleDescription(description=role_data_config.description)
        langs = [Language.EN.value]

        if (
            role_task
            and role_task.source == role_desc.model_dump()
            and role_task.languages == langs
        ):
            log.info(f"translate source same,role_id: {role.id}")
            continue
        log.info(f"init task,role_id: {role.id}")
        await translate_service.upsert_task(
            TranslateTaskType.ROLE_DESCRIPTION.value,
            str(role.id),
            role_desc.model_dump(),
            langs,
        )


async def init_and_run_role_task_single(role_id: int, init_task: bool):
    # init roles
    role = await role_loader_service.get_by_id(role_id)
    role_data_config = RoleDataConfig(**json_util.convert_to_dict(role.data_config))
    role_desc = TranslateRoleDescription(description=role_data_config.description)
    langs = [Language.EN.value]
    task = None
    if init_task:
        task = await translate_service.upsert_task(
            TranslateTaskType.ROLE_DESCRIPTION.value,
            str(role.id),
            role_desc.model_dump(),
            langs,
        )
    else:
        task = await translate_service.get_task(
            TranslateTaskType.ROLE_DESCRIPTION.value, str(role.id)
        )
    if not task:
        return None
    await translate_service.update_processing(task.id, 0)
    target = await run_task(task)
    if not target:
        await translate_service.reset_task(task.id)
        return None
    await translate_service.finish_task(task.id, target)
    return target


async def run_task(task: TranslateTask,language: str = ""):
    role_desc = TranslateRoleDescription(**json_util.convert_to_dict(task.source))
    inputs = {"env_location": env_util.get_admin_api_domain()}
    res_content = await new_chat_bot.dfy_run_task(
        task.task_key, DfyApiKey.ROLE_DESC_TRANSLATE.value, inputs
    )
    if not res_content or "<translate_result>" not in res_content:
        return None
    # 获取<translate_result>标签中的内容
    desc = re.findall(r"<translate_result>(.*?)</translate_result>", res_content, re.S)
    if not desc:
        return None
    out_put_terms = re.findall(r"<output_terms>(.*?)</output_terms>", res_content, re.S)
    text_guide = re.findall(r"<test_guide>(.*?)</test_guide>", res_content, re.S)
    orig_token = token_util.num_tokens_from_string(role_desc.description)
    new_token = token_util.num_tokens_from_string(desc[0])
    log.info("run_task token diff: orig:%s,new:%s", orig_token, new_token)
    mid_ret = TranslateRoleDescription(
        description=desc[0],
        terms=str(out_put_terms[0]).split("\n") if out_put_terms else [],
        text_guide=text_guide[0] if text_guide else "",
    )
    target = {}
    target[Language.EN.value] = mid_ret.model_dump()
    return target
