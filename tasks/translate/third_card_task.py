# import logging
# from common.role_card import ThirdCardInfo, ThirdCardStatus, ThirdPlatform
# from persistence import char_book_dao, third_card_dao
# from services.role import (
#     character_book_service,
#     role_loader_service,
#     third_card_service,
# )
# from utils import token_util


# log = logging.getLogger(__name__)


# async def start_task():
#     third_card_list: list[ThirdCardInfo] = await third_card_dao.list_no_role(
#         ThirdPlatform.RISUAI.value, 0, 10000
#     )
#     for third_card in third_card_list:
#         if (
#             len(third_card.status) > 0
#             and third_card.status != ThirdCardStatus.NEW.value
#         ):
#             continue
#         role_card_config = await third_card_service.analysis_third_card(
#             third_card.card_id
#         )
#         if role_card_config is None:
#             third_card.status = ThirdCardStatus.ERROR.value
#             await third_card_dao.update(third_card)
#             continue

#         role_config = await third_card_service.init_or_update_role_config(
#             third_card, role_card_config
#         )
#         if role_config is None:
#             third_card.status = ThirdCardStatus.ERROR.value
#             await third_card_dao.update(third_card)
#             continue
#         third_card.language = role_config.def_language
#         third_card.role_id = role_config.id
#         third_card.contains_book = role_card_config.character_book is not None
#         third_card.card_name = role_card_config.name
#         third_card.role_name = role_card_config.name

#         log.info(f"update third card: {third_card.model_dump()}")
#         await third_card_dao.update(third_card)


# async def full_image():
#     third_card_list: list[ThirdCardInfo] = await third_card_dao.list(
#         ThirdPlatform.RISUAI.value, 0, 100000
#     )
#     for third_card in third_card_list:
#         third_card.full_image_url = third_card.load_image_url()
#         log.info(f"update third card: {third_card.model_dump()}")
#         await third_card_dao.update(third_card)

#     third_card_list: list[ThirdCardInfo] = await third_card_dao.list(
#         ThirdPlatform.ROCHAT.value, 0, 100000
#     )
#     for third_card in third_card_list:
#         third_card.full_image_url = third_card.load_image_url()
#         log.info(f"update third card: {third_card.model_dump()}")
#         await third_card_dao.update(third_card)


# async def update_character_book_info():
#     third_card_list: list[ThirdCardInfo] = await third_card_dao.list_all()
#     for third_card in third_card_list:
#         if not third_card.role_id:
#             continue
#         if not third_card.contains_book:
#             continue
#         role_card_config = await role_loader_service.load_by_id(third_card.role_id)
#         if not role_card_config:
#             continue
#         if not role_card_config.book_id:
#             continue
#         character_book = await char_book_dao.get_by_book_id(role_card_config.book_id)
#         if (
#             not character_book
#             or not character_book.enabled
#             or not character_book.entries
#         ):
#             continue

#         third_card.entry_sum_num = len(character_book.entries)
#         third_card.entry_enable_num = len(
#             [entry for entry in character_book.entries if entry.enabled]
#         )
#         constant_entries = [
#             entry
#             for entry in character_book.entries
#             if entry.enabled and entry.constant
#         ]
#         third_card.entry_constant_num = len(constant_entries)
#         third_card.entry_constant_token = sum(
#             [
#                 token_util.num_tokens_from_string(entry.content)
#                 for entry in constant_entries
#             ]
#         )

#         selective_entries = [
#             entry
#             for entry in character_book.entries
#             if entry.enabled and not entry.constant
#         ]
#         third_card.entry_selective_num = len(selective_entries)
#         third_card.entry_selective_token = sum(
#             [
#                 token_util.num_tokens_from_string(entry.content)
#                 for entry in selective_entries
#             ]
#         )

#         third_card.entry_valid_num = len(selective_entries) + len(constant_entries)
#         third_card.entry_valid_token = (
#             third_card.entry_constant_token + third_card.entry_selective_token
#         )
#         third_card.full_image_url = third_card.load_image_url()
#         log.info(f"update third card: {third_card.model_dump()}")
#         await third_card_dao.update(third_card)
