import logging
from ai import inner_bot
from common.common_constant import Language
from common.role_model import RoleDataConfig, SceneConfig
from common.translate_model import TranslateRole, TranslateTaskStatus, TranslateTaskType
from persistence.models.models import TranslateTask
from services import translate_service
from services.role import role_loader_service
from services.role_config_service import RoleConfigService
from utils import json_util
from utils.exception_util import async_ignore_catch_exception

log = logging.getLogger(__name__)


async def init_role_single_task(role_id: int):

    role = await role_loader_service.get_by_id(role_id)
    role_task = await translate_service.get_task(
        TranslateTaskType.ROLE.value, str(role_id)
    )
    if role_task and role_task.updated_at > role.updated_at:
        return None
    translate_role: TranslateRole = TranslateRole.init_from_role_config(role)
    langs = [x.value for x in Language]
    if role_task and TranslateRole.equal_translate_role(
        translate_role, TranslateRole(**json_util.convert_to_dict(role_task.source))
    ):
        # log.info(f"translate source same,role_id: {role.id}")
        return None

    return await translate_service.upsert_task(
        TranslateTaskType.ROLE.value,
        str(role.id),
        translate_role.model_dump(),
        langs,
    )


async def init_role_task():
    # init roles
    role_task_list = await translate_service.list_tasks_by_type(
        TranslateTaskType.ROLE.value
    )
    role_task_map = {x.task_key: x for x in role_task_list}
    role_configs = await role_loader_service.list_public()
    for role in role_configs:
        role_task = role_task_map.get(str(role.id), None)
        if role_task and role_task.updated_at > role.updated_at:
            continue
        translate_role: TranslateRole = TranslateRole.init_from_role_config(role)
        langs = [x.value for x in Language]
        
        if role_task and TranslateRole.equal_translate_role(
            translate_role, TranslateRole(**json_util.convert_to_dict(role_task.source))
        ):
            # log.info(f"translate source same,role_id: {role.id}")
            continue

        await translate_service.upsert_task(
            TranslateTaskType.ROLE.value,
            str(role.id),
            translate_role.model_dump(),
            langs,
        )


@async_ignore_catch_exception
async def run_role_task(task: TranslateTask, language: str = ""):
    target = task.target
    for lang in task.languages:
        if language and lang != language:
            continue
        translate = TranslateRole(**json_util.convert_to_dict(task.source))

        async def translate_scenes(scenas: list[SceneConfig]) -> list[SceneConfig]:
            if scenas is None or len(scenas) == 0:
                return []
            ret = []
            for s in scenas:
                first_message = await inner_bot.translate(s.first_message, lang)
                scenario = await inner_bot.translate(s.scenario, lang)
                config = SceneConfig(
                    index=s.index,
                    scenario=scenario,
                    first_message=first_message,
                )
                ret.append(config)
            return ret

        mid_ret = TranslateRole(
            card_name=await inner_bot.translate(translate.card_name, lang),
            role_name=await inner_bot.translate(translate.role_name, lang),
            first_message=await inner_bot.translate(translate.first_message, lang),
            scenario=await inner_bot.translate(translate.scenario, lang),
            introduction=await inner_bot.translate(translate.introduction, lang),
            simple_intro=await inner_bot.translate(translate.simple_intro, lang),
            status_block=await inner_bot.translate(translate.status_block, lang),
            status_block_init=await inner_bot.translate(
                translate.status_block_init, lang
            ),
            muilte_scenes=await translate_scenes(translate.muilte_scenes),
        )
        target[lang] = mid_ret.model_dump()
    return target
