import logging

from ai import inner_bot
from common.common_constant import Language
from common.translate_model import TranslateGroup, TranslateTaskType
from persistence.models.models import ChatGroupConfig, TranslateTask
from services import translate_service
from services.role import role_group_service
from utils import json_util


log = logging.getLogger(__name__)


async def init_group_task(group_id: int = 0):
    # init groups
    group_task_list = await translate_service.list_tasks_by_type(
        TranslateTaskType.GROUP.value
    )
    group_task_map = {x.task_key: x for x in group_task_list}
    group_configs = []
    if not group_id:
        group_configs = await role_group_service.list_public_config()
    else:
        group = await role_group_service.get_original(group_id)
        group_configs.append(group)

    async def init(group: ChatGroupConfig):
        group_task = group_task_map.get(str(group.id), None)
        if group_task and group_task.updated_at > group.updated_at:
            return
        translate_group = TranslateGroup.from_group(group)
        langs = [x for x in Language.fetch_all()]
        if (
            group_task
            and group_task.source == translate_group.model_dump()
            and group_task.languages == langs
        ):
            log.info(f"translate source same,group_id: {group.id}")
            return

        await translate_service.upsert_task(
            TranslateTaskType.GROUP.value,
            str(group.id),
            translate_group.model_dump(),
            langs,
        )

    for group in group_configs:
        await init(group)


async def run_group_task(task: TranslateTask,language:str = ""):
    target = task.target or {}
    for lang in task.languages:
        if language and lang != language:
            continue
        translate = TranslateGroup(**json_util.convert_to_dict(task.source))
        mid_ret = TranslateGroup(
            name=await inner_bot.translate(translate.name, lang),
            simple_intro=await inner_bot.translate(translate.simple_intro, lang),
            introduction=await inner_bot.translate(translate.introduction, lang),
            scenario=await inner_bot.translate(translate.scenario, lang),
        )
        target[lang] = mid_ret.model_dump()
    return target
