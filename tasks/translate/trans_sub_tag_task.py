import os
import re
from ai import inner_bot
from common.common_constant import Language
from common.translate_model import TranslateSubTag, TranslateTaskType
from persistence.models.models import SubTag, TranslateTask
from properties import prop_util
from services import translate_service



async def run_sub_tag_task(task: TranslateTask):
    target: dict = {}
    for lang in task.languages:
        tag_name = task.source.get("tag_name", "") # type: ignore
        if bool(re.fullmatch(r"[A-Z]+", tag_name)):
            target[lang] = TranslateSubTag(tag_name=tag_name).model_dump()
        else:
            ret = await inner_bot.translate(tag_name, lang)
            target[lang] = TranslateSubTag(tag_name=ret).model_dump()
    return target


# async def sub_tags_update():
#     # load ../common/translate/sub_tags.txt文件
#     sub_tags: list[str] = prop_util.read_line_sub_tags()
#     source_sub_tag_map = {x[0]: x for x in sub_tags}
    
#     task_list = await translate_service.list_tasks_by_type(
#         TranslateTaskType.SUB_TAG.value
#     )
#     for task in task_list:
#         source = TranslateSubTag(**task.source)
#         if task.target is None or task.target == {}:
#             continue
#         en_source = TranslateSubTag(**task.target[Language.EN.value])
#         tw_source = TranslateSubTag(**task.target[Language.ZH_TW.value])
#         if source.tag_name in source_sub_tag_map:
#             source_sub_tag = source_sub_tag_map[source.tag_name]
#             en_source.tag_name = source_sub_tag[1]
#             tw_source.tag_name = source_sub_tag[2]
#             task.target[Language.EN.value] = en_source.model_dump()
#             task.target[Language.ZH_TW.value] = tw_source.model_dump()
#             await translate_service.update_task(task)


async def init_sub_tags_task():
    # init sub_tags
    sub_task_list = await translate_service.list_tasks_by_type(
        TranslateTaskType.SUB_TAG.value
    )
    sub_ids = [int(x.task_key) for x in sub_task_list]
    sub_tags: list[SubTag] = await SubTag.all()
    for sub_tag in sub_tags:
        if sub_tag.id in sub_ids:
            continue
        lanagues = [Language.EN.value, Language.ZH_TW.value]
        trans_sub_tag = TranslateSubTag(tag_name=sub_tag.tag_name)
        await translate_service.upsert_task(
            TranslateTaskType.SUB_TAG.value,
            str(sub_tag.id),
            trans_sub_tag.model_dump(),
            lanagues,
        )
