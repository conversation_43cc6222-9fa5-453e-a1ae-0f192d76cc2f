import logging
import math
import os
from datetime import datetime, UTC, timezone
import os
from pydantic import BaseModel
import mysql.connector
from mysql.connector.abstracts import MySQLConnectionAbstract
from mysql.connector.pooling import PooledMySQLConnection
from persistence.models.models import FavoriteStats, GroupStatistic, RoleStatistic
from utils import utils

log = logging.getLogger(__name__)


class RoleInfo(BaseModel):
    role_id: int
    created_at: datetime
    uid: int
    role_diff_days: int = 0

    def diff_days(self):
        now = datetime.now(timezone.utc)
        return (now - self.created_at).days


class RoleStat(BaseModel):
    role_id: int
    total_conversation: int
    total_message: int
    user_count: int
    total_diamond: int
    hot_v1: int
    h24_haiku_count: int
    hot_temp: int


def stat_sql(
    conn: PooledMySQLConnection | MySQLConnectionAbstract,
) -> list[RoleStat]:
    cursor = conn.cursor()
    # 角色列表
    cursor.execute(
        """select 
        id, 
        created_at,
        uid
        from role_config
        where privacy = 1 and status = 1
        """
    )
    mid_list = cursor.fetchall()
    role_diff_days = {}
    role_info_list = []
    # black_user_ids = [2312696]
    black_user_ids = []
    black_role_ids = []
    for x in mid_list:
        role_info = RoleInfo(
            role_id=int(x[0]),  # type: ignore
            created_at=x[1].astimezone(timezone.utc),  # type: ignore
            uid=int(x[2]),  # type: ignore
        )
        role_diff_days[role_info.role_id] = role_info.diff_days()
        role_info_list.append(role_info)
        if role_info.uid in black_user_ids:
            black_role_ids.append(role_info.role_id)
    log.info("black role ids %s", black_role_ids)        
    

    # 推广角色用户数据
    cursor.execute(
        """SELECT start_role,count(1) as num  from user_register_channel 
        WHERE start_role > 0 GROUP BY start_role order by num desc
    """
    )
    mid_list = cursor.fetchall()
    channel_role_register_num = {}
    for x in mid_list:
        channel_role_register_num[int(x[0])] = int(x[1])  # type: ignore
    # 角色统计热度值
    cursor.execute(
        """select 
        role_id, 
        count(distinct(conversation_id)) as conv_count,
        count(1) as msg_count,
        count(distinct(user_id)) as user_count,
        sum(case when consume > 1 then consume 
            when price is not null and price > 0 then price
            else 0 end) as total_diamond,
        count(CASE WHEN chs.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and chs.chat_product_mid='m1' THEN 1 END) AS h24_hk_count
        from chat_history_statistic chs left join product p on p.product_id = chs.product_id group by role_id
        """
    )
    ret_list = cursor.fetchall()
    data_list = []
    for x in ret_list:
        numerator = int(x[3]) + int(x[1]) + int(x[2]) + int(x[4]) + 666  # type: ignore
        diff_days = role_diff_days.get(x[0], 0)  # type: ignore
        denominator = math.pow(diff_days + 1, 1 / 100)
        hot = int((numerator / denominator))
        if channel_role_register_num.get(x[0], 0) >= 500:  # type: ignore
            hot = int(hot * 0.2)
        total_message = int(x[2])  # type: ignore
        if total_message >= 100 * 10000:
            hot = int(hot * 0.001)
        elif total_message >= 50 * 10000:
            hot = int(hot * 0.01)
        elif total_message >= 5 * 10000:
            hot = int(hot * 0.05)
        elif total_message >= 10000:
            hot = int(hot * 0.1)
        if int(x[0]) in black_role_ids:  # type: ignore
            log.info(f"black role {x[0]} {hot}")
            hot = int(hot / ((diff_days + 1) * (diff_days + 1)))
        hot_temp = hot
        role_stat = RoleStat(
            role_id=int(x[0]),  # type: ignore
            total_conversation=int(x[1]),  # type: ignore
            total_message=int(x[2]),  # type: ignore
            user_count=int(x[3]),  # type: ignore
            total_diamond=int(x[4]),  # type: ignore
            hot_v1=hot,
            h24_haiku_count=int(x[5]),  # type: ignore
            hot_temp=hot_temp,
        )

        data_list.append(role_stat)
    cursor.close()
    return data_list


async def start_job():
    connection_string = os.environ["MYSQL_SLAVE_URL"]
    conn_params = utils.parse_mysql_connection_string(connection_string)
    with mysql.connector.connect(**conn_params) as conn:
        data_list = stat_sql(conn)
        if not data_list:
            return
        for mid in data_list:
            log.info(f"updating role statistic {mid.role_id}, {mid.model_dump()}")
            await RoleStatistic.update_or_create(
                role_id=mid.role_id,
                defaults={
                    "total_conversation": mid.total_conversation,
                    "total_message": mid.total_message,
                    "user_count": mid.user_count,
                    "total_diamond": mid.total_diamond,
                    "hot_v1": mid.hot_v1,
                    "h24_haiku_count": mid.h24_haiku_count,
                    "hot_temp": mid.hot_temp,
                },
            )
        log.info("updating role statistic done,len(data_list): %s", len(data_list))
