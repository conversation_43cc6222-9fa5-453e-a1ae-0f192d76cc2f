import os
from datetime import datetime, UTC, timezone
import logging
import os
import mysql.connector
from mysql.connector.abstracts import MySQLConnectionAbstract
from mysql.connector.pooling import PooledMySQLConnection
from persistence.models.models import FavoriteStats
from utils import utils

log = logging.getLogger(__name__)


def stat_sql(
    conn: PooledMySQLConnection | MySQLConnectionAbstract,
) -> list[FavoriteStats]:
    cursor = conn.cursor()
    cursor.execute(
        "select mode_target_id,mode_type,count(1) from user_favorite_record where status=1 group by mode_target_id,mode_type"
    )
    items = cursor.fetchall()
    res = [
        FavoriteStats(
            mode_target_id=item[0],  # type: ignore
            mode_type=item[1],  # type: ignore
            favorite_count=item[2],  # type: ignore
        )
        for item in items
    ]  # type: ignore
    cursor.close()
    return res  # type: ignore


async def start_job():
    connection_string = os.environ["MYSQL_SLAVE_URL"]
    conn_params = utils.parse_mysql_connection_string(connection_string)
    with mysql.connector.connect(**conn_params) as conn:
        fav_list = stat_sql(conn)
        if not fav_list:
            return
        for fav in fav_list:
            log.info(
                f"Role favorite statistic job, {fav.mode_target_id}, {fav.mode_type}, {fav.favorite_count}"
            )
            await FavoriteStats.update_or_create(
                mode_target_id=fav.mode_target_id,
                mode_type=fav.mode_type,
                defaults={"favorite_count": fav.favorite_count},
            )
    log.info("Role favorite statistic job finished")
