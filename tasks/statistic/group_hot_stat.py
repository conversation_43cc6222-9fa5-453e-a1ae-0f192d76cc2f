import logging
import math
import os
from datetime import datetime, UTC, timezone
import os
import mysql.connector
from mysql.connector.abstracts import MySQLConnectionAbstract
from mysql.connector.pooling import PooledMySQLConnection
from persistence.models.models import FavoriteStats, GroupStatistic
from utils import utils

log = logging.getLogger(__name__)


def stat_sql(
    conn: PooledMySQLConnection | MySQLConnectionAbstract,
) -> list[GroupStatistic]:
    cursor = conn.cursor()
    # 角色列表
    cursor.execute(
        """select 
        id, 
        created_at
        from chat_group_config
        where public = 1 and status = 1
        """
    )
    mid_list = cursor.fetchall()
    role_diff_days = {}
    for x in mid_list:
        created_at = x[1].astimezone(timezone.utc)  # type: ignore
        now = datetime.now(timezone.utc)
        diff_day = (now - created_at).days
        role_diff_days[int(x[0])] = diff_day  # type: ignore

    # 角色统计热度值
    cursor.execute(
        """select 
        mode_target_id, 
        count(distinct(conversation_id)) as conv_count,
        count(1) as msg_count,
        count(distinct(user_id)) as user_count,
        sum(consume) as total_diamond,
        count(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and model='claude-3-haiku' THEN 1 END) AS h24_haiku_count
        from chat_history_statistic 
        where mode_type = 'group'
        group by mode_target_id
        """
    )
    ret_list = cursor.fetchall()
    data_list = []
    for x in ret_list:
        numerator = int(x[3]) + int(x[1]) + int(x[2]) + int(x[4]) + 666  # type: ignore
        diff_days = role_diff_days.get(x[0], 0)  # type: ignore
        denominator = math.pow(diff_days + 1, 1 / 100)
        hot = int((numerator / denominator))
        total_message = int(x[2])  # type: ignore
        if total_message >= 100 * 10000:
            hot = int(hot * 0.001)
        elif total_message >= 50 * 10000:
            hot = int(hot * 0.01)
        elif total_message >= 5 * 10000:
            hot = int(hot * 0.05)
        elif total_message >= 10000:
            hot = int(hot * 0.1)
        group_stat = GroupStatistic(
            group_id=int(x[0]),  # type: ignore
            total_conversation=int(x[1]),  # type: ignore
            total_message=int(x[2]),  # type: ignore
            user_count=int(x[3]),  # type: ignore
            total_diamond=int(x[4]),  # type: ignore
            hot_v1=hot,
            h24_haiku_count=int(x[5]),  # type: ignore
        )
        data_list.append(group_stat)
    cursor.close()
    return data_list


async def start_job():
    connection_string = os.environ["MYSQL_SLAVE_URL"]
    conn_params = utils.parse_mysql_connection_string(connection_string)
    with mysql.connector.connect(**conn_params) as conn:
        group_stats = stat_sql(conn)
        if not group_stats:
            return
        for mid in group_stats:
            log.info(f"updating group statistic {mid.group_id}, {mid.hot_v1}")
            await GroupStatistic.update_or_create(
                group_id=mid.group_id,
                defaults={
                    "total_conversation": mid.total_conversation,
                    "total_message": mid.total_message,
                    "user_count": mid.user_count,
                    "total_diamond": mid.total_diamond,
                    "hot_v1": mid.hot_v1,
                    "h24_haiku_count": mid.h24_haiku_count,
                },
            )
    log.info("group hot statistic job done")