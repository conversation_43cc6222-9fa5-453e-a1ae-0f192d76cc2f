import logging
import math
import os
from datetime import datetime, UTC, timezone
import os
import mysql.connector
from mysql.connector.abstracts import MySQLConnectionAbstract
from mysql.connector.pooling import PooledMySQLConnection
from mysql.connector.abstracts import MySQLCursorAbstract
import pytz
from persistence import redis_client
from persistence.models.models import FavoriteStats, GroupStatistic, UserSummaryStats
from utils import utils
from pydantic import BaseModel

log = logging.getLogger(__name__)


class UserSummary(BaseModel):

    # 用户基本信息
    user_id: int = 0
    nickname: str = ""  # 昵称
    email: str = ""  # 邮箱
    llm_model: str = "claude-3-haiku"
    register_source: str = ""  # 注册来源(TMA,USA_WEB,BOT)
    register_at: int = 0  # 注册时间
    register_at_day: int = 0  # 注册时间的日期

    # 用户Tg信息
    tg_id: int = 0  # tg用户ID
    tg_first_name: str = ""  # tg用户名
    tg_last_name: str = ""  # tg用户姓
    tg_user_name: str = ""  # tg用户名

    # 用户聊天信息
    role_count: int = 0  # 聊天角色数量
    cov_count: int = 0  # 聊天回合次数
    turn_count: int = 0  # 聊天轮次
    ai_count: int = 0  # 聊天AI次数
    ai_haiku_count: int = 0  # 聊天haiku次数
    ai_sonnet3_count: int = 0  # 聊天sonnet3次数
    ai_sonnet35_count: int = 0  # 聊天sonnet35次数
    ai_opus_count: int = 0  # 聊天opus次数

    activate_days: int = 0  # 聊天天数
    # 聊天留存天数（1，2，4，8，16，32，64,128）
    # 表示：1天（当天），2天，3天，4天，5天，6天，7天，8天
    chat_days: int = 0

    # 用户支付与付费信息
    total_balance: int = 0  # 钻石余额
    pay_count: int = 0  # 付费次数
    pay_amount_sum: int = 0  # 付费金额
    first_pay_at: int = 0  # 首次付费时间

    # 邀请相关信息
    channel_id: int = 0  # 频道ID
    invite_link: str = ""  # 邀请链接
    invite_count: int = 0  # 邀请人数
    from_user_id: int = 0  # 邀请人ID
    joined_chat_type: str = ""  # 加入的聊天类型
    joined_chat_id: str = ""  # 聊天ID
    start_role: int = 0  # 开始角色

    joined_group_count: int = 0  # 加入群组次数
    joined_channel_count: int = 0  # 加入频道次数


async def start_user(
    user_ids: list[int], cursor: MySQLCursorAbstract
) -> dict[int, UserSummary]:
    user_maps = {x: UserSummary() for x in user_ids}
    cursor.execute(
        "select id,nickname,llm_model,register_source,created_at,email from users_pw"
    )
    all_users_sql_ret = cursor.fetchall()
    for x in all_users_sql_ret:
        user = user_maps.get(
            x[0],
        )  # type: ignore
        user.user_id = int(x[0])
        user.nickname = str(x[1])
        user.llm_model = str(x[2]) if x[2] else "claude-3-haiku"
        user.register_source = str(x[3])
        user.register_at = int(utc_to_timestamp(x[4]))
        # register_at_day 为注册时间的日期，yyyyMMdd格式，utc+8时区
        user.register_at_day = to_utc8_day_index_by_utc(user.register_at)
        user.email = str(x[5])

    user_ids_sql_str = f'({",".join([str(x) for x in user_ids])})'
    # Tg基本信息
    cursor.execute(
        """
        select
        uid,
        tg_id,
        first_name,
        last_name,
        user_name from tg_user where uid in %s
        """
        % user_ids_sql_str
    )
    rets = cursor.fetchall()
    for x in rets:
        mid_user = user_maps.get(x[0])
        mid_user.tg_id = int(x[1])
        mid_user.tg_first_name = x[2]
        mid_user.tg_last_name = x[3]
        mid_user.tg_user_name = x[4]

    # 聊天信息
    cursor.execute(
        """
        select
        user_id,
        count(distinct role_id) as role_count,
        count(distinct conversation_id) as cov_count,
        count(distinct message_id) as turn_count,
        count(1) as ai_count,
        sum(case when model = 'claude-3-haiku' then 1 else 0 end) as ai_haiku_count,
        sum(case when model = 'claude-3-sonnet' then 1 else 0 end) as ai_sonnet3_count,
        sum(case when model = 'claude-3.5-sonnet' then 1 else 0 end) as ai_sonnet35_count,
        sum(case when model = 'claude-3-opus' then 1 else 0 end) as ai_opus_count
        from chat_history_statistic where user_id in %s group by user_id
        """
        % user_ids_sql_str
    )
    rets = cursor.fetchall()
    for x in rets:
        mid_user = user_maps.get(int(x[0]))
        mid_user.role_count = int(x[1])
        mid_user.cov_count = int(x[2])
        mid_user.turn_count = int(x[3])
        mid_user.ai_count = int(x[4])
        mid_user.ai_haiku_count = int(x[5])
        mid_user.ai_sonnet3_count = int(x[6])
        mid_user.ai_sonnet35_count = int(x[7])
        mid_user.ai_opus_count = int(x[8])
    cursor.execute(
        """
        select
        user_id,
        DATE_FORMAT(CONVERT_TZ(created_at, '+00:00', '+08:00'),'%%Y%%m%%d') as chat_day
        from chat_history_statistic where user_id in %s group by user_id,chat_day
        """
        % user_ids_sql_str
    )
    rets = cursor.fetchall()
    ret_list = [{"user_id": x[0], "chat_day": x[1]} for x in rets]
    # 按照user_id分组
    user_chat_days = {}
    for x in ret_list:
        if x["user_id"] not in user_chat_days:
            user_chat_days[x["user_id"]] = []
        user_chat_days[x["user_id"]].append(x["chat_day"])
    for x in user_chat_days:
        chat_days = list(user_chat_days[x])
        mid_user = user_maps.get(x)
        mid_user.activate_days = len(chat_days)
        chat_days.sort()
        for day in chat_days:
            register_at_date = datetime.strptime(
                str(mid_user.register_at_day), "%Y%m%d"
            )
            current_date = datetime.strptime(str(day), "%Y%m%d")
            date_difference = (current_date - register_at_date).days + 1
            if date_difference in [1, 2, 3, 4, 5, 6, 7, 8]:
                mid_user.chat_days = mid_user.chat_days | (1 << (date_difference - 1))

    # 支付订单相关信息
    cursor.execute(
        """
        select
        user_id,
        SUM( CASE
            WHEN pay_currency = 'CNY' THEN pay_fee
            WHEN pay_currency = 'USD' THEN pay_fee * 7.5
            ELSE 0 END
        ) as pay_amount_sum,
        count(1) as pay_count,
        min(finished_at) as first_pay_at
        from recharge_order
        where user_id in %s and status='SUCCEED' and pay_fee>0
        group by user_id
        """
        % user_ids_sql_str
    )
    rets = cursor.fetchall()
    for x in rets:
        mid_user = user_maps.get(x[0])
        mid_user.pay_amount_sum = int(x[1])
        mid_user.pay_count = int(x[2])
        mid_user.first_pay_at = int(x[3].timestamp()) if x[3] else 0

    # 钻石相关信息
    cursor.execute(
        """
        select user_id, total_balance from account where user_id in %s
        """
        % user_ids_sql_str
    )
    rets = cursor.fetchall()
    for x in rets:
        mid_user = user_maps.get(x[0])
        mid_user.total_balance = x[1]
    cursor.execute(
        "SELECT user_id, sum(balance) from expirable_award where user_id in %s and balance > 0 and expires_at>current_timestamp() group by user_id"
        % user_ids_sql_str
    )
    rets = cursor.fetchall()
    for x in rets:
        mid_user = user_maps.get(x[0])
        mid_user.total_balance += int(x[1])

    # 邀请相关数据
    cursor.execute(
        """
        select inviter_user_id as user_id,
        count(1) as invite_count
        from invitation where inviter_user_id in %s
        group by inviter_user_id """
        % user_ids_sql_str
    )
    rets = cursor.fetchall()
    for x in rets:
        mid_user = user_maps.get(x[0])
        mid_user.invite_count = x[1]

    print("start invitation")
    cursor.execute(
        """
        select invitee_user_id as user_id,
        inviter_user_id
        from invitation where invitee_user_id in %s
        """
        % user_ids_sql_str
    )
    rets = cursor.fetchall()
    for x in rets:
        mid_user = user_maps.get(x[0])
        mid_user.from_user_id = int(x[1])

    cursor.execute(
        """
        select user_id,
        sum(case when chat_id = -1002223050046 then 1 else 0 end) as join_group_count,
        sum(case when chat_id = -1002201418897 then 1 else 0 end) as join_channel_count
        from chat_join_task where user_id in %s
        group by user_id
        """
        % user_ids_sql_str
    )
    rets = cursor.fetchall()
    for x in rets:
        mid_user = user_maps.get(x[0])
        mid_user.joined_group_count = int(x[1])
        mid_user.joined_channel_count = int(x[2])

    cursor.execute(
        "select user_id, channel_id, invite_link, chat_id, joined_chat_type, start_role from user_register_channel where user_id in %s"
        % user_ids_sql_str
    )
    rets = cursor.fetchall()
    for x in rets:
        mid_user = user_maps.get(x[0])
        mid_user.channel_id = int(x[1])
        mid_user.invite_link = str(x[2])
        mid_user.joined_chat_id = str(x[3])
        mid_user.joined_chat_type = str(x[4])
        mid_user.start_role = int(x[5])
    return user_maps


def utc_to_timestamp(utc_time):
    utc_dt = pytz.utc.localize(utc_time)
    return utc_dt.timestamp()


def to_utc8_day_index_by_utc(timestamp: int):
    dt = datetime.fromtimestamp(timestamp, timezone.utc)
    dt = dt.astimezone(pytz.timezone("Asia/Shanghai"))
    return int(dt.strftime("%Y%m%d"))


def to_day_index_by_utc8(timestamp: int):
    dt = datetime.fromtimestamp(timestamp, timezone.utc)
    dt = dt.astimezone(pytz.timezone("Asia/Shanghai"))
    return int(dt.strftime("%Y%m%d"))


async def start_job():
    user_ids = redis_client.pop_active_user(500)
    if not user_ids:
        log.info("no active user")
        return
    log.info("all_user_statistic job start, user_ids: %s", user_ids)
    connection_string = os.environ["MYSQL_SLAVE_URL"]
    conn_params = utils.parse_mysql_connection_string(connection_string)
    with mysql.connector.connect(**conn_params) as conn:
        cursor = conn.cursor()
        user_summary = await start_user(user_ids, cursor)
        cursor.close()
        if not user_summary:
            return
        for user_id, summary in user_summary.items():
            await UserSummaryStats.update_or_create(
                user_id=summary.user_id,
                defaults={
                    "nickname": summary.nickname,
                    "llm_model": summary.llm_model,
                    "register_source": summary.register_source,
                    "register_at": summary.register_at,
                    "email": summary.email,
                    "tg_id": summary.tg_id,
                    "tg_first_name": summary.tg_first_name,
                    "tg_last_name": summary.tg_last_name,
                    "tg_user_name": summary.tg_user_name,
                    "role_count": summary.role_count,
                    "cov_count": summary.cov_count,
                    "turn_count": summary.turn_count,
                    "ai_count": summary.ai_count,
                    "ai_haiku_count": summary.ai_haiku_count,
                    "ai_sonnet3_count": summary.ai_sonnet3_count,
                    "ai_sonnet35_count": summary.ai_sonnet35_count,
                    "ai_opus_count": summary.ai_opus_count,
                    "activate_days": summary.activate_days,
                    "chat_days": summary.chat_days,
                    "total_balance": summary.total_balance,
                    "pay_amount_sum": summary.pay_amount_sum,
                    "pay_count": summary.pay_count,
                    "first_pay_at": summary.first_pay_at,
                    "invite_link": summary.invite_link,
                    "channel_id": summary.channel_id,
                    "invite_count": summary.invite_count,
                    "from_user_id": summary.from_user_id,
                },
            )

    log.info("group hot statistic job done")
