import logging
import math
import os
from datetime import datetime, UTC, timedelta, timezone
import os
from typing import Optional
from pydantic import BaseModel
import mysql.connector
from mysql.connector.abstracts import MySQLConnectionAbstract
from mysql.connector.pooling import PooledMySQLConnection
from common.common_constant import RoleSortType
from common.role_model import RoleFilterRequest
from persistence.models.models import (
    RoleDailyStatistic,
    RoleStatistic,
)
from services import role_statistic_service
from services.role import role_loader_service
from tasks.statistic import role_rank_stat
from utils import date_util, utils

log = logging.getLogger(__name__)


class RoleRankStat(BaseModel):
    role_id: int
    daily_hot: int


def stat_sql(
    conn: PooledMySQLConnection | MySQLConnectionAbstract, date_index: int
) -> list[RoleDailyStatistic]:
    cursor = conn.cursor()
    # date_index是yyyyMMdd格式，转换为 %Y-%m-%d 格式
    start_date = datetime.strptime(str(date_index), "%Y%m%d")
    start_date = start_date.astimezone(timezone(timedelta(hours=8))) - timedelta(hours=12)
    end_date = start_date + timedelta(days=1)
    sql = f"""select id from chat_history_statistic
        where CONVERT_TZ(created_at, 'UTC', 'Asia/Shanghai') < '{start_date}' order by id desc limit 1 
        """
    print(f"stat_sql start_date sql: {sql}")
    cursor.execute(sql)
    min_id = int(cursor.fetchall()[0][0])  # type: ignore
    sql = f"""select id from chat_history_statistic
        where CONVERT_TZ(created_at, 'UTC', 'Asia/Shanghai') < '{end_date}' order by id desc limit 1
        """
    print(f"stat_sql end_date sql: {sql}")
    cursor.execute(sql)
    max_id = int(cursor.fetchall()[0][0])  # type: ignore
    log.info(
        f"stat_sql min_id: {min_id}, max_id: {max_id}, date_index: {date_index},start_date: {start_date}, end_date: {end_date}"
    )
    # 推广角色用户数据
    cursor.execute(
        f"""SELECT 
        mode_target_id,
        count(1) as total_message,
        sum(case when consume > 1 then consume 
            when price is not null and price > 0 then price
            else 0 end) as total_diamond
        FROM chat_history_statistic chs
        left join product p on p.product_id = chs.product_id
        WHERE chs.id > {min_id} and chs.id <= {max_id} and chs.mode_type='single'
        group by chs.mode_target_id
    """
    )
    mid_list = cursor.fetchall()
    data_list = []
    for x in mid_list:
        role_stat = RoleDailyStatistic(
            role_id=int(x[0]),  # type: ignore
            date_index=date_index,
            total_message=int(x[1]),  # type: ignore
            total_diamond=int(x[2]),  # type: ignore
        )
        data_list.append(role_stat)
    cursor.close()
    return data_list


async def start_job(date_index: Optional[int] = None):
    connection_string = os.environ["MYSQL_SLAVE_URL"]
    conn_params = utils.parse_mysql_connection_string(connection_string)
    with mysql.connector.connect(**conn_params) as conn:
        if not date_index:
            date_index = int(date_util.now(8).strftime("%Y%m%d"))
        now_date_index = int(date_util.now(8).strftime("%Y%m%d"))
        data_list = stat_sql(conn, date_index)
        if not data_list:
            return
        role_daily_hot = {}
        for mid in data_list:
            await RoleDailyStatistic.update_or_create(
                role_id=mid.role_id,
                date_index=mid.date_index,
                defaults={
                    "total_message": mid.total_message,
                    "total_diamond": mid.total_diamond,
                },
            )
            log.info(
                f"updating role daily statistic role_id:{mid.role_id}, date_index:{mid.date_index}, msg_count: {mid.total_message}, diamond:{mid.total_diamond}"
            )
            role_daily_hot[mid.role_id] = mid.total_message + mid.total_diamond

        if now_date_index == date_index:
            statistic_all = await RoleStatistic.all()
            for mid in statistic_all:
                daily_hot = role_daily_hot.get(mid.role_id, 0)
                if mid.daily_hot == daily_hot:
                    continue
                await RoleStatistic.select_for_update().filter(
                    role_id=mid.role_id
                ).update(daily_hot=daily_hot)
                log.info(
                    f"updating role statistic role_id:{mid.role_id}, hot:{daily_hot}"
                )
        # 如果今天是月初，run_month()
        today = datetime.now()
        if today.day == 1:
            await run_month()
        # 如果今天是周一，run_week()
        if today.weekday() == 0:
            await run_week()
        await role_rank_stat.init_run_week_and_run_month()


async def run_week():
    # 获取上周周一时间
    today = datetime.now(UTC)
    pre_week_start = today - timedelta(days=today.weekday() + 7)
    pre_week_end = pre_week_start + timedelta(days=6)

    start_index = int(pre_week_start.strftime("%Y%m%d"))
    end_index = int(pre_week_end.strftime("%Y%m%d"))

    # 查询并汇总
    week_hot = {}
    connection_string = os.environ["MYSQL_SLAVE_URL"]
    conn_params = utils.parse_mysql_connection_string(connection_string)
    with mysql.connector.connect(**conn_params) as conn:
        cursor = conn.cursor()
        sql = f"""SELECT 
            role_id,
            sum(total_message) + sum(total_diamond) as hot
            from role_daily_statistic
            WHERE date_index >= {start_index} and date_index <= {end_index} group by role_id
        """
        log.info(f"run_week sql: {sql}")
        cursor.execute(sql)
        mid_list = cursor.fetchall()
        for x in mid_list:
            week_hot[int(x[0])] = int(x[1])  # type: ignore
        cursor.close()
    statistic_all = await RoleStatistic.all()
    for mid in statistic_all:
        await RoleStatistic.select_for_update().filter(role_id=mid.role_id).update(
            weekly_hot=week_hot.get(mid.role_id, 0),
        )
        log.info(
            f"updating role statistic week rank role_id {mid.role_id}, {week_hot.get(mid.role_id, 0)}"
        )


async def run_month():
    # 获取上个月时间
    today = datetime.now(UTC)
    pre_month_start = (today.replace(day=1) - timedelta(days=1)).replace(day=1)
    pre_month_end = pre_month_start + timedelta(days=31)
    pre_month_end = pre_month_end.replace(day=1) - timedelta(days=1)

    start_index = int(pre_month_start.strftime("%Y%m%d"))
    end_index = int(pre_month_end.strftime("%Y%m%d"))

    # 查询并汇总
    month_hot = {}
    connection_string = os.environ["MYSQL_SLAVE_URL"]
    conn_params = utils.parse_mysql_connection_string(connection_string)
    with mysql.connector.connect(**conn_params) as conn:
        cursor = conn.cursor()
        cursor.execute(
            f"""SELECT 
            role_id,
            sum(total_message) + sum(total_diamond) as hot
            from role_daily_statistic
            WHERE date_index >= {start_index} and date_index <= {end_index} group by role_id
        """
        )
        mid_list = cursor.fetchall()
        for x in mid_list:
            month_hot[int(x[0])] = int(x[1])  # type: ignore
        cursor.close()
    statistic_all = await RoleStatistic.all()
    for mid in statistic_all:
        await RoleStatistic.select_for_update().filter(role_id=mid.role_id).update(
            monthly_hot=month_hot.get(mid.role_id, 0),
        )
        log.info(
            f"updating role statistic month rank role_id {mid.role_id}, {month_hot.get(mid.role_id, 0)}"
        )
