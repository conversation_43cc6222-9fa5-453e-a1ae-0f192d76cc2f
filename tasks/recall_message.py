from datetime import datetime, UTC, timezone
import logging
import os
import random
import mysql.connector
from mysql.connector.abstracts import MySQLConnectionAbstract
from mysql.connector.pooling import PooledMySQLConnection
from aiogram.utils.keyboard import InlineKeyboardBuilder
from common.bot_common import Button, MessageTemplate
from persistence.models.models import RecallMessageHistory, UserBotActive
from services import bot_services, tg_config_service, user_active_service
from services.user_service import user_service
from services.bot_message_service import send_by_template
from utils import env_const, utils

messages = [
    "爱别人你只想跟你上床，我不一样！沙发，厨房，我都可以~(´,,•ω•,,)♡",
    "明明对你的爱不掺水分，可是想你的时候，为什么总是……湿湿的……🙈",
    "我是一个不懂坚持的人，但有一件事我决定干一辈子……就是你！(つд⊂)",
    "要脑子有什么用……它只会想你🤧",
    """报告！我变心了，今天变得比昨天还喜欢你喔~o(〃'▽'〃)o""",
    "感觉，我只能中午和你见面……不然的话，我早晚会爱上你🤧",
    "我的手虽然小，但握住你刚刚好~😑",
    "今天去医院了喵……医生说我血糖低，需要一点甜言蜜语~(´,,•ω•,,)♡",
    "其实今天有一点想你。其实不只一点，其实也不只今天……",
    "下面有点湿湿的，不信你摸摸！",
    "爱到深处腿自开，啪到腻时说拜拜。",
    "生活不只有眼前的苟且，还有湿和你在床上的狂野。",
    "我想和你从床上到客厅 从客厅到厨房 从厨房到阳台 从阳台到厕所 在回床上",
    "我用三寸不烂之舌，吞先生精兵两亿之多",
    "我湿了怎么办呀",
    "想和你朝朝又暮暮，嗯嗯又啊啊，雨都夹着雪，你为什么不夹着我。",
    "你已经弄乱了我的心，什么时候来弄乱我的床呀？",
    "哎，我昨天晚上梦到你了呢！你说我是想你了呢还是想你了呢？",
    "我怀念的是污话不说，我怀念的是一起颤抖。",
    "你已经把我的心弄乱了，什么时候来弄乱我的床？",
    "我腿都张开了，就等先生把腰借我盘一盘",
    "饭在锅里人在床上你随便啊〜",
    "我的拳头和下面只为你坚硬，你的眼睛和下面只为我湿润。",
    "我今天出来太急，裙子下面真空的",
    "不想撞南墙了，只想撞先生的胸膛。",
    "先生，苦海无涯，回头是我。",
    "牛肉猪肉和羊肉，你喜欢吃哪种肉?我知道了，一定是我这块心头肉。",
    "我是可爱的小姑娘，你是可爱。",
    "不思进取，思你?",
    "你是最好的，如果真有比你要好的人，我就装作没看见。",
    "落叶归根，我归你",
    "段位和你，我都想上",
    "来者何人?你的人。",
    "请你改邪归我",
    "你会喜欢我吗?不会我教你啊",
    "抱怨没用，抱我啊",
    "我给你备注为一行，因为干一行，爱一行。",
    "不要让我看见你，不然见你一次喜欢一次",
    "小猪佩奇你佩我",
    "你吃烧烤回先烤什么?我会先考虑你",
    "你知道我喜欢谁嘛?不知道你就看第一个字",
    "可爱不是长久之计，可爱我是长久之计",
    "醉卧沙场君莫笑 古来征战几人回",
    "我这里有一杯96年的蜜汁，要交换就给我一根96年的香肠",
    "只有和你在一起的时候，才不会去羡慕别人",
    "信不信我给你一套莞式服务?",
    "巭孬嫑夯昆勥茓",
    "你主动点，我们不仅会有故事，还会有孩子",
    "与你相遇，如入梦中，从此不愿醒矣",
    "众生皆苦只有你是草莓味的",
    "在外禁欲脸，在家小马达",
    "少年强则少女扶墙 少女强则少男躺床",
    "你主动出鸡，我们才会有结果",
    "我今天请你吃饭吧？ 不了，改日吧。",
    "我手里有一把土，你要栽我手里吗？",
    "谁家闺女借我用用，明年还你一大一小。",
    "十个男人九个抠",
    "听说你想睡我，鼓起勇气来啊",
    "我不写天上的月亮，只希望尘世的姑娘，用三寸舌头，量你口中柔长，对着腿间湿漉的星光，许下一个愿望，跟我结婚吧，姑娘。",
    "在有我俩的一间房，东风起，麦儿黄，给你吃我做的蛋炒饭，听你骂”孩子没睡呢，臭流氓”",
    "右手敬礼，左手牵你",
    "我有一个大的荷尔蒙想要放",
    "前半生走马观花睡姑娘，反正你也不相信感情，干脆后半生就住我床上，能做了做一场，不能做了就陪我喝喝酒唱唱歌",
    "你笑起来很甜，尝起来很咸",
    "像我这么帅的人，你想跟我约炮，我不怪你",
    "我要睡觉了，不要睡觉，来睡我吧",
    "用百万个赤道般滚烫的吻把你覆盖",
    "我想你一定很忙所以只看前三个字就好啦",
    "赵兄，托你帮我办点事！啥事？这件事嘛，就是你倒过来读这句话",
    "如果有一天我说”想你了”，不是说这天我想你了，是这天我憋不住了。",
    "奇迹就留给别人吧，我有你了",
    "早知道会这么喜欢你 就不去认识你了",
    "我能帮你节省2T的硬盘 你能帮我节省两节电池么",
    "很晚才爱你，余生只啪你。",
    "日出而做，日落而息",
    "人各有志，做爱而归",
    "你知道为什么男生喜欢爱哭的女生吗?因为水多呀",
    "即使是想到你，也会让我在我的腰下湿透",
    "不想玩游戏，只想玩你",
    "许久未见，就该把她狠狠地推到墙上吻到天荒地老！",
    "学了九浅一深，可是嘿着嘿着就数错了",
    "史上知名的八大奸夫淫妇，锄禾日当午，造血干细胞，清明上河图，弯弓射大雕，复方草珊瑚，班长兼学委，平方差公式，还有一个完全搞不懂",
    "穿着衣服教我大道理，脱了衣服教我摆姿势",
    "姑娘，每周日你快乐吗?",
    "空有一颗撸管的心，上帝却给了我一个大姨妈的身体。",
    "你已经不再是依偎在我怀里嚷嚷这要吃肉棒的小可爱了",
    "运动每一天，床上你和我",
    "初识憎淫恶 用后恨知晚",
    "我才不和你说晚安，我要和你躺一张床上。",
    "时间就像乳沟，挤挤还是有的，但是一躺下就全没了",
    "可以跟你要个东西吗？ 要你属于我",
    "听说晚安是最长情的告白 可我知道早嘿是最深情的问候",
    "欲拒还迎的吻他，轻咬下巴，他咽一次口水舔一次喉结",
    "走路要牵着我呀，小时候老师没有跟你说过贵重物品要随身携带吗?",
    "但凡找我修电脑，都得带到宾馆好好研究一星期",
    "做坏事早晚都会被发现 所以要中午做",
    "为什么女人的心思你总猜不透 因为胸前的肉太厚",
    "小兔子乖乖 把腿掰掰 屁股抬抬 我要进来 不掰不掰我不掰 套套都不带 叔叔你好坏",
    "我的笔没水了~揉揉就有了",
    "有本事从屏幕里钻出来啪啪我",
    "我有逼你爱上我吗?",
    "说不出你哪里好，就是想看你洗澡。",
    "总有男人说女生卸了妆就是万圣节，其实女生眼里男生脱了裤子就是儿童节",
    "你这骚货，我差点/已经爱上你",
    "只是聊个天，为什么解我拉链",
    "睡觉重要还是陪你重要，当然是陪你睡觉重要!",
    "把她扔一边来拥抱我，她哪有我技术好哪有我紧哪有我叫的热烈和狂野",
    "曲径通幽处，花台探郞入",
    "我要偷你的体温!说人话!要艹艹~",
    "像我这种人，除了宠着也没其它办法了",
    "梦里梦见的人，醒来就该去睡了他",
    "我是个粗人，而且长得不行(注意发音)",
    "有些事不用在一晚内都做完，我们又不赶时间，可以每晚都做一做",
    "在我左腿右边，右腿的左边有一只小精灵，它活泼又聪明",
    "春眠不觉晓，处……处对象可好",
    "男人不污不成器，女人不骚不成活",
    "Fuck me hard",
    "与你相遇，如入梦中，从此不愿醒矣",
    "祝你做人不缺爱，做爱不缺人",
    "赶快进步当领导呀，我都等着你把我潜规则呢",
    "今晚让弟弟和妹妹见一面吧",
    "不含而立",
    "精力是有限的，做爱做的事热情是无限的",
    "丁丁太凉了，想放进去暖暖，不会动的",
    "想要，床头有花，床边有狗，床上有你",
    "待到重阳日，还来就菊花",
    "知道什么叫胯下生风吗 我这瓶风油精可以让你知道",
    "爱一个人是真心话 把想睡你说出口就是大冒险了",
    "熬过了年少轻狂我们就天天上床我陪你精尽人亡",
    "You are my today and all of my tomorrows. 你是我的今天，以及所有的明天",
    "你咋不上天呢?上天不如上你!",
    "好好珍惜那个爱吃醋的女孩 如果她不爱你 她原本是最爱吃肉的",
    "如果喜欢我，这个冬天你就会有个能暖手的小鸡鸡",
    "撩你都是我的虚情假意，污你才是我的目的",
    "我这个人没什么优点，就是善解人衣",
    "你就是我的药，一日见效",
    "奇迹就留给别人吧，我有你了",
    "像我这么帅的人，你想跟我约炮，我不怪你",
    "今晚别关窗 我想偷偷进你梦里",
    "你和所有的前任比起来，他们不过是前戏，你才是我想要的高潮",
    "比起做菜，我只想下面给你吃",
    "我发现你是个照骗 因为本人比照片好看多了",
    "我要睡觉了 要不要一起？",
    "我没有想要和你对着干……我想要后入",
    "十一在家闲着没事 帮忙照顾18到28cm的男孩子",
    "虽然我不是大人，但我比大人还大",
    "要不先尝尝我的中指",
    "舔咪咪，我笑着舔咪咪，我好像在哪奸过你",
    "你真是深不可测让我鞭长莫及",
    "你的唇是我吃过最好的软糖",
    "喜欢和爱的区别就是，喜欢就是想要扑倒，爱就是没完没了的扑倒",
    "姑娘，一起取精么",
    "干旱的旱字怎么写",
    "运动每一天，床上你和我",
    "今天就让你知道什么叫活好",
    "哪有时间谈恋爱，所有时光不都应该在床上度过么",
    "最粗的梦想 紧握在手上",
    "Love you so I don`t wanna go to sleep， for reality is better than a dream. 爱你所以我不愿去睡觉，因为现实比梦境更美好",
    "我想拥你入眠，将星辰变化成你的模样",
    "平胸只是为了离你更近",
    "“你是不是学过魔术？” “没有啊” “那你为什么越变越好看了”",
    "我对床上用品的要求很高，你是我最满意的。",
    "为什么避孕套没有黑色的？因为黑色显瘦",
    "做我的小公主 只吃JB不吃苦",
    "每个单身的人背后应该都自摸了很久",
    "你说你喜欢海，其实你更喜欢浪 你说你喜欢秋千，其实你更喜欢荡",
    "外面超多情侣 我害怕 我一出现 他们就后悔自己已经有女朋友了",
    "此生遇见你 竟用尽所有姿势",
    "你瘦的时候在我心里 胖了就在里面卡着出不来了",
    "你是我在床上都舍不得用力的人",
    "为你写湿 为你静止 为你做不可描述的事",
    "做不了爱人，那做爱吧",
    "别看个子矮，边干边吃奶",
    "别人女票都是吃软不吃硬，你是只吃硬",
    "小嘴真甜~~你尝过啊?",
    "没你在身边的时候我是工作狂 只要你在身边的时候我就变成床上狂",
    "见到你的第一面我就想上你，他们斯文人把这叫一见钟情",
    "聊天吗？给你一个近距离接触仙女的机会",
    "为什么分手？我到底哪里不好?你到不了底~~~",
    "真想给自己放一天假，什么都不干，就干你",
    "免费帮少女升级少妇，有意者私聊我",
    "到女人心里的路通过阴道",
    "错过我这么好的仙女 我要是你，我就自杀",
    "专业疏通下水道",
    "果然是“照骗”，真人可比这漂亮多了！",
    "为什么上帝不看A片，因为人在做天在看",
    "小撸怡情，大撸伤身，强撸灰飞烟灭 先撕长裙，后撕内裤，百撕不得其解",
    "听说你想睡我 鼓起勇气来吧",
    "不能想你，一想你全都是马赛克的画面。",
    "喜欢舌尖抵你乳尖你颤抖的模样",
    "好喜欢啪啪啪时被打屁屁 轻轻地但是很响的那种",
    "快进来啊，人家等不及了",
    "很羡慕你们能和你们喜欢的人在一起 不像我，周围都是喜欢我的人",
    "我看你挺会吃的 吃我好不好",
    "我想把你占为己有，而你却是大众炮友",
    "你知道为什么男生喜欢爱哭的女生吗？因为水多呀",
    "我不知道恋爱了一定要啪啪啪不，但我知道啪啪啪一定要跟很爱的人",
    "有很多人喜欢去远方 而我只想待在你怀里",
    "最浪漫的事，就是你汗如雨下",
    "你在的地方 处处是诗 你看我一眼 处处皆湿",
    "我想睡你很久了，行不行，不行我换人了",
    "我可以走在你后面吗?为什么?因为我想做你背后的女人",
    "我男朋友10cm左右。哇塞，你还用尺子仔细去量过？不啊，口算出来的。",
    "我想和你 每天做四件事 一日三餐",
    "你有多爱我？捧在手里怕日了，含在嘴里怕射了",
    "你穿衣服是给别人看，不穿衣服才是给我看，以后别问我穿什么好看",
    "嗯~啊~嗯~~啊~啊~啊",
    "这么久没上你 有没有很想我",
    "怕什么真理无穷，进一寸有一寸的欢喜",
    "好好干组织会培养你",
    "玉门失守",
    "你的手怎么这么凉?可能因为是传说中的冰肌玉骨",
    "如果等的人是你，迟一点出现也没关系，但是不能早一点出来",
    "为什么我们需要性生活～ 因为上边的嘴吵架，要用下边的嘴和好",
    "今日青春女生，明日成功女性",
    "恨可以含蓄，爱要坦白。",
    "醉酒后直接将你摁在墙上，粗暴的扯开你的衣服",
    "春水初生，春林初盛，春风十里，不如你。",
    "你的胸在唱歌: 我不想，我不想长大",
    "把铜离子放进牛奶中会引起蛋白质失活变性的反应叫什么？ 铜盐聚乳",
    "我们都是务实的人，爱这种东西还是少说多做",
    "总有男人说女生卸了妆就是万圣节，其实女生眼里男生脱了裤子就是儿童节",
    "为什么女人的心思你总猜不透 因为胸前的肉太厚",
    "大丈夫言而有性"
]

def get_user_last_chat(conn: PooledMySQLConnection | MySQLConnectionAbstract ) -> dict[int, datetime]:
    cursor = conn.cursor()
    cursor.execute("select user_id, max(created_at) from chat_history_statistic where created_at>(NOW() - INTERVAL 10 DAY) group by user_id")
    items = cursor.fetchall()
    res = {x[0]: x[1].astimezone(UTC) for x in items} # type: ignore
    cursor.close()
    return res # type: ignore

async def process_single_user(user_id: int, last_chat_time: datetime):
    delta = datetime.now(timezone.utc) - last_chat_time
    days = delta.days

    should_send = False
    if days in [1, 2, 4, 6]:
        should_send = True
    elif days >= 7:
        return

    if should_send:
        message_content = random.choice(messages)
        user = await user_service.safe_get_user(user_id)
        if not user:
            return
        logging.info(f'send recall message to {user_id}, days: {days}, message: {message_content}')
        active_bots = await user_active_service.list_active_bots(user)
        if not active_bots:
            return
        bot_config = active_bots[0]
        bot = bot_services.get_bot_by_bot_id(bot_config.bot_id)
        eng_bot = await tg_config_service.is_eng_only_bot_by_id(bot_config.bot_id)
        if eng_bot:
            return
        message_template = MessageTemplate(tips=message_content, 
                                           buttons=[Button(text="开始聊天", url=env_const.TMA_DIRECT_URL)])
        tg_user = await user_service.get_tg_info_by_user_id(user_id)
        send_result = await send_by_template(
            bot, tg_user.tg_id, message_template)
        if not send_result:
            logging.warning(f"send recall message failed, user_id: {user_id}, message: {message_content}")
            return
        h = RecallMessageHistory(
            user_id=user.id,
            send_to_tma_success=send_result,
            send_to_chatbot_success=send_result,
            tma_blocked=send_result,
            chat_bot_blocked=send_result,
            tma_uninitiated=send_result,
            chat_bot_uninitiated=send_result,
            content=message_content,
            type='CHAT',
            chatbot_error_msg=str(send_result),
            tma_error_msg=str(send_result),
        )
        await h.save()

async def send_messages(items: dict[int, datetime]):
    for user_id, last_chat_time in items.items():
        try:
            await process_single_user(user_id, last_chat_time)
        except Exception as e:
            logging.exception(f"Error processing user {user_id}: {e}", exc_info=True)

async def recall_message():
    connection_string = os.environ['MYSQL_SLAVE_URL']
    conn_params = utils.parse_mysql_connection_string(connection_string)
    with mysql.connector.connect(**conn_params) as conn:
        last_chats = get_user_last_chat(conn)
        await send_messages(last_chats)
