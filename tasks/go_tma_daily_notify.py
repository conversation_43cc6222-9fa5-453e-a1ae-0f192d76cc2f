import asyncio
from datetime import timed<PERSON><PERSON>
import logging
import os
import mysql.connector
from mysql.connector.abstracts import MySQLConnectionAbstract
from mysql.connector.pooling import PooledMySQLConnection
from common import copywriting_templates
from services import tg_message_service
from utils import utils
from services.bot_services import chat_bot, send_go_tma_tip

def get_chatbot_only_users(conn: PooledMySQLConnection | MySQLConnectionAbstract ) -> list[int]:
    cursor = conn.cursor()
    cursor.execute("SELECT user_id FROM chat_history_statistic GROUP BY user_id HAVING COUNT(DISTINCT platform) = 1 AND MAX(platform) = 'CHAT_BOT'")
    items = cursor.fetchall()
    res = [int(x[0]) for x in items] # type: ignore
    result = []
    windowed = utils.window_seq(res, 100)
    for gr in windowed:
        grs = [str(x) for x in gr]
        ids = f'({",".join(grs)})'
        cursor.execute("SELECT tg_id FROM tg_user WHERE uid in %s" % ids)
        tg_ids = cursor.fetchall()
        result = result + [int(x[0]) for x in tg_ids] # type: ignore
    cursor.close()
    return result

async def send_messages(tg_ids: list[int]):
    for tid in tg_ids:
        logging.info(f'send daily goto tma message to {tid}')
        message = await send_go_tma_tip(chat_bot, tid)
        await asyncio.sleep(0.2)

async def tma_daily_notify():
    connection_string = os.environ['MYSQL_SLAVE_URL']
    conn_params = utils.parse_mysql_connection_string(connection_string)
    with mysql.connector.connect(**conn_params) as conn:
        tg_ids = get_chatbot_only_users(conn)
        await send_messages(tg_ids)
