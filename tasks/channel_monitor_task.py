import logging
from datetime import datetime
from typing import List

from services.channel_monitor_service import channel_monitor_service
from services.channel_weight_service import channel_weight_service


logger = logging.getLogger(__name__)


async def run_channel_monitoring_task():
    task_start_time = datetime.now()
    logger.info(f"开始执行渠道监控任务: {task_start_time}")
    
    try:
        # 1. 运行基础监控周期
        monitor_result = await channel_monitor_service.run_monitoring_cycle()
        
        # 2. 获取所有支付类型
        pay_types = await get_all_pay_types()
        
        # 3. 为每个支付类型运行权重调整
        total_adjustments = 0
        for pay_type in pay_types:
            try:
                logger.debug(f"调整支付类型 {pay_type} 的渠道权重")
                weight_results = await channel_weight_service.adjust_channel_weights_by_pay_type(pay_type)
                total_adjustments += len(weight_results)
                
                # 记录调整详情
                for result in weight_results:
                    logger.info(f"权重调整: {result.channel}-{result.pay_type} "
                               f"{result.old_weight}% -> {result.new_weight}% ({result.reason})")
                
            except Exception as e:
                logger.error(f"调整支付类型 {pay_type} 权重时出错: {str(e)}")
        
        # 4. 任务完成统计
        task_end_time = datetime.now()
        duration = (task_end_time - task_start_time).total_seconds()
        
        logger.info(f"渠道监控任务完成: "
                   f"处理渠道 {monitor_result.channels_processed} 个, "
                   f"监控调整 {monitor_result.channels_adjusted} 个, "
                   f"权重调整 {total_adjustments} 个, "
                   f"创建告警 {monitor_result.alerts_created} 个, "
                   f"耗时 {duration:.2f}秒")
        
        # 5. 错误报告
        if monitor_result.errors:
            logger.error(f"监控任务发现 {len(monitor_result.errors)} 个错误:")
            for error in monitor_result.errors:
                logger.error(f"  - {error}")
        
        return {
            "success": True,
            "channels_processed": monitor_result.channels_processed,
            "channels_adjusted": monitor_result.channels_adjusted,
            "weight_adjustments": total_adjustments,
            "alerts_created": monitor_result.alerts_created,
            "errors": monitor_result.errors,
            "duration_seconds": duration
        }
        
    except Exception as e:
        error_msg = f"渠道监控任务执行失败: {str(e)}"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "duration_seconds": (datetime.now() - task_start_time).total_seconds()
        }

async def get_all_pay_types() -> List[str]:
    return ["wechat", "alipay"]

async def run_emergency_channel_check():
    logger.warning("执行紧急渠道检查")
    
    try:
        # 获取所有支付类型的权重摘要
        pay_types = await get_all_pay_types()
        emergency_issues = []
        
        for pay_type in pay_types:
            try:
                summary = await channel_weight_service.get_channel_weight_summary(pay_type)
                
                # 检查紧急情况
                if summary["active_channels"] == 0:
                    emergency_issues.append(f"支付类型 {pay_type} 无活跃渠道")
                elif summary["active_channels"] == 1:
                    emergency_issues.append(f"支付类型 {pay_type} 仅剩1个活跃渠道")
                elif summary["total_weight"] < 50:
                    emergency_issues.append(f"支付类型 {pay_type} 总权重过低: {summary['total_weight']}%")
                
            except Exception as e:
                emergency_issues.append(f"检查支付类型 {pay_type} 时出错: {str(e)}")
        
        if emergency_issues:
            logger.critical(f"发现紧急问题: {emergency_issues}")
        else:
            logger.info("紧急检查完成，无异常")
        
        return {
            "success": True,
            "emergency_issues": emergency_issues
        }
        
    except Exception as e:
        error_msg = f"紧急检查失败: {str(e)}"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg
        }


async def get_monitoring_status():
    """获取监控状态信息"""
    try:
        pay_types = await get_all_pay_types()
        status_info = {
            "timestamp": datetime.now().isoformat(),
            "pay_types": {}
        }
        
        for pay_type in pay_types:
            try:
                summary = await channel_weight_service.get_channel_weight_summary(pay_type)
                status_info["pay_types"][pay_type] = summary
            except Exception as e:
                status_info["pay_types"][pay_type] = {"error": str(e)}
        
        return status_info
        
    except Exception as e:
        logger.error(f"获取监控状态失败: {str(e)}")
        return {
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }


async def schedule_channel_monitoring():
    await run_channel_monitoring_task()

async def schedule_emergency_check():
    await run_emergency_channel_check()
