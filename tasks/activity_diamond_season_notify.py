import json
from aiogram.enums import ParseMode
from aiogram import Bo<PERSON>
from datetime import UTC, datetime, timedelta
import os
from dotenv import load_dotenv
import logging
import math
import random
from common.activity_diamond_model import NoticeMethod
from common.bot_common import Button, DiamondSeasonRewardCallback, MessageTemplate
from persistence.models.models import ActivityDiamondLotteryQueue, ActivityDiamondLotteryResult, ActivityDiamondSeason, ActivityDiamondTask, ActivityDiamondTaskParticipant, DiamondSeasonStatus, DiamondTaskStatus, LotteryClaimStatus, LotteryQueueStatus, ParticipationStatus, TgChannelConfig
from persistence.redis_client import redis_client
from services import bot_message_service, bot_services, tg_config_service, user_diamond_season_service, user_service
from tortoise.transactions import in_transaction


load_dotenv()

WELFARE_CHANNEL_ID = os.getenv("DIAMOND_SEASON_CHANNEL_ID")

REWARD_MSG="香港时间{start_time}-{end_time}消耗返还奖励🟡已发放，可领取‼️"
REWARD_GOTTEN_MSG="香港时间{start_time}-{end_time}消耗返还奖励🟡已发放到您的账户‼️"
# REWARD_MSG="香港时间{start_time}-{end_time}消耗返还奖励🟡已发放，可领取‼️（请尽快领取哦，{expire_time}未及时领取会失去领取资格，无法补发）"
EXPIRE_MSG="未在规定的时间内及时领取香港时间{start_time}-{end_time}返还奖励🟡，返还奖励🟡已失效已无法领取，无法补发"

TASK_COMPLETED_USER_INFO_TEMPLATE= """🎉🎉<b>恭喜以下用户完成香港时间{start_time}-{end_time}消耗💎返还🟡活动，奖励名单：</b>（不分先后顺序）

{user_info}"""

TASK_COMPLETED_USER_INFO_SUFFIX_TEMPLATE="活动回顾：{channel_url}/{message_id}"

log = logging.getLogger(__name__)


# 定时任务：赛季即将开始的预热通知
async def send_season_warming_up_notice():
    channel_id = WELFARE_CHANNEL_ID
    current_time = datetime.now(UTC).replace(tzinfo=None)
    log.info(f"send_season_warming_up_notice begin, current_time:{current_time}")
    # 查询过去 1 个小时内已开始预热，但未发通知的赛季
    start_date = current_time - timedelta(hours=1)
    season_list = await ActivityDiamondSeason.filter(warming_up_start_at__gt=start_date,  warming_up_start_at__lte=current_time, warming_up_end_at__gt=current_time, status=DiamondSeasonStatus.IN_PROGRESS.value, warming_up_notice_sent=False).all()
    if not season_list:
        return None
    log.info(f"send_season_warming_up_notice, season size:{len(season_list)}")
    bot = bot_services.diamond_season_channel_bot
    for season in season_list:
        # 发送开始通知
        start_notice = season.warming_up_notice
        try:
            await bot.send_message(chat_id=channel_id, text=start_notice, parse_mode=ParseMode.HTML)
            # 标记已发送
            season.warming_up_notice_sent = True
            await season.save()
            log.info(f"send_season_warming_up_notice success, season_id:{season.season_id},chat_id:{channel_id}")
        except Exception as e:
            logging.error(f'diamond season: send_season_warming_up_notice notice failed: season_id:{season.season_id}, chat id {channel_id} : {e}')

# 定时任务：赛季的预热通知发送到用户群（如果后台配置了按时间间隔发送）
async def send_warming_up_notice_to_user_group():
    current_time = datetime.now(UTC).replace(tzinfo=None)
    log.info(f"send_warming_up_notice_to_user_group begin, current_time:{current_time}")
    season_config = await user_diamond_season_service.get_current_warming_up_diamond_season()
    if not season_config:
        return
    method_config = season_config.warming_up_notice_method
    if method_config is None or method_config.method != NoticeMethod.TIMER.value:
        return
    # 分钟
    interval = method_config.interval
    redis_key = f"season_notice:{season_config.season_id}"
    last_sent_ts = redis_client.get(redis_key)
    if last_sent_ts is None or int(last_sent_ts.decode('utf-8')) + interval * 60 < datetime.now(UTC).timestamp():
        main_group = await tg_config_service.get_main_group()
        try:
            #send notice
            idx = random.randint(0, len(bot_services.helper_bots) - 1)   
            random_bot = bot_services.helper_bots[idx]      
            await random_bot.send_message(main_group.chat_id, season_config.warming_up_notice)
            redis_client.set(redis_key, int(datetime.now(UTC).timestamp()))
            redis_client.expire(redis_key, int(season_config.warming_up_end_at - season_config.warming_up_start_at))
            log.info(f"send_warming_up_notice_to_user_group success, season_id:{season_config.season_id},chat_id:{main_group.chat_id}")
        except Exception as e:
            logging.error(f'send_warming_up_notice_to_user_group failed: season_id:{season_config.season_id}, chat id {main_group.chat_id} : {e}')
    

# 定时任务：将当前进行中的任务插入待抽奖队列
async def check_task_to_be_lottery():
    diamond_task_config, _ = await user_diamond_season_service.get_current_diamond_task()
    if diamond_task_config is None:
        return
    result = await ActivityDiamondLotteryQueue.filter(task_id=diamond_task_config.task_id).first()
    if result is not None:
        return
    await ActivityDiamondLotteryQueue.create(task_id=diamond_task_config.task_id, lottery_at=datetime.fromtimestamp(diamond_task_config.end_at, UTC), status=LotteryQueueStatus.UNCOMPLETED.value)
    log.info(f"check_task_to_be_lottery success: task_id:{diamond_task_config.task_id},inserted")

# 定时任务：执行抽奖
async def peform_lottery():
    # 查询已到了抽奖时间的抽奖队列
    lottery_list = await ActivityDiamondLotteryQueue.filter(lottery_at__lte=datetime.now(UTC).replace(tzinfo=None), status=LotteryQueueStatus.UNCOMPLETED.value).all()
    for lottery_queue in lottery_list:
        log.info(f"peform_lottery,task_id:{lottery_queue.task_id}")
        # 忽略状态
        task = await ActivityDiamondTask.filter(task_id=lottery_queue.task_id).first()
        if task is None:
            log.error(f"peform_lottery,task_id:{lottery_queue.task_id},task not found")
            continue

        user_ids = await user_diamond_season_service.get_task_completed_participants(lottery_queue.task_id)
        if not user_ids:
            log.error(f"peform_lottery,task_id:{lottery_queue.task_id},no completed participants")
            lottery_queue.status = LotteryQueueStatus.COMPLETED.value
            await lottery_queue.save()
            continue
        # 从参与者中随机抽取prize_count个用户
        scaled_prize_count = math.ceil(len(user_ids) * task.grand_prize_count / task.max_participants)
        n = min(scaled_prize_count, len(user_ids))
        winner_ids = random.sample(user_ids, n)
        diamond_amount = math.ceil(task.required_diamond_amount * task.grand_prize_return_rate / 10000)
        lottery_results = []
        for user_id in winner_ids:
            lottery_result = ActivityDiamondLotteryResult(season_id=task.season_id, task_id=lottery_queue.task_id, user_id=user_id, diamond_amount=diamond_amount, status=LotteryClaimStatus.UNCLAIMED.value)
            lottery_results.append(lottery_result)
        async with in_transaction():
            await ActivityDiamondLotteryResult.bulk_create(lottery_results)
            lottery_queue.status = LotteryQueueStatus.COMPLETED.value
            await lottery_queue.save()
            log.info(f"peform_lottery succuess,task_id:{lottery_queue.task_id}, winners:{winner_ids}")
        # 抽完奖就可以在用户群发送该任务的整体结果了
        await _send_task_completed_user_result(task, user_ids, winner_ids, diamond_amount)

async def _send_task_completed_user_result(task: ActivityDiamondTask, user_ids, winner_ids, grand_prize: int):
    required_diamond_amount = task.required_diamond_amount
    return_rate = task.return_rate
    regular_prize = math.ceil(required_diamond_amount * return_rate/10000)
    user_info_str = await _list_completed_participants(user_ids, winner_ids, grand_prize, regular_prize)

    msg = TASK_COMPLETED_USER_INFO_TEMPLATE.format(start_time=_format_datetime(task.start_at), end_time=_format_datetime(task.end_at), user_info=user_info_str) 

    message_id_raw = redis_client.get(f"diamond_task:msg_id:{str(task.task_id)}")
    channel_id = WELFARE_CHANNEL_ID
    if message_id_raw  is not None: 
        message_id = int(message_id_raw.decode('utf-8')) 
        channel_obj = await TgChannelConfig.filter(chat_id=channel_id).first()
        if channel_obj:
            msg_suffix = TASK_COMPLETED_USER_INFO_SUFFIX_TEMPLATE.format(channel_url=channel_obj.url, message_id=message_id)
            msg = msg + "\n\n" + msg_suffix
    
    try:     
        bot = bot_services.diamond_season_channel_bot  
        await bot.send_message(channel_id, msg, parse_mode=ParseMode.HTML)
        #await _handle_pin_unpin_tg_msg(main_group.chat_id, message.message_id, random_bot)
    except Exception as e:
        logging.error(f'_send_task_completed_user_result failed: task_id:{task.task_id}, {e}')


async def _handle_pin_unpin_tg_msg(chat_id: int, msg_id: int, random_bot: Bot):
    try: 
        await random_bot.pin_chat_message(chat_id=chat_id, message_id=msg_id, disable_notification=True)
        msg_obj = {'chat_id': chat_id, 'msg_id': msg_id, 'bot_id': random_bot.id}
        redis_key = 'diamond_task:pin_msg'
        msg_list_raw_str = redis_client.get(redis_key)
        
        if msg_list_raw_str is None:
            msg_list = [msg_obj]
            redis_client.set(redis_key, json.dumps(msg_list))
        else:
            msg_list:list = json.loads(msg_list_raw_str.decode('utf-8'))
            log.info(f'_handle_pin_unpin_tg_msg, msg_list: {msg_list}')
            msg_list.append(msg_obj)
            if len(msg_list) > 3:
                # 只保留最新的3条
                to_be_delete_msg_list = msg_list[:-3]
                unpin_list = []
                for to_be_delete_msg_obj in to_be_delete_msg_list:
                    try:
                        sender_bot = await tg_config_service.get_sender_bot_by_id(to_be_delete_msg_obj['bot_id'])
                        await sender_bot.unpin_chat_message(chat_id=to_be_delete_msg_obj['chat_id'], message_id=to_be_delete_msg_obj['msg_id'])
                        unpin_list.append(to_be_delete_msg_obj['msg_id'])
                    except Exception as e:
                        logging.error(f'_handle_pin_unpin_tg_msg unpin failed: chat_id:{chat_id}, msg obj:{to_be_delete_msg_obj}, {e}')
                msg_list = [item for item in msg_list if item['msg_id'] not in unpin_list]
                log.info(f'_handle_pin_unpin_tg_msg, unpin_list: {unpin_list}, msg_list: {msg_list}')
            redis_client.set(redis_key, json.dumps(msg_list))
        # 1年
        redis_client.expire(redis_key, 3600*24*360)
    except Exception as e:
        logging.error(f'_handle_pin_unpin_tg_msg failed: chat_id:{chat_id}, msg_id:{msg_id}, bot:{random_bot}, {e}')

async def _list_completed_participants(user_ids:list[int], winner_ids: list[int], grand_prize: int, regular_prize: int):  
    user_ids.sort()
    winner_id_set = set(winner_ids)
    nickname_map = await user_service.map_nickname(user_ids)
    messages = []
    for index, user_id in enumerate(user_ids, start=1):
        nickname = nickname_map.get(user_id, f"用户{user_id}")
        if user_id in winner_id_set:
            message = f"{index}. {nickname}，获得{grand_prize}🟡（大奖）"
        else:
            message = f"{index}. {nickname}，获得{regular_prize}🟡"
        messages.append(message)
    return "\n".join(messages)  
        
# 定时任务：通知活动开始
async def send_task_start():
    chat_bot_name = os.getenv("DIAMOND_SEASON_CHAT_BOT")
    tma_bot_name = os.getenv("DIAMOND_SEASON_TMA_BOT")
    channel_id = WELFARE_CHANNEL_ID
    current_time = datetime.now(UTC).replace(tzinfo=None)
    log.info(f"send_task_start begin, current_time:{current_time}")
    # 先得查询当前进行中的赛季
    season = await ActivityDiamondSeason.filter(start_at__lte=current_time, end_at__gte=current_time, status=DiamondSeasonStatus.IN_PROGRESS.value).first()
    if season is None:
        return None
    log.info(f"send_task_start, season_id:{season.season_id}")
    # 查询过去 1 个小时内已开始，但未发通知的任务
    start_date = current_time - timedelta(hours=1)
    task_list = await ActivityDiamondTask.filter(season_id=season.season_id,start_at__gt=start_date, start_at__lte=current_time, end_at__gt=current_time, status=DiamondTaskStatus.ACTIVE.value, start_notice_sent=False).all()
    log.info(f"send_task_start, task_list size:{len(task_list)}")
    bot = bot_services.diamond_season_channel_bot
    for task in task_list:
        # 发送开始通知
        start_notice = task.start_notice
        task_id = str(task.task_id)
        msg = user_diamond_season_service.START_NOTICE_MSG.format(start_notice_config=start_notice, tma_bot_user_name=tma_bot_name, bot_user_name=chat_bot_name, task_id=task_id)
        try:
            message = await bot.send_message(chat_id=channel_id, text=msg, parse_mode=ParseMode.HTML)
            redis_key = f"diamond_task:msg_id:{task_id}"
            redis_client.set(redis_key, message.message_id)
            redis_client.expire(redis_key, (task.end_at - task.start_at).seconds + 3600*24)
            # 标记已发送
            task.start_notice_sent = True
            await task.save()
            log.info(f"send_task_start success, task_id:{task_id},chat_id:{channel_id}")
        except Exception as e:
            logging.error(f'diamond season: send_task_start notice failed: task_id:{task_id}, chat id {channel_id} : {e}')
        
  

# 定时任务：通知当前时间段的活动已结束
async def send_task_end():
    channel_id = WELFARE_CHANNEL_ID
    current_time = datetime.now(UTC).replace(tzinfo=None)
    log.info(f"send_task_end,current_time:{current_time}")
    # 先得查询当前进行中的赛季；或是刚结束1小时内的赛季（防止赛季的最后1个任务的结束通知发不出去）
    end_date_start = current_time - timedelta(hours=1)
    season = await ActivityDiamondSeason.filter(start_at__lte=current_time, end_at__gte=end_date_start, status=DiamondSeasonStatus.IN_PROGRESS.value).first()
    if season is None:
        return None
    # 查询过去 1 个小时内刚刚结束的任务
    task_list = await ActivityDiamondTask.filter(season_id=season.season_id, end_at__gt=end_date_start, end_at__lte=current_time, status=DiamondTaskStatus.ACTIVE.value, end_notice_sent=False).all()
    bot = bot_services.diamond_season_channel_bot
    for task in task_list:
        # 发送活动结束通知
        end_notice = task.end_notice
        try:
            await bot.send_message(chat_id=channel_id, text=end_notice, parse_mode=ParseMode.HTML)
            # 标记已发送
            task.end_notice_sent = True
            await task.save()
            log.info(f"send_task_end success, task_id:{task.task_id},chat_id:{channel_id}")
        except Exception as e:
            logging.error(f'diamond season: send_task_end notice failed: task_id:{task.task_id}, chat id {channel_id} : {e}')

# 定时任务: 通知活动未完成的用户做任务失败(分成5个定时任务触发，每个任务处理1/5的数据，按用户id取模分片)
# partition_num: 0-4
async def send_task_failure(partition_num: int):
    current_time = datetime.now(UTC).replace(tzinfo=None)
    min_time = current_time - timedelta(hours=3)   
    # 查出需要发送消息的用户列表
    participant_list = await ActivityDiamondTaskParticipant.filter(status=ParticipationStatus.ENROLLED.value, failure_msg_sent=False, task_end_at__lte=current_time, task_end_at__gt=min_time).all()
    if not participant_list:
        return
    # 按用户id取模分片
    target_participant_list = [participant for participant in participant_list if participant.user_id % 5 == partition_num]
    log.info(f"send_task_failure(partition number: {partition_num}) begin: target participant_list size: {len(target_participant_list)}") 
    if not target_participant_list:
        return
    
    task_map = {}
    count = 0
    for participant in target_participant_list:
        task = task_map.get(participant.task_id, None)
        if task is None:
            # 忽略状态
            task = await ActivityDiamondTask.filter(task_id=participant.task_id).first()
            if task is None:
                log.error(f"send_task_failure(partition number: {partition_num}),task_id:{participant.task_id},task not found")
                continue
            task_map[participant.task_id] = task

        if participant.recharge_msg_sent:
            msg = f"香港时间{_format_datetime(task.start_at)}-{_format_datetime(task.end_at)}消耗💎任务您未完成（完成进度{participant.diamond_consumption}/{task.required_diamond_amount}）❗️❗️在您完成任务的过程中💎已经消耗完，当时已提醒您充值补充💎数量可继续完成任务。"
        else:
            msg = f"香港时间{_format_datetime(task.start_at)}-{_format_datetime(task.end_at)}消耗💎任务您未完成（完成进度{participant.diamond_consumption}/{task.required_diamond_amount}）❗️❗️"

        #  发送消息
        template = MessageTemplate(       
            log_tag="DIAMOND_SEASON_TASK_FAILURE",
            tips=msg
        )
        try:
            user = await user_service.get_by_id(participant.user_id)
            bot, _ = await bot_message_service.send_user_template_message(user, template)
            if bot is None:
                log.error(f"send_task_failure(partition number: {partition_num}) error: task_id:{participant.task_id},user_id:{participant.user_id},send message failed")
                continue
            # 更新db，标记已发送
            participant.failure_msg_sent = True
            await participant.save()
            count += 1
        except Exception as e:
            log.error(f"send_task_failure(partition number: {partition_num}) error: task_id:{participant.task_id},user_id:{participant.user_id},send message failed:{e}")

    log.info(f"send_task_failure(partition number: {partition_num}) end: processed people count:{count}")  

        

# 定时任务: 通知活动达标用户领取奖励(分成5个定时任务触发，每个任务处理1/5的数据，按用户id取模分片)
# partition_num: 0-4
async def send_task_reward(partition_num: int):
    current_time = datetime.now(UTC).replace(tzinfo=None)
    # 查出需要发送消息的用户列表 （活动结束时间得早于当前时间，但还没有发送过消息）
    participant_list = await ActivityDiamondTaskParticipant.filter(status=ParticipationStatus.COMPLETED.value, reward_msg_sent=False, task_end_at__lte=current_time).all()
    log.info(f"send_task_reward begin, current_time:{current_time}")
    if not participant_list:
        log.warning(f"send_task_reward(partition number: {partition_num}) no participant found")
        return
    # 按用户id取模分片
    target_participant_list = [participant for participant in participant_list if participant.user_id % 5 == partition_num]
    log.info(f"send_task_reward(partition number: {partition_num}) begin: target participant_list size: {len(target_participant_list)}") 
    if not target_participant_list:
        return
    
    
    # 查询已完成抽奖可发奖励的任务
    task_id_list = [participant.task_id for participant in target_participant_list]
    task_id_list = list(set(task_id_list))
    lottery_finished_list = await ActivityDiamondLotteryQueue.filter(task_id__in=task_id_list, status=LotteryQueueStatus.COMPLETED.value).all()
    lottery_finished_task_id_list = [lottery.task_id for lottery in lottery_finished_list]
    if not lottery_finished_task_id_list:
        log.warning(f"send_task_reward(partition number: {partition_num}) no lottery finished task found")
        return
    task_list = await ActivityDiamondTask.filter(task_id__in=lottery_finished_task_id_list).all()
    if not task_list:
        log.warning(f"send_task_reward(partition number: {partition_num}) no task found")
        return
    
    task_map = {str(task.task_id): task for task in task_list} 
    count = 0
    for participant in target_participant_list:
        task = task_map.get(participant.task_id, None)
        if task is None:
           continue
        if task.diamond_gotten_manually:
            # 手动领取奖励
            msg = REWARD_MSG.format(start_time=_format_datetime(task.start_at), end_time=_format_datetime(task.end_at))
            template = MessageTemplate(       
                log_tag="DIAMOND_SEASON_TASK_REWARD",
                tips=msg,
                buttons=[
                    Button(text="领取", callback_data=DiamondSeasonRewardCallback(task_id=participant.task_id, user_id=participant.user_id)),
                ],
            )
        else:
            # 自动发放奖励
            result, desc = await user_diamond_season_service.receive_diamond_reward(participant.user_id, participant.task_id)
            if result is None:
                log.error(f"send_task_reward(partition number: {partition_num}) error: task_id:{participant.task_id},user_id:{participant.user_id}, automatically receive reward failed:{desc}")
                continue
            msg = REWARD_GOTTEN_MSG.format(start_time=_format_datetime(task.start_at), end_time=_format_datetime(task.end_at))
            template = MessageTemplate(       
                log_tag="DIAMOND_SEASON_TASK_REWARD",
                tips=msg)
        try:
            user = await user_service.get_by_id(participant.user_id)
            bot, sent_msg = await bot_message_service.send_user_template_message(user, template)
            if bot is None:
                log.error(f"send_task_reward(partition number: {partition_num}) error: task_id:{participant.task_id},user_id:{participant.user_id},send message failed")
                continue
            log.info(f"send_task_reward(partition number: {partition_num}) success: task_id:{participant.task_id},user_id:{participant.user_id},send message success")
            # 更新db，标记已发送
            participant.reward_msg_sent = True
            await participant.save()
            count += 1
        except Exception as e:
            log.error(f"send_task_reward(partition number: {partition_num}) error: task_id:{participant.task_id},user_id:{participant.user_id},send message failed:{e}")

    log.info(f"send_task_reward(partition number: {partition_num}) end: processed people count:{count}")   

def _format_datetime(dt_utc: datetime):
    dt_utc8 = dt_utc + timedelta(hours=8)
    formatted_date = dt_utc8.strftime("%Y年%m月%d日")
    start_time = dt_utc8.strftime("%H:%M")
    return f"{formatted_date}{start_time}"       
