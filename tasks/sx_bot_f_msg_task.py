import asyncio
import logging
import os
import datetime
import uuid



from services.sx_bot_service import SXUserService,send_message,get_u_all_name
from common.models.forward_msg import ForwardMsg
from services.recharge_service import check_recharge_user
from services.account_service import AccountService
from services.user_service import UserService

from queue_msg.redis_queue import RedisQueue

log = logging.getLogger(__name__)

user_service = UserService()

REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")

forward_msg_queue = RedisQueue(queue_name='forward_msg_queue',url=REDIS_URL)

FORWAD_GROUP_ID = int(os.getenv("FORWARD_GROUP_ID",0))


MAX_MESSAGE_LENGTH = 4096


async def forward_msg(message: ForwardMsg):

    msg_type = message.msg_type
    tg_id = message.tg_id
    user_id = message.user_id
    channel_id = message.channel_id
    
    return 
    #获取用户余额
    t_balance = await AccountService.get_total_balance(user_id)
    
    message.t_balance = t_balance
    
    is_vip = await check_recharge_user(user_id=user_id)

    if is_vip:
        #放入队列
        await forward_msg_queue.enqueue(msg_id=str(uuid.uuid4()),message=message.dict())
    # sx_user = await SXUserService.get_sx_user(tg_id)


    # if not sx_user:
    #     log.info(f"forward_msg: sx_user not found for tg_id={tg_id}，create new sx_user")
    #     sx_user = await SXUserService.create_sx_user(tg_id=tg_id,user_id=user_id,group_id=FORWAD_GROUP_ID,channel_id=channel_id,is_vip=is_vip)

    # if is_vip and sx_user and sx_user.forum_topic_name.find("VIP用户") == -1:
    #     sx_user.forum_topic_name = f"VIP用户-用户c_{channel_id}:{tg_id}"
    #     sx_user = await SXUserService.update_sx_user_topic_name(sx_user)
    # if sx_user and msg_type == 'text':

    #     t_balance = await AccountService.get_total_balance(user_id)

    #     name = await get_u_all_name(user_id)

    #     current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    #     #发送用户状态信息
    #     u_s_msg = f"\n\n####{current_time}:用户状态信息: uid#{user_id},tg_id#{tg_id},channel_id#{channel_id},\n用户名:{name}\n💎 当前余额:{t_balance}"

    #     # 拼接消息并截取
    #     full_message = message.text + u_s_msg

    #     if len(full_message) > MAX_MESSAGE_LENGTH:
    #         log.warning(f"forward_msg: message too long, cut it, len={len(full_message)}, message={full_message}")
    #         full_message = full_message[-MAX_MESSAGE_LENGTH:]

    #     await send_message(chat_id=FORWAD_GROUP_ID, text=full_message,message_thread_id=sx_user.message_thread_id)
    # else:
    #     log.error(f"forward_msg error: sx_user {sx_user},not found for message={message}")

