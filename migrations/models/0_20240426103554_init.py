from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS `aerich` (
    `id` INT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `version` VA<PERSON>HAR(255) NOT NULL,
    `app` VARCHAR(100) NOT NULL,
    `content` JSON NOT NULL
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `account` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `user_id` BIGINT NOT NULL UNIQUE,
    `award_balance` BIGINT NOT NULL,
    `charge_balance` BIGINT NOT NULL,
    `total_balance` BIGINT NOT NULL,
    <PERSON><PERSON>Y `idx_account_user_id_6b740d` (`user_id`)
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `check_in` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `account_id` CHAR(36) NOT NULL,
    `check_in_at` DATE NOT NULL,
    UNIQUE KEY `uid_check_in_account_b3d00e` (`account_id`, `check_in_at`)
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `google_user` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `account_id` CHAR(36) NOT NULL UNIQUE,
    `identity` VARCHAR(128) NOT NULL UNIQUE,
    `name` VARCHAR(128) NOT NULL,
    `profile_image` VARCHAR(1024) NOT NULL,
    KEY `idx_google_user_account_7fde2c` (`account_id`)
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `invitation` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `invitation_id` CHAR(36) NOT NULL UNIQUE,
    `account_id` CHAR(36) NOT NULL UNIQUE,
    `inviter_id` CHAR(36) NOT NULL,
    `invited_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    KEY `idx_invitation_account_daab9c` (`account_id`),
    KEY `idx_invitation_inviter_ad1566` (`inviter_id`)
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `ledger_entry` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `user_id` BIGINT NOT NULL,
    `ledger_entry_id` CHAR(36) NOT NULL UNIQUE,
    `transaction_id` VARCHAR(128) NOT NULL UNIQUE,
    `amount` BIGINT NOT NULL,
    `type` VARCHAR(128) NOT NULL  COMMENT 'DEBIT: DEBIT\nCREDIT: CREDIT',
    `source` VARCHAR(128) NOT NULL  COMMENT 'RE_CHARGE: RE_CHARGE\nPAY: PAY',
    `before_balance` BIGINT NOT NULL,
    `after_balance` BIGINT NOT NULL,
    `description` VARCHAR(255) NOT NULL,
    KEY `idx_ledger_entr_user_id_da1446` (`user_id`),
    KEY `idx_ledger_entr_ledger__83ddb9` (`ledger_entry_id`),
    KEY `idx_ledger_entr_transac_bc70e9` (`transaction_id`)
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `oauth_user` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `uid` BIGINT NOT NULL,
    `provider_name` VARCHAR(50) NOT NULL,
    `provider_user_id` VARCHAR(512) NOT NULL,
    `user_info` JSON NOT NULL,
    UNIQUE KEY `uid_oauth_user_provide_9eadb6` (`provider_name`, `provider_user_id`),
    KEY `idx_oauth_user_uid_14c5ee` (`uid`),
    KEY `idx_oauth_user_provide_26228c` (`provider_user_id`)
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `pay_order` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `user_id` BIGINT NOT NULL,
    `pay_order_id` CHAR(36) NOT NULL UNIQUE,
    `product_id` CHAR(36) NOT NULL,
    `product_price` BIGINT NOT NULL,
    `total_fee` BIGINT NOT NULL,
    `amount` BIGINT NOT NULL,
    `status` VARCHAR(128) NOT NULL  COMMENT 'CREATED: CREATED\nSUCCESS: SUCCESS\nFAILED: FAILED',
    `finished_at` DATETIME(6),
    KEY `idx_pay_order_user_id_66f104` (`user_id`),
    KEY `idx_pay_order_pay_ord_cf9e1c` (`pay_order_id`)
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `product` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `product_id` CHAR(36) NOT NULL UNIQUE,
    `name` VARCHAR(128) NOT NULL UNIQUE,
    `price` BIGINT NOT NULL,
    KEY `idx_product_product_5ec362` (`product_id`)
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `recharge_order` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `recharge_order_id` CHAR(36) NOT NULL UNIQUE,
    `user_id` BIGINT NOT NULL,
    `amount` BIGINT NOT NULL,
    `pay_fee` BIGINT NOT NULL  DEFAULT 0,
    `pay_currency` VARCHAR(16) NOT NULL  DEFAULT '',
    `status` VARCHAR(128) NOT NULL  COMMENT 'INIT: INIT\nPENDING: PENDING\nSUCCEED: SUCCEED\nFAILED: FAILED\nCANCELED: CANCELED',
    `recharge_channel` VARCHAR(128) NOT NULL  COMMENT 'STRIPE: STRIPE\nCHECK_IN: CHECK_IN\nACCOUNT_CREATION: ACCOUNT_CREATION\nINVITATION: INVITATION\nINVITEE_RECHARGE_REWARD: INVITEE_RECHARGE_REWARD',
    `out_order_id` VARCHAR(128) NOT NULL UNIQUE,
    `finished_at` DATETIME(6),
    `raw_response` LONGTEXT,
    KEY `idx_recharge_or_recharg_2232f7` (`recharge_order_id`),
    KEY `idx_recharge_or_user_id_84798e` (`user_id`),
    KEY `idx_recharge_or_out_ord_cf3eef` (`out_order_id`)
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `role_config` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `role_name` VARCHAR(512) NOT NULL,
    `role_avatar` VARCHAR(512) NOT NULL,
    `introduction` LONGTEXT NOT NULL,
    `data_config` JSON NOT NULL,
    `tags` LONGTEXT NOT NULL,
    `spec_version` VARCHAR(128) NOT NULL  COMMENT '默认酒馆v2版本' DEFAULT 't_v2',
    `uid` BIGINT NOT NULL  COMMENT '创建者id,0表示系统创建',
    `status` BOOL NOT NULL  COMMENT '是否启用' DEFAULT 1,
    `privacy` BOOL NOT NULL  COMMENT '是否公开' DEFAULT 0,
    KEY `idx_role_config_uid_c1e3ce` (`uid`)
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `system_config` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `openai_max_context` INT NOT NULL,
    `openai_max_tokens` INT NOT NULL,
    `openai_temperature` DOUBLE NOT NULL,
    `openai_top_p` DOUBLE NOT NULL,
    `openai_frequency_penalty` DOUBLE NOT NULL,
    `openai_presence_penalty` DOUBLE NOT NULL,
    `openai_stop` LONGTEXT NOT NULL,
    `prompt_content` LONGTEXT NOT NULL,
    `name` LONGTEXT NOT NULL,
    `des` LONGTEXT NOT NULL
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `tag_list_order` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `tags_order` JSON NOT NULL
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `users_pw` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `email` VARCHAR(255) NOT NULL UNIQUE,
    `password_hash` VARCHAR(2048) NOT NULL,
    `nickname` VARCHAR(50) NOT NULL
) CHARACTER SET utf8mb4;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        """
