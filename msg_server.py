from asyncio import sleep
import asyncio
import logging
import os, json
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from fastapi.concurrency import asynccontextmanager
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from tortoise import <PERSON><PERSON>ise, run_async
from tortoise.contrib.fastapi import register_tortoise
from dotenv import load_dotenv
import sentry_sdk
from common import loggers

load_dotenv()

from services.sx_bot_service import MessageSender,API_TOKEN as bot_token
from tasks.sx_bot_f_msg_task import forward_msg_queue,forward_msg_queue

from controllers.test_contorller.test_sx_bot import test_sx_router

import uvicorn

from tasks import tg_message_task



cors_origins = os.getenv("CORS_ORIGINS", "").split(",")
sentry_dsn = os.getenv("SENTRY_DSN")
if sentry_dsn:
    sentry_sdk.init(
        dsn=sentry_dsn,
        traces_sample_rate=1.0,
        profiles_sample_rate=1.0,
    )

if os.environ.get("ENV") == "production":
    app = FastAPI(redoc_url=None, docs_url=None, openapi_url=None)
else:
    app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=[
        "Conversation-Id",
        "Message-Id",
        "Message-Version",
        "Human-Message-Id",
        "Current-Language",
    ],
)
app.include_router(test_sx_router)

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s (%(filename)s:%(lineno)d) [%(levelname)s] [%(thread)d] %(message)s",
    handlers=[logging.StreamHandler()],
)
log = logging.getLogger(__name__)

log.info("Starting server...")


Tortoise.init_models(["persistence.models.models"], "models")
register_tortoise(
    app=app,
    db_url=os.environ["MYSQL_URL"],
    modules={"models": ["persistence.models.models"]},
    generate_schemas=True,
)
scheduler = AsyncIOScheduler()

forward_message_sender = MessageSender(bot_token=bot_token, queue=forward_msg_queue, name="sx_bot")

@app.on_event("startup")
async def startup_event():
    
    asyncio.create_task(forward_message_sender.process_queue())

# Api Url：http://127.0.0.1:8800/docs
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8950)
