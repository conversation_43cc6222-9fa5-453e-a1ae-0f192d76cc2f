import asyncio
import logging
import os
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from tortoise import Tortoise
from tortoise.contrib.fastapi import register_tortoise
from dotenv import load_dotenv
load_dotenv()

import sentry_sdk
from common import loggers
from controllers.group_helper_webhook import group_helper_router, on_startup, on_shutdown
from controllers.health_check import health_check_router
from controllers.discussion_webhook import bot_discussion_router
import uvicorn

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s (%(filename)s:%(lineno)d) [%(levelname)s] [%(thread)d] %(message)s",
    handlers=[logging.StreamHandler()],
)
log = logging.getLogger(__name__)

log.info("Starting server...")

cors_origins = os.getenv("CORS_ORIGINS", "").split(",")
sentry_dsn = os.getenv("SENTRY_DSN")
if sentry_dsn:
    sentry_sdk.init(
        dsn=sentry_dsn,
        traces_sample_rate=1.0,
        profiles_sample_rate=1.0,
    )

if os.environ.get("ENV") == "production":
    app = FastAPI(redoc_url=None, docs_url=None, openapi_url=None)
else:
    app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=[
        "Conversation-Id",
        "Message-Id",
        "Message-Version",
        "Human-Message-Id",
        "Current-Language",
    ],
)

app.include_router(group_helper_router)
app.include_router(health_check_router)
app.include_router(bot_discussion_router)
# app.include_router(telegram_webhook.tg_router)

@app.on_event("startup")
async def startup_event():
    asyncio.create_task(on_startup())

@app.on_event("shutdown")
async def shutdown_event():
    on_shutdown()

Tortoise.init_models(["persistence.models.models"], "models")
register_tortoise(
    app=app,
    db_url=os.environ["MYSQL_URL"],
    modules={"models": ["persistence.models.models"]},
    generate_schemas=True,
)

# Api Url：http://127.0.0.1:8800/docs
# 解析命令行参数
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8903)
