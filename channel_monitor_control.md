每15分钟进行一次成率相关运算：
以下只能处理支付渠道<=5个。

1、排除一些订单：
 a. 支付成功后，同支付方式、同金额的订单：注意只排除同支付方式的。
 b. 支付成功的订单前，不同金额的支付订单；

 注：不排除：多渠道测试全部排除、FAILED 订单单独报警处理、渠道一次循环前的尝试、同渠道重复尝试
以下这3种可以排除，但需要增加个验证：多渠道测试全部排除、渠道一次循环前的尝试、同渠道重复尝试
可能增加的验证：域名更换、后续有成功支付

新手保护期5单：需要注意，最近3小时内才生效的渠道，可能不会到5单，但也不应该报警，应该等待其到达7单后再参与计算。

2、以半小时为单位，划分过去3个小时的所有订单，共分6个时间单位。后面计算，最低取最近半小时内订单，不足则取最近1小时（2个时间单位），还不足则取最近1.5小时（3个时间单位）……

3、为每个支付方式下的每个渠道划分参与计算的订单：
“生效中的”每个渠道、每个支付方式，单独进行计算，按照1的规则去以半小时为单位取订单，一旦满足10单则不再取更多时间单位，如过取满6个时间单位够5单则可进行计算，否则，3小时6个时间单位不满5单则进行报警，单量异常低。



以下处理，都去分开支付方式，在同一支付方式下进行计算、比较、处理：
4、检测成率最低要求只：（会停止部分渠道）
在相同的支付方式下，如果所有支付渠道，成率同时低于45%（<0.45），则取此时成率最高的渠道，权重设置为100%，终止后续操作，并进行报警“所有渠道低于45%”。（尝试电话报警）

5、处理与最高成率差距大的渠道：
计算成率最高渠道，将成率差>=25%的其他渠道统一设置权重5%。处理后，同一支付方式下，所有渠道都与最高成率差>=25%，剩余所有权重都给这个最高成率渠道，进行报警“成率差距大且仅剩1个”（尝试电话报警），并终止后续处理。

6、处理成率低于35%的渠道：
成率低于35%（<0.35），则权重设置为5%，并对这个渠道进行报警。
处理后，同一支付方式下，只剩余最后1个渠道，剩余所有权重都给这个渠道，进行报警“仅剩1个35%以上渠道”（尝试电话报警），并终止后续处理。


7、处理成率低于45%的渠道：针对上述4中所有成率低于35%的渠道之外的渠道
针对成率低于45%的渠道（<0.45，>=0.35）：
如果还有成率高于45%的渠道，则对那些成率低于45%的渠道，将其权重设置为10%。
处理后，同一支付方式下，只剩余最后1个渠道，剩余所有权重都给这个渠道，进行报警“仅剩1个45%以上渠道”（尝试电话报警），并终止后续处理。


8、按步骤执行处理剩余的支付渠道：
a. 如果最高成率与第二成率相差15%及以上：最高成率的权重设置为60%，剩余的第2、3、4……等渠道均分剩余权重。（5、6、7处理的渠道不再重新设置权重），终止后续处理。
b. 处理与最高成率相差15%以上的渠道：所有与最高成率相差15%以上的渠道，权重设置为10%。
c. 如果最高成率与第二成率相差10%以上：最高成率的权重设置为50%，剩余的第2、3、4……等渠道均分剩余权重。（4和5的渠道不再重新设置权重），终止后续处理。
d. 处理与最高成率相差10%以上的渠道：所有与最高成率相差10%以上的渠道，权重设置为15%。剩余渠道均分剩余权重。