import os
import re
from fastapi import Depends, HTTPException, Header
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from fastapi.routing import APIRouter
from admin.auth import is_admin_user
from common.entity import RegexRuleRequest, RegexRuleResponse
from common.models.chat_request import PresetConfig
from persistence.models.models import RegexAffect, RegexOption
from persistence.presets import presetsPersistence
from services import config_service, regex_service
from pydantic import BaseModel
from utils import message_utils, response_util
import utils.regex_converter as rc


regex_router = APIRouter(dependencies=[Depends(is_admin_user)])


@regex_router.post("/regex/save")
async def saveReg(rule: RegexRuleRequest):
    rule_model = await regex_service.add_regex_rule(rule)
    return RegexRuleResponse.from_model(rule_model)


@regex_router.post("/regex/update")
async def updateReg(request: RegexRuleRequest):
    rule = await regex_service.get_regex_rule_by_id(request.rule_id)
    if rule is None:
        return response_util.def_error("rule not found")
    request.copy_to_model(rule)
    rule = await regex_service.update_regex_rule(rule)
    return RegexRuleResponse.from_model(rule)


class TestRegexRuleRequest(BaseModel):
    regex: str = ""
    testRegTxt: str = ""
    replacement: str = ""


@regex_router.post("/regex/test")
async def test_regex_rule(rule: TestRegexRuleRequest):
    if not rule.regex or not rule.testRegTxt:
        return response_util.ok(data={"success": True, "testRegTxt": "Empty"})
    content = message_utils.run_js_regex(rule.regex, rule.replacement, rule.testRegTxt)
    return response_util.ok(data={"success": True, "testRegTxt": content})


@regex_router.get("/regex/all")
async def get_all_regex_rules():
    affects = list(RegexAffect.__members__.values())
    options = list(RegexOption.__members__.values())
    all_regex = await regex_service.get_all_regex_rules()
    all_regex.sort(key=lambda x: x.sort_order)
    rules = [RegexRuleResponse.from_model(rule) for rule in all_regex]
    return response_util.ok(
        data={"all_regex": rules, "affects": affects, "options": options}
    )


@regex_router.post("/regex/delete")
async def delete_regex_rule(rule_id: str):
    rule = await regex_service.get_regex_rule_by_id(rule_id)
    if rule is None:
        return response_util.def_error("rule not found")
    rule = await regex_service.delete_regex_rule(rule)
    return response_util.ok()


@regex_router.post("/regex/update_order")
async def update_regex_order(
    rule_id: str,
    order: int,
):

    regex_list = await regex_service.get_all_regex_rules()
    regex_list.sort(key=lambda x: x.sort_order)
    regex_order_map = {str(regex.rule_id): i * 10 for i, regex in enumerate(regex_list)}
    for regex in regex_list:
        if str(regex.rule_id) == rule_id:
            regex_order_map[str(regex.rule_id)] = regex_order_map[rule_id] + order*15
    regex_list.sort(key=lambda x: regex_order_map[str(x.rule_id)])
    for i, regex in enumerate(regex_list):
        if regex.sort_order != i:
            regex.sort_order = i
            await regex_service.update_regex_rule(regex)

    return response_util.ok(data={"message": "Order updated successfully"})
