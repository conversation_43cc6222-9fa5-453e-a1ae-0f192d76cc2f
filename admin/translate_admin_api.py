import asyncio
from io import String<PERSON>
import json
import logging
from typing import Optional
from fastapi import APIRouter
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from ai import inner_bot
from common.common_constant import Language
from common.translate_model import (
    TranslateSubTag,
    TranslateTaskStatus,
    TranslateTaskType,
)
from persistence import redis_client
from persistence.models.models import TranslateResource
from services import translate_service
from tasks import translate_task
from tasks.translate import trans_group_task
from utils import json_util, model_util, response_util, thread_util

translate_router = APIRouter()


@translate_router.get("/translate/run/task")
async def download_sub_tags(task_type: TranslateTaskType, task_key: str):
    task = await translate_service.get_task(task_type.value, task_key)
    if not task:
        return response_util.def_error("task not found")
    method = translate_task.task_method_map.get(task_type.value)
    if not method:
        return response_util.def_error("method not found")
    target = await method(task)
    return response_util.ok({"target": target})


@translate_router.get("/translate/resource/configs")
async def get_resource_config():
    # 获取翻译资源配置
    config = await TranslateResource.filter().only("category").distinct()
    categories = [item.category for item in config]
    status = [
        TranslateTaskStatus.FINISHED.value,
        TranslateTaskStatus.PENDING.value,
        TranslateTaskStatus.PROCESSING.value,
        TranslateTaskStatus.ERROR.value,
    ]
    return response_util.ok({"category_list": categories, "status_list": status})


@translate_router.get("/translate/resource/list")
async def get_resource_list(
    category: str = "",
    keywords: str = "",
    status: str = TranslateTaskStatus.PENDING.value,
    review: bool = False,
):
    filter = {}
    category = category if category != "全部" else ""
    if category:
        filter["category"] = category
    if keywords:
        filter["text__contains"] = keywords
    filter["status"] = status
    filter["review"] = review
    resource_list = await TranslateResource.filter(**filter).order_by("updated_at")
    language_configs = [
        {"field": Language.val_and_region(x.value), "name": Language.load_desc(x.value)}
        for x in Language
    ]
    return response_util.ok(
        {"resource_list": resource_list, "language_configs": language_configs}
    )


@translate_router.get("/translate/resource/retry")
async def retry_translate(id: int):

    resource = await TranslateResource.get(id=id)
    if not resource:
        return response_util.def_error("resource not found")
    resource_dict = await model_util.model_to_dict(TranslateResource, resource)
    resource_dict["zh_cn"] = resource.text
    func_map = {}
    for lang in Language:
        if lang == Language.ZH:
            resource_dict["zh_cn"] = resource.text
            continue
        func_map[lang] = inner_bot.translate_web(resource.text, lang.value)

    translate_results = await thread_util.parallel_execute_map(func_map, concurrency=5)
    for lang, translate_text in translate_results.items():
        update_field = lang.fetch_field_name()
        if (
            translate_text
            and isinstance(translate_text, str)
            and translate_text.strip()
        ):
            resource_dict[update_field] = translate_text.strip()

        # update_field = lang.fetch_field_name()
        # translate_text = await inner_bot.translate_web(resource.text, lang.value)
        # if translate_text:
        #     resource_dict[update_field] = translate_text.strip()
    return response_util.ok({"resource": resource_dict})


@translate_router.post("/translate/resource/review")
async def review_translate(review_request: dict):

    resource = await TranslateResource.get(id=review_request.get("id"))
    if not resource:
        return response_util.def_error("resource not found")
    update_dict = {}
    for lang in Language:
        if lang.fetch_field_name() in review_request:
            update_dict[lang.fetch_field_name()] = review_request[
                lang.fetch_field_name()
            ]
    if not update_dict:
        return response_util.def_error("没有需要更新的翻译内容")

    update_dict["review"] = True
    update_dict["status"] = TranslateTaskStatus.FINISHED.value

    await TranslateResource.filter(id=resource.id).update(**update_dict)
    resource = await TranslateResource.get(id=review_request.get("id"))
    return response_util.ok({"resource": resource})


@translate_router.post("/translate/resource/translate_all")
async def translate_all_resources():
    if not redis_client.acquire_lock("translate_all_resources", "1", 60):
        return response_util.def_error("翻译任务正在进行中，请稍后再试")

    async def run_all_task():
        resources = await TranslateResource.filter(status="pending").all()
        for resource in resources:
            if not resource.text:
                continue
            update_dict = {}
            for lang in Language:
                if lang == Language.ZH:
                    update_dict["zh_cn"] = resource.text
                    continue
                update_field = lang.fetch_field_name()
                translate_text = await inner_bot.translate_web(
                    resource.text, lang.value
                )
                translate_text = translate_text.strip()
                update_dict[update_field] = translate_text
            update_dict["status"] = TranslateTaskStatus.FINISHED.value
            update_dict["review"] = False
            if not update_dict:
                continue
            await TranslateResource.filter(id=resource.id).update(**update_dict)
        redis_client.release_lock("translate_all_resources", "1")

    asyncio.create_task(run_all_task())
    return response_util.ok({"message": "翻译任务已提交，请稍后查看结果"})


# 翻译指定语言内容
@translate_router.post("/translate/resource/retry_by_specific_language")
async def translate_specific_language(
    resource_id: int = 0,
    all_resource: bool = False,
    all_language: bool = False,
    language: Optional[Language] = None,
    skip_translated_text: bool = False,
):
    if not all_language and not language:
        return response_util.def_error("请指定翻译语言或设置 all_language 为 True")
    language_list = [x.value for x in Language] if all_language else []
    if not language_list and language:
        language_list = [language.value]
    if not language_list:
        return response_util.def_error("没有可翻译的语言")
    resource_list = []
    if all_resource:
        resource_list = await TranslateResource.all()
    if resource_id > 0:
        resource = await TranslateResource.get(id=resource_id)
        resource_list.append(resource)
    if not resource_list:
        return response_util.def_error("没有待翻译的资源")

    async def run_task():
        for resource in resource_list:
            resource_dict = await model_util.model_to_dict(TranslateResource, resource)
            update_dict = {}
            for lang in language_list:
                update_field = Language(lang).fetch_field_name()
                if skip_translated_text and resource_dict.get(update_field):
                    continue
                translate_text = await inner_bot.translate_web(resource.text, lang)
                translate_text = translate_text.strip()
                if not translate_text:
                    continue
                # 更新指定字段
                update_dict[update_field] = translate_text
            if update_dict:
                await TranslateResource.filter(id=resource.id).update(**update_dict)
            logging.info(
                f"Translated resource {resource.id} to {language}: {resource.text} -> {update_dict}"
            )

    asyncio.create_task(run_task())
    return response_util.ok({"success": True, "message": "Translation task started"})


@translate_router.post("/translate/task/retry_by_specific_language")
async def translate_task_by_language(
    task_id: int,
    all_language: bool = False,
    language: Optional[Language] = None,
):
    async def run_task():
        return await translate_task.run_retry_task_by_language(
            task_id, language.value if language else "", all_language
        )

    asyncio.create_task(run_task())
    return response_util.ok({"success": True, "message": "Translation task started"})


@translate_router.post("/translate/task/role/retry_single_by_specific_language")
async def role_retry_single_by_specific_language(
    role_ids: str = "",
    all_language: bool = False,
    language: Optional[Language] = None,
):
    if not role_ids:
        return response_util.def_error("role_ids cannot be empty")
    role_ids_list = [int(x) for x in role_ids.split(",") if x.isdigit()]
    if not role_ids_list:
        return response_util.def_error("No valid role IDs provided")

    async def run_task():
        for role_id in role_ids_list:
            task = await translate_service.get_task(TranslateTaskType.ROLE.value, str(role_id))
            if not task:
                continue
            if task.status != TranslateTaskStatus.FINISHED.value:
                logging.warning(f"Role task not finished for role_id: {role_id}")
                continue
            await translate_task.run_retry_task_by_language(
                task.id, language.value if language else "", all_language
            )

    asyncio.create_task(run_task())
    return response_util.ok({"success": True, "message": "Translation task started"})


@translate_router.post("/translate/task/role/retry_all_by_specific_language")
async def translate_role_task_by_language(
    start_role_id: int = 0,
    limit: int = 1,
    all_language: bool = False,
    language: Optional[Language] = None,
):

    task_keys = await translate_service.list_task_keys(TranslateTaskType.ROLE)
    all_role_ids = [int(key) for key in task_keys if key.isdigit()]
    all_role_ids.sort()
    role_ids = [rid for rid in all_role_ids if rid >= start_role_id][:limit]

    async def run_single_task(role_id: int):
        task = await translate_service.get_task(
            TranslateTaskType.ROLE.value, str(role_id)
        )
        if not task:
            logging.warning(f"Role task not found for role_id: {role_id}")
            return
        if task.languages and len(json_util.convert_to_list(task.languages)) > 3:
            logging.info(f"Role task already translated for role_id: {role_id}")
            return
        if task.status != TranslateTaskStatus.FINISHED.value:
            logging.warning(f"Role task not finished for role_id: {role_id}")
            return
        await translate_task.run_retry_task_by_language(
            task.id, language.value if language else "", all_language
        )

    tasks = [run_single_task(role_id) for role_id in role_ids]
    asyncio.create_task(thread_util.parallel_executes(tasks, 10))
    return response_util.ok(
        {
            "success": True,
            "message": "Translation task started",
            "all_role_ids": all_role_ids,
            "role_ids": role_ids,
        }
    )


@translate_router.post("/translate/task/group/retry_single_by_specific_language")
async def retry_single_by_specific_language(
    group_id: int = 0,
    all_language: bool = False,
    language: Optional[Language] = None,
):
    task = await translate_service.get_task(
        TranslateTaskType.GROUP.value, str(group_id)
    )
    if not task:
        await trans_group_task.init_group_task(group_id)
        task = await translate_service.get_task(
            TranslateTaskType.GROUP.value, str(group_id)
        )
    if not task:
        return response_util.def_error("task not found")
    if task.status != TranslateTaskStatus.FINISHED.value:
        logging.warning(f"Group task not finished for group_id: {group_id}")
        return

    async def run_task():
        return await translate_task.run_retry_task_by_language(
            task.id, language.value if language else "", all_language
        )

    asyncio.create_task(run_task())
    return response_util.ok(
        {
            "success": True,
            "message": "Translation task started",
            "group_id": group_id,
        }
    )


@translate_router.post("/translate/task/group/retry_all_by_specific_language")
async def translate_group_task_by_language(
    all_language: bool = False,
    language: Optional[Language] = None,
):
    task_keys = await translate_service.list_task_keys(TranslateTaskType.GROUP)
    all_group_ids = [int(key) for key in task_keys if key.isdigit()]
    all_group_ids.sort()

    async def run_single_task(group_id: int):
        task = await translate_service.get_task(
            TranslateTaskType.GROUP.value, str(group_id)
        )
        if not task:
            logging.warning(f"Group task not found for group_id: {group_id}")
            return
        if task.languages and len(json_util.convert_to_list(task.languages)) > 3:
            logging.info(f"Group task already translated for group_id: {group_id}")
            return
        if task.status != TranslateTaskStatus.FINISHED.value:
            logging.warning(f"Group task not finished for group_id: {group_id}")
            return
        await translate_task.run_retry_task_by_language(
            task.id, language.value if language else "", all_language
        )

    run_tasks = [run_single_task(group_id) for group_id in all_group_ids]
    asyncio.create_task(thread_util.parallel_executes(run_tasks, 5))
    return response_util.ok(
        {
            "success": True,
            "message": "Translation task started",
            "all_group_ids": all_group_ids,
            "group_ids": all_group_ids,
        }
    )
