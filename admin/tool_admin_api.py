import logging
from fastapi import APIRouter, Depends, File, Form, UploadFile

from admin.auth import is_op_user
from common.common_constant import CosPrefix, UploadImageCategory
from utils import cos_util, response_util


tools_router = APIRouter(dependencies=[Depends(is_op_user)])

log = logging.getLogger(__name__)


@tools_router.post("/tools/upload/img")
async def upload_img(
    img: UploadFile = File(None),
    cos_prefix: CosPrefix = Form(...),
):
    img_url = cos_util.upload_image(img, cos_prefix)
    return response_util.ok({"img_url": img_url})
