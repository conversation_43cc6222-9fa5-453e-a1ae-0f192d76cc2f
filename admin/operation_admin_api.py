from datetime import <PERSON><PERSON><PERSON>
from fastapi import Depends, FastAPI, File, Request, Form, UploadFile
from fastapi.responses import JSONResponse
from fastapi.routing import APIRouter
from pydantic import BaseModel

from common.common_constant import ActionType, ChatPlatform, Language, PopupPosition, PopupShowPeriod, PopupUserScope
from common.operation_model import PopupConfigDetail, PopupConfigEdit
from persistence.models.models import PopupConfig
from utils import date_util, response_util

from .auth import is_admin_user, is_op_user
from services import gift_award_service, operation_service

operation_router = APIRouter(dependencies=[Depends(is_op_user)])


@operation_router.get("/operation/popup/list")
async def popup_list():
    popup_list = await operation_service.list_popup_config()
    popup_list.sort(key=lambda x: x.id, reverse=True)

    popup_list = [PopupConfigDetail.from_model(popup) for popup in popup_list]

    data = {
        "list": popup_list,
        "position_list": PopupPosition.list_display(),
        "show_period_list": PopupShowPeriod.list_display(),
        "chat_platform_list": ChatPlatform.list_display(),
        "position_allowed_platform": PopupPosition.allow_platform(),
        "action_type_list": ActionType.list_display(),
        "user_scope_list": PopupUserScope.list_display(),
        "language_list": Language.list_display(),
    }
    return response_util.ok(data)


@operation_router.post("/operation/popup/add")
async def popup_add(popup: PopupConfigEdit):
    if len(popup.title) > 15 or len(popup.content) > 500:
        return response_util.def_error("title or content too long")
    ret = await PopupConfig.create(
        title=popup.title,
        content=popup.content,
        start_at=date_util.timestamp2datetime(popup.start_at),
        end_at=date_util.timestamp2datetime(popup.end_at),
        show_period=popup.show_period.value,
        chat_platform=[x.value for x in popup.chat_platform],
        position=popup.position.value,
        published=False,
        button_text=popup.button_text,
        button_link_url=popup.button_link_url,
        button_action_type=popup.button_action_type,
        show_close_icon=popup.show_close_icon,
        user_scopes=popup.user_scopes,
        un_support_languages=popup.un_support_languages,
    )
    return response_util.ok({"popup": PopupConfigDetail.from_model(ret)})


@operation_router.post("/operation/popup/update")
async def popup_update(popup: PopupConfigEdit):
    if len(popup.title) > 15 or len(popup.content) > 500:
        return response_util.def_error("title or content too long")

    popup_config = await PopupConfig.filter(id=popup.id).first()
    if not popup_config:
        return response_util.def_error("popup not found")
    popup_config.title = popup.title
    popup_config.content = popup.content
    popup_config.start_at = date_util.timestamp2datetime(popup.start_at)
    popup_config.end_at = date_util.timestamp2datetime(popup.end_at)
    popup_config.show_period = popup.show_period.value
    popup_config.chat_platform = [x.value for x in popup.chat_platform]
    popup_config.position = popup.position.value
    popup_config.button_text = popup.button_text
    popup_config.button_link_url = popup.button_link_url
    popup_config.button_action_type = popup.button_action_type
    popup_config.show_close_icon = popup.show_close_icon
    popup_config.user_scopes = popup.user_scopes
    popup_config.un_support_languages = popup.un_support_languages
    await popup_config.save()
    return response_util.ok({"popup": PopupConfigDetail.from_model(popup_config)})


@operation_router.post("/operation/popup/update_published")
async def popup_publish(popup_id: int, published: bool):
    popup = await PopupConfig.filter(id=popup_id).first()
    if not popup:
        return response_util.def_error("popup not found")
    popup.published = published
    await popup.save()
    return response_util.ok({"popup": PopupConfigDetail.from_model(popup)})
