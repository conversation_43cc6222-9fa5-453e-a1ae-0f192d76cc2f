from datetime import <PERSON><PERSON><PERSON>
from fastapi import Depends, FastAPI, File, Request, Form, UploadFile
from fastapi.responses import J<PERSON><PERSON>esponse
from fastapi.routing import APIRouter
from pydantic import BaseModel

from .auth import is_admin_user
from common.entity import GiftAward
from fastapi.encoders import jsonable_encoder
from services import gift_award_service

gift_router = APIRouter(dependencies=[Depends(is_admin_user)])


@gift_router.post("/admin/gift/claim")
async def create_role(user_ids: str):
    user_id_list = user_ids.split(",")
    ret_user_ids = []
    for user_id_str in user_id_list:
        user_id = int(user_id_str)
        gift = await gift_award_service.get_award_by_user_gift(user_id, "G1")
        if gift is not None:
            continue

        gift = await gift_award_service.add_award_by_user_gift(
            user_id, gift_award_service.get_gift_award()
        )
        ret_user_ids.append(user_id)
    return JSONResponse(content=jsonable_encoder(ret_user_ids))
