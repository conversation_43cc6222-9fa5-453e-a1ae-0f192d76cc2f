"""
聊天返钻石的活动
"""
from fastapi import Depends
from fastapi.routing import APIRouter
from pydantic import BaseModel
from common.activity_diamond_model import DiamondSeasonConfig, DiamondTaskConfig
from services import activity_diamond_season_service, user_service
from utils import response_util
from .auth import is_op_user

#activity_diamond_router = APIRouter(dependencies=[Depends(is_op_user)])
activity_diamond_router = APIRouter()


# 获取赛季列表
@activity_diamond_router.get("/activity_diamond/season_list")
async def get_season_list():
    list = await activity_diamond_season_service.get_diamond_season_list()
    return response_util.ok({"list":list})

# 获取赛季详情
@activity_diamond_router.get("/activity_diamond/season")
async def get_season_detail(season_id: str):
    season = await activity_diamond_season_service.get_diamond_season_by_id(season_id)
    if season is None:
        return response_util.error(400, "赛季不存在")
    return response_util.ok({"season":season})

# 创建赛季
@activity_diamond_router.post("/activity_diamond/season_create")
async def create_season(req: DiamondSeasonConfig):
    season_id, msg = await activity_diamond_season_service.create_season(req)
    if season_id is None:
        return response_util.error(400, msg)
    return response_util.ok({"season_id":season_id})

# 更新赛季(不包含状态)
@activity_diamond_router.post("/activity_diamond/season_update")
async def update_season(req: DiamondSeasonConfig):
    resp, msg = await activity_diamond_season_service.update_season(req)
    if resp is None:
        return response_util.error(400, msg)
    return response_util.ok()

# 更新赛季状态
@activity_diamond_router.post("/activity_diamond/season_status_update")
async def update_season_status(season_id: str, status: int):
    season, msg = await activity_diamond_season_service.update_season_status(season_id, status)
    if season is None:
        return response_util.error(400, msg)
    return response_util.ok()



# 获取一个赛季里的任务列表
@activity_diamond_router.get("/activity_diamond/task_list")
async def get_task_list(season_id: str):
    list = await activity_diamond_season_service.get_task_list_by_season_id(season_id)
    return response_util.ok({"list":list})


class TaskBatchUpdateRequest(BaseModel):
    season_id: str
    task_id_deleted: list[str]=[]
    task_created: list[DiamondTaskConfig]=[] # 不传task_id
    task_updated: list[DiamondTaskConfig]=[] # 必须传task_id

# 批量更新一个赛季里的任务列表
@activity_diamond_router.post("/activity_diamond/task_batch_update")
async def update_task_list(batch_req: TaskBatchUpdateRequest):
    _handel_batch_update_input(batch_req)
    resp, msg = await activity_diamond_season_service.batch_update_task(batch_req.season_id, batch_req.task_created, batch_req.task_id_deleted, batch_req.task_updated)
    if resp is None:
        return response_util.error(400, msg)
    return response_util.ok()

def _handel_batch_update_input(batch_req: TaskBatchUpdateRequest):
    # role_ids 如果是str list, 转换成int list
    for task in batch_req.task_created:
        task.role_ids = [int(role_id) for role_id in task.role_ids]
    for task in batch_req.task_updated:
        task.role_ids = [int(role_id) for role_id in task.role_ids]
    

# 查询用户参与活动的情况
@activity_diamond_router.get("/activity_diamond/user_enroll")
async def get_user_enroll_info(user_id: int=0, tg_user_id: int=0):
    if tg_user_id > 0:
        user = await user_service.get_user_by_tg_id(tg_user_id)
        if user is None:
            return response_util.error(400, "user not found")
        user_id = user.id
    list = await activity_diamond_season_service.get_user_enroll_info(user_id)
    return response_util.ok({"list":list})
   




        
    
    
