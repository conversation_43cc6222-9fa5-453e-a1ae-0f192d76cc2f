import asyncio
import json
import logging
import math
import os
import random
import re
from aioitertools import itertools as aioitertools
from fastapi import Depends, HTTPException, Header
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from fastapi.routing import APIRouter
from sse_starlette import EventSourceResponse
from admin.auth import is_admin_user
from common.common_constant import ChatModeType, ChatPlatform, Language
from common.entity import RegexRuleRequest, RegexRuleResponse
from common.models.chat_model import HistoryRequest
from common.models.chat_request import ChatRequest, PresetConfig
from persistence import chat_history_dao
from persistence.models.models import RegexAffect, RegexOption
from persistence.presets import Scenario, presetsPersistence
from services import config_service, message_service, regex_service, user_service
from pydantic import BaseModel
from services.chat import chat_message_service
from services.chat.history import chat_history_service
from services.role import role_loader_service
from services.user import user_chat_service
from utils import message_utils, response_util
import utils.regex_converter as rc


presets_router = APIRouter(dependencies=[Depends(is_admin_user)])


@presets_router.get("/presets/db_original")
async def get_presets_db_original(model: int, nsfw: int, scenario: int):
    presets = await presetsPersistence.get_preset_v2(model, nsfw, scenario)
    return JSONResponse(content=presets)


@presets_router.get("/presets_v2")
async def get_current_presets_v2():
    presets = await presetsPersistence.get_all_preset_for_admin()
    return JSONResponse(content=presets)


@presets_router.get("/presets/llm_models")
async def get_llm_models():
    llm_models = await config_service.list_llm_model_config()
    llm_models = [x.llm_model for x in llm_models]
    llm_models.sort(key=lambda x: x.lower())
    return response_util.ok(data=llm_models)


@presets_router.get("/presets/get")
async def get_presets(model: str, nsfw: int, scenario: int):
    llm_model_config = await config_service.get_llm_model_config_by_model(model)
    if not llm_model_config:
        return response_util.param_error("model not found")

    preset = await presetsPersistence.get_preset_v2(
        llm_model_config.model_int, nsfw, scenario
    )
    return response_util.ok(data=preset)


@presets_router.post("/saveReg")
async def saveReg(rule: RegexRuleRequest):
    rule_model = await regex_service.add_regex_rule(rule)
    return RegexRuleResponse.from_model(rule_model)


@presets_router.post("/updateReg")
async def updateReg(request: RegexRuleRequest) -> RegexRuleResponse:
    rule = await regex_service.get_regex_rule_by_id(request.rule_id)
    if rule is None:
        return JSONResponse(content={"msg": "rule not found"}, status_code=404)
    request.copy_to_model(rule)
    rule = await regex_service.update_regex_rule(rule)
    return JSONResponse(content=jsonable_encoder(RegexRuleResponse.from_model(rule)))


class TestRegexRuleRequest(BaseModel):
    regex: str = ""
    testRegTxt: str = ""
    replacement: str = ""


@presets_router.post("/testReg")
async def test_regex_rule(rule: TestRegexRuleRequest):
    if not rule.regex or not rule.testRegTxt:
        return JSONResponse(
            content=jsonable_encoder({"success": True, "testRegTxt": "Empty"})
        )
    content = message_utils.run_js_regex(rule.regex, rule.replacement, rule.testRegTxt)
    return JSONResponse(
        content=jsonable_encoder({"success": True, "testRegTxt": content})
    )


@presets_router.post("/deleteReg")
async def add_regex_rule(rule_id: str) -> RegexRuleResponse:
    rule = await regex_service.get_regex_rule_by_id(rule_id)
    if rule is None:
        return JSONResponse(content={"msg": "rule not found"}, status_code=404)
    rule = await regex_service.delete_regex_rule(rule)
    return JSONResponse(content={"success": True})


@presets_router.get("/debug/chat/configs")
async def debug_llm_models(role_id: int = 0):
    llm_models = await config_service.list_llm_model_config()

    llm_models = [x.llm_model for x in llm_models]
    llm_models.sort(key=lambda x: x.lower())
    roles = await role_loader_service.list_public_with_fields(["id", "card_name"])
    roles.sort(key=lambda x: x.id)
    roles = [
        {
            "id": role.id,
            "card_name": role.card_name,
        }
        for role in roles
    ]
    languages = [lang.value for lang in Language]
    if not role_id:
        role_id = roles[0]["id"] if roles else 0
    # await chat_history_dao.list_conversation_ids(
    #     user_id=user_id, role_id=role_id, conversation_id=conversation_id
    # )
    return response_util.ok(
        {"llm_models": llm_models, "roles": roles, "languages": languages}
    )


@presets_router.get("/debug/chat/recent_roles")
async def get_recent_roles(user_id: int = 0):
    llm_models = await config_service.list_llm_model_config()

    role_ids = await chat_history_dao.get_recent_roles(user_id)
    card_name_map = await role_loader_service.map_card_name_by_ids(role_ids)
    roles = [
        {
            "id": id,
            "card_name": card_name_map.get(id, ""),
        }
        for id in role_ids
    ]
    return response_util.ok({"llm_models": llm_models, "roles": roles})


@presets_router.get("/debug/chat/conversation_list")
async def debug_chat_conv_list(role_id: int = 0, user_id: int = 0, language: str = ""):
    if not user_id:
        return response_util.def_error("user_id is required")
    if not role_id:
        return response_util.def_error("role_id is required")
    if not language:
        return response_util.def_error("language is required")

    user = await user_service.safe_get_user(user_id)
    if not user:
        return response_util.def_error("Invalid user_id")

    conv_list = await chat_message_service.list_conversation(
        user_id, ChatModeType.SINGLE.value, role_id, language
    )
    return response_util.ok(
        {
            "conversation_list": conv_list,
        }
    )


@presets_router.get("/debug/chat/history_messages")
async def debug_chat_history(
    role_id: int = 0, user_id: int = 0, conversation_id: str = "", language: str = ""
):
    if not user_id:
        return response_util.def_error("user_id is required")
    if not role_id:
        return response_util.def_error("role_id is required")
    if not language:
        return response_util.def_error("language is required")

    user = await user_service.get_user_by_id(user_id)
    if not user:
        return response_util.def_error("Invalid user_id")
    history_req = HistoryRequest(
        mode_type=ChatModeType.SINGLE.value,
        role_id=role_id,
        conversation_id=conversation_id,
        language=language,
    )
    history_res = await chat_message_service.list_user_history(user, history_req)
    return response_util.ok(history_res.model_dump())


class ChatNewStartRequest(BaseModel):
    role_id: int = 0
    language: str = ""
    user_id: int = 0
    llm_model: str = ""


@presets_router.post("/debug/chat/new_start")
async def debug_chat_new_start(chat_new_body: ChatNewStartRequest):
    history_req = HistoryRequest(
        mode_type=ChatModeType.SINGLE.value,
        role_id=chat_new_body.role_id,
        new_start=1,
        language=chat_new_body.language,
    )
    user = await user_service.get_user_by_id(chat_new_body.user_id)
    if not user or not history_req.role_id or not history_req.language:
        return response_util.def_error("Invalid user_id, role_id or language")
    history_req = await chat_message_service.init_first_history_message(
        user,
        history_req,
        ChatPlatform.TMA.value,
    )
    history_response = await chat_message_service.list_user_history(user, history_req)
    return response_util.ok(history_response.model_dump())


@presets_router.post("/debug/chat/user_input")
async def debug_chat_user_input(body: dict):
    user_id = body.get("user_id", 0)
    language = body.get("language", "")
    user = await user_service.get_user_by_id(user_id)
    if not user:
        return response_util.def_error("Invalid user_id")
    chat_request = ChatRequest(
        role_id=body.get("role_id", 0),
        message=body.get("message", ""),
        conversation_id=body.get("conversation_id", ""),
        language=language,
    )
    chat_next_input = await user_chat_service.build_chat_input_param(
        user_id, chat_request, language
    )
    history = await chat_message_service.save_user_message(
        user, chat_next_input, chat_request
    )
    human_message_id = history.message_id
    input = await user_chat_service.build_chat_input_param(
        user_id, chat_request, language
    )
    input.fixed_preset_model = body.get("llm_model", "")
    input = await chat_message_service.build_model_config(input, Scenario.CHAT.value)
    input = await chat_message_service.build_request_history(input)
    input = await chat_message_service.build_character_book(input)
    chat_response = await chat_message_service.llm_call_auth_retry(input)
    if not chat_response or not chat_response.response:
        return response_util.def_error("Failed to get chat response")
    history_iter, response_iter = aioitertools.tee(chat_response.response)

    async def history_task():
        await message_service.save_new_history_content_v2(
            user,
            chat_request,
            input,
            input_token_sum=chat_response.token_sum,
            first_chunk=chat_response.first_chunk,
            response_iter=history_iter,
        )
        input.save_finished = True

    asyncio.create_task(history_task())
    ai_replay = await message_utils.process_iterator_v2(
        input, response_iter, first_chunk=chat_response.first_chunk
    )
    ai_replay = ai_replay.response if ai_replay else "No response from AI"
    # ai_replay = f"Mocked response for user {user_id} with message ID {input.message_id}"
    return response_util.ok(
        {
            "human_message_id": human_message_id,
            "version": str(history.version),
            "replay_content": ai_replay,
            "message_id": input.message_id,
        },
    )

@presets_router.post("/debug/chat/del_message")
async def debug_chat_del_message(body: dict):
    user_id = body.get("user_id", 0)
    message_id = body.get("message_id", "")
    user = await user_service.get_user_by_id(int(user_id))
    if not user or not message_id:
        return response_util.def_error("无效用户或消息ID")
    count = await chat_message_service.del_messages(int(user_id), [message_id])
    if count == 0:
        return response_util.def_error("删除失败，消息ID可能不存在")
    return response_util.ok(message="消息删除成功")

class ChatRetryRequest(BaseModel):
    user_id: int = 0
    role_id: int = 0
    conversation_id: str = ""
    retry_times: int = 0
    retry_message_id: str = ""
    language: str = ""
    llm_model: str = ""

@presets_router.post("/debug/chat/retry")
async def debug_chat_retry(body: ChatRetryRequest):
    user_id = body.user_id
    language = body.language
    user = await user_service.get_user_by_id(user_id)
    if not user:
        return response_util.def_error("Invalid user_id")
    chat_request = ChatRequest(
        role_id=body.role_id,
        conversation_id=body.conversation_id,
        retry_message_id=body.retry_message_id,
        isRetry=True,
        language=language,
    )
    input = await user_chat_service.build_chat_input_param(
        user_id, chat_request, language
    )
    input.fixed_preset_model = body.llm_model
    input = await chat_message_service.build_model_config(input, Scenario.CHAT.value)
    input = await chat_message_service.build_request_history(input)
    input = await chat_message_service.build_character_book(input)
    # 开启 retry_times 次任务
    semaphore = asyncio.Semaphore(10)  # 限制并发数为10
    replay_list = []

    async def run_task():
        async with semaphore:
            chat_response = await chat_message_service.llm_call_auth_retry(input)
            if not chat_response or not chat_response.response:
                logging.error(
                    f"Failed to get chat response for user {user_id}, message ID {input.message_id}"
                )
                replay_list.append("请求错误，请稍后再试")
                return
            ai_replay = await message_utils.process_iterator_v2(
                input, chat_response.response, first_chunk=chat_response.first_chunk
            )
            if not ai_replay or not ai_replay.response:
                replay_list.append("请求错误，请稍后再试")
            logging.info(
                f"Retry response: {ai_replay.response}, user_id: {user_id}, message_id: {input.message_id}"
            )
            replay_list.append(ai_replay.response)

    task_queue = []
    # 开启 retry_times 次任务
    for _ in range(body.retry_times):
        task_queue.append(run_task())
    task_queue = [asyncio.create_task(task) for task in task_queue]

    async def queue_response():
        sleep_time = 0
        sum_count = 0
        while (
            True and sleep_time < 120 and sum_count < body.retry_times
        ):  # 最多等待120秒
            if replay_list:
                response = replay_list.pop(0)
                sum_count += 1
                response = {"content": response, " message_id": input.message_id}
                yield {"event": "data", "data": json.dumps(response)}
            else:
                await asyncio.sleep(3)  # 等待3秒钟再检查队列
                sleep_time += 3
        yield {"event": "end", "data": ""}

    # 循环停留 60秒，取queue中的数据，stream格式返回
    return EventSourceResponse(content=queue_response())
    # return response_util.ok(
    #     {
    #         "replay_list": replay_list,
    #         "message_id": input.message_id,  # 模拟返回的消息ID
    #     }
    # )


@presets_router.post("/debug/chat/retry_test")
async def debug_chat_test(body: ChatRetryRequest):
    api_replay_queue = []

    async def run_task(index: int = 0):
        async with semaphore:
            await asyncio.sleep(random.randint(1, 5))  # 模拟网络延迟
            ai_replay = f"这个是测试消息\nMocked response,radom content: {index}\n"
            api_replay_queue.append(ai_replay)
            logging.info(f"Mocked response: {ai_replay}")
            return ai_replay

    # 开启 retry_times 次任务
    semaphore = asyncio.Semaphore(2)
    queue_task = []
    for index in range(body.retry_times):
        queue_task.append(run_task(index))

    queue_task = [asyncio.create_task(task) for task in queue_task]

    # 等待所有任务完成
    # await asyncio.gather(*queue_task)
    async def queue_response():
        sleep_time = 0
        sum_count = 0
        while (
            True and sleep_time < 120 and sum_count < body.retry_times
        ):  # 最多等待120秒
            if api_replay_queue:
                response = api_replay_queue.pop(0)
                sum_count += 1
                if response:
                    response = {"content": response}
                    yield {"event": "data", "data": json.dumps(response)}
            else:
                await asyncio.sleep(3)  # 等待3秒钟再检查队列
                sleep_time += 3
        yield {"event": "end", "data": ""}

    # 循环停留 60秒，取queue中的数据，stream格式返回
    return EventSourceResponse(content=queue_response())
