import copy
from datetime import timezone
import json
import logging
from fastapi import APIRouter, Depends
from pydantic import BaseModel

from admin.auth import is_op_user
from common.common_constant import AuditStatus, ChatModeType, WelfareTaskType
from common.role_model import (
    AuditChatGroupEdit,
    ManageRoleBrief,
    RoleAuditDetail,
    RoleEditDetail,
    RolePublishDetail,
)
from persistence.models.models import RoleConfig
from services import role_config_service, user_service, welfare_service
from services.role import (
    role_audit_service,
    role_group_service,
    role_loader_service,
    role_verify_service,
)
from services.role_config_service import RoleConfigService
from utils import date_util, json_util, response_util

log = logging.getLogger(__name__)
audit_router = APIRouter(dependencies=[Depends(is_op_user)])


@audit_router.get("/roles/auditing/filter_list")
async def roles_auditing_filter_list():

    audit_list = await role_audit_service.list_by_status(AuditStatus.AUDITING.value)
    audit_list.sort(key=lambda x: x.updated_at, reverse=True)
    group_ids = [
        x.mode_target_id for x in audit_list if x.mode_type == ChatModeType.GROUP.value
    ]
    role_ids = [
        x.mode_target_id for x in audit_list if x.mode_type == ChatModeType.SINGLE.value
    ]
    gv_maps = await role_audit_service.publish_max_version(
        ChatModeType.GROUP.value, group_ids
    )
    rv_maps = await role_audit_service.publish_max_version(
        ChatModeType.SINGLE.value, role_ids
    )
    group_map = await role_group_service.map_detail_by_ids(group_ids)
    role_map = await role_loader_service.map_brief_by_ids_with_admin(role_ids)
    user_ids = [x.user_id for x in audit_list]
    uid_reject_count_map = await user_service.map_reject_count_by_admin(user_ids)



    def submit_version(mode_type, mode_target_id):
        if mode_type == ChatModeType.GROUP.value:
            return gv_maps.get(mode_target_id, 0) + 1
        return rv_maps.get(mode_target_id, 0) + 1

    ret_list = [
        {
            "mode_type": x.mode_type,
            "mode_target_id": x.mode_target_id,
            "role": role_map.get(x.mode_target_id),
            "group": group_map.get(x.mode_target_id),
            "submit_version": submit_version(x.mode_type, x.mode_target_id),
        }
        for x in audit_list
    ]
    return response_util.success({"ret_list": ret_list, "uid_reject_count_map": uid_reject_count_map})


class ApprovedRequest(BaseModel):
    mode_type: str
    mode_target_id: int
    tag: str = ""
    role_json: str = ""


@audit_router.post("/roles/audit/approved")
async def roles_approved(body: ApprovedRequest):
    log.info(f"roles_approved, body: {body}")
    mode_type = body.mode_type
    mode_target_id = body.mode_target_id
    audit_info = await role_audit_service.get_role_audit_by_admin(
        mode_target_id, mode_type
    )
    if not audit_info or audit_info.status != AuditStatus.AUDITING.value:
        return response_util.def_error("Audit not found or error status")
    first_approved = bool(not audit_info.open_group_id and not audit_info.open_role_id)
    published_data = {}
    submit_at = audit_info.created_at
    if mode_type == ChatModeType.SINGLE.value:
        role_dict = json.loads(body.role_json)
        role_dict = json_util.remove_nulls(role_dict)
        edit_role = RoleEditDetail(**json_util.convert_to_dict(role_dict))
        open_role = await role_config_service.approved_role(
            audit_info, body.tag, edit_role
        )
        await role_audit_service.publish_role(audit_info.id, open_role.id)
        open_role = await role_config_service.load_role_edit_detail(open_role.id)
        published_data = open_role.model_dump()

    if mode_type == ChatModeType.GROUP.value:
        error = await role_verify_service.admin_group_approved_error(audit_info)
        if error:
            return response_util.response_param_error(error)
        chat_group_edit = json.loads(body.role_json)
        chat_group_edit = AuditChatGroupEdit(
            **json_util.convert_to_dict(chat_group_edit)
        )
        open_group = await role_group_service.approved_group(
            audit_info.mode_target_id, audit_info.open_group_id, chat_group_edit
        )
        await role_audit_service.publish_group(audit_info.id, open_group.id)
        open_group = await role_group_service.load_chat_group_edit(open_group.id)
        published_data = open_group.model_dump()

    if published_data:

        if mode_type == ChatModeType.SINGLE.value:
            author_original_role = await role_config_service.load_role_edit_detail(mode_target_id)
        if mode_type == ChatModeType.GROUP.value:
            author_original_role = await role_group_service.load_chat_group_edit(mode_target_id)


        await role_audit_service.add_publish_history(
            audit_info.user_id,
            mode_type,
            mode_target_id,
            audit_info,
            submit_at,
            published_data,
            author_original_role.model_dump(),
        )
    # 首次奖励
    if first_approved and mode_type == ChatModeType.SINGLE.value:
        await welfare_service.receive_audit_reward(
            audit_info.user_id,
            mode_type,
            mode_target_id,
        )
    # 重置用户的连续审核不通过的记录
    author = await user_service.safe_get_user(audit_info.user_id)
    if author:
        publish_privilege = json_util.convert_to_dict(author.publish_role_privilege)
        if publish_privilege and publish_privilege["reject_count"] > 0:
            publish_privilege["reject_count"] = 0
            author.publish_role_privilege = publish_privilege
            await author.save()

    return response_util.success({})


class RejectedRequest(BaseModel):
    mode_type: str
    mode_target_id: int
    reason: str = "Does not meet specifications"


@audit_router.post("/roles/audit/rejected")
async def roles_rejected(body: RejectedRequest):
    log.info(f"roles_rejected, body: {body}")
    mode_type = body.mode_type
    mode_target_id = body.mode_target_id
    audit_info = await role_audit_service.get_by_mode(mode_type, mode_target_id)
    if audit_info is None or audit_info.status != AuditStatus.AUDITING.value:
        return response_util.def_error("Audit not found or error status")

    # 更新审核状态
    await role_audit_service.rejected(audit_info.id, body.reason)
    data = None
    if mode_type == ChatModeType.SINGLE.value:
        data = await role_config_service.load_role_edit_detail(mode_target_id)
    if mode_type == ChatModeType.GROUP.value:
        data = await role_group_service.load_chat_group_edit(mode_target_id)
    if data:
        await role_audit_service.add_publish_history(
            audit_info.user_id,
            mode_type,
            mode_target_id,
            audit_info,
            submit_at=audit_info.created_at,
            publish_data=data.model_dump(),
            original_data=data.model_dump(),
            status=AuditStatus.REJECTED.value,
            reason=body.reason,
        )
    # 连续6次审核不通过，禁止该用户的发布权限
    await try_to_forbid_user_publish_privilege(audit_info.user_id)
    
    return response_util.success({})

# 连续6次审核不通过，禁止该用户的发布权限
async def try_to_forbid_user_publish_privilege(user_id: int):
    author = await user_service.safe_get_user(user_id) 
    if not author:
        return
    publish_privilege = json_util.convert_to_dict(author.publish_role_privilege) # type: ignore
    # hard code 一个 begin_time （该提交审核封禁功能的代码上线时间， 历史的封禁既往不咎）
    begin_ts = 1745402325
    if publish_privilege:
        # 如果有运营手动解封，ts的值是运营解封的timestamp
        operation_ts = publish_privilege["ts"]
        if operation_ts > begin_ts:
            begin_ts = operation_ts

    latest_history = await role_audit_service.get_latest_publish_list_by_user(user_id, 6)
    if not latest_history:
        return
    # 连续拒审的次数
    count = 0
    for history in latest_history:
        created_ts = int(history.created_at.replace(tzinfo=timezone.utc).timestamp())
        log.info(f"created_ts: {created_ts}, created_at: {history.created_at}")   
        # 如果是运营手动解封的时间戳，跳过
        if created_ts < begin_ts:  # type: ignore
            break
        if history.status == AuditStatus.APPROVED.value:
            break
        count += 1
    publish_privilege["reject_count"] = count
    # ts 存储的是运营手动解封的时间戳
    publish_privilege.setdefault("ts", 0)
    log.info(f"final publish_privilege: {publish_privilege}")
    author.publish_role_privilege = publish_privilege      
    await author.save()

@audit_router.get("/roles/publish/list")
async def publish_list(mode_type: str, mode_target_id: int):
    audit_detail = None
    if mode_type == ChatModeType.GROUP.value:
        group = await role_group_service.load_chat_group_edit(mode_target_id)
        audit_detail = RoleAuditDetail(
            mode_type=mode_type, mode_target_id=mode_target_id, group=group
        )
    if mode_type == ChatModeType.SINGLE.value:
        role = await role_config_service.load_role_edit_detail(mode_target_id)
        audit_detail = RoleAuditDetail(
            mode_type=mode_type, mode_target_id=mode_target_id, role=role
        )
    publish_list = await role_audit_service.publish_list(mode_type, mode_target_id)
    publish_list.sort(key=lambda x: x.publish_version, reverse=True)
    publish_list = [RolePublishDetail.from_history(x) for x in publish_list]
    audit_info = await role_audit_service.get_by_mode(mode_type, mode_target_id)

    # 刚开始没有数据，获取线上第一条当作原始数据
    if not publish_list and audit_info:
        if audit_info.open_role_id:
            role_edit = await role_config_service.load_role_edit_detail(
                audit_info.open_role_id
            )
            role_config = await role_loader_service.get_by_id(audit_info.open_role_id)
            publish_detail = RolePublishDetail(
                user_id=audit_info.user_id,
                mode_type=mode_type,
                mode_target_id=mode_target_id,
                publish_version=0,
                submit_at=date_util.datetime2utc8str(audit_info.created_at),
                published_at=date_util.datetime2utc8str(role_config.created_at),
                publish_data=role_edit.model_dump(),
                original_data=role_edit.model_dump(),
                status=audit_info.status,
                status_desc=AuditStatus.get_desc(audit_info.status),
                reason=audit_info.reason,
            )
            publish_list.append(publish_detail)
        if audit_info.open_group_id:
            group_edit = await role_group_service.load_chat_group_edit(
                audit_info.open_group_id
            )
            group_config = await role_group_service.get_original(
                audit_info.open_group_id
            )
            publish_detail = RolePublishDetail(
                user_id=audit_info.user_id,
                mode_type=mode_type,
                mode_target_id=mode_target_id,
                publish_version=0,
                submit_at=date_util.datetime2utc8str(audit_info.created_at),
                published_at=date_util.datetime2utc8str(group_config.created_at),
                publish_data=group_edit.model_dump(),
                original_data=group_edit.model_dump(),
                status=audit_info.status,
                status_desc=AuditStatus.get_desc(audit_info.status),
                reason=audit_info.reason,
            )
            publish_list.append(publish_detail)
    return response_util.ok(
        data={"audit_detail": audit_detail, "publish_list": publish_list}
    )
