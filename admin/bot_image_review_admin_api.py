from datetime import datetime
import logging
from typing import Dict
from fastapi import Depends
from fastapi.responses import JSONResponse
from fastapi.routing import APIRouter
from pydantic import BaseModel

from persistence.models.models_bot_image import BotImgGenTaskReview, ImgReviewStatus


from services.img_bot.img_review_service import ImageReviewService
from tortoise.contrib.pydantic import pydantic_model_creator

from .auth import is_op_user

from utils import (
    response_util,
)


img_review_router = APIRouter()

log = logging.getLogger(__name__)


class BotImgGenTaskReviewModel(BaseModel):
    id: int
    user_id: int
    image_url: str
    tg_id: int
    review_status: ImgReviewStatus
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True
        from_attributes = True


class ImageReviewPageRequest(BaseModel):
    review_status: ImgReviewStatus
    current_page: int = 1
    last_id: int = 0
    page_size: int = 20


class ImageReviewPageResponse(BaseModel):
    current_page: int
    total_records: int
    total_page: int
    page_size: int = 20
    last_id: int = 0
    reviews: list[BotImgGenTaskReviewModel]


@img_review_router.get(
    "/bot_img_review/list_all", description="获取所有待审核的图片生成任务"
)
async def list_all_new_review_tasks(
    current_page: int = 1, page_size: int = 20, last_id: int = 0
) -> JSONResponse:
    """
    获取指定审核状态的审核任务列表
    """
    all_task, total_page, total_count = await ImageReviewService.get_review_tasks_page(
        review_status=ImgReviewStatus.INIT, last_id=last_id, page_size=page_size
    )

    res = ImageReviewPageResponse(
        current_page=current_page,
        total_page=total_page,
        total_records=total_count,
        page_size=page_size,
        last_id=all_task[-1].id if all_task else 0,
        reviews=[BotImgGenTaskReviewModel.from_orm(task) for task in all_task],
    )

    return response_util.ok(data=res.model_dump())


# @img_review_router.post("/bot_img_review/list_page")
# async def list_review_tasks_page(
#     request: ImageReviewPageRequest,
# ) -> ImageReviewPageResponse:
#     """
#     分页获取指定审核状态的审核任务列表
#     """
#     all_task, total_cnt = await ImageReviewService.get_review_tasks_page(
#         review_status=request.review_status,
#         last_id=request.last_id,
#         page_size=request.page_size,
#     )

#     return ImageReviewPageResponse(
#         current_page=request.current_page,
#         page_size=request.page_size,
#         last_id=all_task[-1].id if all_task else 0,
#         reviews=[BotImgGenTaskReviewModel.from_orm(task) for task in all_task],
#     )


@img_review_router.post(
    "/bot_img_review/update_status_batch", description="批量更新审核任务的状态"
)
async def update_review_status_batch(
    review_updates: Dict[int, ImgReviewStatus],
) -> JSONResponse:
    """
    批量更新审核任务的状态

    """

    # 按状态分组
    approved_ids = [
        review_id
        for review_id, status in review_updates.items()
        if status == ImgReviewStatus.APPROVED
    ]
    rejected_ids = [
        review_id
        for review_id, status in review_updates.items()
        if status == ImgReviewStatus.REJECTED
    ]
    
    init_ids = [
        review_id
        for review_id, status in review_updates.items()
        if status == ImgReviewStatus.INIT
    ]

    # 更新 approved 状态
    if approved_ids:
        await ImageReviewService.update_review_status_batch(
            review_ids=approved_ids, new_status=ImgReviewStatus.APPROVED
        )

    # 更新 rejected 状态
    if rejected_ids:
        await ImageReviewService.update_review_status_batch(
            review_ids=rejected_ids, new_status=ImgReviewStatus.REJECTED
        )
    # 更新 init 状态
    if init_ids:
        await ImageReviewService.update_review_status_batch(
            review_ids=init_ids, new_status=ImgReviewStatus.INIT
        )

    return response_util.ok(
        data={"message": "Batch update successful"},
        error_code=0,
        message="Batch update successful",
    )
    
    


@img_review_router.get(
    "/bot_img_review/rejected_list", description="获取黑名单图片列表"
)
async def get_rejected_review_list(
    current_page: int = 1, page_size: int = 20
) -> JSONResponse:
    """
    获取黑名单列表
    """
    all_task, total_page, total_count = await ImageReviewService.get_review_task_by_offset_page(
        review_status=ImgReviewStatus.REJECTED, page=current_page, page_size=page_size
    )

    response = ImageReviewPageResponse(
        current_page=current_page,
        total_page=total_page,
        total_records=total_count,
        page_size=page_size,
        last_id=all_task[-1].id if all_task else 0,
        reviews=[BotImgGenTaskReviewModel.from_orm(task) for task in all_task],
    )
    return response_util.ok(data=response.model_dump())
