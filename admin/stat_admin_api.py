from datetime import datetime, timedelta
import logging
import os
from fastapi import APIRouter

from utils import date_util, response_util, utils
import mysql.connector


stat_router = APIRouter()


async def run_sql(sql: str):
    logging.info(f"Running SQL: {sql}")

    connection_string = os.environ["MYSQL_SLAVE_URL"]
    conn_params = utils.parse_mysql_connection_string(connection_string)
    with mysql.connector.connect(**conn_params) as conn:
        cursor = conn.cursor(dictionary=True)
        cursor.execute(sql)
        result_list = cursor.fetchall()
        cursor.close()
        return result_list


@stat_router.get("/stat/activity/role/publish_card")
async def get_role_publish_card(
    start_date: str,
    end_date: str,
):
    # utc8读取时间，格式为 YYYY-MM-DD
    start_date_time = date_util.str2datetime8(start_date, "%Y-%m-%d")
    end_date_time = date_util.str2datetime8(end_date, "%Y-%m-%d") + timedelta(days=1)
    start_date_time = date_util.utc8_to_utc(start_date_time)
    end_date_time = date_util.utc8_to_utc(end_date_time)
    start_data = date_util.datetime2str(start_date_time, "%Y-%m-%d %H:%M:%S.%f")
    end_data = date_util.datetime2str(end_date_time, "%Y-%m-%d %H:%M:%S.%f")

    sql_ret = await run_sql(
        f"""SELECT id from chat_history_statistic chs where chs.created_at < '{end_data}' ORDER BY id desc limit 1"""
    )
    if not sql_ret:
        return response_util.ok({"headers": [], "data_list": []})
    chs_end_id = sql_ret[0]["id"] if sql_ret else 0  # type: ignore
    sql_ret = await run_sql(
        f"""SELECT id from chat_history_statistic chs where chs.created_at <= '{start_data}' ORDER BY id desc limit 1"""
    )
    if not sql_ret:
        return response_util.ok({"headers": [], "data_list": []})
    chs_start_id = sql_ret[0]["id"] if sql_ret else 0  # type: ignore

    sql_ret = await run_sql(
        f"""
        SELECT id from user_like_record chs
        where chs.created_at < '{start_data}' ORDER BY id desc
        limit 1
    """
    )
    like_start_id = sql_ret[0]["id"] if sql_ret else 0  # type: ignore
    sql_ret = await run_sql(
        f"""
        SELECT id from user_like_record chs
        where chs.created_at <= '{end_data}' ORDER BY id desc
        limit 1
    """
    )
    like_end_id = sql_ret[0]["id"] if sql_ret else 0  # type: ignore
    cal_sql = f"""
        SELECT 
        chs.mode_target_id as '角色卡ID',
        rc.card_name as '角色卡名称',
        rc.uid as '用户ID',
        rc.nickname as '用户名',
        count(distinct(user_id)) as '聊天人数',
        count(distinct(chs.conversation_id)) as '聊天回合数',
        count(chs.id) as '聊天次数',

        sum(CASE WHEN chat_product_mid = 'm17' THEN 1 ELSE 0 END ) as '经济模式',
        sum(CASE WHEN chat_product_mid = 'm1' THEN 1 ELSE 0 END ) as '极速模式',
        sum(CASE WHEN chat_product_mid = 'm14' THEN 1 ELSE 0 END ) as '魅惑模式',
        sum(CASE WHEN chat_product_mid = 'm15' THEN 1 ELSE 0 END ) as '野猫模式',
        sum(CASE WHEN chat_product_mid = 'm6' THEN 1 ELSE 0 END ) as '世界卡模式',
        sum(CASE WHEN chat_product_mid = 'm11' THEN 1 ELSE 0 END ) as '超长记忆模式',
        sum(CASE WHEN chat_product_mid = 'm3' THEN 1 ELSE 0 END ) as '全能模式',
        sum(case WHEN chat_product_mid = 'm44' THEN 1 ELSE 0 END ) as '超全能模式',

        sum(consume) as '消耗钻石汇总',
        sum(
            CASE 
            WHEN chat_product_mid = 'm17' THEN 50
            WHEN chat_product_mid = 'm1' THEN 100
            WHEN chat_product_mid = 'm14' THEN 300
            WHEN chat_product_mid = 'm15' THEN 400
            WHEN chat_product_mid = 'm6' THEN 600
            WHEN chat_product_mid = 'm11' THEN 1200
            WHEN chat_product_mid = 'm3' THEN 1500
            WHEN chat_product_mid = 'm44' THEN 4500
            ELSE 0 END
        ) as '消耗钻石汇总（按照产品价格汇总）',
        ulr.like_count as '点赞总数',
        count(distinct(chs.conversation_id))*100 + count(chs.id) * 10 + sum(
            CASE 
            WHEN chat_product_mid = 'm17' THEN 50
            WHEN chat_product_mid = 'm1' THEN 100
            WHEN chat_product_mid = 'm14' THEN 300
            WHEN chat_product_mid = 'm15' THEN 400
            WHEN chat_product_mid = 'm6' THEN 600
            WHEN chat_product_mid = 'm11' THEN 1200
            WHEN chat_product_mid = 'm3' THEN 1500
            WHEN chat_product_mid = 'm44' THEN 4500
            ELSE 0 END
        ) + 1 as '用户热度值'

        FROM chat_history_statistic chs 
        LEFT JOIN (SELECT rc.id,rc.card_name,up.nickname,rc.uid FROM role_config rc LEFT JOIN users_pw up on rc.uid = up.id where rc.uid > 0) rc on rc.id= chs.mode_target_id
        LEFT JOIN (SELECT mode_target_id,sum(count) as like_count  from user_like_record where id >= {like_start_id} and id <= {like_end_id} GROUP BY mode_target_id) ulr on chs.mode_target_id=ulr.mode_target_id

        where chs.id >= {chs_start_id} and chs.id <= {chs_end_id} and chs.mode_type = 'single' 
        and chs.mode_target_id in ( 
        SELECT id from role_config where created_at >= '{start_data}' and created_at <= '{end_data}' and `status`=1 and privacy=1 and uid > 0
        )
        GROUP BY chs.mode_target_id"""
    sql_ret = await run_sql(cal_sql)
    headers = [
        "角色卡ID",
        "角色卡名称",
        "用户ID",
        "用户名",
        "聊天人数",
        "聊天回合数",
        "聊天次数",
        "经济模式",
        "极速模式",
        "魅惑模式",
        "野猫模式",
        "世界卡模式",
        "超长记忆模式",
        "全能模式",
        "超全能模式",
        "消耗钻石汇总",
        "消耗钻石汇总（按照产品价格汇总）",
        "点赞总数",
        "用户热度值",
    ]
    if not sql_ret:
        return response_util.ok({"headers": headers, "data_list": []})
    data_list = []
    for row in sql_ret:
        row_data = []
        for i, header in enumerate(headers):
            row_data.append(str(row[header]))  # type: ignore
        data_list.append(row_data)

    return response_util.ok({"headers": headers, "data_list": data_list})
