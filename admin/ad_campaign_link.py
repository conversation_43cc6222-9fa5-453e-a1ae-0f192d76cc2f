from itertools import groupby
from fastapi import Depends
from fastapi.responses import JSONResponse
from fastapi.routing import APIRouter
from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel

from services import tg_ad_link_service
from .auth import is_op_user
from fastapi.encoders import jsonable_encoder
from persistence.models.models import TgAdLink
from utils import env_const

tg_link_router = APIRouter(dependencies=[Depends(is_op_user)])

class ChannelInfoReq(BaseModel):
    link: str

class GenerateTgLinkReq(BaseModel):
    tg_link: str
    role_id: int
    bot_type: str = 'TMA' # TMA or CHAT_BOT

@tg_link_router.post('/channel_id')
async def get_channel_id(req: ChannelInfoReq):
    link = await tg_ad_link_service.get_by_tg_link(req.link)
    if link:
        return JSONResponse(content={'channel_id': link.channel_id, 'is_new': False})
    else:
        cur_id = await tg_ad_link_service.get_max_channel_user_id()
        next = cur_id + 1
        await tg_ad_link_service.create_tg_link_channel(req.link, next)
        return JSONResponse(content={'channel_id': next, 'is_new': True})

@tg_link_router.post('/generate_link')
async def generate_link(req: GenerateTgLinkReq):
    if not req.tg_link.startswith('https://t.me/'):
        return JSONResponse(content={'message': 'Invalid tg link'}, status_code=400)
    channel_id = await tg_ad_link_service.get_or_create_channel(req.tg_link)
    query = f'u_{channel_id}-r_{req.role_id}'
    if req.bot_type == 'TMA':
        url = f'{env_const.TMA_DIRECT_URL}?startapp={query}'
    else:
        url = f'{env_const.CHAT_BOT_URL}?start={query}'
    await tg_ad_link_service.get_or_create_target_link(req.tg_link, channel_id, req.role_id, req.bot_type, url)
    return JSONResponse(content={'url': url})

class TgAdLinResponse(BaseModel):
    tg_link: str
    channel_id: int
    role_id: int
    target_type: str
    target_link: str
    create_time: int

    @staticmethod
    def from_model(model: TgAdLink) -> 'TgAdLinResponse':
        return TgAdLinResponse(tg_link=model.tg_link, channel_id=model.channel_id, role_id=model.role_id, target_type=model.target_type, target_link=model.target_link, create_time=int(model.created_at.timestamp()))

@tg_link_router.get('/all_links')
async def get_all_channels():
    channels = await tg_ad_link_service.get_all_links()
    channels = [TgAdLinResponse.from_model(x) for x in channels]
    grouped_links = groupby(channels, key=lambda x: x.tg_link)
    result = {}
    for key, group in grouped_links:
        group = list(group)
        group.sort(key=lambda x: x.create_time, reverse=True)
        result[key] = group
    return JSONResponse(content=jsonable_encoder(result))

class LinkReq(BaseModel):
    tg_link: str

@tg_link_router.post('/links_by_channel')
async def get_all_links_by_channel(req: LinkReq):
    channel_id = await tg_ad_link_service.get_or_create_channel(req.tg_link)
    links = await tg_ad_link_service.get_all_links_by_channel(channel_id)
    links = [TgAdLinResponse.from_model(x) for x in links]
    return JSONResponse(content=jsonable_encoder({'links': links}))
