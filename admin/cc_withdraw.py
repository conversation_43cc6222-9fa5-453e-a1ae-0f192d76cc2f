from datetime import UTC, datetime
import hashlib
import logging
import os
from typing import Optional
import uuid
from fastapi import Request
from fastapi.responses import JSONResponse
from fastapi.routing import APIRouter
from pydantic import BaseModel, Field
import requests

from services import cc_recharge_service
from persistence.models.models import WithdrawOrder

#cc_withdraw_router = APIRouter(dependencies=[Depends(get_current_user)])

cc_withdraw_router = APIRouter()

cc_host = os.environ['CC_RECHARGE_HOST']
cc_withdraw_app_id = os.environ['CC_WITHDRAW_APP_ID']
cc_withdraw_app_key = os.environ['CC_WITHDRAW_APP_KEY']

cc_withdraw_notify_url = f'{os.environ['TG_WEBHOOK_URL']}/cc_withdraw/notify'

class CCWithdrawNotify(BaseModel):
    app_id: str
    success: int
    app_order_no: str
    order_no: str
    pay_amount: str
    pay_time: str
    sign: str
    note: Optional[str] = Field(default=None)
    message: Optional[str] = Field(default=None)

    def verify_sign(self, app_key: str) -> bool:
        params = self.model_dump(exclude_unset=True, exclude={'sign'})
        params = [(k, str(v)) for k, v in params.items() if v is not None and v != '' and v != 0]
        sorted_params = sorted(params, key=lambda x: x[0])
        sign_str = '&'.join(f'{k}={v}' for k, v in sorted_params) + app_key
        sign = hashlib.md5(sign_str.encode()).hexdigest()
        return sign.lower() == self.sign.strip()

class CCWithdrawRequest(BaseModel):
    fee: int
    name: str
    account_no: str
    user_id: int

@cc_withdraw_router.post('/cc_withdraw/notify')
async def cc_withdraw_notify(req: Request):
    data = await req.form()
    logging.info(f'cc_recharge_notify: {data}')
    notify = CCWithdrawNotify(**data)

    s = notify.verify_sign(cc_withdraw_app_key)
    if not s:
        return JSONResponse(content={'message': '提现失败，签名验证失败'}, status_code=400)

    order_id = str(uuid.UUID(notify.app_order_no, version=4))
    await cc_recharge_service.withdraw_success(order_id, datetime.now(UTC), notify.order_no, notify.model_dump_json())
    return 'success'

@cc_withdraw_router.post('/cc_recharge/withdraw')
async def cc_withdraw(req: CCWithdrawRequest):
    card_name = req.name
    card_no = req.account_no
    user_id = req.user_id
    order: WithdrawOrder = await cc_recharge_service.create_withdraw_order(
        fee=req.fee, currency='CNY', card_name=card_name, card_no=card_no,
        bank_id='510', bank_province='上海', bank_city='上海',
        bank_branch_name='others', user_id=user_id)

    params = {
        'app_id': cc_withdraw_app_id,
        'app_order_no': str(order.withdraw_order_id).replace('-', ''),
        'amount': req.fee,
        'notify_url': cc_withdraw_notify_url,
        'card_name': card_name,
        'card_no': card_no,
        'bank_id': '510',
        'province': '上海',
        'city': '上海',
        'branc_name': 'ALIPAY'
    }
    sorted_params = sorted(params.items(), key=lambda x: x[0])
    sign_str = '&'.join(f'{k}={v}' for k, v in sorted_params) + cc_withdraw_app_key
    sign = hashlib.md5(sign_str.encode()).hexdigest()
    params['sign'] = sign.lower()

    # post params as application/x-www-form-urlencoded
    proxies = None
    proxy = os.environ.get('CC_PROXY')
    if proxy:
        proxies = {
            'http': proxy,
            'https': proxy,
        }
    logging.info(f'cc_withdraw: {params}')
    resp = requests.post(f'{cc_host}/order/withdrawal/create', 
                         data=params, proxies=proxies)
    raw_text = resp.text
    logging.info(f'cc_withdraw: {raw_text}')
    data = resp.json()
    if data['success'] != 1:
        return JSONResponse(content={'message': f'提现失败, {data["message"]}'}, status_code=400)

    return JSONResponse(content={'message': 'success'})