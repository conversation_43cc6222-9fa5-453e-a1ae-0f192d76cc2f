from hashlib import md5
from io import BytesIO
import io
import json
import logging
import os
import time
import uuid
from PIL import Image
from fastapi import APIRouter, Depends, Response, UploadFile
from fastapi.responses import JSONResponse, StreamingResponse
import pandas as pd
from common.common_constant import CosPrefix, ThirdCardTags
from common.role_card import (
    RoleCardInfo,
    TavernCard,
    ThirdCardInfo,
    ThirdPlatform,
)
from common.role_model import RoleDataConfig, RoleEditDetail, SceneConfig
from persistence import char_book_dao, third_card_dao
from persistence.models.models import RoleConfig, ThirdCard
from presets import role_fill_preset
from services.role import third_card_service
from services.role_config_service import RoleConfigService
from dotenv import load_dotenv
from urllib.parse import quote

from .auth import is_op_user

from utils import cos_util, image_util, json_util, response_util, role_util

load_dotenv()

char_book_router = APIRouter(dependencies=[Depends(is_op_user)])

log = logging.getLogger(__name__)


UNAUTHORIZED = JSONResponse(status_code=401, content={"message": "Unauthorized"})


@char_book_router.post("/role_card/analysis/img")
async def analysis_card_img(image: UploadFile):
    analysis_card_info = await RoleConfigService.analysis_card_img(image)
    return JSONResponse(content=analysis_card_info)


@char_book_router.get("/role_card/export/json")
async def export_json(role_id: int):
    export_ret = await RoleConfigService.load_tavern_card(role_id=role_id)
    return JSONResponse(content=export_ret.model_dump())


@char_book_router.get("/role_card/export/img")
async def export_img(role_id: int):
    export_ret = await RoleConfigService.load_tavern_card(role_id=role_id)
    if export_ret is None:
        return JSONResponse(content={"message": "No card detected"}, status_code=400)
    img_byte_arr = await image_util.build_card_image(export_ret)
    if img_byte_arr is None:
        return JSONResponse(content={"message": "No card detected"}, status_code=400)

    img_byte_arr = img_byte_arr.getvalue()  #
    encoded_filename = quote(export_ret.name + ".png", safe="")  # 对文件名进行编码
    headers = {
        "Content-Disposition": f"attachment; filename*=UTF-8''{encoded_filename}"
    }
    return StreamingResponse(
        BytesIO(img_byte_arr), media_type="image/png", headers=headers
    )


# @char_book_router.post("/role_card/import/batch/imgs")
# async def batch_import(file_path: str):
#     # 读取文件
#     all_files = []
#     for root, dirs, files in os.walk(file_path):
#         for file in files:
#             all_files.append(os.path.join(root, file))
#     for file_path in all_files:
#         try:
#             file_name = f"{uuid.uuid4().hex}"
#             image = Image.open(file_path)
#             tavern_card = image_util.load_new_image_info(image)
#             image = image_util.remove_image_info(image)
#             # 读取image的bytes
#             img_byte_arr = io.BytesIO()
#             image.save(img_byte_arr, format="PNG")
#             image_url = image_util.image_util.upload_image(
#                 img_byte_arr.getvalue(), file_name, "tr-avatar-1323765209", "image/png"
#             )
#             if tavern_card is None:
#                 log.error(f"Failed to analysis image: {file_path}")
#                 continue
#             book_id = ""
#             role_card_config = tavern_card.to_role_card()
#             if role_card_config.character_book is not None:
#                 continue
#                 # role_card_config.character_book.book_id = str(uuid.uuid4())
#                 # book_id = role_card_config.character_book.book_id
#                 # await char_book.char_book_persistence.insert(
#                 #     role_card_config.character_book
#                 # )
#             # 只支持酒馆的图片
#             new_role = RoleEditDetail(
#                 card_name=role_card_config.name,
#                 name=role_card_config.name,
#                 role_name=role_card_config.name,
#                 introduction="",
#                 role_avatar=image_url,
#                 description=role_card_config.description,
#                 personality=role_card_config.personality,
#                 scenario=role_card_config.scenario,
#                 first_message=role_card_config.first_mes,
#                 example_dialog=role_card_config.mes_example,
#                 book_id=book_id,
#             )
#             role_config = new_role.to_role_config()
#             role_config.privacy = True
#             role_config.tags = "上新"
#             role_config.def_language = role_util.detect_language(new_role).value
#             await RoleConfigService.add_role_config(role_config)
#         except Exception as e:
#             log.error(f"Failed to import image: {file_path}, error: {e}")


# @char_book_router.get("/role_card/analysis_third_card/get_original_json")
# async def analysis_third_card_get_original_json(
#     card_id: str,
# ):
#     third_card = await third_card_service.analysis_third_card_original(card_id)
#     return JSONResponse(content=third_card)


# @char_book_router.get("/role_card/analysis_third_card/batch")
# async def analysis_third_card_batch(
#     platform: ThirdPlatform,
# ):
#     # await third_card_task.start_task()
#     await third_card_task.update_character_book_info()
#     return JSONResponse(content={"message": "Success"})


@char_book_router.get("/role_card/analysis_third_card/update_orders")
async def analysis_third_card_batch_orders():
    third_cards = await third_card_dao.list(ThirdPlatform.ROCHAT.value, 0, 10000)
    third_card = [x for x in third_cards if x.role_id > 0]
    third_card.sort(key=lambda x: x.popularity, reverse=True)
    order_ids = [x.role_id for x in third_card]
    await RoleConfigService.add_update_role_orders("RoChat", order_ids)

    third_cards = await third_card_dao.list(ThirdPlatform.RISUAI.value, 0, 10000)
    third_card = [x for x in third_cards if x.role_id > 0]
    third_card.sort(key=lambda x: x.popularity, reverse=True)
    order_ids = [x.role_id for x in third_card]
    await RoleConfigService.add_update_role_orders("RisuAi", order_ids)

    return JSONResponse(content={"message": "Success"})


# @char_book_router.get("/role_card/analysis_third_card/single")
# async def analysis_third_card(card_id: str):
#     third_card = await third_card_dao.get_by_card_id(card_id)
#     role_card_config = await third_card_service.analysis_third_card(card_id)
#     if role_card_config is None:
#         return JSONResponse(content={"message": "No card detected"}, status_code=400)

#     role_config = await third_card_service.init_or_update_role_config(
#         role_card_config, third_card
#     )
#     third_card.language = role_config.def_language
#     third_card.role_id = role_config.id
#     third_card.contains_book = role_card_config.character_book is not None
#     third_card.card_name = role_card_config.name
#     third_card.role_name = role_card_config.name

#     log.info(f"update third card: {third_card.model_dump()}")
#     await third_card_dao.update(third_card)
#     return JSONResponse(
#         content=jsonable_encoder(
#             {
#                 "role_config": role_config,
#                 "third_card": third_card.model_dump(),
#                 "tavern_card": role_card_config.model_dump(),
#             }
#         )
#     )


@char_book_router.post("/role_card/third_card/import_bot3")
async def import_third_cards(
    file_excel: UploadFile,
    platform: ThirdPlatform = ThirdPlatform.BOT3,
):
    # 读取Excel文件
    try:
        df = pd.read_excel(file_excel.file)
        count = 0
        for index, row in df.iterrows():
            try:
                third_card = ThirdCard(
                    third_id=row.get("id", str(uuid.uuid4())),
                    third_platform=platform.value,
                    url=row.get("url", ""),
                    image_url=row.get("image_url", ""),
                    title=row.get("title", ""),
                    tags=row.get("tags", ""),
                    description=row.get("description", ""),
                    personality=row.get("personality", ""),
                    first_message=row.get("first_message", ""),
                    examples=row.get("examples_of_dialogue", ""),
                    scenario=row.get("scenario", ""),
                    priority=100000 - count,  # 优先级，越小越靠前
                )
                if third_card.scenario == "(empty)":
                    third_card.scenario = ""
                if third_card.examples == "(empty)":
                    third_card.examples = ""
                if third_card.title == "(empty)":
                    third_card.title = ""
                if third_card.description == "(empty)":
                    third_card.description = ""
                if third_card.personality == "(empty)":
                    third_card.personality = ""
                if third_card.first_message == "(empty)":
                    third_card.first_message = ""
                existing_card = await ThirdCard.filter(
                    third_platform=third_card.third_platform, third_id=third_card.third_id
                ).first()
                count += 1
                if not existing_card:
                    await third_card.save()
                    existing_card = third_card
                if existing_card.imported and existing_card.role_id > 0:
                    log.info(f"Card {existing_card.third_id} already imported, skipping.")
                    continue
                image_url = cos_util.transform_url_to_s3_url(
                    existing_card.image_url, CosPrefix.ROLE.value
                )
                role_card_info = RoleCardInfo(
                    name=existing_card.title,
                    description=existing_card.description,
                    personality=existing_card.personality,
                    scenario=existing_card.scenario,
                    first_mes=existing_card.first_message,
                    mes_example=existing_card.examples,
                    tags=[ThirdCardTags.BOT3_AI.value],
                    sub_tags=existing_card.tags.split(",") if existing_card.tags else [],
                )
                role_config = await third_card_service.add_role_config(
                    role_card_info, image_url=image_url
                )
                if not role_config:
                    log.error(
                        f"Failed to add role config for card: {existing_card.third_id}"
                    )
                    continue
                existing_card.role_id = role_config.id
                existing_card.imported = True
                await existing_card.save()
                log.info(
                    f"Imported card: {existing_card.third_id} with role ID: {role_config.id}"
                )
            except Exception as e:
                log.error(
                    f"Error importing card at index {index}: {e}, row data: {row.to_dict()}"
                )
        log.info(f"Total cards imported: {count}")
        return JSONResponse(content={"message": "Import successful"})
    except Exception as e:
        log.error(f"Failed to import third cards: {e}")
        return JSONResponse(content={"message": str(e)}, status_code=500)
