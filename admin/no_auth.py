import os
from typing import Optional
from fastapi import Depends, Form, HTTPException, Header, Request, Response
from fastapi.routing import APIRouter
from pydantic import BaseModel

from common.common_constant import AdminUserRole
from common.models.chat_request import PresetConfig
from persistence.presets import presetsPersistence
from utils import response_util
from .auth import set_cookie
from admin import auth

no_auth_router = APIRouter()


class UserLoginRequest(BaseModel):
    email: str
    password: str


class UserLoginResponse(BaseModel):
    success: bool
    reason: Optional[str] = None


def depends_auth_header(authorization: str | None = Header(None)):
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Authorization"},
    )
    if not authorization:
        raise credentials_exception
    auth = os.environ["ADMIN_AUTH_HEADER"]
    token = authorization.split(" ")[1]
    if auth != token:
        raise credentials_exception
    return None


@no_auth_router.post("/login")
async def login(req: UserLoginRequest,request:Request, response: Response) -> UserLoginResponse:
    auth_user =  await auth.check_auth_new(req.email, req.password)
    if not auth_user:
        return UserLoginResponse(success=False, reason="密码错误")
    if not auth_user.role:
        return UserLoginResponse(success=False, reason="已注册成功，无后台权限，请联系管理员授权")
    set_cookie(req.email,request,response)
    return UserLoginResponse(success=True)


@no_auth_router.post("/presets_v2")
async def presets(
    preset_config: PresetConfig, auth: str = Depends(depends_auth_header)
):
    await presetsPersistence.save_preset_v2(
        preset_config.presets,
        preset_config.model,
        preset_config.nsfw,
        preset_config.scenario,
    )
    return "OK"


@no_auth_router.get("/logout")
async def logout(request:Request):
    response = response_util.ok(message="logout success")
    response.set_cookie("token", "", expires=0, samesite="none", secure=True)
    return response