import logging
from fastapi import Depends
from fastapi.responses import JSONResponse
from fastapi.routing import APIRouter
from pydantic import BaseModel

from common.common_constant import ChatModeType, RoleTag
from services.role import role_loader_service
from utils import response_util

from .auth import is_op_user
from fastapi.encoders import jsonable_encoder
from services import tag_service
from services.role_config_service import RoleConfigService
from persistence.models.models import (
    RoleOrder,
    SubTag,
)

tag_router = APIRouter(dependencies=[Depends(is_op_user)])

log = logging.getLogger(__name__)


class CategoryOrderRequest(BaseModel):
    category: str
    orders: list[int] = []

    # 设置指定卡片的位置
    mode_type: str = ChatModeType.SINGLE.value
    mode_target_id: int = 0
    position: int = 0


class TagUpdatePositionRequest(BaseModel):
    sub_tag: str
    order: int


@tag_router.post("/sub_tag/update_order")
async def update_position(request: TagUpdatePositionRequest):
    await tag_service.update_sub_tag_order(request.sub_tag, request.order)
    all_tags = await tag_service.list_sub_tags_with_enabled()
    all_tags.sort(key=lambda x: x.order)
    sub_tags = [tag.tag_name for tag in all_tags]
    return response_util.ok({"sub_tags": sub_tags})


class TagOpRequest(BaseModel):
    tag: str
    category: str = ""


@tag_router.post("/sub_tag/add")
@tag_router.post("/addTag")
async def add_tag(req: TagOpRequest) -> list[str]:
    await tag_service.update_or_create_sub_tag(req.tag, req.category)
    all_tags = await tag_service.list_sub_tags_with_enabled()
    all_tags.sort(key=lambda x: x.order)
    return [tag.tag_name for tag in all_tags]


@tag_router.post("/sub_tag/add_or_update")
async def add_or_update(req: TagOpRequest):
    if not req.tag or not req.category:
        return response_util.def_error("tag or category is empty")
    await tag_service.update_or_create_sub_tag(req.tag, req.category)
    category_list = await tag_service.list_sub_tag_category()
    all_tags = await tag_service.list_sub_tags_with_enabled()
    all_tags.sort(key=lambda x: x.order)
    category_groups = []
    for category in category_list:
        sub_tags = [tag.tag_name for tag in all_tags if tag.category == category]
        category_groups.append({"category": category, "sub_tags": sub_tags})
    return response_util.ok({"category_groups": category_groups})


@tag_router.get("/sub_tag/list")
@tag_router.get("/queryTags")
async def query_tags() -> list[str]:
    all_tags = await tag_service.list_sub_tags_with_enabled()
    all_tags.sort(key=lambda x: x.order)
    return [tag.tag_name for tag in all_tags]


@tag_router.get("/sub_tag/category_list")
async def query_tag_category():
    all_tags = await tag_service.list_sub_tags_with_enabled()
    sub_tags = [tag.tag_name for tag in all_tags]
    category_groups = await tag_service.list_category_group_sub_tags()
    return response_util.ok({"category_groups": category_groups, "sub_tags": sub_tags})


@tag_router.post("/sub_tag/delete")
@tag_router.post("/delTag")
async def del_tag(req: TagOpRequest) -> list[str]:
    tag = await tag_service.get_sub_tag_by_name(req.tag)
    list_roles = await role_loader_service.list_public()
    list_roles = [x for x in list_roles if req.tag in x.sub_tags]
    if tag and len(list_roles) == 0:
        await tag_service.delete_sub_tag(tag)
    all_tags = await tag_service.list_sub_tags_with_enabled()
    all_tags.sort(key=lambda x: x.order)
    return [tag.tag_name for tag in all_tags]


@tag_router.get("/tag_orders")
async def tag_orders():
    tag_orders = await tag_service.list_tags_with_orders()
    return tag_orders


# @tag_router.get("/update_role_tags_by_file")
# async def update_role_tags_by_file(init: bool = False):
#     role_tags = await tag_service.update_role_tags_by_file(init)
#     return JSONResponse(content={"message": jsonable_encoder(role_tags)})


# @tag_router.get("/init_sub_tag_by_file")
# async def init_sub_tag_by_file(init: bool = False):
#     ret = await tag_service.init_sub_tags(init)
#     return JSONResponse(content={"message": jsonable_encoder(ret)})


# @tag_router.get("/remove_un_used_sub_tags")
# async def remove_un_used_sub_tags(init: bool = False):
#     ret = await tag_service.remove_un_used_tags(init)
#     return JSONResponse(content={"message": jsonable_encoder(ret)})


# @tag_router.get("/role_orders")
# async def get_category_order():
#     orders = await RoleConfigService.get_role_orders()
#     orders = {o.category: o.orders for o in orders}
#     return JSONResponse(content=orders)


@tag_router.post("/save_role_orders")
async def save_category_order(request: CategoryOrderRequest):
    if request.mode_type and request.mode_target_id:
        await tag_service.update_tag_order(
            request.mode_type, request.mode_target_id, request.position
        )
    return JSONResponse(content=request.model_dump())


@tag_router.post("/save_tag_orders")
async def get_tags_order(tag_orders: list[str]):
    await tag_service.add_update_tag_list(tag_orders)
    return response_util.success(tag_orders)
