import json
import logging
from fastapi import Depends
from fastapi.responses import JSONResponse
from fastapi.routing import APIRouter

from pydantic import BaseModel
import requests

from ai import lite_llm_bot
from common import common_constant
from common.common_constant import (
    Env,
    Language,
    ProductType,
    RoleLevelType,
    UserModelFilter,
)
from common.global_config_model import LlmModelDetail
from common.models.chat_model import ModelStatus
from persistence.models.models import LlmModelConfig, ModelWaterConfig
from services import config_service, product_service
from utils import env_util, json_util, response_util
from .auth import is_admin_user


global_config_router = APIRouter()
log = logging.getLogger(__name__)


@global_config_router.post("/global/config/model_water/add")
async def add_water_update(product_mid: str, to_model: str, num: int, use_filter: str):
    product = await product_service.get_original_chat_product_by_mid(product_mid)
    if not product:
        return response_util.def_error("产品不存在")
    # new
    model_config = await config_service.get_water_config_v1(
        product.model, use_filter, to_model
    )
    if model_config and model_config.enabled:
        return response_util.def_error("该产品已经存在掺水配置，请勿重复添加")
    waters = await config_service.list_water_config_by_model_and_use_filter(
        product.model, use_filter
    )
    max_order = max([x.sort_order for x in waters]) if waters else 0
    if model_config:
        # 更新
        model_config.num = num
        model_config.original_num = num
        model_config.enabled = True
        model_config.sort_order = max_order + 1
        await model_config.save()
        return response_util.ok()
    await ModelWaterConfig(
        llm_model=product.model,
        to_llm_model=to_model,
        num=num,
        original_num=num,
        use_filter=use_filter,
        sort_order=max_order + 1,
    ).save()
    return response_util.ok()


@global_config_router.post("/global/config/model_water/update_by_id")
async def update_by_id(id: int, num: int):
    # 更新
    model_config = await config_service.get_water_config_by_id(id)
    if not model_config or not model_config.enabled:
        return response_util.def_error("产品不存在或未启用")
    model_config.num = num
    model_config.original_num = num
    model_config.request_status = ModelStatus.AVAILABLE.value
    await model_config.save()
    return response_util.ok()


@global_config_router.post("/global/config/model_water/delete")
async def add_water_delete(id: int):
    # 删除
    model_config = await config_service.get_water_config_by_id(id)
    if not model_config:
        return response_util.def_error("产品不存在")
    model_config.enabled = False
    await model_config.save()
    return response_util.ok()


@global_config_router.post("/global/config/model_water/update_sort_order")
async def update_sort_order(id: int, sort_order: int):
    model_config = await config_service.get_water_config_by_id(id)
    if not model_config:
        return response_util.def_error("模型不存在")
    model_list = await config_service.list_water_config_by_model_and_use_filter(
        model_config.llm_model, model_config.use_filter
    )
    model_order = []
    for index, model in enumerate(model_list):
        so_val = index * 10
        if model.id == id:
            so_val = so_val - 15 if sort_order > 0 else so_val + 15
        model_order.append((model.id, so_val))
    model_order.sort(key=lambda x: x[1])
    model_order_map = {}
    for index, (model_id, sort_order) in enumerate(model_order):
        model_order_map[model_id] = index
    for model in model_list:
        model.sort_order = model_order_map[model.id]
        await model.save()
    return response_util.ok()


class AddBackupRequest(BaseModel):
    water_config_id: int
    backup_models: list[str] = []


@global_config_router.post("/global/config/model_water/add_backup")
async def add_backup_to_water_config(add_backup: AddBackupRequest):

    model_config = await config_service.get_water_config_by_id(
        add_backup.water_config_id
    )
    if not model_config:
        return response_util.def_error("模型不存在")
    model_config.backup_to_llm_models = add_backup.backup_models
    await model_config.save()
    return response_util.ok()


@global_config_router.get("/global/config/model_water/list_configs")
async def list_configs(product_mid: str, use_filter: str):
    product = await product_service.get_original_chat_product_by_mid(product_mid)
    if not product:
        return response_util.def_error("产品不存在")
    configs = await config_service.list_water_config_by_model_and_use_filter(
        product.model, use_filter
    )
    config_list = []
    for config in configs:
        status_desc = ModelStatus.to_desc(config.request_status)
        if config.original_num != config.num:
            status_desc += f" (待恢复)"
        config_list.append(
            {
                "id": config.id,
                "llm_model": config.llm_model,
                "to_llm_model": config.to_llm_model,
                "num": config.num,
                "use_filter": UserModelFilter.display(config.use_filter),
                "sort_order": config.sort_order,
                "request_status": status_desc,
                "out_skip_count": config.out_skip_count,
                "out_sleep_min": config.out_sleep_min,
                "out_sleep_max": config.out_sleep_max,
                "backup_to_llm_models": ",".join(
                    json_util.convert_to_list(config.backup_to_llm_models)
                ),
            }
        )
    return response_util.ok({"water_configs": config_list})

@global_config_router.post("/global/config/model_water/update_speed")
async def update_speed(body:dict):
    id = body.get("id", 0)
    out_skip_count = body.get("out_skip_count", 0)
    out_sleep_min = body.get("out_sleep_min", 0)
    out_sleep_max = body.get("out_sleep_max", 0)
    model_config = await config_service.get_water_config_by_id(id)
    if not model_config:
        return response_util.def_error("模型不存在")
    model_config.out_skip_count = out_skip_count
    model_config.out_sleep_min = out_sleep_min
    model_config.out_sleep_max = out_sleep_max
    await model_config.save()
    return response_util.ok()


@global_config_router.get("/global/config/all")
async def get_all_global_config():

    products = await product_service.list_original_products(ProductType.CHAT, True)
    # product_model_map = {x.model: x for x in products}

    products.sort(key=lambda x: x.price)
    original_model_list = [x.model for x in products]
    llm_models = await config_service.list_llm_model_config()
    llm_models.sort(key=lambda x: x.llm_model.lower())
    to_model_list = [x.llm_model for x in llm_models]
    original_model_list = [x for x in original_model_list]
    config_maps = {}

    product_model_price = {x.model: x.price for x in products}
    water_list = await config_service.list_model_water_config()
    water_list.sort(key=lambda x: product_model_price.get(x.llm_model, 0))
    add_water_config = {}
    # water_list按照 to_llm_model分组
    for product in products:
        product_water_list = [
            water for water in water_list if water.llm_model == product.model
        ]
        product_water_list.sort(key=lambda x: x.id)
        for index, water in enumerate(product_water_list):
            key = f"{product.short_name}_{index+1} ({product.model})"
            add_water_config[key] = {
                "to_model": water.to_llm_model,
                "interval": water.num,
                "llm_model": water.llm_model,
                "product_name": product.short_name,
                "status": ModelStatus.to_desc(water.request_status),
                "out_skip_count": water.out_skip_count,
                "out_sleep_min": water.out_sleep_min,
                "out_sleep_max": water.out_sleep_max,
                "sort_order": water.sort_order,
                "use_filter": water.use_filter,
                "id": water.id,
            }

    original_model_list = [f"{x.short_name}" for x in products]
    config_maps["add_water_config"] = add_water_config
    config_maps["original_model_list"] = original_model_list
    config_maps["to_model_list"] = to_model_list
    config_maps["language_list"] = common_constant.Language.fetch_config()

    products = await product_service.list_display_chat_product(
        language=Language.ZH.value
    )
    config_maps["product_list"] = [x.model_dump() for x in products]
    to_model_details = [LlmModelDetail.from_config(x).model_dump() for x in llm_models]
    config_maps["to_model_detail_list"] = to_model_details
    return JSONResponse(content=config_maps)


class AddModelRequest(BaseModel):
    llm_model: str
    request_llm_model: str
    min_p: bool = False
    repetition_penalty: bool = False

    def support_params(self):
        params = []
        if self.min_p:
            params.append("min_p")
        if self.repetition_penalty:
            params.append("repetition_penalty")
        return params


@global_config_router.post("/global/config/model/add")
async def add_model_config(add_model: AddModelRequest):
    log.info(f"add_model_config: {add_model}")
    add_model.llm_model = add_model.llm_model.strip()
    add_model.request_llm_model = add_model.request_llm_model.strip()
    model_config = await config_service.get_llm_model_config_by_model(
        add_model.llm_model
    )
    if model_config:
        model_config.enabled = True
        model_config.request_llm_model = add_model.request_llm_model
        model_config.support_params = add_model.support_params()
        model_config.check_llm_request = True
        # model_config.request_cluster = (
        #     "FREE" if "free" in add_model.llm_model else "PRO"
        # )
        await model_config.save()
        return response_util.ok()

    all_llm_configs = await LlmModelConfig.all()
    model_int = max([x.model_int for x in all_llm_configs]) + 1

    await LlmModelConfig.create(
        llm_model=add_model.llm_model,
        model_int=model_int,
        enabled=True,
        request_llm_model=add_model.request_llm_model,
        support_params=add_model.support_params(),
        check_llm_request=True,
        request_cluster="FREE" if "free" in add_model.llm_model else "PRO",
    )
    if env_util.get_current_env() == Env.PROD:
        url = "https://tavern-admin-api.655356.xyz/no_auth/llm_model/add"
        params = {
            "llm_model": add_model.llm_model,
            "request_llm_model": add_model.request_llm_model,
            "min_p": add_model.min_p,
            "repetition_penalty": add_model.repetition_penalty,
        }
        ret = requests.post(url, params=params).json()
        log.info(f"add llm model {add_model} to stag, ret: {ret}")
    return response_util.ok()


@global_config_router.post("/global/config/model/delete")
async def delete_model_config(llm_model: str):
    model_config = await config_service.get_llm_model_config_by_model(llm_model)
    if not model_config:
        return response_util.def_error("模型不存在")
    water_configs = await config_service.list_model_water_config()
    water_configs = [x for x in water_configs if x.to_llm_model == llm_model]
    if water_configs:
        products = [
            await product_service.get_chat_product_by_model(x.llm_model)
            for x in water_configs
        ]
        product_names = [x.short_name for x in products if x]
        return response_util.def_error(
            f"模型正在使用中，无法删除,请先删相关产品（{','.join(product_names)}）中掺水配置"
        )
    if model_config:
        model_config.enabled = False
        await model_config.save()
    return response_util.ok()


@global_config_router.get("/global/config/model/check_status")
async def check_model_status(llm_model: str):
    model_config = await config_service.get_llm_model_config_by_model(llm_model)
    if not model_config:
        return response_util.def_error("模型不存在")
    url = config_service.get_lite_llm_base_url(model_config.request_cluster)
    check_status,message = await lite_llm_bot.check_models_success(model_config.llm_model, url)
    return response_util.ok(data={"check_status": check_status,"message":message})
