import json
import os
import random
from typing import Annotated, Optional
import uuid
import bcrypt
from fastapi import Depends, FastAPI, File, Header, Request, Form, Response, UploadFile
from fastapi.routing import APIRouter

from common.common_constant import Language
from common.models.chat_model import BuildHistoryQuery
from common.models.common_res_model import SpeakerRes
from common.role_model import RoleConfigResponse
from persistence.models.models import RegexOption
from services import product_service, tag_service, user_service
from services.chat import chat_message_service
from services.role_config_service import RoleConfigService
from services.voice_speaker_service import VoiceSpeakerService
from utils import env_const, response_util, token_util

open_api_router = APIRouter()


@open_api_router.get("/open/api/user/chat/history")
@open_api_router.get("/user/chat/history")
async def chat_history(
    user_id: int,
    sum_token: int,
    role_id: int,
    conversation_id: str,
    api_token: str = Header(),
):
    if api_token != "dify:aa7e1280cd514ad2834637f6f8896676":
        return response_util.param_error("token error")

    user = await user_service.get_by_id(user_id)
    if user is None:
        return response_util.param_error("user not found")
    query = BuildHistoryQuery(
        user_id=user_id,
        nickname=user.nickname,
        mode_type="single",
        mode_target_id=role_id,
        conversation_id=conversation_id,
        language=Language.ZH.value,
        add_name=True,
        regex_option=RegexOption.FORMAT_PROMPT.value,
        use_display_user_name= True,
    )
    history_items = await chat_message_service.build_history(query)
    history_ret = []
    history_items = list(reversed(history_items))
    for item in history_items:
        sum_token -= token_util.num_tokens_from_string(item.content)
        if sum_token < 0:
            break
        history_ret.append(item)
    return response_util.ok({"history": list(reversed(history_ret))})


@open_api_router.get("/open/api/roles/create_config")
async def createConfig(role_id: int = 0, api_token: str = Header()):
    if api_token != "dify:aa7e1280cd514ad2834637f6f8896676":
        return response_util.param_error("token error")

    USER_UN_DISPLAY_TAGS = ["状态栏", "可发私照"]

    all_tags = await tag_service.list_sub_tags_with_enabled()
    all_tags = [tag for tag in all_tags if tag.tag_name not in USER_UN_DISPLAY_TAGS]
    ret: RoleConfigResponse = await RoleConfigService.get_created_config(
        role_id=role_id
    )
    speaker_list = await VoiceSpeakerService.get_active_speakers()
    speaker_list = (
        [SpeakerRes.from_model(speaker) for speaker in speaker_list]
        if speaker_list
        else []
    )
    ret.all_tags = [tag.tag_name for tag in all_tags]
    ret.tag_orders = await tag_service.list_tags_with_orders()
    ret.speakers = speaker_list
    ret.chat_products = await product_service.list_chat_product_new()
    return ret
