import asyncio
import logging
from typing import Annotated
from fastapi import APIRouter, Depends, File, Form, UploadFile
from fastapi.responses import JSONResponse
from common.bot_common import (
    BOT_DESCRIPTION,
    BOT_SHORT_DESCRIPTION,
    MessageTemplate,
)
from common.common_constant import (
    BotCategory,
    BotReplace,
    ChannelCategory,
    GroupCategory,
)
from persistence import role_broadcast_history_dao
from persistence.models.models import TgChannelConfig, TgGroupConfig
from services import (
    bot_message_service,
    tg_config_service,
    tg_message_service,
    user_service,
)
from services.role import role_loader_service
from tasks import init_config_task
from utils import response_util
from utils.exception_util import ParamException

from aiogram.types.bot_command import BotCommand


tg_config_router = APIRouter()

log = logging.getLogger(__name__)


@tg_config_router.post("/tg/config/refresh/add_bot")
async def add_bot(token: str, bot_category: BotCategory):
    bot = await init_config_task.add_bot(token, bot_category)
    return response_util.ok({"bot": bot})


@tg_config_router.post("/tg/config/refresh/get_bot_info")
async def bot_info(token: str):
    bot_config = await tg_config_service.get_bot_config_by_token(token)
    sender_bot = tg_config_service.builder_sender_bot_by_token(token)
    ret = {}
    if sender_bot:
        web_hook_info = await sender_bot.get_webhook_info()
        ret["web_hook_info"] = web_hook_info
        desc = await sender_bot.get_my_description()
        short_desc = await sender_bot.get_my_short_description()
        ret["description"] = desc.description
        ret["short_description"] = short_desc.short_description
        me = await sender_bot.get_me()
        ret["me"] = me
    if bot_config:
        ret["bot_config"] = bot_config
    return response_util.ok(ret)


@tg_config_router.post("/tg/config/refresh/init_bot")
async def init_chat_bot(
    token: str,
    tma_bot_id: str = "",
    config_webhook: bool = False,
    web_hook_domain: str = "",
    webhook_id: str = "",
    config_bot_name: bool = False,
    bot_name: str = "",
    config_commands: bool = False,
    config_description: bool = False,
    # config_photo: bool = False,
    # photo: Annotated[UploadFile, File()] = File(None),
):
    tg_bot_config = await tg_config_service.get_bot_config_by_token(token)
    if not tg_bot_config:
        return response_util.def_error("bot not found")
    if tma_bot_id:
        tg_bot_config.tma_bot_id = tma_bot_id
        await tg_bot_config.save()
    category = tg_bot_config.category
    sender_bot = await tg_config_service.get_sender_bot_by_id(tg_bot_config.id)
    if config_webhook and web_hook_domain and tg_bot_config.username:
        webhook_id = webhook_id if webhook_id else tg_bot_config.username
        await sender_bot.set_webhook(
            f"{web_hook_domain}/role_hook/{webhook_id}",
            allowed_updates=["message", "chat_member", "callback_query"],
            secret_token="8e2104bd",
        )
    if config_bot_name and bot_name:
        await sender_bot.set_my_name(bot_name)

    if config_commands and category == BotCategory.CHAT_BOT:
        # set commands
        await sender_bot.set_my_commands(
            [
                BotCommand(command="hmai", description="打开小程序"),
                BotCommand(command="list", description="角色列表"),
                BotCommand(command="start", description="开始陪聊"),
                BotCommand(command="reset", description="重新开始"),
                BotCommand(command="new_roles", description="角色上新"),
                BotCommand(command="role_bot", description="角色橱窗"),
                BotCommand(command="recharge", description="充值"),
                BotCommand(command="voucher", description="卡密兑换"),
                BotCommand(command="balance", description="余额查询"),
                BotCommand(command="welfare", description="钻石福利"),
                BotCommand(command="checkin", description="签到"),
                BotCommand(command="invite_link", description="邀请好友"),
                BotCommand(command="archives", description="聊天存档"),
                BotCommand(command="settings", description="设置"),
                BotCommand(command="help", description="帮助"),
            ]
        )
    if config_description and category == BotCategory.CHAT_BOT:
        # set description
        description = await bot_message_service.format_content_replace(BOT_DESCRIPTION)
        if description:
            await sender_bot.set_my_description(description)
        short_description = await bot_message_service.format_content_replace(
            BOT_SHORT_DESCRIPTION
        )
        if short_description:
            await sender_bot.set_my_short_description(short_description)


@tg_config_router.post("/tg/config/refresh/add_channel")
async def init_channel(username: str, channel_category: ChannelCategory):
    tg_info = await init_config_task.add_channel(username, channel_category)
    return response_util.ok({"tg_info": tg_info})


@tg_config_router.post("/tg/config/refresh/add_group")
async def init_group(username: str, group_category: GroupCategory, main: bool):
    tg_info = await init_config_task.add_group(username, group_category, main)
    return response_util.ok({"tg_info": tg_info})


@tg_config_router.post("/tg/config/refresh/step1")
async def init_tg_config():
    """
    1、check与校验配置（主资源是否都存在）

    2、check与校验配置（helper机器人都被添加到了channel、group中）
    """
    try:
        # await init_config_task.init_tg_config()
        await init_config_task.check_bot_config()
        await init_config_task.check_channel_config()
        await init_config_task.check_group_config()
        await init_config_task.notify_default_template()
    except ParamException as e:
        log.error("init_tg_config error: %s", e)
        return response_util.def_error(e.detail)
    return response_util.success("ok")


@tg_config_router.post("/tg/config/refresh/group")
async def refresh_channel_group(chat_id: int):
    """
    刷新group配置
    """
    if chat_id == 0:
        groups = await tg_config_service.list_group()
        for group in groups:
            try:
                log.info("refresh_channel_config: %s", group.chat_id)
                await bot_message_service.refresh_group(group.chat_id)
            except Exception as e:
                log.error("refresh_channel_config error: %s", e)
        return response_util.ok({"groups": groups})

    group = await tg_config_service.get_group_config_by_chat_id(chat_id)
    if not group:
        return response_util.def_error("channel not found")
    await bot_message_service.refresh_channel_desc(group.chat_id)
    return response_util.ok({"group": group})


@tg_config_router.post("/tg/config/refresh/channel")
async def refresh_channel_config(chat_id: int):
    """
    刷新channel配置
    """
    if chat_id == 0:
        channels = await tg_config_service.list_channel()
        for channel in channels:
            try:
                log.info("refresh_channel_config: %s", channel.chat_id)
                await bot_message_service.refresh_channel_desc(channel.chat_id)
            except Exception as e:
                log.error("refresh_channel_config error: %s", e)

    tg_channel_config = await tg_config_service.get_channel_by_chat_id(chat_id)
    if not tg_channel_config:
        return response_util.def_error("channel not found")
    await bot_message_service.refresh_channel_desc(tg_channel_config.chat_id)
    return response_util.ok({"tg_channel_config": tg_channel_config})


@tg_config_router.post("/tg/config/refresh/role_channel_content")
async def refresh_role_channel_content(chat_id: int):
    """
    刷新channel配置
    """
    if chat_id == 0:
        channels = await tg_config_service.list_channel(ChannelCategory.ROLE)
        for channel in channels:
            asyncio.create_task(
                bot_message_service.refresh_channel_message(channel.chat_id)
            )
        return response_util.ok({"channels": channels})

    tg_channel_config = await tg_config_service.get_channel_by_chat_id(chat_id)
    if not tg_channel_config:
        return response_util.def_error("channel not found")
    asyncio.create_task(
        bot_message_service.refresh_channel_message(tg_channel_config.chat_id)
    )
    # await bot_message_service.refresh_channel_message(tg_channel_config.chat_id)
    return response_util.ok({"tg_channel_config": tg_channel_config})


@tg_config_router.post("/tg/config/refresh/role_old_channel_content")
async def refresh_role_old_channel_content(chat_id: int):
    """
    刷新channel配置
    """
    tg_channel_config = await tg_config_service.get_channel_by_chat_id(chat_id)
    if not tg_channel_config:
        return response_util.def_error("channel not found")
    asyncio.create_task(
        bot_message_service.refresh_old_channel_message(tg_channel_config.chat_id)
    )
    return response_util.ok({"tg_channel_config": tg_channel_config})


@tg_config_router.post("/tg/config/refresh/role_channel_content_alone")
async def refresh_role_channel_content_alone(chat_id: int, message_id: int):
    """
    刷新channel配置
    """
    tg_channel_config = await tg_config_service.get_channel_by_chat_id(chat_id)
    if not tg_channel_config:
        return response_util.def_error("channel not found")
    await bot_message_service.refresh_channel_message_alone(
        tg_channel_config.chat_id, message_id
    )
    return response_util.ok({"tg_channel_config": tg_channel_config})


@tg_config_router.post("/tg/config/refresh/step2")
async def refresh_bot_config():
    """
    1、刷新短简介、长简介配置（bot、channel、group）

    2、刷新pinned消息数据（channel、group）
    """
    update_list = await bot_message_service.refresh_bot_config()
    return response_util.ok({"update_list": update_list})


@tg_config_router.post("/tg/config/refresh/step3")
async def refresh_bot_resource():
    """
    ## 刷新历史数据（耗时长，异步执行，结果查看报警群）
    1、刷新channel历史消息数据（角色卡数据）

    2、刷新group历史消息数据（分享数据）(待定)
    """
    # await bot_message_service.refresh_bot_resource()
    return JSONResponse(content={"message": "Refresh success"})


# @tg_config_router.post("/tg/config/send_user_message")
# async def send_user_message(user_id: int):
#     CHANNEL_BOTTOM_MESSAGE = MessageTemplate(
#         log_tag="CHANNEL_BOTTOM_MESSAGE",
#         tips=f"请关注【幻梦福利频道】 @{BotReplace.MAIN_WELFARE_CHANNEL.value} 加入【幻梦AI用户群】 @{BotReplace.MAIN_GROUP.value} 完成签到",
#         buttons=[
#             Button(
#                 text="关注频道", url=f"https://t.me/{BotReplace.MAIN_GROUP_ROLE.value}"
#             ),
#             Button(text="加入群", url=f"https://t.me/{BotReplace.MAIN_GROUP.value}"),
#             Button(text="签到", callback_data=CheckInCallback()),
#         ],
#     )
#     template = await bot_message_service.format_template_replace(CHANNEL_BOTTOM_MESSAGE)

#     user = await user_service.get_by_id(user_id)
#     await bot_message_service.send_user_template_message(user, template)

#     return response_util.ok({"template": template})


# @tg_config_router.post("/no_auth/roles/broadcast/role_retry")
# async def role_retry(chat_id: int, push: bool = False):
#     log.info(f"role_retry,chat_id:{chat_id}")
#     his_list = await role_broadcast_history_dao.list_by_chat_id(chat_id)
#     role_ids = [his.role_id for his in his_list]
#     his_list = await role_broadcast_history_dao.list_old_by_chat_id(chat_id)
#     role_ids.extend([his.get("role_id", 0) for his in his_list])
#     role_ids = list(set(role_ids))

#     roles = await role_loader_service.list_public()
#     roles.sort(key=lambda x: x.updated_at)
#     roles = [x for x in roles if x.id not in role_ids]
#     push_role_ids = [x.id for x in roles]

#     async def run_task():
#         for role_id in push_role_ids:
#             await broadcast_role_to_chat(chat_id, role_id)
#             await asyncio.sleep(1)

#     if push:
#         asyncio.create_task(run_task())

#     return response_util.ok({"role_ids": push_role_ids})


@tg_config_router.post("/tg/config/send_user_message_by_template")
async def send_user_message_by_template(user_id: int, template: MessageTemplate):
    template = await bot_message_service.format_template_replace(template)
    user = await user_service.get_by_id(user_id)
    await bot_message_service.send_user_template_message(user, template)

    return response_util.ok({"template": template})
