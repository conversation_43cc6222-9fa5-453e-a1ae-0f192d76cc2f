import asyncio
from io import String<PERSON>
from fastapi import APIRouter
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from ai import inner_bot
from common.common_constant import (
    BenefitChannel,
    BenefitUsageScopeType,
    ChatBenefitType,
    Language,
)
from common.translate_model import (
    TranslateSubTag,
    TranslateTaskStatus,
    TranslateTaskType,
)
from persistence import redis_client
from persistence.models.models import ChatBenefit, TranslateResource
from services import translate_service, user_service
from services.user import user_benefit_service
from tasks import translate_task
from utils import response_util

benefit_router = APIRouter()


@benefit_router.get("/benefit/configs")
async def benefit_list():
    chat_benefits = await ChatBenefit.filter().all()
    benefit_list = [
        {
            "id": benefit.id,
            "title": benefit.title,
            "type": benefit.type,
            "type_desc": ChatBenefitType(benefit.type).to_desc(),
            "limit_times": benefit.limit_times,
            "reward_times": benefit.reward_times,
            "reward_valid_days": benefit.reward_valid_days,
            "usage_scope_type": benefit.usage_scope_type,
            "usage_scope_type_desc": BenefitUsageScopeType(
                benefit.usage_scope_type
            ).to_desc(),
            "usage_scope_id": benefit.usage_scope_id,
        }
        for benefit in chat_benefits
    ]
    return response_util.ok({"benefit_list": benefit_list})


@benefit_router.post("/benefit/user/reward")
async def user_reward(body: dict):
    chat_benefit_id = body.get("chat_benefit_id")
    user_id = body.get("user_id")
    if not chat_benefit_id or not user_id:
        return response_util.def_error("缺少有效参数")
    user = await user_service.get_by_id(user_id)
    if not user:
        return response_util.def_error("User not found")

    await user_benefit_service.reward_benefit(
        user, chat_benefit_id, BenefitChannel.SIGN_IN
    )
    return response_util.ok({"message": "Reward successful"})
