[program:aibot-app-server]
command=/root/micromamba/envs/tavern-server/bin/python -m uvicorn aibot_app_server:app --host 0.0.0.0 --port 9200 --workers 1
directory=/root/aibot_app_server/tavern-server
autostart=true
autorestart=true
stderr_logfile=/data/logs/pm2/aibot-app-server/err.log
stdout_logfile=/data/logs/pm2/aibot-app-server/out.log

[program:aibot-task-server]
command=/root/micromamba/envs/tavern-server/bin/python -m uvicorn aibot_task_server:app --host 0.0.0.0 --port 9220 --workers 1
directory=/root/aibot_app_server/tavern-server
autostart=true
autorestart=true
stderr_logfile=/data/logs/pm2/aibot-task-server/err.log
stdout_logfile=/data/logs/pm2/aibot-task-server/out.log

[program:aibot-app-admin-server]
command=/root/micromamba/envs/tavern-server/bin/python -m uvicorn aibot_app_admin_server:app --host 0.0.0.0 --port 9230 --workers 1
directory=/root/aibot_app_server/tavern-server
autostart=true
autorestart=true
stderr_logfile=/data/logs/pm2/aibot-app-admin-server/err.log
stdout_logfile=/data/logs/pm2/aibot-app-admin-server/out.log

[program:image-bot-server]
command=/root/micromamba/envs/tavern-server/bin/python -m uvicorn image_bot_app_server:app --host 0.0.0.0 --port 9240 --workers 1
directory=/root/aibot_app_server/tavern-server
autostart=true
autorestart=true
stderr_logfile=/data/logs/pm2/image-bot-server/err.log
stdout_logfile=/data/logs/pm2/image-bot-server/out.log