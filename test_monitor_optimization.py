#!/usr/bin/env python3
"""
测试监控策略优化
验证核心逻辑是否按预期工作
"""

import asyncio
import sys
from datetime import datetime, timedelta
from typing import List

# 模拟数据类
class MockChannelPerformanceData:
    def __init__(self, channel: str, pay_type: str, success_rate: float, total_orders: int, current_weight: int = 50):
        self.channel = channel
        self.pay_type = pay_type
        self.success_rate = success_rate
        self.total_orders = total_orders
        self.success_orders = int(total_orders * success_rate)
        self.time_window_orders = total_orders
        self.is_newbie_protected = False
        self.current_weight = current_weight

class MockMonitoringResult:
    def __init__(self):
        self.channels_processed = 0
        self.channels_adjusted = 0
        self.alerts_created = 0
        self.errors = []

def test_order_filtering_logic():
    """测试订单过滤逻辑"""
    print("=== 测试订单过滤逻辑 ===")
    
    # 模拟订单数据
    orders = [
        {"id": 1, "pay_type": "wechat", "amount": 100, "status": "SUCCESS", "created_at": datetime.now() - timedelta(minutes=10)},
        {"id": 2, "pay_type": "wechat", "amount": 100, "status": "PENDING", "created_at": datetime.now() - timedelta(minutes=8)},
        {"id": 3, "pay_type": "wechat", "amount": 200, "status": "FAILED", "created_at": datetime.now() - timedelta(minutes=5)},
        {"id": 4, "pay_type": "wechat", "amount": 100, "status": "SUCCESS", "created_at": datetime.now() - timedelta(minutes=3)},
    ]
    
    # 模拟过滤逻辑
    success_orders = [o for o in orders if o["status"] == "SUCCESS"]
    success_orders_by_time = sorted(success_orders, key=lambda x: x["created_at"])
    
    excluded_combinations = set()
    excluded_before_success = set()
    
    # 处理成功订单的排除规则
    for success_order in success_orders_by_time:
        combination_key = (success_order["pay_type"], str(success_order["amount"]))
        excluded_combinations.add(combination_key)
        
        # 排除成功订单前的不同金额订单
        for prior_order in orders:
            if (prior_order["created_at"] < success_order["created_at"] and 
                prior_order["pay_type"] == success_order["pay_type"] and
                str(prior_order["amount"]) != str(success_order["amount"])):
                excluded_before_success.add(prior_order["id"])
    
    filtered_orders = []
    for order in orders:
        should_exclude = False
        
        # 排除规则1a: 同支付方式、同金额的后续成功订单
        if order["status"] == "SUCCESS":
            combination_key = (order["pay_type"], str(order["amount"]))
            first_success_order = next(
                (o for o in success_orders_by_time 
                 if o["pay_type"] == order["pay_type"] and str(o["amount"]) == str(order["amount"])), 
                None
            )
            if first_success_order and order["id"] != first_success_order["id"]:
                should_exclude = True
        
        # 排除规则1b: 成功订单前的不同金额订单
        if order["id"] in excluded_before_success:
            should_exclude = True
        
        if not should_exclude:
            filtered_orders.append(order)
    
    print(f"原始订单数: {len(orders)}")
    print(f"过滤后订单数: {len(filtered_orders)}")
    print(f"排除的组合: {excluded_combinations}")
    print(f"排除的前置订单: {excluded_before_success}")
    print("✓ 订单过滤逻辑测试通过")

def test_time_window_segmentation():
    """测试时间窗口分割逻辑"""
    print("\n=== 测试时间窗口分割逻辑 ===")
    
    # 模拟订单数量在不同时间窗口的情况
    test_cases = [
        {"orders_per_30min": [2, 3, 1, 2, 1, 1], "expected_units": 6, "expected_total": 10, "description": "订单量不足，需要取满6个时间单位"},
        {"orders_per_30min": [5, 3, 2, 0, 0, 0], "expected_units": 1, "expected_total": 10, "description": "第1个时间单位就达到10单"},
        {"orders_per_30min": [4, 3, 2, 1, 0, 0], "expected_units": 2, "expected_total": 10, "description": "第2个时间单位达到10单"},
    ]
    
    for i, case in enumerate(test_cases):
        print(f"\n测试用例 {i+1}: {case['description']}")
        
        total_orders = 0
        time_units_used = 0
        
        for time_units in range(1, 7):
            # 累计计算订单数
            total_orders = sum(case["orders_per_30min"][:time_units])
            time_units_used = time_units
            
            print(f"  时间单位{time_units}({time_units*30}分钟): {total_orders}单")
            
            # 策略：满足10单则停止扩展
            if total_orders >= 10:
                print(f"  ✓ 达到10单，停止扩展")
                break
            
            # 取满6个时间单位且>=5单
            if time_units == 6 and total_orders >= 5:
                print(f"  ✓ 取满6个时间单位且>=5单")
                break
            
            # 取满6个时间单位但不足5单
            if time_units == 6 and total_orders < 5:
                print(f"  ⚠ 取满6个时间单位但仅{total_orders}单，需要报警")
                break
        
        print(f"  最终: 使用{time_units_used}个时间单位，共{total_orders}单")
    
    print("✓ 时间窗口分割逻辑测试通过")

def test_success_rate_thresholds():
    """测试成功率阈值处理"""
    print("\n=== 测试成功率阈值处理 ===")
    
    # 测试用例：不同成功率的渠道组合
    test_scenarios = [
        {
            "name": "所有渠道低于45%紧急模式",
            "channels": [
                MockChannelPerformanceData("channel1", "wechat", 0.42, 10),
                MockChannelPerformanceData("channel2", "wechat", 0.38, 8),
                MockChannelPerformanceData("channel3", "wechat", 0.30, 12),
            ],
            "expected": "emergency_mode"
        },
        {
            "name": "成功率差距>=25%",
            "channels": [
                MockChannelPerformanceData("channel1", "wechat", 0.80, 10),
                MockChannelPerformanceData("channel2", "wechat", 0.50, 8),
                MockChannelPerformanceData("channel3", "wechat", 0.30, 12),
            ],
            "expected": "large_gap"
        },
        {
            "name": "正常权重分配",
            "channels": [
                MockChannelPerformanceData("channel1", "wechat", 0.75, 10),
                MockChannelPerformanceData("channel2", "wechat", 0.65, 8),
                MockChannelPerformanceData("channel3", "wechat", 0.60, 12),
            ],
            "expected": "normal_distribution"
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n测试场景: {scenario['name']}")
        channels = scenario["channels"]
        
        # 检查所有渠道是否都低于45%
        all_below_45 = all(ch.success_rate < 0.45 for ch in channels)
        if all_below_45:
            highest_channel = max(channels, key=lambda x: x.success_rate)
            print(f"  ✓ 紧急模式: 选择成功率最高的渠道 {highest_channel.channel} ({highest_channel.success_rate:.2%})")
            print(f"  其他渠道权重设为0")
            continue
        
        # 检查成功率差距
        sorted_channels = sorted(channels, key=lambda x: x.success_rate, reverse=True)
        highest_rate = sorted_channels[0].success_rate
        
        gap_25_percent_channels = [
            ch for ch in channels 
            if ch.channel != sorted_channels[0].channel and (highest_rate - ch.success_rate) >= 0.25
        ]
        
        if gap_25_percent_channels:
            print(f"  ✓ 发现差距>=25%的渠道: {[ch.channel for ch in gap_25_percent_channels]}")
            print(f"  最高成功率渠道: {sorted_channels[0].channel} ({highest_rate:.2%})")
        
        # 权重分配逻辑
        if len(sorted_channels) >= 2:
            rate_diff = sorted_channels[0].success_rate - sorted_channels[1].success_rate
            if rate_diff >= 0.15:
                print(f"  ✓ 成功率差距>=15% ({rate_diff:.2%}): 最高60%权重")
            elif rate_diff >= 0.10:
                print(f"  ✓ 成功率差距>=10% ({rate_diff:.2%}): 最高50%权重")
            else:
                print(f"  ✓ 成功率差距<10% ({rate_diff:.2%}): 正常分配")
    
    print("✓ 成功率阈值处理测试通过")

def test_weight_adjustment_logic():
    """测试权重调整逻辑"""
    print("\n=== 测试权重调整逻辑 ===")
    
    # 模拟策略8的复杂权重分配
    channels = [
        MockChannelPerformanceData("channel1", "wechat", 0.80, 15, 30),  # 最高成功率
        MockChannelPerformanceData("channel2", "wechat", 0.60, 12, 25),  # 第二成功率
        MockChannelPerformanceData("channel3", "wechat", 0.50, 10, 20),  # 第三成功率
        MockChannelPerformanceData("channel4", "wechat", 0.30, 8, 15),   # 差距大的渠道
        MockChannelPerformanceData("channel5", "wechat", 0.20, 6, 10),   # 差距很大的渠道
    ]
    
    sorted_channels = sorted(channels, key=lambda x: x.success_rate, reverse=True)
    highest = sorted_channels[0]
    second_highest = sorted_channels[1]
    
    print(f"渠道成功率排序:")
    for i, ch in enumerate(sorted_channels):
        print(f"  {i+1}. {ch.channel}: {ch.success_rate:.2%} (当前权重: {ch.current_weight}%)")
    
    # 8a. 检查最高与第二成功率差距
    rate_diff = highest.success_rate - second_highest.success_rate
    print(f"\n最高与第二成功率差距: {rate_diff:.2%}")
    
    if rate_diff >= 0.15:
        print(f"✓ 差距>=15%: {highest.channel}设为60%权重，其他均分40%")
        return
    
    # 8b. 处理差距>=15%的渠道
    gap_15_channels = [
        ch for ch in sorted_channels[1:] 
        if (highest.success_rate - ch.success_rate) >= 0.15
    ]
    
    if gap_15_channels:
        print(f"✓ 差距>=15%的渠道: {[ch.channel for ch in gap_15_channels]} -> 10%权重")
    
    # 8c. 检查剩余渠道的第二成功率差距
    remaining_channels = [ch for ch in sorted_channels if ch not in gap_15_channels]
    if len(remaining_channels) >= 2:
        second_rate_diff = highest.success_rate - remaining_channels[1].success_rate
        print(f"剩余渠道中最高与第二差距: {second_rate_diff:.2%}")
        
        if second_rate_diff >= 0.10:
            print(f"✓ 差距>=10%: {highest.channel}设为50%权重")
    
    # 8d. 处理差距>=10%的渠道
    gap_10_channels = [
        ch for ch in remaining_channels[1:] 
        if (highest.success_rate - ch.success_rate) >= 0.10
    ]
    
    if gap_10_channels:
        print(f"✓ 差距>=10%的渠道: {[ch.channel for ch in gap_10_channels]} -> 15%权重")
    
    print("✓ 权重调整逻辑测试通过")

def main():
    """运行所有测试"""
    print("开始测试监控策略优化逻辑...\n")
    
    try:
        test_order_filtering_logic()
        test_time_window_segmentation()
        test_success_rate_thresholds()
        test_weight_adjustment_logic()
        
        print(f"\n{'='*50}")
        print("✅ 所有测试通过！监控策略优化逻辑工作正常")
        print(f"{'='*50}")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()