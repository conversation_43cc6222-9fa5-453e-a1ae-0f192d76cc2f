import shutil
from typing import List
from dotenv import load_dotenv
from fastapi import Depends, FastAPI, File, HTTPException, UploadFile, status
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
from tortoise import Tortoise
from tortoise.contrib.fastapi import register_tortoise
import uvicorn
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBasic, HTTPBasicCredentials
import datetime
import logging
import os

from services.bot_group.bot_group_service import AutoReplyRuleService
from utils.cos_util import upload_to_s3_original_url



# from aiogram import LoggingMiddleware


from common import loggers
from common.models.ai_bot_admin.group_config import AutoReplyRuleBO, AutoReplyRuleCreate, AutoReplyRuleUpdate, AutoSendMsgTaskBO



from persistence.models.models_bot_group import AutoReplyRule, AutoSendMessageConfig,DeleteGroupMsgImageCfg




logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s (%(filename)s:%(lineno)d) [%(levelname)s] %(message)s",handlers=[logging.StreamHandler(),loggers.local_bot_help_handler])

log = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()

security = HTTPBasic()
app = FastAPI()
UPLOAD_DIR = "uploads"
os.makedirs(UPLOAD_DIR, exist_ok=True)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有 HTTP 方法
    allow_headers=["*"],  # 允许所有 HTTP 头
)

Tortoise.init_models(["persistence.models.models_bot_group","persistence.models.models"], "models")
register_tortoise(
    app=app,
    db_url=os.environ["MYSQL_URL"],
    modules={"models": ["persistence.models.models_bot_group","persistence.models.models"]},
    generate_schemas=True,
)

def get_current_username(credentials: HTTPBasicCredentials = Depends(security)):
    correct_username = os.getenv("BASIC_AUTH_USERNAME", "admin")
    correct_password = os.getenv("BASIC_AUTH_PASSWORD", "huanmeng007")
    if credentials.username != correct_username or credentials.password != correct_password:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials.username
# 配置静态文件路径
app.mount("/static", StaticFiles(directory="front-admin-app/build/static"), name="static")


def build_cron__date_exp(date: datetime.datetime):
    # 解析为北京时间的cron表达式
    date = date.astimezone(datetime.timezone(datetime.timedelta(hours=8)))
    minute = date.minute
    hour = date.hour 
    day = date.day
    month = date.month
    cron_expression = f"{minute} {hour} {day} {month} *"
    return cron_expression

def build_cron_exp(interval_seconds:int):
    if interval_seconds < 60:
        cron_expression = f"*/{interval_seconds} * * * * *"
    elif interval_seconds < 3600:
        minutes = interval_seconds // 60
        cron_expression = f"*/{minutes} * * * *"
    elif interval_seconds < 86400:
        hours = interval_seconds // 3600
        cron_expression = f"0 */{hours} * * *"
    elif interval_seconds < 604800:
        days = interval_seconds // 86400
        cron_expression = f"0 0 */{days} * *"
    else:
        weeks = interval_seconds // 604800
        cron_expression = f"0 0 1 */{weeks} *"
    
    return cron_expression


@app.get("/")
async def read_index():
    return FileResponse("front-admin-app/build/index.html")


# 定时消息设置相关
@app.post("/auto_send_msg/send_config")
async def send_auto_send_msg_config(auto_send_config: dict):
    log.info(f"send_auto_send_msg_config:{auto_send_config}")
    
    if auto_send_config['id'] == 0:
    
        auto_send_message_config = AutoSendMessageConfig(
        title=auto_send_config['title'],
        content=auto_send_config['content'],
        type=auto_send_config['type'],
        start_time=datetime.datetime.fromisoformat(auto_send_config['startDate']),
        end_time=datetime.datetime.fromisoformat(auto_send_config['endDate']),
        enabled=auto_send_config['enabled'],
        delete_previous=auto_send_config['isDelete'],
        )
        log.info(f"auto_send_config:{auto_send_message_config}")
    else:
        auto_send_message_config = await AutoSendMessageConfig.get(id=auto_send_config['id'])
        if not auto_send_message_config:
            raise ValueError("AutoSendMessageConfig not found")
        auto_send_message_config.title = auto_send_config['title']
        auto_send_message_config.content = auto_send_config['content']
        auto_send_message_config.start_time = datetime.datetime.fromisoformat(auto_send_config['startDate'])
        auto_send_message_config.end_time = datetime.datetime.fromisoformat(auto_send_config['endDate'])
        auto_send_message_config.enabled = auto_send_config['enabled']
        auto_send_message_config.delete_previous = auto_send_config['isDelete']
    
    auto_send_message_config.to_group_id = auto_send_config['groupId']
    if "topicId" in auto_send_config:
        auto_send_message_config.to_thread_id = auto_send_config['topicId']
    
    # 图片消息
    if "imageUrl" in auto_send_config and auto_send_config['imageUrl']:
        auto_send_message_config.msg_type = 1
        auto_send_message_config.msg_content ={
            "msg_type":1,
            "msg_content":auto_send_config['content'],
            "image_url":auto_send_config['imageUrl']
        }
    else:
        auto_send_message_config.msg_type = 0
        auto_send_message_config.msg_content ={
            "msg_type":0,
            "msg_content":auto_send_config['content']
        }
    
    if auto_send_config['type'] == "Date":
        auto_send_message_config.job_type = "Date"
        #
        send_time = datetime.datetime.fromisoformat(auto_send_config['runDate'])
        cron_exe  = build_cron__date_exp(send_time)
        auto_send_message_config.send_time = send_time
        auto_send_message_config.cron_expression = cron_exe
    elif auto_send_config['type'] == "Interval":
        auto_send_message_config.job_type = "Interval"
        auto_send_message_config.interval = auto_send_config['interval']  
        cron_exe = build_cron_exp(auto_send_config['interval'])
        auto_send_message_config.cron_expression = cron_exe
        auto_send_message_config.send_time = datetime.datetime.now()
    elif auto_send_config['type'] == "Threshold":
        auto_send_message_config.job_type = "Threshold"
        auto_send_message_config.message_threshold = auto_send_config['messageThreshold']
        auto_send_message_config.send_time = datetime.datetime.now()
        auto_send_message_config.cron_expression = build_cron_exp(60)
    else:
       raise ValueError("Invalid job type")
    
    await auto_send_message_config.save()
   
        
        
@app.get("/auto_send_msg/list")
async def list_auto_send_msg() -> list[AutoSendMsgTaskBO]:
    auto_send_message_configs_list = []
    
    auto_send_message_configs = await AutoSendMessageConfig.all()
   
    for auto_send_message_config in auto_send_message_configs:
       
        
        auto_send_bot = AutoSendMsgTaskBO(
           id=auto_send_message_config.id,
           groupId=auto_send_message_config.to_group_id,
           topicId=auto_send_message_config.to_thread_id,
           title=auto_send_message_config.title,
           content=auto_send_message_config.content,
           type=auto_send_message_config.job_type,
           interval=auto_send_message_config.interval,
           startDate=auto_send_message_config.start_time.isoformat(),
           endDate=auto_send_message_config.end_time.isoformat(),
           runDate=auto_send_message_config.send_time.isoformat(),
           enabled=auto_send_message_config.enabled,
           isDelete=auto_send_message_config.delete_previous,
            messageThreshold=auto_send_message_config.message_threshold
       )
        
        if auto_send_message_config.msg_type == 1:
            auto_send_bot.imageUrl = auto_send_message_config.msg_content['image_url'] # type: ignore
       
        auto_send_message_configs_list.append(auto_send_bot)
        
    return auto_send_message_configs_list

@app.post("/auto_send_msg/file_upload")
async def image_file_upload(file: UploadFile = File(...)):
    # file_location = os.path.join(UPLOAD_DIR, file.filename) # type: ignore
    # with open(file_location, "wb") as buffer:
    #     shutil.copyfileobj(file.file, buffer)
    
    print(f"file_location:{file.filename}")
    
    # file_url = f"/{UPLOAD_DIR}/{file.filename}"
    image_url = upload_to_s3_original_url(file.file.read(),file.filename)
    # # return JSONResponse(content={"url": file_url})
    # url = "http://pic.people.com.cn/NMediaFile/2025/0304/MAIN17410623510829PZVUZJKME.jpg"
    
    return {"imageUrl": image_url}

@app.post("/group_msg/image_del_cfg")
async def add_or_update_delete_group_msg_image_cfg(config: dict):
    log.info(f"add_or_update_delete_group_msg_image_cfg: {config}")
    
    skip_tg_ids = []
    if "skipTgIds" in config and config['skipTgIds'] != "":
        skip_tg_ids_str_list = config['skipTgIds'].split(",")
        skip_tg_ids = [int(x) for x in skip_tg_ids_str_list]
    if  "id" not in config or  config['id'] == 0:
        delete_group_msg_image_cfg = DeleteGroupMsgImageCfg(
            group_id=config['groupId'],
            delete_time= config['deleteTime'],
            is_enable=config['isEnable'],
            skip_tg_ids = skip_tg_ids
        )
        log.info(f"delete_group_msg_image_cfg: {delete_group_msg_image_cfg}")
    else:
        delete_group_msg_image_cfg = await DeleteGroupMsgImageCfg.get(id=config['id'])
        if not delete_group_msg_image_cfg:
            raise ValueError("DeleteGroupMsgImageCfg not found")
        delete_group_msg_image_cfg.group_id = config['groupId']
        delete_group_msg_image_cfg.delete_time = config['deleteTime']
        delete_group_msg_image_cfg.is_enable = config['isEnable']
        delete_group_msg_image_cfg.skip_tg_ids = skip_tg_ids
    
    await delete_group_msg_image_cfg.save()
    return {"status": "success"}

@app.get("/group_msg/list_image_cfg")
async def list_delete_group_msg_image_cfg():
    delete_group_msg_image_cfg_list = []
    
    delete_group_msg_image_cfgs = await DeleteGroupMsgImageCfg.all()
   
    for delete_group_msg_image_cfg in delete_group_msg_image_cfgs:
       
       
        skip_tg_ids_str = ",".join([str(x) for x in delete_group_msg_image_cfg.skip_tg_ids])
        
        delete_group_msg_image_cfg_bo = {
           "id":delete_group_msg_image_cfg.id,
           "groupId":delete_group_msg_image_cfg.group_id,
           "deleteTime":delete_group_msg_image_cfg.delete_time,
           "isEnable":delete_group_msg_image_cfg.is_enable,
           "skipTgIds":skip_tg_ids_str
        }
        
        delete_group_msg_image_cfg_list.append(delete_group_msg_image_cfg_bo)
        
    return delete_group_msg_image_cfg_list


@app.post("/auto_reply_rules", response_model=AutoReplyRuleBO)
async def create_auto_reply_rule(rule: AutoReplyRuleCreate):
    log.info(f"create_auto_reply_rule: {rule}")
    rule_obj = await AutoReplyRule.create(**rule.dict())
    return AutoReplyRuleBO.from_orm(rule_obj)
  
@app.get("/auto_reply_rules/{rule_id}", response_model=AutoReplyRuleBO)
async def get_auto_reply_rule(rule_id: int):
    rule = await AutoReplyRule.get(id=rule_id)
    if not rule:
        raise HTTPException(status_code=404, detail="AutoReplyRule not found")
    return AutoReplyRuleBO.from_orm(rule)

@app.post("/auto_reply_rules/{rule_id}", response_model=AutoReplyRuleBO)
async def update_auto_reply_rule(rule_id: int, rule: AutoReplyRuleUpdate):
    log.info(f"update_auto_reply_rule: {rule}")
    rule_obj = await AutoReplyRule.get_or_none(rule_id=rule_id)
    if not rule_obj:
        raise HTTPException(status_code=404, detail="AutoReplyRule not found")
    await rule_obj.update_from_dict(rule.dict(exclude_unset=True))
    await rule_obj.save()
    return AutoReplyRuleBO.from_orm(rule_obj)

@app.get("/auto_reply_rules", response_model=List[AutoReplyRuleBO])
async def get_all_auto_reply_rules():
    
    #先返回1和2的规则
    rules = await AutoReplyRule.filter(keyword_type__lte=2).order_by("-pripority","-updated_at").all()
    if not rules:
        raise HTTPException(status_code=404, detail="AutoReplyRule not found")
    
    return [AutoReplyRuleBO.from_orm(rule) for rule in rules]

@app.get("/{full_path:path}")
async def read_react_app(full_path: str):
    return FileResponse("front-admin-app/build/index.html")


if __name__ == "__main__":

    uvicorn.run(app, host='0.0.0.0', port=9230)
