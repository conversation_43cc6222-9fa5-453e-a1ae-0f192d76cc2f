import random
import hashlib
from dataclasses import dataclass
from typing import Dict, Optional


@dataclass
class TokenConfig:
    weight: float
    description: str


class TokenAllocator:
    def __init__(self):
        # 定义tokens及其配置
        self.token_configs = {
            "pst-KBxbYoH2YJDtvdKrq9MeXCzYT1YgQyGhYyqIUsyZrma3GAv1uBX46twT5lwSNonb": TokenConfig(
                weight=0.5,
                description="kimi880622",
            ),
            "pst-zqQZ3WBcyiJt9LvifP39LNnUpaleNTDY2Jb7EIcfdPhJFcQQ0w4imwkg4559R70f": TokenConfig(
                weight=0.4,
                description="huanmeng2026",
            ),
            "pst-mxTVHbM9xIprs7mwWQcls4RJXXJTnU4Wh7rWTC6NjQgK0Ax8tH7cXwNnUxFe8IUW": TokenConfig(
                weight=0.1,
                description="huanmeng2027",
            ),
        }

        # 验证权重总和
        total_weight = sum(config.weight for config in self.token_configs.values())
        if abs(total_weight - 1.0) > 0.0001:
            raise ValueError(f"Token weights must sum to 1.0, got {total_weight}")

    def get_token(self, request_id: str) -> str:
        """
        基于请求ID分配token

        Args:
            request_id: 请求的唯一标识符

        Returns:
            分配的token
        """

        # 生成哈希值
        hash_value = int(hashlib.md5(request_id.encode()).hexdigest(), 16)
        random_value = hash_value / (2**128)

        # 根据权重分配token
        total_weight = sum(config.weight for config in self.token_configs.values())
        cumulative_prob = 0

        for token, config in self.token_configs.items():
            cumulative_prob += config.weight / total_weight
            if random_value <= cumulative_prob:
                return token

        return list(self.token_configs.keys())[-1]

    def get_token_info(self, token: str) -> Dict:
        """
        获取token的详细信息

        Args:
            token: token标识符

        Returns:
            包含token详细信息的字典
        """
        config = self.token_configs.get(token)
        if not config:
            return {}

        return {
            "weight": config.weight,
            "description": config.description,
        }
