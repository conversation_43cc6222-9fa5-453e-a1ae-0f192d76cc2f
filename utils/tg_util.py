import asyncio
from datetime import datetime
import json
import logging
import os
import re
from urllib.parse import unquote
from aiogram import Bot
import requests

from common.common_constant import Env, TgGroupChatId
from services import tg_config_service
from utils import exception_util

log = logging.getLogger(__name__)

token = "8136624037:AAE5ue82CH69Xv_wVFIEPkg4UCAVIVGDE1Q"
sender_bot = tg_config_service.builder_sender_bot_by_token(token)

pay_monitor_chat_id = '-1002550423097'

channel_monitor_chat_id = '-1002885389340'

stag_pay_monitor_chat_id = '-1002894269267'

def send_message(data: dict, chat_id: str = TgGroupChatId.STAG_MONITOR.value):
    try:
        # data 拼装成字符串
        env = Env.from_str(os.getenv("ENV", ""))
        text = ""
        for key in data:
            text += f"{key}: {data[key]}\n"
        data = {"chat_id": chat_id, "text": text}
        log.info(f"Sending message to telegram: {data}")
        env = Env.from_str(os.getenv("ENV", ""))
        if env == Env.LOCAL:
            log.info(f"Skip Message, Environment: {env}, Data: {data}")
            return
        # sender_bot.send_message(chat_id,data)
    except Exception as e:
        log.error(f"Error sending message to telegram: {e}")


async def send_monitor(data: dict, prod_only: bool = False):
    try:
        env = Env.from_str(os.getenv("ENV", ""))
        text = ""
        for key in data:
            text += f"{key}: {data[key]}\n"
        env = Env.from_str(os.getenv("ENV", ""))
        text += f"Environment: {env}\n"
        if env == Env.LOCAL:
            log.info(f"Skip Message, Environment: {env}, Data: {data}")
            return
        if prod_only and env != Env.PROD:
            log.info(f"Skip Message, Environment: {env}, Data: {data}")
            return
        log.info(f"Sending message to telegram: {text}")
        await sender_bot.send_message("@fancy_monitor", text)
    except Exception as e:
        log.error(f"Error sending message to telegram: {e}")


async def sm(data: dict, chat_id: str = "@fancy_monitor",
             prod_only: bool = False, auto_truncate: bool = True):
    try:
        env = Env.from_str(os.getenv("ENV", ""))
        text = ""
        for key in data:
            text += f"{key}: {data[key]}\n"
        env = Env.from_str(os.getenv("ENV", ""))
        text += f"Environment: {env}\n"
        if env == Env.LOCAL:
            log.info(f"Skip Message, Environment: {env}, Data: {data}")
            return
        if prod_only and env != Env.PROD:
            log.info(f"Skip Message, Environment: {env}, Data: {data}")
            return
        log.info(f"Sending message to telegram: {text}")
        if env == Env.STAG:
            chat_id = TgGroupChatId.STAG_MONITOR.value
        if auto_truncate:
            if len(text) > 4096:
                text = text[:4095]
        await sender_bot.send_message(chat_id, text)
    except Exception as e:
        log.error(f"Error sending message to telegram: {e}")


def send_xss_message(user_id: str, nickname: str, role_json: str):
    data = {
        "title": "XSS Attack In Create",
        "user_info": nickname + f"({user_id})",
        "role_json": f"role_name:{role_json})",
        "date": datetime.now(),
    }
    send_message(data)


def get_user_id_by_tg(init_data: str) -> int:
    try:
        if init_data.startswith(", "):
            init_data = init_data[2:]
        param_chunks = unquote(init_data).split("&")
        hash_str = next((x for x in param_chunks if x[: len("hash=")] == "hash="), None)
        hash_split_list = hash_str.split("=")
        if len(hash_split_list) < 1:
            log.error(f"Invalid tg init data: {init_data}")
            raise exception_util.http_auth("Invalid tg init data")
        hash_str = hash_split_list[1]

        sorted_init_data = sorted(
            [
                chunk.split("=")
                for chunk in param_chunks
                if chunk[: len("hash=")] != "hash="
            ],
            key=lambda x: x[0],
        )
        user_str = next((x for x in sorted_init_data if x[0] == "user"), None)
        if not user_str:
            return 0
        user_info = json.loads(user_str[1])
        tg_id = user_info["id"]
        return int(tg_id)
    except Exception as e:
        return 0


# 支持的HTML标签列表
supported_tags = [
    "<b>",
    "</b>",
    "<strong>",
    "</strong>",
    "<i>",
    "</i>",
    "<em>",
    "</em>",
    "<u>",
    "</u>",
    "<ins>",
    "</ins>",
    "<s>",
    "</s>",
    "<strike>",
    "</strike>",
    "<del>",
    "</del>",
    "<code>",
    "</code>",
    "<pre>",
    "</pre>",
    "<a href=",
    "</a>",
]

# 定义正则表达式，匹配所有HTML标签
html_tag_pattern = re.compile(r"<.*?>")


def clean_unsupported_tags(message, supported_tags):
    # 使用正则表达式找到所有HTML标签
    tags = html_tag_pattern.findall(message)

    for tag in tags:
        tag_simple = re.sub(r"\s.*", "", tag)  # 去除标签中的属性部分，仅保留标签名
        if tag_simple not in supported_tags:
            message = message.replace(tag, "")
    return message
