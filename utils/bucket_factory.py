from typing import List
import logging
from pyrate_limiter import AbstractBucket, BucketFactory, Rate, RateItem, AbstractClock, RedisBucket
from redis import Redis

class RedisClock(AbstractClock):
    def __init__(self, redis_client: Redis) -> None:
        self.redis_client = redis_client

    def now(self):
        unix_time, microseconds = self.redis_client.time() # type: ignore
        return unix_time + microseconds / 1_000_000

class UserBasedBucketFactory(BucketFactory):
    def __init__(self, base_name: str, redis_client: Redis, rates: List[Rate]) -> None:
        super().__init__()
        self.base_name = base_name
        self.redis_client = redis_client
        self.buckets = {}
        self.clock = RedisClock(self.redis_client)
        self.rates = rates

    def wrap_item(self, name: str, weight: int = 1) -> RateItem:
        now = self.clock.now()
        name = f"{self.base_name}_{name}"
        logging.debug(f"wrap_item, name={name}, ts={now}")
        return RateItem(name, now, weight=weight)

    def get(self, item: RateItem) -> AbstractBucket:
        """For simplicity's sake, all items route to the same, single bucket"""
        name = item.name
        logging.debug(f"get, name={name}, buckets={self.buckets}")
        if name not in self.buckets:
            new_bucket = RedisBucket.init(
                rates=self.rates, redis=self.redis_client, bucket_key=name)
            self.buckets.update({name: new_bucket})
        return self.buckets[name]
