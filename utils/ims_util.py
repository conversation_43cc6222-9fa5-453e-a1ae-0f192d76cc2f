# -*- coding: utf-8 -*-
from datetime import datetime
import hashlib
import hmac
from io import BytesIO
import json
import logging
import mimetypes
import time
import uuid

from pydantic import BaseModel
from pytz import utc
from http.client import HTTPSConnection

import requests

from common.common_constant import ImageCheckLabel, ImageCheckSuggestions, S3Bucket
from utils import cos_util

log = logging.getLogger(__name__)


img_video_suffix = ("jpg", "jpeg", "png", "gif", "bmp", "webp", "tiff","webm",  "mp4","avi", "mov", "flv", "wmv", "asf", "asx", "rm", "rmvb")

def sign(key, msg):
    return hmac.new(key, msg.encode("utf-8"), hashlib.sha256).digest()


secret_id = "IKIDcJ7ubJow78xjLm7YnvzzvpfQ361mbQBu"
secret_key = "riXEbqumRvCF6lDSVlpzzmPpU2NWTGsI"

service = "ims"
host = "ims.tencentcloudapi.com"
region = "ap-singapore"
version = "2020-12-29"
action = "ImageModeration"

endpoint = "https://ims.tencentcloudapi.com"
algorithm = "TC3-HMAC-SHA256"



class ModerationCheckResult(BaseModel):
    suggestions: ImageCheckSuggestions
    check_label: ImageCheckLabel
    request_id: str = ""
    description: str = ""
    new_image_url: str = ""

    raw_response: dict = {}


def upload_img_to_cos(image_url: str, chat_id: int = 0) -> str:
    # 下载url图片为bytes
    response = requests.get(image_url)
    if response.status_code != 200:
        log.error(f"download image failed,url:{image_url}: {response.status_code}")
        return ""
    date_index = time.strftime("%Y%m%d", time.localtime())
    random_str = f"{chat_id}_{uuid.uuid4().hex}"
    image_name = date_index + "/" + random_str
    images = BytesIO(response.content)
    mime_type = response.headers.get("Content-Type")
    if mime_type:
        if image_url.endswith(img_video_suffix): 
            file_extension = "." + image_url.split(".")[-1]
        else:
            file_extension = mimetypes.guess_extension(mime_type)
        if file_extension:
            image_name += file_extension
    mime_type = mime_type if mime_type else ""
    image_url = cos_util.upload_to_s3_original_url(
        images.getvalue(), image_name, mime_type, S3Bucket.GROUP_IMAGE_BUCKET
    )
    return image_url


def image_moderation(url: str, chat_id: int = 0) -> ModerationCheckResult:
    """
    Perform image moderation by uploading the image to a cloud storage and sending a request to a moderation service.
    Args:
        url (str): The URL of the image to be moderated.
        chat_id (int, optional): The chat ID associated with the image. Defaults to 0.
    Returns:
        ModerationCheckResult: The result of the moderation check, including suggestions, description, new image URL, and raw response if available.
    ref: https://cloud.tencent.com/document/product/1125/53242
    raw_response example:
     "Response": {
        "RequestId": "a61237dd-c2a0-43e7-a3da-d27022d39ba7",
        "DataId": "a61237dd-c2a0-43e7-a3da-d27022d39ba7",
        "BizType": "182600012300002017",
        "Suggestion": "Block",
        "FileMD5": "",
        "Label": "Porn",
        "SubLabel": "SexyBehavior",
        "Score": 90,
        "LabelResults": [
            {
                "Scene": "Porn",
                "Suggestion": "Block",
                "Label": "Porn",
                "SubLabel": "SexyBehavior",
                "Score": 90,
                "Details": []
            }
        ],
        "ObjectResults": [
            {
                "Scene": "QrCode",
                "Suggestion": "Block",
                "Label": "Ad",
                "SubLabel": "",
                "Score": 100,
                "Names": [
                    "QRCODE"
                ],
                "Details": [
                    {
                        "Id": 0,
                        "Name": "QRCODE",
                        "Value": "https://test.com/test",
                        "SubLabel": "QRCODE",
                        "Score": 100,
                        "Location": {
                            "X": 155.01746,
                            "Y": 396.01746,
                            "Width": 769.9824,
                            "Height": 769.98254,
                            "Rotate": 0
                        }
                    }
                ]
            }
        ],
        "OcrResults": [],
        "LibResults": [],
        "RecognitionResults": [],
        "Extra": ""
    }
    """
    
    new_image_url = upload_img_to_cos(url, chat_id)
    if not new_image_url:
        return ModerationCheckResult(
            suggestions=ImageCheckSuggestions.REVIEW,
            check_label=ImageCheckLabel.UNKNOWN,
            description="upload image failed",
            new_image_url="",
        )

    payload = json.dumps({"FileUrl": new_image_url, "BizType": "10000"})
    # ************* 步骤 1：拼接规范请求串 *************
    http_request_method = "POST"
    canonical_uri = "/"
    canonical_querystring = ""
    ct = "application/json; charset=utf-8"
    ct = (ct, host, action.lower())
    canonical_headers = "content-type:%s\nhost:%s\nx-tc-action:%s\n" % ct
    signed_headers = "content-type;host;x-tc-action"
    hashed_request_payload = hashlib.sha256(payload.encode("utf-8")).hexdigest()
    timestamp = int(time.time())
    date = datetime.fromtimestamp(timestamp, utc).strftime("%Y-%m-%d")
    canonical_request = "\n".join(
        [
            http_request_method,
            canonical_uri,
            canonical_querystring,
            canonical_headers,
            signed_headers,
            hashed_request_payload,
        ]
    )

    # canonical_request = (http_request_method + "\n" +
    #                     canonical_uri + "\n" +
    #                     canonical_querystring + "\n" +
    #                     canonical_headers + "\n" +
    #                     signed_headers + "\n" +
    #                     hashed_request_payload)

    # ************* 步骤 2：拼接待签名字符串 *************
    credential_scope = date + "/" + service + "/" + "tc3_request"
    hashed_canonical_request = hashlib.sha256(
        canonical_request.encode("utf-8")
    ).hexdigest()
    string_to_sign = "\n".join(
        [algorithm, str(timestamp), credential_scope, hashed_canonical_request]
    )

    # string_to_sign = (algorithm + "\n" +
    #                 str(timestamp) + "\n" +
    #                 credential_scope + "\n" +
    #                 hashed_canonical_request)

    # ************* 步骤 3：计算签名 *************
    secret_date = sign(("TC3" + secret_key).encode("utf-8"), date)
    secret_service = sign(secret_date, service)
    secret_signing = sign(secret_service, "tc3_request")
    signature = hmac.new(
        secret_signing, string_to_sign.encode("utf-8"), hashlib.sha256
    ).hexdigest()

    # ************* 步骤 4：拼接 Authorization *************
    credential = "Credential=" + secret_id + "/" + credential_scope
    signed_headers = "SignedHeaders=" + signed_headers
    signature = "Signature=" + signature
    author_content = ", ".join([credential, signed_headers, signature])
    authorization = algorithm + " " + author_content
    # authorization = (algorithm + " " +
    #                 "Credential=" + secret_id + "/" + credential_scope + ", " +
    #                 "SignedHeaders=" + signed_headers + ", " +
    #                 "Signature=" + signature)

    # ************* 步骤 5：构造并发起请求 *************
    headers = {
        "Authorization": authorization,
        "Content-Type": "application/json; charset=utf-8",
        "Host": host,
        "X-TC-Action": action,
        "X-TC-Timestamp": timestamp,
        "X-TC-Version": version,
    }
    if region:
        headers["X-TC-Region"] = region

    try:
        req = HTTPSConnection(host)
        req.request("POST", "/", headers=headers, body=payload.encode("utf-8"))
        resp = req.getresponse()
        ret = json.loads(resp.read())
        log.info(f"image_moderation url:{new_image_url}.response: {json.dumps(ret)}")
        if "Response" in ret and "Suggestion" in ret["Response"]:
            suggestion = ret["Response"]["Suggestion"]
            suggestion = ImageCheckSuggestions.from_str(suggestion)
            log.info(f"image_moderation suggestion: {suggestion}")
            return ModerationCheckResult(
                suggestions=suggestion,
                check_label=ImageCheckLabel.from_str(ret["Response"]["Label"]),
                description="success",
                new_image_url=new_image_url,
                request_id=ret["Response"]["RequestId"],
                raw_response=ret["Response"]
            )
    except Exception as err:
        log.error(f"image_moderation error: {err}")
    return ModerationCheckResult(
        suggestions=ImageCheckSuggestions.PASS,
        check_label=ImageCheckLabel.UNKNOWN,
        description="unknown error",
        new_image_url=new_image_url,
        raw_response=ret
    )
