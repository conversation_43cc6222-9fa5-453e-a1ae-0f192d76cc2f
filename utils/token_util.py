import logging
import tiktoken


def num_tokens_from_string(content: str) -> int:
    if content is None or content == "":
        return 0
    encoding = tiktoken.get_encoding("cl100k_base")

    num_tokens = len(encoding.encode(content))
    return num_tokens


def num_token_from_dict(input: dict) -> dict:
    ret = {}
    for key in input.keys():
        val = input[key]
        if isinstance(val, str):
            ret[key] = num_tokens_from_string(val)
    return ret


def num_token_from_list(input: list) -> int:
    ret = 0
    for val in input:
        if isinstance(val, str):
            ret += num_tokens_from_string(val)
    return ret
