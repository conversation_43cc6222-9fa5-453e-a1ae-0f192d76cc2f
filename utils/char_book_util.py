import random
from common.models.chat_request import ChatH<PERSON>oryItem
from common.role_card import (
    CharacterBookEdit,
    EntryExtensions,
    CharacterBook,
    BookEntry,
)
from langchain_core.messages.base import BaseMessage

from utils import convert_util, token_util


def sum_token(book: CharacterBookEdit, book_all: bool = False) -> int:
    if not book or not book.entries or not book.enabled:
        return 0
    sum_token = 0
    for entry in book.entries:
        if not entry.enabled:
            continue
        if not book_all and not entry.constant:
            continue
        sum_token += token_util.num_tokens_from_string(entry.content)
    return sum_token


# Filter the character book by probability
# def filter_by_probability(book: CharacterBook | None) -> CharacterBook | None:
#     if not book:
#         return book

#     def check_probability(extensions: EntryExtensions):
#         if not extensions:
#             return True
#         if extensions.useProbability and extensions.probability < 100:
#             return random.randint(0, 99) <= extensions.probability
#         return True

#     book.entries = [
#         entry for entry in book.entries if check_probability(entry.extensions)
#     ]
#     return book


# def filter_by_enabled(book: <PERSON>B<PERSON> | None) -> CharacterBook | None:
#     if book is None or book.enabled is None or not book.enabled:
#         return None

#     book.entries = [entry for entry in book.entries if entry.enabled]
#     return book


# def assemble_trigger_content(
#     book_entry: BookEntry, history_message: list[ChatHistoryItem]
# ) -> str:
#     if book_entry.extensions is None or book_entry.extensions.scan_depth is None:
#         scan_depth = 1
#     else:
#         scan_depth = book_entry.extensions.scan_depth
#     if scan_depth * 2 >= len(history_message):
#         return "\n".join([msg.content for msg in history_message])
#     return "\n".join([msg.content for msg in history_message[-scan_depth * 2 :]])


# def filter_by_user_input(
#     book: CharacterBook | None, history_messages: list[BaseMessage]
# ) -> CharacterBook | None:
#     if not book or not book.entries:
#         return book

#     def check_keys(entry: BookEntry):
#         # 常量，固定插入，非常量，关键词触发
#         if entry.constant:
#             return True
#         # 关键词触发
#         if not entry.keys or len(entry.keys) == 0 or entry.constant is None:
#             return False
#         ## 补充逻辑相关
#         user_input = assemble_trigger_content(entry, history_messages)
#         # 单词完整匹配
#         if len(entry.secondary_keys) == 0:
#             return any(key in user_input for key in entry.keys)

#         if not entry.extensions or entry.extensions.selectiveLogic is None:
#             return False
#         # AND ANY 仅当主键和任何一个可选过滤键处于扫描上下文中时才激活该条目。
#         if entry.extensions.selectiveLogic == 0:
#             key1_exist = any(key in user_input for key in entry.keys)
#             key2_exist = any(key in user_input for key in entry.secondary_keys)
#             return key1_exist and key2_exist
#         # NOT ALL 如果所有可选过滤器都在扫描上下文中，则尽管有主键触发器，仍会阻止激活条目
#         elif entry.extensions.selectiveLogic == 1:
#             key1_exist = any(key in user_input for key in entry.keys)
#             key2_exist = all(key in user_input for key in entry.secondary_keys)
#             return key1_exist and not key2_exist
#         # NOT ANY 仅当主键和可选过滤键均不在扫描上下文中时才激活条目。
#         elif entry.extensions.selectiveLogic == 2:
#             key1_exist = any(key in user_input for key in entry.keys)
#             key2_exist = any(key in user_input for key in entry.secondary_keys)
#             return not key1_exist and not key2_exist
#         # AND ALL 仅当主键和所有可选过滤键都存在时才激活条目。
#         elif entry.extensions.selectiveLogic == 3:
#             key1_exist = all(key in user_input for key in entry.keys)
#             key2_exist = all(key in user_input for key in entry.secondary_keys)
#             return key1_exist and key2_exist
#         return False

#     book.entries = [entry for entry in book.entries if check_keys(entry)]
#     return book


def get_content_by_world_info(book: CharacterBook | None, before: bool) -> str:
    if not book or not book.entries:
        return ""

    format_str = "[Details of the fictional world the RP is set in:\n{0}]"
    entry_list: list[BookEntry] = []
    for entry in book.entries:
        if not entry.extensions or not entry.extensions.position:
            continue
        if before and entry.extensions.position == 0:
            entry_list.append(entry)
        elif not before and entry.extensions.position == 1:
            entry_list.append(entry)
    if not entry_list:
        return ""
    entry_list.sort(key=lambda x: convert_util.to_int(x.insertion_order), reverse=True)
    list_str_entry = [x.content for x in entry_list]
    return format_str.format("\n".join(list_str_entry))


def get_content_by_world_info_new(book: CharacterBook | None) -> str:
    if not book or not book.entries:
        return ""
    format_str = "[Details of the fictional world the RP is set in:\n{0}]"
    entry_list: list[BookEntry] = []
    for entry in book.entries:
        if not entry.constant or entry.enabled:
            continue
        entry_list.append(entry)
    if not entry_list:
        return ""
    entry_list.sort(key=lambda x: convert_util.to_int(x.insertion_order))
    list_str_entry = [x.content for x in entry_list]
    return format_str.format("\n".join(list_str_entry))


def get_content_by_insert_depth(
    book: CharacterBook | None, depth: int, end: bool
) -> list[dict]:
    if not book or not book.entries:
        return []
    entry_list = []
    for entry in book.entries:
        if not entry.extensions or not entry.extensions.depth:
            continue
        if entry.extensions.depth == depth:
            entry_list.append(entry)
        if end and entry.extensions.depth > depth:
            entry_list.append(entry)
    if not entry_list:
        return []
    entry_list.sort(key=lambda x: x.insertion_order, reverse=True)
    ret_msg = []
    for entry in entry_list:
        ret_msg.append(
            {
                "role": EntryExtensions.get_role_str(entry.extensions.role),
                "content": entry.content,
            }
        )
    return ret_msg


def list_contents(book: CharacterBook | None):
    if not book or not book.entries:
        return []
    entry_list: list[BookEntry] = []
    for entry in book.entries:
        if not entry.extensions or not entry.extensions.depth:
            continue
        if not entry.insertion_order:
            entry.insertion_order = 0
        entry_list.append(entry)

    entry_list.sort(key=lambda x: x.load_order())
    ret_msg = []
    for entry in entry_list:
        if not entry.extensions:
            continue
        ret_msg.append(
            {
                "role": entry.extensions.load_role_str(),
                "content": entry.content,
            }
        )


def list_constant_book_entries(book: CharacterBook):
    if not book.entries:
        return []
    entry_list: list[BookEntry] = []
    entry_list = [x for x in book.entries if x.constant and x.enabled]
    for entry in entry_list:
        if not entry.insertion_order:
            entry.insertion_order = 0
    entry_list.sort(key=lambda x: x.load_order())
    if entry_list and len(entry_list) > 3:
        entry_list = entry_list[:3]
    return entry_list


def list_keywords_book_entries(
    book: CharacterBook, chat_list: list[ChatHistoryItem]
) -> list[BookEntry]:
    if not book.entries:
        return []
    entry_list: list[BookEntry] = []
    entry_list = [x for x in book.entries if not x.constant and x.enabled]
    entry_list = [x for x in entry_list if x.keys]
    for entry in book.entries:
        if not entry.insertion_order:
            entry.insertion_order = 0
    entry_list.sort(key=lambda x: x.load_order())
    if not entry_list:
        return []
    ret_list = []
    scan_depth = 4
    for entry in entry_list:
        input_str = ""
        if scan_depth >= len(chat_list):
            input_str = "\n".join([msg.content for msg in chat_list])
        else:
            input_str = "\n".join([msg.content for msg in chat_list[-scan_depth:]])
        if any(key in input_str for key in entry.keys):
            ret_list.append(entry)
    return ret_list
