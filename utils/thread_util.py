import asyncio
import random

from common.models.chat_model import AddWaterMid


async def skip_now(water_mid: AddWaterMid):
    if not water_mid.add_water:
        return water_mid
    if water_mid.config_skip_count and water_mid.skip_count <= 0:
        water_mid.skip_count = water_mid.config_skip_count
        water_mid.skip_output = False
        await asyncio.sleep(
            random.uniform(water_mid.config_sleep_min, water_mid.config_sleep_max)
        )
        return water_mid

    if water_mid.skip_count > 0:
        water_mid.skip_count -= 1
        water_mid.skip_output = True
    return water_mid


# 构造一个方法，可以并行执行一个函数列表，返回结果列表，并传递一个并发参数
async def parallel_executes(funcs, concurrency=5) -> list:
    semaphore = asyncio.Semaphore(concurrency)

    async def run_with_semaphore(index, func):
        async with semaphore:
            return index, await func

    tasks = [run_with_semaphore(index, func) for index, func in enumerate(funcs)]
    results = await asyncio.gather(*tasks)
    return [result for index, result in results]


async def parallel_execute_map(func_map: dict, concurrency=5) -> dict:
    semaphore = asyncio.Semaphore(concurrency)

    async def run_with_semaphore(key, func):
        async with semaphore:
            return key, await func

    tasks = [run_with_semaphore(key, func) for key, func in func_map.items()]
    results = await asyncio.gather(*tasks)
    return {key: result for key, result in results}
