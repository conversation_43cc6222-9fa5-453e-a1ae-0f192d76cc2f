def remove_nulls(obj):
    """
    Recursively remove all keys with None values from a dictionary
    """
    if isinstance(obj, dict):
        return {k: remove_nulls(v) for k, v in obj.items() if v is not None}
    elif isinstance(obj, list):
        return [remove_nulls(elem) for elem in obj]
    else:
        return obj


def convert_to_dict(obj: dict | list):
    if isinstance(obj, dict):
        return obj
    return {}


def convert_to_list(obj: dict | list):
    if isinstance(obj, list):
        return obj
    return []


def remove_null_dict(obj):
    """
    Recursively remove all keys with None values from a dictionary
    """
    if isinstance(obj, dict):
        return {k: v for k, v in obj.items() if v is not None}
    return obj


from tortoise.contrib.pydantic import pydantic_model_creator


async def model_to_json(data_object, clazz):
    pydantic = pydantic_model_creator(clazz)
    ret = await pydantic.from_tortoise_orm(data_object)
    return ret.model_dump()
