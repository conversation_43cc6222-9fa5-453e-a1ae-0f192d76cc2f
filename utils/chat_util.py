import uuid
from common.chat_bot_model import <PERSON>tPara<PERSON>


def request_extra_body(chat_params: ChatParams):
    top_k = chat_params.presets["top_k"]
    return {
        "top_k": top_k,
        "metadata": {
            "trace_user_id": chat_params.user_id,
            "generation_name": "llm-call",
            "session_id": chat_params.history_data.conversation_id,
            "trace_metadata": {"role_id": chat_params.role_id},
        },
    }


def translate_request_extra_body():
    return {
        "metadata": {
            "trace_user_id": "translate-task",
            "generation_name": "llm-translate",
            "session_id": uuid.uuid4().hex,
        },
    }
