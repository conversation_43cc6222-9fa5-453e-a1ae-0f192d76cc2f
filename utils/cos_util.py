from enum import Enum
import logging
import time
from typing import Optional
import uuid
import boto3
import random

from fastapi import UploadFile
import requests

from common.common_constant import CosPrefix, Env, S3Bucket, S3BucketUrlPrefix
from utils import env_util

log = logging.getLogger(__name__)
# 新老映射关系：

# sgp-ai-data-1323765209 (官方角色卡，私照)
URL_DOMAIN = "ai-data.424224.xyz"
URL_PREFIX = "https://ai-data.424224.xyz/"
REGION = "ap-southeast-1"
BUCKET = "ai-data.424224.xyz"
AWS_ACCESS_KEY_ID = "********************"
AWS_SECRET_ACCESS_KEY = "dmeq9TbQILSLzQeWFDjXdJQWM/UsXcYwtfaJenfp"

# tr-avatar-1323765209 (头像，个人用户上传角色卡)

s3_client = boto3.client(
    "s3",
    aws_access_key_id=AWS_ACCESS_KEY_ID,
    aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
    region_name=REGION,
)


def random_group_image_name(bot_id: int = 0) -> str:
    date_index = time.strftime("%Y%m%d", time.localtime())
    random_str = f"{uuid.uuid4().hex}"
    return f"{date_index}/{bot_id}/{random_str}"


def random_name(prefix: str = "", suffix: str = "") -> str:
    random_str = f"{uuid.uuid4().hex}"
    if prefix:
        random_str = f"{prefix}/{random_str}"
    if suffix:
        random_str = f"{random_str}.{suffix}"
    return random_str


def transform_url_to_s3_url(url: str,prefix:str) -> str:
    if not url:
        return ""
    # 下载图片，读取类型，上传到s3
    if not url.startswith("http://") and not url.startswith("https://"):
        return ""
    try:
        response = requests.get(url)
        if response.status_code != 200:
            log.error(f"Failed to download image from {url}, status code: {response.status_code}")
            return ""
        file_data = response.content
        suffix = ""
        if url.find(".") != -1:
            suffix = url.split(".")[-1]
        if suffix not in ["jpg", "jpeg", "png", "gif"]:
            suffix = ""
        mimetype = response.headers.get("Content-Type", "")
        object_name = random_name(prefix, suffix)
        return upload_to_s3(file_data, object_name, mimetype)
    except Exception as e:
        log.error(f"Error downloading image from {url}: {e}")
    return ""


def upload_to_s3_original_url(
    file_data: bytes,
    object_name=None,
    mimetype: str = "",
    bucket: S3Bucket = S3Bucket.DEF_BUCKET,
) -> str:
    # 如果未指定对象名称，则使用文件名
    if object_name is None:
        object_name = random_name()
    if object_name.startswith("/"):
        object_name = object_name[1:]
    if env_util.get_current_env() != Env.PROD.value:
        object_name = "dev/" + object_name
    try:
        if mimetype:
            s3_client.put_object(
                Bucket=bucket.value,
                Key=object_name,
                Body=file_data,
                ContentType=mimetype,
            )
        else:
            s3_client.put_object(Bucket=bucket.value, Key=object_name, Body=file_data)

        return S3BucketUrlPrefix.get_url_prefix(bucket) + object_name
    except Exception as e:
        log.error(f"upload to s3 failed: {e}")
        return ""


def upload_to_s3(
    file_data, object_name=None, mimetype: str = "", bucket: str = BUCKET
) -> str:
    # 如果未指定对象名称，则使用文件名
    if object_name is None:
        object_name = random_name()
    if object_name.startswith("/"):
        object_name = object_name[1:]
    if env_util.get_current_env() != Env.PROD.value:
        object_name = "dev/" + object_name
    try:
        if mimetype:
            s3_client.put_object(
                Bucket=bucket, Key=object_name, Body=file_data, ContentType=mimetype
            )
        else:
            s3_client.put_object(Bucket=bucket, Key=object_name, Body=file_data)
        return URL_PREFIX + object_name
    except Exception as e:
        log.error(f"upload to s3 failed: {e}")
        return ""

def upload_object(
    upload_file: Optional[UploadFile], cosPrefix: CosPrefix
) -> str:
    if not upload_file:
        return ""
    file_data = upload_file.file.read()
    suffix = ""
    if upload_file.filename and upload_file.filename.find(".") != -1:
        suffix = upload_file.filename.split(".")[-1]
    content_type = str(upload_file.content_type)
    if not suffix and "image" in content_type:
        suffix = content_type.split("/")[-1]
    file_name = random_name(cosPrefix.value, suffix)

    return upload_to_s3(file_data, file_name, content_type)


def upload_image(upload_file: Optional[UploadFile], cosPrefix: CosPrefix) -> str:
    if not upload_file:
        return ""
    file_data = upload_file.file.read()
    suffix = ""
    if upload_file.filename and upload_file.filename.find(".") != -1:
        suffix = upload_file.filename.split(".")[-1]
    content_type = str(upload_file.content_type)
    if not suffix and "image" in content_type:
        suffix = content_type.split("/")[-1]
    file_name = random_name(cosPrefix.value, suffix)

    return upload_to_s3(file_data, file_name, content_type)


def copy_object(source_file: str, target_file: str):
    try:
        s3_client.copy_object(Bucket=BUCKET, CopySource=source_file, Key=target_file)
        return URL_PREFIX + target_file
    except Exception as e:
        log.error(f"copy object failed: {e}")
        return ""


# 读取本地文件，~/Downloads/1726078166528.mp4

# def upload_local_file(file_path: str, cosPrefix: CosPrefix) -> str:
#     try:
#         with open(file_path, "rb") as f:
#             file_data = f.read()
#             suffix = ""
#             if file_path and file_path.find(".") != -1:
#                 suffix = file_path.split(".")[-1]
#             content_type = "video/mp4"
#             file_name = random_name(cosPrefix.value, suffix)
#             url =  upload_to_s3(file_data, file_name, content_type)
#             log.info(f"upload local file success: {url}")
#     except Exception as e:
#         log.error(f"upload local file failed: {e}")
#         return ""
