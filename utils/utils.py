import re
from urllib.parse import Parse<PERSON><PERSON><PERSON>, urlparse, parse_qs, urle<PERSON><PERSON>, urlunparse

def remove_duplicates_chats(items: list[str]) -> list[str]:
    seen = set()
    new_items = []
    for item in items:
        value = item.content
        if value not in seen:
            seen.add(value)
            new_items.append(item)
    return new_items

def window_seq(seq: list, window_size: int) -> list:
    result = []
    for i in range(0, len(seq), window_size):
        result.append(seq[i:i + window_size])
    return result

def split_message_text(original_text: str) -> list[str]:
    pattern = re.compile(r'[^()（）]+|\(.*?\)|（.*?）')
    r = []
    for line in original_text.splitlines():
        matches = re.findall(pattern, line)
        r.extend(matches)
    return r

def remove_middle(lst: list, num: int=5) -> list:
    middle = len(lst) // 2
    return lst[:middle - num//2] + lst[middle + num//2 + num % 2:]

def change_url_path(url: str, new_path: str) -> str:
    parsed_url = urlparse(url)
    query_params = parse_qs(parsed_url.query)

    new_url = urlunparse((parsed_url.scheme, parsed_url.netloc, new_path, '', urlencode(query_params, doseq=True), ''))
    return new_url

def parse_mysql_connection_string(conn_string):
    # Turns the connection string into a parsed URL result
    parse_result: ParseResult = urlparse(conn_string)

    # Extract components from the parsed connection string
    username = parse_result.username
    password = parse_result.password
    host = parse_result.hostname
    port = parse_result.port
    database = parse_result.path.lstrip('/')  # Strip '/' from the left of the path to get database name

    # Construct the dictionary for connection parameters
    connection_params = {
        'user': username,
        'password': password,
        'host': host,
        'port': port,
        'database': database
    }
    return connection_params

def get_last_path_segment(url):
    path = urlparse(url).path
    return path.rstrip('/').split('/')[-1]
