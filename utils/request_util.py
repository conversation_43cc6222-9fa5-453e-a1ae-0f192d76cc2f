import logging
import aiohttp
import asyncio
from typing import Optional, Dict, Any

from common.common_constant import Env
from utils import env_util


log = logging.getLogger(__name__)


async def post_json(
    url: str,
    post_data: Dict[str, Any],
    headers: Dict[str, Any] = {},
    timeout: int = 10,
    max_retries: int = 1,
) -> Optional[Dict]:
    for attempt in range(max_retries):
        try:
            timeout_client = aiohttp.ClientTimeout(total=timeout)
            async with aiohttp.ClientSession(timeout=timeout_client) as session:
                async with session.post(
                    url, json=post_data, headers=headers
                ) as response:
                    if response.status == 200:
                        res_json =  await response.json()
                        log.info("Successfully posted to %s, response: %s", url, res_json)
                        return res_json
                    elif response.status >= 500 and attempt < max_retries - 1:
                        log.warning(
                            "Server error (%s) for %s, reason: %s, retrying... (attempt %d/%d)",
                            response.status,
                            url,
                            await response.text(),
                            attempt + 1,
                            max_retries,
                        )
                        await asyncio.sleep(0.1)
                        continue
                    else:
                        log.warning(
                            "Failed to post to %s, status: %s, reason: %s",
                            url,
                            response.status,
                            await response.text(),
                        )
                        response.raise_for_status()
        except aiohttp.ClientError as e:
            log.error("Failed to post to %s -> %s", url, e)
            if attempt == max_retries - 1:
                return None
            await asyncio.sleep(0.1)
    return None


async def get_bytes(url: str, timeout: int = 10, max_retries: int = 1):
    log.info("Requesting URL: %s with timeout %d and max retries %d", url, timeout, max_retries)
    for attempt in range(max_retries):
        try:
            timeout_client = aiohttp.ClientTimeout(total=timeout)
            # 增加代理
            proxy = None
            if env_util.get_current_env() == Env.LOCAL:
                proxy = "http://127.0.0.1:7890"
            async with aiohttp.ClientSession(timeout=timeout_client) as session:
                async with session.get(url,proxy=proxy) as response:
                    if response.status == 200:
                        return await response.read()
                    elif response.status >= 500 and attempt < max_retries - 1:
                        await asyncio.sleep(0.1)  # Exponential backoff
                        continue
                    else:
                        response.raise_for_status()
        except aiohttp.ClientError as e:
            log.error("Failed to get response from %s -> %s", url, e)
            if attempt == max_retries - 1:
                return None
            await asyncio.sleep(0.1)
    return None


async def get_text(url: str, timeout: int = 10, max_retries: int = 1):
    bytes = await get_bytes(url, timeout, max_retries)
    if bytes is None:
        return None
    return bytes.decode("utf-8")


async def get_json(url: str, timeout: int = 10, max_retries: int = 1):
    for attempt in range(max_retries):
        try:
            timeout_client = aiohttp.ClientTimeout(total=timeout)
            async with aiohttp.ClientSession(timeout=timeout_client) as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        return await response.json()
                    elif response.status >= 500 and attempt < max_retries - 1:
                        await asyncio.sleep(0.1)  # Exponential backoff
                        continue
                    else:
                        response.raise_for_status()
        except aiohttp.ClientError as e:
            if attempt == max_retries - 1:
                return None
            await asyncio.sleep(0.1)
    return None
