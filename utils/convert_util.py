import json


def is_str(obj):
    return isinstance(obj, str)


def is_dict(obj):
    return isinstance(obj, dict)


def to_dict(obj) -> dict:
    if is_dict(obj):
        return obj
    if is_str(obj):
        return json.loads(obj)
    return {}


def to_int(obj, default_val: int = 0) -> int:
    if obj is None:
        return default_val
    if isinstance(obj, int):
        return obj
    if is_str(obj):
        return int(obj)
    return obj

def to_str(obj) -> str:
    if is_str(obj):
        return obj
    return str(obj)