


from tortoise.contrib.pydantic import pydantic_model_creator


PYDANTIC_MODEL_CACHE = {}

async def model_to_dict(model, model_data) -> dict:
    if model in PYDANTIC_MODEL_CACHE:
        tr_pydantic = PYDANTIC_MODEL_CACHE[model]
    else:
        tr_pydantic = pydantic_model_creator(model)
        PYDANTIC_MODEL_CACHE[model] = tr_pydantic
    resource_pydantic = await tr_pydantic.from_tortoise_orm(model_data)
    resource_dict = resource_pydantic.dict()
    return resource_dict