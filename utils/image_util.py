import base64
from io import BytesIO
import json
import logging
import os
import time
import random
from typing import Optional
from PIL import Image, PngImagePlugin
from fastapi import UploadFile
from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client
import requests

from common.common_constant import Env
from common.role_card import (
    CharacterBook,
    EntryExtensions,
    BookEntry,
    TavernCard,
)
from utils import env_util, request_util

log = logging.getLogger(__name__)


# SECRET_ID = "IKIDVs31sb8SaXHuJWWRk3Bg2NbgZGl5OWVJ"
# SECRET_KEY = "g9OXVHrRs8AzbS6Wo36vYyBB1f5KSNfm"
# BUCKET = "sgp-ai-data-1323765209"


# class ImageUtil:
#     def __init__(
#         self,
#         secret_id: str = SECRET_ID,
#         secret_key: str = SECRET_KEY,
#         bucket: str = BUCKET,
#     ):
#         region = "ap-singapore"
#         token = None
#         scheme = "https"
#         config = CosConfig(
#             Region=region,
#             SecretId=secret_id,
#             SecretKey=secret_key,
#             Token=token,
#             Scheme=scheme,
#         )
#         self.client = CosS3Client(config)
#         self.bucket = bucket

#     def upload_image(
#         self,
#         img: bytes,
#         img_name: str = None,
#         bucket: str | None = None,
#         mimetype: str | None = None,
#     ) -> str:
#         if img_name is None:
#             img_name = f"image_{int(time.time())}_{random.randint(1, 1000)}.jpg"
#         bucket = bucket if bucket is not None else self.bucket
#         if mimetype is not None:
#             re = self.client.put_object(
#                 Bucket=bucket, Body=img, Key=img_name, ContentType=mimetype
#             )
#         else:
#             re = self.client.put_object(Bucket=bucket, Body=img, Key=img_name)
#         # print(f'xx: ,{img_name},{re}')
#         # 获取图片的url
#         image_url = self.client.get_presigned_url(
#             Method="GET",
#             Bucket=bucket,
#             Key=img_name,
#             Expired=31536000,  # 1年后过期，过期时间请根据自身场景定义
#         )
#         return image_url


# image_util = ImageUtil()


# async def upload_img(upload_file: Optional[UploadFile], def_url: str = "") -> str:
#     if upload_file:
#         return image_util.upload_image(await upload_file.read())
#     return def_url


# def upload_image(
#     img: bytes,
#     img_name: str = None,
#     bucket: str | None = None,
#     mimetype: str | None = None,
# ) -> str:
#     return image_util.upload_image(img, img_name, bucket, mimetype)


async def build_card_image(tavern_card: TavernCard) -> BytesIO:
    avatar = tavern_card.avatar
    if avatar is None or len(avatar) == 0:
        return None
    # 下载图片
    image_bytes = await request_util.get_bytes(avatar)
    if not image_bytes:
        return None
    image = Image.open(BytesIO(image_bytes))
    # 读取图片信息
    # tavern_card 通过utf-8格式，转换为base64编码

    ecoded_data = base64.b64encode(json.dumps(tavern_card.model_dump()).encode("utf-8"))
    card_data = ecoded_data.decode("utf-8")
    meta = PngImagePlugin.PngInfo()
    meta.add_text("chara", card_data)
    img_byte_arr = BytesIO()
    image.save(img_byte_arr, format="PNG", pnginfo=meta)  # 将图像保存到BytesIO对象中
    return img_byte_arr


def remove_image_info(image: Image):
    image.text["chara"] = ""
    image.info = {}
    return image


def save_to_local(image: bytes, path: str, type: str, name: str = None):
    time_index = time.strftime("%Y%m%d%H%M", time.localtime())
    new_name = f"image_{time_index}_{random.randint(1, 1000)}"
    name = new_name if not name else f"{name}.{type}"
    path = f"{path}{name}" if path.endswith("/") else f"{path}/{name}"
    with open(f"{path}", "wb") as f:
        f.write(image)
    log.info(f"save image to local: {path}")
    return path


def save_cards_to_local(img_bytes: bytes, image_type: str, file_name: str):
    try:
        dir_path = "/data/cards/"
        if env_util.get_current_env() == Env.LOCAL:
            dir_path = "/tmp/cards/"
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
        if file_name.endswith(f".{image_type}"):
            file_name = file_name.replace(f".{image_type}", "")
        time_index = time.strftime("%Y%m%d%H%M", time.localtime())
        new_name = f"{file_name}_{time_index}_{random.randint(1, 1000)}"
        save_to_local(img_bytes, dir_path, image_type, new_name)
        return new_name
    except Exception as e:
        log.error(f"save cards to local error: {e}")
        return None


def load_new_image_info(image: Image):
    image.load()
    info = image.info
    chara = info.get("chara")
    # chara进行 base64 解码，注意中文字符需要先进行 encode
    base64_decoded_data = base64.b64decode(chara)
    img_data = json.loads(base64_decoded_data.decode("utf-8"))
    tavern_card = TavernCard(**img_data)

    ## 解析 character note 转换为 character book
    if tavern_card is None or tavern_card.data is None:
        return tavern_card
    if tavern_card.data.extensions is None:
        return tavern_card
    depth_prompt = tavern_card.data.extensions.get("depth_prompt")
    if depth_prompt is None:
        return tavern_card
    role = depth_prompt.get("role")
    depth = depth_prompt.get("depth")
    prompt = depth_prompt.get("prompt")

    # 如果有prompt，添加到character book中
    if prompt and depth:
        book_entry = BookEntry(
            constant=True,
            content=prompt,
            insertion_order=100,
            # comment="character note",
            enabled=True,
            extensions=EntryExtensions(
                position=4, role=EntryExtensions.get_role_int(role), depth=depth
            ),
        )
        # 没有世界书，有作者注释
        if tavern_card.data.character_book is None:
            char_book_name = f"{tavern_card.data.name}'s Character Notes"
            tavern_card.data.character_book = CharacterBook(
                name=char_book_name, entries=[], enabled=True
            )
        tavern_card.data.character_book.entries.append(book_entry)
    if tavern_card.data.exist_book() == False:
        return tavern_card

    # 设置character book的depth
    for entry in tavern_card.data.character_book.entries:
        if entry.extensions.position in [2, 3] and depth is not None:
            entry.extensions.depth = depth
        if entry.extensions.position in [2, 3]:
            entry.extensions.position = 4

    return tavern_card


def load_original_image_info(image: Image):
    image.load()
    info = image.info
    chara = info.get("chara")

    # chara进行 base64 解码，注意中文字符需要先进行 encode
    base64_decoded_data = base64.b64decode(chara)
    img_data = json.loads(base64_decoded_data.decode("utf-8"))
    return img_data


# 获取指定文件夹底下所有的文件，调用load_image_info方法，并将结果写入到json为结尾的文件
def batch_load(directory, output_directory):
    if os.path.exists(output_directory):
        os.system(f"rm -rf {output_directory}")

    os.makedirs(output_directory)
    all_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            all_files.append(os.path.join(root, file))
    for file_path in all_files:
        try:
            image = Image.open(file_path)
            info = load_original_image_info(image)
            output_file = output_directory + os.path.basename(file_path) + ".json"
            if "character_book" in json.dumps(info):
                output_file = (
                    output_directory
                    + os.path.basename(file_path)
                    + "_charbook"
                    + ".json"
                )
            with open(output_file, "w") as f:
                json.dump(info, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(e)


# batch_load('/Users/<USER>/data/AIProject/rochat/', '/Users/<USER>/data/AIProject/rochat_json/')

def random_noise_to_image(img_bytes: bytes):
    image = Image.open(BytesIO(img_bytes))

    # Add random noise to the image
    width, height = image.size
    noise = Image.effect_noise((width, height), 10)
    # Convert images to RGBA mode to ensure compatibility
    image = image.convert('RGBA')
    noise = noise.convert('RGBA')

    # Resize noise to match image dimensions
    noise = noise.resize(image.size)

    # Blend images
    image = Image.blend(image, noise, 0.1)
    # Save the modified image to a BytesIO object
    img_byte_arr = BytesIO()
    image.save(img_byte_arr, format='PNG')
    img_byte_arr.seek(0)
    return img_byte_arr
