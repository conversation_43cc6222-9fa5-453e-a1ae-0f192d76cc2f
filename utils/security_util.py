import logging
import re


log = logging.getLogger(__name__)

# import bleach
def contains_xss(input_string):
    # 正则表达式来匹配常见的XSS标签和模式
    xss_patterns = [
        re.compile(r"<\s*script\b[^>]*>(.*?)<\s*/\s*script\s*>", re.IGNORECASE),
        re.compile(r'<\s*img\b[^>]*src\s*=\s*["\']?javascript:', re.IGNORECASE),
        re.compile(r"<\s*iframe\b[^>]*>(.*?)<\s*/\s*iframe\s*>", re.IGNORECASE),
        re.compile(r"<\s*object\b[^>]*>(.*?)<\s*/\s*object\s*>", re.IGNORECASE),
        re.compile(r'on\w+\s*=\s*["\']?javascript:', re.IGNORECASE),
    ]

    # 检查每个模式
    for pattern in xss_patterns:
        if pattern.search(input_string):
            return True
    return False


# def clean_html(input_html):
#     return bleach.clean(input_html)


def remove_html_tags(text: str):
    if text is None or text == "":
        return text
    # 使用正则表达式匹配所有HTML标签
    pattern = re.compile(r"<[^>]+>")
    ret = re.sub(pattern, "", text)
    if len(text) != len(ret):
        log.info(f"Remove HTML Tag: {text} -> {ret}")
    
    return ret

# # 测试
# test_string_with_xss = '<SCRIPT>alert("XSS")</script>'
# test_string_with_img_xss = '<img src="javascript:alert(\'XSS\')">'
# test_string_without_xss = 'This is a plain text.'

# print(clean_html(test_string_with_xss))  # 应该输出: True
# print(clean_html(test_string_with_img_xss))  # 应该输出: True
# print(clean_html(test_string_without_xss))  # 应该输出: False
