from enum import Enum
from typing import Optional
from fastapi import Response
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from sse_starlette import EventSourceResponse

from common.common_constant import Env, Error<PERSON><PERSON>
from common.models.chat_model import ChatFinishData, ChatHistory
from common.models.chat_request import ChatHistoryItem
from utils import env_util
from utils.translate_util import _tl


class CommonResponse(BaseModel):
    error_code: int = 0
    error_key: str = ""
    message: str = ""
    data: Optional[dict | str | list] = None


def param_error(msg: str = "", language: str = "zh"):
    return Response(content=_tl(msg, language), status_code=400)


def json_param_error(msg: Optional[str] = "", language: str = "zh"):
    if env_util.get_current_env() == Env.PROD:
        return JSONResponse(
            content={"message": "request params error"}, status_code=400
        )
    return JSONResponse(
        content={"message": "request params error", "exception": msg}, status_code=400
    )


def success(data):
    return JSONResponse(content=jsonable_encoder(data))


# 业务侧的校验
def ok(
    data: Optional[dict | str | list] = None, error_code: int = 0, message: str = ""
):
    return JSONResponse(
        content=jsonable_encoder(
            {"data": data, "error_code": error_code, "message": message}
        )
    )


def error(error_code: int = 0, message: str = ""):
    return JSONResponse(
        content=jsonable_encoder({"error_code": error_code, "message": message})
    )


def def_error(message: str = "", language: str = "zh"):
    return JSONResponse(
        content=jsonable_encoder({"error_code": -1, "message": message})
    )


def response_param_error(key: str, language: str = "zh"):
    return Response(content=_tl(key, language), status_code=400)


def chat_error_response(
    language: str = "zh", key: str = "CHAT_SYSTEM_ERROR", need_retry: bool = False
):
    async def response_stream(language: str = "zh"):
        finish_data = ChatFinishData(need_retry=need_retry)
        yield {"event": "data", "data": ""}
        yield {
            "event": "data",
            "data": _tl(ErrorKey.safe_parse(key).message(), language),
        }
        yield {"event": "end", "data": finish_data.model_dump_json()}
        return

    return EventSourceResponse(
        content=response_stream(language),
    )


async def response_stream(
    language: str = "zh", key: str = "CHAT_SYSTEM_ERROR", need_retry: bool = False
):
    finish_data = ChatFinishData(need_retry=need_retry)

    finish_data = ChatFinishData(need_retry=need_retry)
    yield {
        "event": "data",
        "data": _tl(ErrorKey.safe_parse(key).message(), language),
    }
    yield {"event": "end", "data": finish_data.model_dump_json()}
    return


def chat_error_message(message: str = "", error_code: int = 0, extra_data: dict = {}):
    def response_stream():
        finish_data = ChatFinishData(
            need_retry=False, error_code=error_code, extra_data=extra_data
        )
        yield {
            "event": "data",
            "data": message,
        }
        yield {"event": "end", "data": finish_data.model_dump_json()}
        return

    return EventSourceResponse(
        content=response_stream(),
    )


def chat_repeat_response(language: str = "zh"):
    def response_stream(language: str = "zh"):
        finish_data = ChatFinishData(need_retry=False)
        yield {
            "event": "data",
            "data": _tl(ErrorKey.CHAT_REPEAT_REQUEST_ERROR.message(), language),
        }
        yield {"event": "end", "data": finish_data.model_dump_json()}
        return

    return EventSourceResponse(
        content=response_stream(language),
    )


def chat_success(chat_history: ChatHistoryItem, headers: dict, conv_id: str):
    def response_stream():
        finish_data = ChatFinishData(
            success=True,
            conversation_id=conv_id,
            message_id=chat_history.message_id,
            message_version=str(chat_history.version),
            need_retry=chat_history.can_continue_replay,
        )
        yield {
            "event": "data",
            "data": chat_history.content,
        }
        yield {"event": "end", "data": finish_data.model_dump_json()}
        return

    return EventSourceResponse(content=response_stream(), headers=headers)


def auth_error(message: str, origin: str):
    return JSONResponse(
        content={"msg": message},
        status_code=401,
        headers={
            "Access-Control-Allow-Origin": str(origin),
            "access-control-allow-credentials": "true",
            "Access-Control-Allow-Methods": "*",
            "Access-Control-Allow-Headers": "*",
        },
    )
