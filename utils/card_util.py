from common.role_card import RoleCardInfo
from common.role_model import SceneConfig


def list_new_scenes(role_card: RoleCardInfo) -> list[SceneConfig]:
    ret_list = []
    if "类脑社区" in role_card.first_mes:
        role_card.first_mes = ""
    if len(role_card.first_mes) > 0 or len(role_card.scenario) > 0:
        ret_list.append(
            SceneConfig(
                index=0, first_message=role_card.first_mes, scenario=role_card.scenario
            )
        )
    if len(role_card.alternate_greetings) > 0:
        for index, mes in enumerate(role_card.alternate_greetings):
            ret_list.append(
                SceneConfig(
                    index=index + 1, first_message=mes, scenario=role_card.scenario
                )
            )
    return ret_list
