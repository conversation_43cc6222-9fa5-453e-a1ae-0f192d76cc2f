from pydantic import BaseModel

from common.chat_bot_model import AnalysisExampleRet
from common.role_model import RoleDataConfig
from utils import str_util


def analysis_example(
    data_config: RoleDataConfig,
    new_example_chat_start: str,
    user_name: str,
    role_name: str,
) -> AnalysisExampleRet:
    ret = []
    if len(data_config.muilte_examples) > 0:
        index = 1
        for example in data_config.muilte_examples:
            if example.user == "" and example.char == "":
                continue
            mid = ""
            if example.user != "":
                mid = user_name + ":" + example.user
            if example.char != "":
                mid = f"{mid}\n\n<rep>\n{role_name}:{example.char}\n</rep>"
            mid = str_util.format_user(mid, user_name)
            mid = str_util.format_char(mid, role_name)
            ret.append(f"Example{index}:\n{mid}")
            index += 1
        return AnalysisExampleRet(messages=ret, need_merge=True)

    example_dialog = data_config.example_dialog
    if len(example_dialog) == 0:
        return AnalysisExampleRet(messages=[], need_merge=False)

    if "START:" in example_dialog:
        for mid in example_dialog.split("START:"):
            if mid == "" or mid == "\n":
                continue
            str_mid = mid
            if "{{user}}" not in mid and "{{char}}" not in mid:
                str_mid = "{{char}}:" + mid
            str_mid = str_util.format_user(str_mid, user_name)
            str_mid = str_util.format_char(str_mid, role_name)
            ret.append(str_mid)

    if "<START>" in example_dialog:
        for mid in example_dialog.split("<START>"):
            if not mid or mid == "\n":
                continue
            str_mid = mid
            if "{{user}}" not in mid and "{{char}}" not in mid:
                str_mid = "{{char}}:" + mid
            str_mid = new_example_chat_start + "\n" + mid
            str_mid = str_util.format_user(str_mid, user_name)
            str_mid = str_util.format_char(str_mid, role_name)
            ret.append(str_mid)
        return AnalysisExampleRet(messages=ret, need_merge=True)
    if "{{user}}:" in example_dialog:
        split_list = example_dialog.split("\n")
        str_mid = split_list[0]
        # 从1开始遍历
        for i in range(1, len(split_list)):
            mid = split_list[i]
            if "{{user}}:" in mid:
                str_mid = str_util.format_user(str_mid, user_name)
                str_mid = str_util.format_char(str_mid, role_name)
                ret.append(str_mid)
                str_mid = mid
                continue
            str_mid += "\n" + mid

        if str_mid:
            str_mid = str_util.format_user(str_mid, user_name)
            str_mid = str_util.format_char(str_mid, role_name)
            ret.append(str_mid)
        return AnalysisExampleRet(messages=ret, need_merge=True)

    if "{{char}}:" in example_dialog:
        split_list = example_dialog.split("\n")
        str_mid = split_list[0]
        # 从1开始遍历
        for i in range(1, len(split_list)):
            mid = split_list[i]
            if "{{char}}:" in mid:
                str_mid = (
                    f"<rep>\n{str_mid}\n</rep>"
                    if "{{user}}:" not in str_mid
                    else str_mid
                )
                str_mid = str_util.format_user(str_mid, user_name)
                str_mid = str_util.format_char(str_mid, role_name)
                ret.append(str_mid)
                str_mid = mid
                continue
            str_mid += "\n" + mid

        if str_mid:
            str_mid = str_util.format_user(str_mid, user_name)
            str_mid = str_util.format_char(str_mid, role_name)
            ret.append(str_mid)
        return AnalysisExampleRet(messages=ret, need_merge=True)
    return AnalysisExampleRet(messages=[], need_merge=True)
