
import functools
import logging
import time

log = logging.getLogger(__name__)

def async_timeit_decorator(func):
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        result = await func(*args, **kwargs)  # 异步等待函数执行
        end_time = time.time()
        log.info(f"{func.__name__} cost: {end_time - start_time:.4f} s")
        return result

    return async_wrapper


def timeit_decorator(func):
    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)  # 同步执行函数
        end_time = time.time()
        log.info(f"{func.__name__} cost: {end_time - start_time:.4f} s")
        return result

    return sync_wrapper