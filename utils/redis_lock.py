
import logging
import time
from redis import Redis
from datetime import datetime


log = logging.getLogger(__name__)

class RedisLock:
    def __init__(self, client, key, timeout=60, wait_timeout=10, sleep_interval=0.1):
        self.client = client
        self.key = key
        self.timeout = timeout
        self.wait_timeout = wait_timeout
        self.sleep_interval = sleep_interval
        self.locked = False

    def __enter__(self):
        start_time = time.time()
        while time.time() - start_time < self.wait_timeout:
            if self.client.set(self.key, "locked", ex=self.timeout, nx=True):
                self.locked = True
                log.info(f"Acquired lock: {self.key}")
                return self
            time.sleep(self.sleep_interval)
        raise TimeoutError("Failed to acquire Redis lock within the wait timeout")

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.locked:
            self.client.delete(self.key)
            self.locked = False