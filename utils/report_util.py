# from datetime import datetime
# import os

# from dotenv import load_dotenv
# from posthog import Posthog

# from common.models.chat_model import ChatHistory
# from persistence.models.models import User


# # load_dotenv()

# # posthog = Posthog(
# #     project_api_key=os.environ["POSTHOG_API_KEY"],
# #     host="https://us.i.posthog.com",
# # )

# # def userRegister(user: User):
# #     properties = {
# #         "user_id": user.id,
# #         "email": user.email,
# #         "nickname": user.nickname,
# #         "timestamp": int(datetime.now().timestamp())
# #         # "created_at": datetime.fromtimestamp(user.created_at),
# #     }
# #     posthog.capture(distinct_id=user.id, event="UserRegister", properties=properties)
# #     return "success"


# def aiChat(chatHistory: ChatHistory):

#     properties = {
#         "user_id": chatHistory.user_id,
#         "role_id": chatHistory.role_id,
#         "conversation_id": chatHistory.conversation_id,
#         "message_id": chatHistory.message_id,
#         "version": chatHistory.version,
#         "input_token_count": chatHistory.input_token_count,
#         "output_token_count": chatHistory.output_token_count,
#         "model": chatHistory.model,
#         "original_model": chatHistory.original_model,
#         "timestamp": int(datetime.now().timestamp()),
#         "created_at": datetime.fromtimestamp(chatHistory.timestamp),
#         "duration": int(chatHistory.duration),
#     }
#     posthog.capture(
#         distinct_id=chatHistory.user_id, event="AiChat", properties=properties
#     )
#     return "success"
