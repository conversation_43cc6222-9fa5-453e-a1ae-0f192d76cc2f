import json
import logging
import os
import re
from langchain_core.messages.base import BaseMessage
from langchain.schema import HumanMessage, SystemMessage, AIMessage
import yaml
from common.chat_bot_model import AnalysisExampleRet, PresetSwitchQuery
from common.common_constant import Language, LlmModel, PresetReplace
from common.models.chat_model import ChatNextInput
from common.role_card import CharacterBook
from utils import char_book_util, str_util, token_util

logger = logging.getLogger(__name__)

def find_stop_sequence(preset: dict, char_name: str, user_name: str) -> list[str]:
    include_body = preset.get("custom_include_body", "")
    if include_body == "":
        return []
    try:
        data = yaml.safe_load(include_body)
        ret = data[0].get("stop_sequences", [])
        if len(ret) == 0:
            return []
        for index, stop_sequence in enumerate(ret):
            ret[index] = str_util.format_char(stop_sequence, char_name)
            ret[index] = str_util.format_user(ret[index], user_name)
        return ret
    except Exception as e:
        logger.error(f"find_stop_sequence error,input_str:{include_body}, {e}")
        return []

# def all_custom_params(preset: dict) -> dict:
#     include_body = preset.get("custom_include_body", "")
#     if include_body == "":
#         return {}
#     try:
#         data = yaml.safe_load(include_body)
#         ret = {}
#         if not data:
#             return {}
#         for mid_data in data:
#             ret.update(mid_data)
#         return ret
#     except Exception as e:
#         logger.error(f"find_stop_sequence error,input_str:{include_body}, {e}")
#         return {}

def find_top_k(preset: dict, def_val: float = 0) -> float:
    include_body = preset.get("custom_include_body", "")
    if include_body == "":
        return def_val
    try:
        data = yaml.safe_load(include_body)
        if len(data) < 2:
            return def_val
        return data[1].get("top_k", def_val)
    except Exception as e:
        logger.error(f"find_stop_sequence error,input_str:{include_body}, {e}")
        return def_val


# 从preset中找到injection_position
def is_absolute_position(preset: dict) -> bool:
    position_flag = preset.get("injection_position")
    if position_flag is not None and position_flag == 1:
        return True
    return False


# 从preset中找到injection_depth
def find_absolute_position_depth(preset: dict) -> int:
    return preset.get("injection_depth")


def find_insert_depth_presets(preset_prompts: list[dict]) -> list[dict]:
    insert_depth_msg = []
    for _, preset in enumerate(preset_prompts):
        position_flag = preset.get("injection_position")
        if position_flag is not None and position_flag == 1:
            insert_depth_msg.append(preset)
    return insert_depth_msg


def build_message(role: str, content: str) -> BaseMessage:
    if role == "user" or role == "human":
        return HumanMessage(content=content)
    elif role == "system":
        return SystemMessage(content=content)
    elif role == "assistant" or role == "ai":
        return AIMessage(content=content)
    else:
        return HumanMessage(content=content)


def exist_example(preset_prompts: list[dict]) -> bool:
    for _, preset in enumerate(preset_prompts):
        if preset["identifier"] == "dialogueExamples":
            return True
    return False


def exit_language_setting(content: str) -> bool:
    if "Simplified Chinese" in content:
        return True
    return False


def reset_language_setting(content: str, language: str) -> str:
    if language == Language.ZH.value:
        return content
    ret_content = content.replace("Simplified Chinese", Language.load_english(language))
    val = Language.val_and_region(language)
    ret_content = ret_content.replace("zh_CN", val)
    return ret_content


# def assembling_messages(
#     preset_prompts: list[dict],
#     description: str,
#     personality: str,
#     example_ret: AnalysisExampleRet,
#     scenario: str,
#     history_messages: list[BaseMessage],
#     role_name: str,
#     user_name: str,
#     data_config: dict,
#     last_message_id: int,
#     last_message: str | None,
#     charactor_book: CharacterBook = None,
#     replay_max_count: int = 1000,
#     chat_next_input: ChatNextInput = None,
# ) -> list[BaseMessage]:
#     final_messages: list[BaseMessage] = []
#     ret_prompts: list[dict] = []
#     absolute_presets: list[dict] = []

#     # 处理group_examples
#     example_messages: list[BaseMessage] = []
#     example_messages = [
#         build_message("system", example)
#         for example in example_ret.fetch_merged_message()
#     ]
#     # 格式化内容
#     # start:如下 4 个 identifier：[charDescription, charPersonality,dialogueExamples,scenario]是需要用角色卡的相关字段来填充，需要单独处理
#     for _, preset in enumerate(preset_prompts):
#         identifier = preset["identifier"]
#         mid_preset = {
#             "identifier": preset["identifier"],
#             "content": preset.get("content", ""),
#             "role": preset.get("role", "system"),
#         }

#         if "Enforce the max length of the rep is" in preset.get("content", ""):
#             pattern = r"(Enforce the max length of the rep is )(.*?)( tokens.)"

#             def replace_match(match):
#                 return match.group(1) + str(int(replay_max_count)) + match.group(3)

#             content = re.sub(pattern, replace_match, preset.get("content", ""))
#             mid_preset["content"] = content

#         if identifier == "charDescription" and str_util.is_not_empty(description):
#             mid_preset["content"] = description
#             mid_preset["role"] = "system"
#             ret_prompts.append(mid_preset)
#         elif identifier == "charPersonality" and str_util.is_not_empty(personality):
#             mid_preset["content"] = personality
#             mid_preset["role"] = "system"
#             ret_prompts.append(mid_preset)
#         elif identifier == "dialogueExamples" and len(example_messages) > 0:
#             ret_prompts.append(mid_preset)
#         elif identifier == "scenario" and str_util.is_not_empty(scenario):
#             mid_preset["content"] = scenario
#             mid_preset["role"] = "system"
#             ret_prompts.append(mid_preset)
#         elif identifier == "worldInfoBefore":
#             content = char_book_util.get_content_by_world_info(charactor_book, True)
#             if not content:
#                 continue
#             mid_preset["content"] = content
#             mid_preset["role"] = "system"
#             ret_prompts.append(mid_preset)
#         elif identifier == "worldInfoAfter":
#             content = char_book_util.get_content_by_world_info(charactor_book, False)
#             if not content:
#                 continue
#             mid_preset["content"] = content
#             mid_preset["role"] = "system"
#             ret_prompts.append(mid_preset)
#         # end: 4 个 identifier处理完毕
#         elif identifier == "chatHistory":
#             ret_prompts.append(dict({"identifier": "chatHistory"}))
#         elif exit_language_setting(preset.get("content", "")):
#             mid_preset["content"] = reset_language_setting(
#                 preset.get("content", ""), chat_next_input.language
#             )
#             ret_prompts.append(mid_preset)
#         elif "content" not in preset or preset["content"].strip() == "":
#             continue
#         else:
#             content = preset["content"]
#             content = str_util.format_status_bar_with_config(data_config, content)
#             content = str_util.format_char(content, role_name)
#             content = str_util.format_user(content, user_name)
#             if last_message:
#                 content = str_util.format_lastmessage(
#                     content, str(last_message_id), last_message
#                 )
#             content = str_util.format_random(content)
#             mid_preset["content"] = content
#             if is_absolute_position(preset):
#                 mid_preset["depth"] = find_absolute_position_depth(preset)
#                 absolute_presets.append(mid_preset)
#                 continue

#             ret_prompts.append(mid_preset)

#     # 处理history_messages，按照配置优先级插入
#     # 由于最后有一条用户消息，所以index从0开始
#     ret_history_message = []
#     absolute_presets = list(reversed(absolute_presets))
#     len_history = len(history_messages)
#     for index, msg in enumerate(reversed(history_messages)):
#         ret_history_message.extend(
#             [
#                 build_message(x["role"], x["content"])
#                 for x in absolute_presets
#                 if x["depth"] == index
#             ]
#         )
#         book_msg = char_book_util.get_content_by_insert_depth(
#             charactor_book, index, index == len_history - 1
#         )
#         if book_msg:
#             ret_history_message.extend(
#                 [build_message(x["role"], x["content"]) for x in book_msg]
#             )
#         ret_history_message.append(msg)
#     ret_history_message.extend(
#         [
#             build_message(x["role"], x["content"])
#             for x in absolute_presets
#             if x["depth"] >= len_history
#         ]
#     )
#     history_messages = list(reversed(ret_history_message))

#     # 合并所有消息
#     for preset in ret_prompts:
#         if preset["identifier"] == "chatHistory":
#             final_messages.extend(history_messages)
#             continue
#         if preset["identifier"] == "dialogueExamples":
#             final_messages.extend(example_messages)
#             continue
#         final_messages.append(build_message(preset["role"], preset["content"]))

#     token_sum = 0
#     for message in final_messages:
#         token_sum += token_util.num_tokens_from_string(message.content)
#     return final_messages, token_sum


def cat_examples(example_model: AnalysisExampleRet | None, reduce_token: int):
    if reduce_token <= 0 or example_model is None or example_model.messages == []:
        return AnalysisExampleRet(), 0
    token_sum = 0
    for example in example_model.messages:
        token_sum += token_util.num_tokens_from_string(example)
    if token_sum <= reduce_token:
        logger.info(
            f"cat all example original_reduce_token:{reduce_token} ret_reduce_token:{token_sum}"
        )
        return AnalysisExampleRet(), token_sum
    # 优先级：muilti_examples>group_examples>single_examples
    list_example = example_model.messages

    cat_token = 0
    cat_index = len(list_example) - 1
    while reduce_token > 0:
        mid_token = token_util.num_tokens_from_string(list_example[cat_index])
        reduce_token -= mid_token
        cat_token += mid_token
        cat_index -= 1
    ret_list = list_example[: cat_index + 1]
    logger.info(
        f"cat_examples cat_index:{cat_index},reduce_token:{cat_token},ret_len:{len(ret_list)}"
    )
    if example_model.need_merge:
        ret_list = ["\n\n".join(ret_list)]
    example_ret = AnalysisExampleRet(
        messages=ret_list, need_merge=example_model.need_merge
    )

    return example_ret, cat_token


def cat_message(history_message: list[BaseMessage], reduce_token: int):
    if reduce_token <= 0:
        return history_message, 0
    index = 0
    msg_len = len(history_message)
    reduce_ret_token = 0
    for msg in history_message:
        index += 1
        reduce_ret_token += token_util.num_tokens_from_string(msg.content)
        if reduce_token - reduce_ret_token <= 0 or msg_len - index <= 10:
            break
    # logger.info(
    #     f"cat_message index:{index},start_context:{history_message[index].content}"
    # )
    return history_message[index : len(history_message)], reduce_ret_token


def cat_message_and_book(
    history_message: list[BaseMessage], book: CharacterBook | None, reduce_token: int
):
    if reduce_token <= 0:
        return history_message, book, 0
    index = 0
    msg_len = len(history_message)
    depth = msg_len + 1
    reduce_ret_token = 0
    for msg in history_message:
        index += 1
        depth -= 1
        # 消息小于10个时不裁剪
        if msg_len - index <= 5:
            break
        # 裁剪消息
        reduce_ret_token += token_util.num_tokens_from_string(msg.content)
        if reduce_token - reduce_ret_token <= 0:
            break
        if book is None or not book.entries:
            continue
        # 裁剪世界书
        mid_entries = book.entries
        mid_entries.sort(key=lambda x: x.insertion_order)
        ret_entries = []
        for entry in mid_entries:
            if not entry.extensions or not entry.extensions.depth:
                continue
            if entry.extensions.depth >= depth and reduce_token > reduce_ret_token:
                reduce_ret_token += token_util.num_tokens_from_string(entry.content)
                continue
            ret_entries.append(entry)
        book.entries = ret_entries
        # 裁剪完后，Token已满足情况，不再继续裁剪
        if reduce_token <= reduce_ret_token:
            break

    # logger.info(
    #     f"cat_message index:{index},start_context:{history_message[index].content},reduce_token:{reduce_ret_token}"
    # )
    return history_message[index : len(history_message)], book, reduce_ret_token


def cat_message_and_book_new(input: ChatNextInput, reduce_token: int):
    history_message = input.history
    book = input.character_book
    if reduce_token <= 0:
        return input, 0
    index = 0
    msg_len = len(history_message)
    depth = msg_len + 1
    reduce_ret_token = 0
    for msg in history_message:
        # 消息小于10个时不裁剪
        if msg_len - index <= 5:
            break
        index += 1
        depth -= 1
        # 裁剪消息
        reduce_ret_token += token_util.num_tokens_from_string(msg.content)
        if reduce_token - reduce_ret_token <= 0:
            break
        if not book or not book.entries:
            continue
        # 裁剪世界书
        mid_entries = book.entries
        mid_entries.sort(key=lambda x: x.insertion_order)
        ret_entries = []
        for entry in mid_entries:
            if not entry.extensions or not entry.extensions.depth:
                continue
            if entry.extensions.depth >= depth and reduce_token > reduce_ret_token:
                reduce_ret_token += token_util.num_tokens_from_string(entry.content)
                continue
            ret_entries.append(entry)
        book.entries = ret_entries
        # 裁剪完后，Token已满足情况，不再继续裁剪
        if reduce_token <= reduce_ret_token:
            break

    # logger.info(
    #     f"cat_message index:{index},start_context:{history_message[index].content},original_reduce_token:{reduce_token},reduce_token:{reduce_ret_token}"
    # )
    history_message = history_message[index : len(history_message)]
    input.history = history_message
    input.character_book = book
    return input, reduce_ret_token


def format_prompts_by_status(
    ps: dict,
    preset_switch: PresetSwitchQuery,
) -> list:
    prompts = ps["prompts"]
    orders = ps["prompt_order"][1]["order"]
    prompts_map = {p["identifier"]: p for p in prompts}

    def check_order(enabled: bool, identifier: str) -> bool:
        p = prompts_map.get(identifier, {})
        if not p:
            return enabled
        name = str(p["name"])
        # check switch
        if name.startswith("CE:"):
            return preset_switch.example_switch
        if name.startswith("PD:"):
            return preset_switch.personality_switch
        if name.startswith("SC:"):
            return preset_switch.scenario_switch
        if name.startswith("FM:"):
            return preset_switch.first_msg_switch
        if name.startswith("WI:"):
            return preset_switch.world_info_switch
        if name.startswith("KW:"):
            return preset_switch.kw_world_info_switch
        if name == "Last Defense":
            return preset_switch.card_public_switch

        # check status
        contain_status_on = re.search("(?:^|:)StatusOn:", p["name"])
        contain_status_off = re.search("(?:^|:)StatusOff:", p["name"])
        if contain_status_on or contain_status_off:
            if preset_switch.status_enable and contain_status_on:
                return True
            if not preset_switch.status_enable and contain_status_off:
                return True
            return False

        # check chat type
        contain_chat = re.search("(?:^|:)Chat:", p["name"])
        contain_role_play = re.search("(?:^|:)RolePlay:", p["name"])
        contain_general = re.search("(?:^|:)General:", p["name"])
        if contain_chat or contain_role_play or contain_general:
            if preset_switch.role_chat_type == "Chat" and contain_chat:
                return True
            if preset_switch.role_chat_type == "RolePlay" and contain_role_play:
                return True
            if preset_switch.role_chat_type == "General" and contain_general:
                return True
            return False
        return enabled

    orders = [o for o in orders if check_order(o["enabled"], o["identifier"])]
    orders_dict = {o["identifier"]: {**o, "order": i} for i, o in enumerate(orders)}

    prompts = [p for p in prompts if p["identifier"] in orders_dict]
    prompts.sort(key=lambda p: orders_dict.get(p["identifier"], {}).get("order", 0))

    return prompts


# 从presets中挪过来的，主要是后台格式化使用，只展示有效的，并且按照order排序的
def get_prompts(ps: dict) -> list:
    prompts = ps["prompts"]
    orders = ps["prompt_order"][1]["order"]
    # orders = [o for o in orders if o["enabled"]]
    orders_dict = {o["identifier"]: {**o, "order": i} for i, o in enumerate(orders)}

    prompts = [p for p in prompts if p["identifier"] in orders_dict]
    prompts.sort(key=lambda p: orders_dict.get(p["identifier"], {}).get("order", 0))
    for prompt in prompts:
        order_mid = orders_dict.get(prompt["identifier"], {})
        prompt["order"] = order_mid.get("order", 0)
        prompt["enabled"] = order_mid.get("enabled", False)
    return prompts


prompts_buffer = {}


def get_prompts_by_file(file_name: str) -> list[BaseMessage]:
    file_path = os.path.join(os.getcwd(), "presets/" + file_name)
    prompts = []
    if file_path not in prompts_buffer:
        with open(file_path, "r") as f:
            ps = json.load(f)
            prompts_buffer[file_path] = ps
    file_contents = prompts_buffer.get(file_path, [])
    for p in file_contents:
        prompts.append(build_message(p["role"], p["content"]))
    return prompts


def list_replace_key(prompts: list[dict]) -> list[str]:
    replace_list = [item.value for item in PresetReplace]
    ret_map = {}
    for prompt in prompts:
        if "content" not in prompt:
            continue
        for key in replace_list:
            if key in prompt["content"]:
                ret_map[key] = 1
    return list(ret_map.keys())


def need_replace_first_msg(prompts: list[dict]) -> bool:
    if PresetReplace.FIRST_MESSAGE.value in list_replace_key(prompts):
        return True
    return False


def replace_first_msg(prompts: list[dict], first_msg: str) -> list[dict]:
    for prompt in prompts:
        if "content" not in prompt:
            continue
        if PresetReplace.FIRST_MESSAGE.value in prompt["content"]:
            prompt["content"] = prompt["content"].replace(
                PresetReplace.FIRST_MESSAGE.value, first_msg
            )
    return prompts


def load_actual_max_context(chat_next_input: ChatNextInput, presets: dict) -> int:
    def_max = presets["openai_max_context"]
    return def_max
    # contain_book = bool(
    #     chat_next_input.keywords_book_entries or chat_next_input.constant_book_entries
    # )
    # return def_max if not contain_book else 10000


def contain_length(content: str) -> bool:
    if "length" not in content:
        return False
    if not ("tokens" in content or "words" in content):
        return False
    pattern = r"\d+(?=\s+words|\stokens)"
    if not re.search(pattern, content):
        return False
    return True


def assembling_messages_v2(preset_prompts: list[dict], chat_next_input: ChatNextInput):
    final_messages: list[BaseMessage] = []
    ret_prompts: list[dict] = []
    absolute_presets: list[dict] = []

    # 处理group_examples
    example_messages: list[BaseMessage] = []
    if chat_next_input.format_example:
        example_messages = [
            build_message("system", example)
            for example in chat_next_input.format_example.fetch_merged_message()
        ]
    # 格式化内容
    # start:如下 4 个 identifier：[charDescription, charPersonality,dialogueExamples,scenario]是需要用角色卡的相关字段来填充，需要单独处理
    # personaDescription 这个identifier 需要用personality字段来填充
    for _, preset in enumerate(preset_prompts):
        identifier = preset["identifier"]
        mid_preset = {
            "identifier": preset["identifier"],
            "content": preset.get("content", ""),
            "role": preset.get("role", "system"),
        }
        if contain_length(preset.get("content", "")):
            content = str(preset.get("content", ""))
            #匹配 \d words或者tokens
            pattern = r"\d+(?=\s+words|\stokens)"
            num = re.search(pattern, content)
            if num:
                num = int(num.group())
                ret_num = int(chat_next_input.replay_len_ratio * num * 2 / 100)
                content = content.replace(str(num), str(ret_num))
                mid_preset["content"] = content

        if identifier == "charDescription" and str_util.is_not_empty(
            chat_next_input.description
        ):
            mid_preset["content"] = chat_next_input.description
            mid_preset["role"] = "system"
            ret_prompts.append(mid_preset)
        elif identifier == "charPersonality" and str_util.is_not_empty(
            chat_next_input.personality
        ):
            mid_preset["content"] = chat_next_input.personality
            mid_preset["role"] = "system"
            ret_prompts.append(mid_preset)
        elif identifier == "dialogueExamples" and len(example_messages) > 0:
            ret_prompts.append(mid_preset)
        elif identifier == "scenario" and str_util.is_not_empty(
            chat_next_input.scenario
        ):
            mid_preset["content"] = chat_next_input.scenario
            mid_preset["role"] = "system"
            ret_prompts.append(mid_preset)
        elif identifier == "personaDescription" and str_util.is_not_empty(
            chat_next_input.user_personality
        ):
            mid_preset["content"] = chat_next_input.user_personality
            if is_absolute_position(preset):
                mid_preset["depth"] = find_absolute_position_depth(preset)
                absolute_presets.append(mid_preset)
                continue
            ret_prompts.append(mid_preset)
        elif identifier == "worldInfoBefore":
            content_list = [x.content for x in chat_next_input.constant_book_entries]
            mid_preset["content"] = "\n".join(content_list)
            mid_preset["role"] = "system"
            ret_prompts.append(mid_preset)
        elif identifier == "chatHistory":
            ret_prompts.append(dict({"identifier": "chatHistory"}))
        elif exit_language_setting(preset.get("content", "")):
            mid_preset["content"] = reset_language_setting(
                preset.get("content", ""), chat_next_input.language
            )
            ret_prompts.append(mid_preset)
        elif "content" not in preset or preset["content"].strip() == "":
            continue
        else:
            mid_preset["content"] = str_util.format_random(mid_preset["content"])
            if is_absolute_position(preset):
                mid_preset["depth"] = find_absolute_position_depth(preset)
                absolute_presets.append(mid_preset)
                continue
            ret_prompts.append(mid_preset)

    # 处理history_messages，按照配置优先级插入
    # 由于最后有一条用户消息，所以index从0开始
    ret_history_message = []
    absolute_presets = list(reversed(absolute_presets))
    len_history = len(chat_next_input.history)
    for index, msg in enumerate(reversed(chat_next_input.history)):
        ret_history_message.extend(
            [
                build_message(x["role"], x["content"])
                for x in absolute_presets
                if x["depth"] == index
            ]
        )
        book_msg = char_book_util.get_content_by_insert_depth(
            chat_next_input.character_book, index, index == len_history - 1
        )
        if book_msg:
            ret_history_message.extend(
                [build_message(x["role"], x["content"]) for x in book_msg]
            )
        ret_history_message.append(build_message(msg.type, msg.content))
    ret_history_message.extend(
        [
            build_message(x["role"], x["content"])
            for x in absolute_presets
            if x["depth"] >= len_history
        ]
    )
    history_messages = list(reversed(ret_history_message))

    # 合并所有消息
    for preset in ret_prompts:
        if preset["identifier"] == "chatHistory":
            final_messages.extend(history_messages)
            continue
        if preset["identifier"] == "dialogueExamples":
            final_messages.extend(example_messages)
            continue
        final_messages.append(build_message(preset["role"], preset["content"]))

    # 续写增加的AI消息
    if chat_next_input.chat_continue and chat_next_input.chat_continue_ai_msg:
        # final_messages.append(build_message("human", chat_next_input.chat_continue_human_msg))
        final_messages.append(
            build_message("ai", chat_next_input.chat_continue_ai_msg.rstrip())
        )

    token_sum = 0
    for message in final_messages:
        token_sum += token_util.num_tokens_from_string(str(message.content))
    return final_messages, token_sum


def replace_kw_world_info(preset_prompts: list[dict], input: ChatNextInput):
    if not input.keywords_book_entries:
        return preset_prompts
    for preset in preset_prompts:
        content = preset.get("content", "")

        if not content or PresetReplace.KEY_MATCHED.value not in content:
            continue
        strs = [x.content for x in input.keywords_book_entries if x]
        replace_val = "\n".join(strs)
        preset["content"] = content.replace(
            PresetReplace.KEY_MATCHED.value, replace_val
        )
        break
    return preset_prompts


def replace_prompts(preset_prompts: list[dict], input: ChatNextInput):
    for preset in preset_prompts:
        if "content" not in preset:
            continue
        content = preset["content"]
        if not content:
            continue
        content = str_util.format_placeholder(
            content, PresetReplace.USER.value, input.request_user_name
        )
        content = str_util.format_placeholder(
            content, PresetReplace.CHAR.value, input.role_name
        )
        content = str_util.format_placeholder(
            content, PresetReplace.STATUS_BAR.value, input.status_block
        )
        content = str_util.format_placeholder(
            content, PresetReplace.STATUS_RULES.value, input.status_rules
        )
        content = str_util.format_placeholder(
            content, PresetReplace.LAST_MESSAGE_ID.value, str(input.last_message_id)
        )
        content = str_util.format_placeholder(
            content, PresetReplace.LAST_MESSAGE.value, input.last_user_message
        )
        preset["content"] = content
    return preset_prompts


def token_for_first_message(preset_prompts: list[dict]):
    token_count = 0
    for preset in preset_prompts:
        if "content" not in preset:
            continue
        content = preset["content"]
        if not content:
            continue
        name = str(preset.get("name", ""))
        if name and name.startswith("FM:"):
            token_count += token_util.num_tokens_from_string(content)
    return token_count


# 场景是按照名字替换的
def token_for_scenario(preset_prompts: list[dict], scenario: str):
    if not scenario:
        return 0
    token_count = token_util.num_tokens_from_string(scenario)
    for preset in preset_prompts:
        if "content" not in preset:
            continue
        content = preset["content"]
        if not content:
            continue
        name = str(preset.get("name", ""))
        if name and name.startswith("SC:"):
            token_count += token_util.num_tokens_from_string(content)
    return token_count


def token_for_example(preset_prompts: list[dict]):
    token_count = 0
    for preset in preset_prompts:
        if "content" not in preset:
            continue
        content = preset["content"]
        if not content:
            continue
        name = str(preset.get("name", ""))
        if name and name.startswith("CE:"):
            token_count += token_util.num_tokens_from_string(content)
    return token_count
