


import json
import os


# translate_buffer = {}
# file_path = os.path.join(os.getcwd(), "properties/translatecommon_translate.json")
# with open(file_path, "r") as f:
#     ps = json.load(f)
#     translate_buffer = ps


# def translate_common(key: str, lang: str) -> str:
#     if key not in translate_buffer:
#         return key
#     if lang not in translate_buffer[key]:
#         return key
#     return translate_buffer[key][lang]

# def translate(key:str,lang:str)->str:
#     return translate_common(key,lang)

def list_file_path_by_dir(directory:str)->list[str]:
    all_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            all_files.append(os.path.join(root, file))
    return all_files