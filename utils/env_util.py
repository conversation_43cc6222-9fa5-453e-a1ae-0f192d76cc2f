import os
from typing import Annotated

from fastapi import Header, Request

from common.common_constant import Env
from persistence.models.models import UserRegisterSource


# 从环境变量获取当前环境
def get_current_env() -> Env:
    return Env.from_str(os.getenv("ENV",""))

def is_prod_env() -> bool:
    return get_current_env() == Env.PROD

def is_target_env(target: Env) -> bool:
    return get_current_env() == target

# 从Header 获取当前语言
def get_current_language_from_request(request: Request) -> str:
    return request.headers.get("Current-Language", "")

# 从Header 获取当前语言
def get_google_redirect_url(source: str) -> str:
    if source == UserRegisterSource.USA_WEB.value:
        return os.getenv("GOOGLE_REDIRECT_URI_USA","")
    return os.getenv("GOOGLE_REDIRECT_URI","")

# 获取google登录后的跳转地址
def get_google_post_login_redirect_uri(source: str) -> str:
    if source == UserRegisterSource.USA_WEB.value:
        return os.getenv("GOOGLE_POST_LOGIN_REDIRECT_URI_USA","")
    return os.getenv("GOOGLE_POST_LOGIN_REDIRECT_URI","")


# 获取删除消息的延迟时间
def get_deleted_message_delay_min() -> int:
    if get_current_env() == Env.STAG:
        return 1
    return 120

def get_admin_api_domain() -> str:
    if get_current_env() == Env.PROD:
        return "https://tavern-admin-api.fancyme.xyz"
    return "https://tavern-admin-api.655356.xyz"

def get_local_user_id() -> int:
    env = os.getenv('ENV',"")
    local_user_id = os.getenv('LOCAL_USER_ID', None)
    if env == "local" and local_user_id:
        return int(local_user_id)
    return 0