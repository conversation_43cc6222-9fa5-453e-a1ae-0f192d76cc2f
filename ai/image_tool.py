import time, os
from typing import Iterator
import logging
import json
import base64
import httpx
import asyncio
from pydantic import BaseModel
import requests
from result import Result, Ok, Err
from random import randint
from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client
from PIL import Image
from io import BytesIO

from utils import env_const

log = logging.getLogger(__name__)


class SDSetting(BaseModel):
    checkpoint: str
    sampler: str
    steps: int
    cfg_scale: float
    width: int
    height: int
    default_prompt: str
    steps: int = 20
    scribble_model: str = "control_v11p_sd15_scribble [d4ba51ff]"


class CalibratePrompt(BaseModel, extra="ignore"):
    prompt: str
    negative_prompt: str
    ori_img: str


class ImageBot:
    def __init__(self, secret_id: str, secret_key: str, bucket: str) -> None:
        region = "ap-singapore"
        token = None
        scheme = "https"
        config = CosConfig(
            Region=region,
            SecretId=secret_id,
            SecretKey=secret_key,
            Token=token,
            Scheme=scheme,
        )
        self.client = CosS3Client(config)
        self.bucket = bucket

    def translate_prompt(self, prompt: str) -> Result[CalibratePrompt, str]:
        url = "http://*************:8000/v1/prompt/calibratePrompt"
        response = requests.get(url, params={"prompt": prompt})
        body = response.json()
        if body["status"] != "ok":
            return Err(f"response status not ok: {body}")
        return Ok(
            CalibratePrompt(
                prompt=body["prompt"],
                negative_prompt=body["neg_prompt"],
                ori_img=body["ori_img"],
            )
        )

    def get_image(self, ori_img: str) -> BytesIO:
        response = self.client.get_object(self.bucket, ori_img)
        fp = response["Body"].get_raw_stream()
        bytes_io = BytesIO()
        bytes_io.write(fp.data)
        return bytes_io

    def txt2img_v2(
        self, prompt: str, sd_setting: SDSetting
    ) -> tuple[str, str, BytesIO]:
        logging.info(sd_setting)
        tp = self.translate_prompt(prompt).ok_value
        prompt = f"{sd_setting.default_prompt},{tp.prompt.strip()}"
        url = "http://*************:7861/sdapi/v1/txt2img"
        bytes_io = self.get_image(tp.ori_img)
        b64_img = base64.b64encode(bytes_io.getvalue()).decode("utf-8")
        image_obj = Image.open(bytes_io)
        payload = {
            "prompt": prompt,
            "negative_prompt": tp.negative_prompt,
            "seed": randint(62, 1462887602),
            "steps": sd_setting.steps,
            "width": image_obj.width,
            "height": image_obj.height,
            "cfg_scale": sd_setting.cfg_scale,
            "override_settings": {"sd_model_checkpoint": sd_setting.checkpoint},
            "alwayson_scripts": {
                "controlnet": {
                    "args": [
                        {
                            "module": "scribble_pidinet",
                            "model": sd_setting.scribble_model,
                            "resize_mode": "Crop and Resize",
                            "control_mode": "Balanced",
                            "enabled": True,
                            "guidance_end": 1,
                            "guidance_start": 0,
                            "input_image": b64_img,
                            "input_mode": "simple",
                            "is_ui": True,
                            "loopback": False,
                            "low_vram": False,
                            "pixel_perfect": False,
                            "processor_res": 512,
                            "resize_mode": "Crop and Resize",
                            "threshold_a": 100,
                            "threshold_b": 200,
                            "pixel_perfect": False,
                            "weight": 1,
                        }
                    ]
                }
            },
        }
        response = requests.post(url, json=payload)
        # payload["alwayson_scripts"]["controlnet"]["args"][0]["input_image"] = ""
        # logging.info(f"txt2img_v2: {payload}")
        return (
            response.json()["images"][0],
            f"sd_prompt:\n{prompt}\n\nnegative_prompt:\n{tp.negative_prompt}\n\nori_image:{tp.ori_img}",
            bytes_io,
        )

    async def txt2img(self, prompt, negative_prompt) -> Iterator[str]:
        url = "http://************:8000/sdapi/v1/txt2img"
        payload = {
            "prompt": prompt,
            # "prompt": "masterpiece, best quality, loverevolution, medium full shot, slender   body,   (nude:1.3), nipples, wet pussy, good vagina, creampie, shiny skin, perfect    slim body, on  bed, white sheets, short hair, black hair, (lying:1.3), (large  breasts:1.3), (blush:1.2),  (spread legs:1.3), nsfw, (sex:1.3), hetero, vaginal,     ecstasy smile, ahegao",
            "negative_prompt": "(worst quality, low quality:1.4), easynegative,     bad_prompt_version2-neg, (clothes:1.5), (cross-eye:1.5), bikini, swimsuit,    (underwear:1.5), man, boy, penis, dick, 3legs, 3hands, bad arm, bad leg, bad   fingers",
            "seed": randint(62887602, 1462887602),
            "steps": 25,
            # "width": 1024,
            # "height": 1024
        }

        headers = {"accept": "application/json", "Content-Type": "application/json"}

        async with httpx.AsyncClient() as client:
            start = time.time()
            r = client.request("POST", url, headers=headers, json=payload)
            # response = requests.request("POST", url, headers=headers, data=payload)
            while True:
                response = await self.get_progress(client)
                image = response["current_image"]
                if image:
                    yield image
                    await asyncio.sleep(1)
                else:
                    break
            response = await r
            logging.info(
                f"image generated for: {prompt}, time:{time.time() - start:.2f}"
            )
            yield response.json()["images"][0]

    async def get_progress(self, client: httpx.AsyncClient):
        url = "http://************:8000/sdapi/v1/progress?skip_current_image=false"

        payload = {}
        headers = {"accept": "application/json"}
        response = await client.request("GET", url, headers=headers, data=payload)
        return response.json()


def get_image_v2(history: list, role_id: int, user_id: int) -> tuple[str, str]:
    def transpose_history(jh):
        return f"{jh['type']}: {jh['content']}"

    rid = str(role_id)
    if rid == "1010":
        rid = "default"
    img_service_host = os.getenv("IMG_SERVICE_HOST")
    url = f"{img_service_host}/img-store/search/"
    parsed_history = list(map(transpose_history, history))
    payload = {"role_id": rid, "user_id": str(user_id), "context": parsed_history}
    headers = {"content-type": "application/json"}
    response = requests.request("POST", url, headers=headers, json=payload)
    if response.status_code != 200:
        log.error(
            f"get_image_v2 failed,user_id: {user_id}, code: {response.status_code}"
        )
        return "", ""
    result = response.json()
    log.info(f"get_image_v2 user_id: {user_id},result: {result}")
    return result.get("img_id"), result.get("image_url")
