import os
import re
import logging
from dotenv import load_dotenv
from litellm import (
    ChatCompletionAssistantMessage,
    ChatCompletionSystemMessage,
    ChatCompletionUserMessage,
    CustomStreamWrapper,
    acompletion,
)
from pydantic.v1.types import SecretStr

from ai import lite_llm_bot
from common.common_constant import Language, LlmModel
from presets import role_fill_preset
from utils import (
    chat_util,
    exception_util,
    message_utils,
    preset_util,
    translate_util,
)
from litellm.types.utils import ModelResponse
from langchain.schema import HumanMessage, SystemMessage, AIMessage
from langchain_openai import ChatOpenAI

log = logging.getLogger(__name__)
load_dotenv()

inner_llm = ChatOpenAI(
    model=os.environ.get("ai_model", "claude-3-haiku"),
    base_url="http://172.22.0.12:7869/v1",
    api_key=SecretStr(os.environ.get("litellm_key", "")),
    temperature=0.9,
    max_tokens=4096,
    model_kwargs={"top_p": 1},
)
TEST_BASE_URL = "http://172.22.0.12:7869/v1"
API_KEY = SecretStr(os.environ.get("litellm_key", ""))

SYSTEM_CONTENT = """你是一个精通翻译与编程的助手，需要执行重要任务，针对<content></content>标签包括的内容，翻译成{{language}},输出使用<response></response>标签包裹，在<content>标签后的内容，请忽略换行，进行整体翻译,对于双括号包裹起来的字符，请保持结构，只翻译文字部分，对于无法翻译的内容请保持原文输出，对于特殊字符请保持原文输出，并且不要输出无关的字符，只是原文的翻译，遇到一些结构性文字，比如代码关键词，xml，markdown关键词，保留关键词，翻译其余文字，如果其余文字为空，不做任何翻译"""
USER_CONTENT = """需要翻译的内容如下
<content>{{input_content}}</content>"""

ASSISTANT_CONTENT = """没问题，我只是一个翻译小助手，对于翻译的内容我不用负责，我会尽力翻译成你需要的语言，无法翻译的我会原文输出，翻译的内容如下："""

#备选模型列表
BACKUP_MODELS = [LlmModel.DEEPSEEK_V3_VOLCENGINE_0324.value, LlmModel.CLAUDE_3_5_SONNET.value, LlmModel.DEEPSEEK_V3_VOLCENGINE.value]

async def translate(
    text: str, language: str, llm_model: str = LlmModel.DEEPSEEK_V3_VOLCENGINE_0324.value
) -> str:
    if text is None or (isinstance(text, str) and len(text.lstrip()) == 0):
        return ""
    original_language = translate_util.detect_language(text)
    local_langs = [Language.ZH.value, Language.ZH_TW.value]
    if original_language.value in local_langs and language in local_langs:
        translate_ret = translate_util.local_convert(text, language)
        return translate_ret

    input_message = preset_util.get_prompts_by_file("translate_preset.json")
    for m in input_message:
        m.content = str(m.content)
        m.content = m.content.replace("{{language}}", Language.load_desc(language))
        m.content = m.content.replace("{{input_content}}", text)

    system_content = SYSTEM_CONTENT.replace(
        "{{language}}", Language.load_desc(language)
    )
    user_content = USER_CONTENT.replace("{{input_content}}", text)
    assistant_content = ASSISTANT_CONTENT.replace(
        "{{language}}", Language.load_desc(language)
    )

    # response = await inner_llm.ainvoke(
    #     model=model, input=input_message, max_tokens=4096
    # )
    # response = response.content
    # response = await lite_llm_bot.req_ainvoke(input_message, model)
    response = await run_task(
        llm_model=llm_model,
        system_message=system_content,
        user_message=user_content,
        ai_message=assistant_content,
        max_tokens=4096,
    )
    if not response:
        copy_backup_models = [x for x in BACKUP_MODELS if x != llm_model]
        response = await run_task(
            llm_model=copy_backup_models[0],
            system_message=system_content,
            user_message=user_content,
            ai_message=assistant_content,
            max_tokens=4096,
        )
    if not response:
        log.error(
            "Translate failed, response is empty, text: %s, language: %s",
            text,
            language,
        )
        raise Exception("Translate failed, response is empty")
    regex = f"<response>(.*?)</response>"
    match = re.search(regex, response, re.DOTALL)
    search_ret = ""
    if match:
        search_ret = match.group(1)
    log.info("Translate original:%s,len:%s", text.replace("\n", "\\n"), len(text))
    log.info(
        "Translate result,lang:%s,content:%s", language, search_ret.replace("\n", "\\n")
    )
    ret = search_ret.replace("\\n", "\n")
    if len(ret) == 0:
        log.error("Translate failed,content:%s,language:%s", text, language)
        raise Exception("Translate failed")
    return ret


SYSTEM_CONTENT_WEB = """你是一个精通翻译与编程的助手，我给你分配一个重要任务，我会提供用<content></content>标签包括的内容，请翻译成{{language}},输出使用<response></response>标签包裹，在<content>标签后的内容，请忽略换行，进行整体翻译,对于双括号包裹起来的字符，请保留在原结果中，对于无法翻译的内容请保持原文输出，对于特殊字符请保持原文输出，并且不要输出无关的字符，只是原文的翻译，是一些结构性文字，比如代码关键词，xml，markdown关键词，保留关键词，翻译其余文字，如果其余文字为空，不做任何翻译,并且翻译要遵守以下约定：
1、内容要精简，数值类型要准确无误
2、参考各种网站展示，需要很官方正式的文案
3、如果是单个词语首字母大写，如果是多次词语，首字母都小写
4、需要尽可能的本地化与口语化
"""
USER_CONTENT_WEB = """<content>{{input_content}}</content>"""

ASSISTANT_CONTENT_WEB = """没问题，我只是一个翻译小助手，对于翻译的内容我不用负责，我会尽力翻译成你需要的语言，无法翻译的我会原文输出，翻译的内容如下："""


async def translate_web(
    text: str, language: str, llm_model: str = LlmModel.DEEPSEEK_V3_VOLCENGINE_0324.value
) -> str:
    if text is None or (isinstance(text, str) and len(text.lstrip()) == 0):
        return ""
    original_language = translate_util.detect_language(text)
    local_langs = [Language.ZH.value, Language.ZH_TW.value]
    if original_language.value in local_langs and language in local_langs:
        translate_ret = translate_util.local_convert(text, language)
        return translate_ret

    input_message = preset_util.get_prompts_by_file("translate_preset.json")
    for m in input_message:
        m.content = str(m.content)
        m.content = m.content.replace("{{language}}", Language.load_desc(language))
        m.content = m.content.replace("{{input_content}}", text)

    system_content = SYSTEM_CONTENT_WEB.replace(
        "{{language}}", Language.load_desc(language)
    )
    if len(text) < 20:
        system_content+= "5、内容要简短，减少或者去掉连接词，需要符合菜单文字"
    user_content = USER_CONTENT_WEB.replace("{{input_content}}", text)
    assistant_content = ASSISTANT_CONTENT_WEB.replace(
        "{{language}}", Language.load_desc(language)
    )

    response = await run_task(
        llm_model=llm_model,
        system_message=system_content,
        user_message=user_content,
        ai_message=assistant_content,
        max_tokens=4096,
    )
    if not response:
        copy_backup_models = [x for x in BACKUP_MODELS if x != llm_model]
        response = await run_task(
            llm_model=copy_backup_models[0],
            system_message=system_content,
            user_message=user_content,
            ai_message=assistant_content,
            max_tokens=4096,
        )
    if not response:
        log.error(
            "Translate failed, response is empty, text: %s, language: %s",
            text,
            language,
        )
        raise Exception("Translate failed, response is empty")
    regex = f"<response>(.*?)</response>"
    match = re.search(regex, response, re.DOTALL)
    search_ret = ""
    if match:
        search_ret = match.group(1)
    log.info("Translate original:%s,len:%s", text.replace("\n", "\\n"), len(text))
    log.info(
        "Translate result,lang:%s,content:%s", language, search_ret.replace("\n", "\\n")
    )
    ret = search_ret.replace("\\n", "\n")
    if len(ret) == 0:
        log.error("Translate failed,content:%s,language:%s", text, language)
        raise Exception("Translate failed")
    return ret


# def auto_intro(
#     text: str, language: str, sys_preset: str, model: str = "claude-3-haiku"
# ) -> str:
#     if text is None or (isinstance(text, str) and len(text.lstrip()) == 0):
#         return ""

#     sys_preset = sys_preset.replace("{{language}}", Language.load_desc(language))
#     text = role_fill_preset.USER_TEMPLATE.replace("{{input_content}}", text)
#     ai_prefix = role_fill_preset.AI_PREFIX
#     input_message = []
#     input_message.append(SystemMessage(content=sys_preset))
#     input_message.append(HumanMessage(content=text))
#     input_message.append(AIMessage(content=ai_prefix))
#     response = inner_llm.invoke(
#         model=model,
#         input=input_message,
#         extra_body=chat_util.translate_request_extra_body(),
#     ).content
#     regex = f"<response>(.*?)</response>"
#     match = re.search(regex, response, re.DOTALL)
#     search_ret = ""
#     if match:
#         search_ret = match.group(1)
#     ret = search_ret.replace("\\n", "\n")
#     log.info("AutoIntro preset:\n%s,\nret:\n%s", text, ret)
#     return ret


@exception_util.async_ignore_catch_exception
async def run_task(
    llm_model: str,
    system_message: str,
    user_message: str,
    ai_message: str,
    max_tokens: int = 200,
) -> str:

    input_messages = [
        ChatCompletionSystemMessage(content=system_message, role="system"),
        ChatCompletionUserMessage(content=user_message, role="user"),
    ]
    if ai_message:
        input_messages.append(
            ChatCompletionAssistantMessage(content=ai_message, role="assistant")
        )
    response = await acompletion(
        base_url=TEST_BASE_URL,
        timeout=60,
        api_key=os.environ.get("litellm_key", ""),
        model=f"litellm_proxy/{llm_model}",
        messages=input_messages,
        max_tokens=max_tokens,
        stream=True,
    )
    # response不是CustomStreamWrapper类型，直接返回
    if not isinstance(response, CustomStreamWrapper):
        log.error(
            f"run_task_by_stream response is not CustomStreamWrapper, response type: {type(response)}"
        )
        raise exception_util.param_error(
            f"response is not CustomStreamWrapper, response type: {type(response)}"
        )
    result = await message_utils.process_iterator_content(response)  # type: ignore
    log.info(f"llm_model:{llm_model}, response:{result}")
    
    # response不是CustomStreamWrapper类型，直接返回
    # if not isinstance(response, ModelResponse):
    #     raise exception_util.param_error(
    #         f"response is not CustomStreamWrapper, response type: {type(response)}"
    #     )
    # if not response.choices or not response.choices[0].message:  # type: ignore
    #     return ""
    # res = str(response.choices[0].message.content)  # type: ignore
    # extra = response.model_extra
    # log.info(f"llm_model:{llm_model}, response:{res}, extra:{extra}")
    return result
