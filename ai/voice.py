import base64
import logging
import os
import random
import uuid
import requests
import json
import re
import yaml
from io import BytesIO
from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client
from dotenv import load_dotenv

from common.common_constant import VoiceContentType
from common.entity import Speaker
from persistence import chat_history_dao
from persistence.models.models import User, VoiceHistory, VoiceSpeaker
from persistence.chat_history import chat_history_persistence
from services import user_role_service
from services.role import role_loader_service
from services.role_config_service import RoleConfigService
from services.voice_speaker_service import VoiceSpeakerService

load_dotenv()

log = logging.getLogger(__name__)

region = "ap-singapore"
config = CosConfig(
    Region=region,
    SecretId=os.environ["COS_SECRET_ID"],
    SecretKey=os.environ["COS_SECRET_KEY"],
    Token=None,
    Scheme="https",
)
cos_client = CosS3Client(config)
bucket = "chat-voice-**********"

MAX_AGE = 7 * 24 * 60 * 60  # s

speakers = {}
with open("speakers.yaml", "r", encoding="utf-8") as f:
    ss = yaml.safe_load(f)
    for k, v in ss.items():
        speaker = Speaker(k, v["url"], v["speed"])
        speakers[k] = speaker


def replace_punctuation(text: str) -> str:
    text = re.sub(r"[.]{3,6}", "……", text)
    eng_punctuation = """!"#$%&'()*+,-./:;<=>?@[\\]^_`{|}"""
    chn_punctuation = "！“＃￥％＆’（）＊＋，－。／：；＜＝＞？＠［＼］＾＿｀｛｜｝"
    trans_table = str.maketrans(eng_punctuation, chn_punctuation)
    # 将所有的英文标点替换为中文标点
    text = text.translate(trans_table)

    # 中文符号只保留白名单的: '·~，。！？—…《》'
    forbidden_punctuations = "；／％［｀｛￥～＋＜＿＝＆｜｝”＞＃：＠＾（）’“＊－］＼"
    text = text.translate(str.maketrans("", "", forbidden_punctuations))
    return re.sub(r"([·~，。！？—…《》])\1+", r"\1", text)


def get_voice(text: str, speaker: VoiceSpeaker | None = None) -> str:
    if speaker is None:
        speaker = VoiceSpeaker(
            speaker_id="",
            api_url="http://43.156.173.20:9000/tts/t2s_v2_bin",
            speed=1.0,
            name="default",
            sample_url="",
        )
    text = replace_punctuation(text)
    text = text.replace("\n", " ")
    data = {"text": text, "speaker_id": speaker.speaker_id, "speed": speaker.speed}
    response = requests.post(speaker.api_url, data=json.dumps(data), timeout=10)
    result = json.loads(response.content)
    return result["data"]

def get_voice_fin(text: str, speaker: VoiceSpeaker) -> bytes:
    text = replace_punctuation(text)
    text = text.replace("\n", " ")
    data = {"text": text, 
            "use_memory_cache": "on",
            "reference_id": speaker.speaker_id,
            "streaming": False}
    response = requests.post(speaker.api_url, json=data, timeout=10)
    response.raise_for_status()
    return response.content

def voice_2_txt(voice: bytes) -> str | None:
    asr_url = os.environ.get("ASR_URL")
    voice_file = {"audio": ("voice.wav", BytesIO(voice))}
    response = requests.post(asr_url, files=voice_file, timeout=10)
    if not response.ok:
        return None
    result = json.loads(response.content)
    return result["text"]


def upload_voice(body: str) -> str:
    body_bytes = base64.b64decode(body)
    file_id = uuid.uuid4().hex
    result = cos_client.upload_file_from_buffer(
        bucket, f"{file_id}.mp3", Body=BytesIO(body_bytes), CacheControl=f"max-age={MAX_AGE}"
    )
    log.info(f"upload voice file DONE: {result}")
    return file_id

def upload_voice_binary(body_bytes: bytes) -> str:
    file_id = uuid.uuid4().hex
    result = cos_client.upload_file_from_buffer(
        bucket, f"{file_id}.mp3", Body=BytesIO(body_bytes), CacheControl=f"max-age={MAX_AGE}"
    )
    log.info(f"upload voice file DONE: {result}")
    return file_id

async def generate_voice(message) -> str:
    role_id = message["role_id"]
    role_config = await RoleConfigService.get_role_config(role_id)
    if not role_config:
        return ""
    speaker = await VoiceSpeakerService.get_speaker_by_id(role_config.speaker_id)

    content: str = message["content"]
    # 0. remove status line
    s_index = content.find("<status>")
    if s_index != -1:
        content = content[:s_index]

    # 1. remove xml tags in response
    # content = re.sub(r'<[^>]+>', '', content, flags=re.MULTILINE|re.DOTALL)
    match = re.search(r"<rep>(.*?)</rep>", content, flags=re.MULTILINE | re.DOTALL)
    if match:
        content = match.group(1)

    content = re.sub(r"<.*?>.*?</.*?>", "", content, flags=re.MULTILINE | re.DOTALL)

    # 2. remove brackets in response
    message_no_quotes = re.sub(
        r"[\(（](.+?)[\)）]", "", content, flags=re.MULTILINE | re.DOTALL
    ).strip()

    # 3. remove role name in response
    if message_no_quotes.startswith(role_config.role_name + ":"):
        message_no_quotes = message_no_quotes[len(role_config.role_name) + 1 :]
    if message_no_quotes.startswith(role_config.role_name + "："):
        message_no_quotes = message_no_quotes[len(role_config.role_name) + 1 :]

    log.info(f"tts role_message: {message_no_quotes}")
    voice = get_voice("".join(message_no_quotes), speaker)
    voice_url_id = upload_voice(voice)
    # voice_url = f'https://chat-voice-**********.cos.ap-singapore.myqcloud.com/{voice_url_id}.mp3'

    # 使用全球加速域名加速访问
    voice_url = f"https://chat-voice.198432.xyz/{voice_url_id}.mp3"

    await chat_history_persistence.save_message_voice(message["_id"], voice_url)
    # TODO 语音限时免费
    # await AccountService.create_pay_order(user_id, TTS_PRODUCT)
    return voice_url


async def generate_voice_new(user: User, message_id: str, version: int) -> str:
    chat_history = await chat_history_dao.get_by_message_id_and_version(
        message_id, version
    )
    user_id = user.id
    if not chat_history or chat_history.user_id != user_id:
        return ""
    if chat_history.voice_url:
        return chat_history.voice_url

    role_id = chat_history.role_id
    role_config = await role_loader_service.get_by_id(role_id)
    user_role_config = await user_role_service.get_user_role_config(user_id, role_id)
    if user_role_config and user_role_config.speaker_id:
        speaker_id = user_role_config.speaker_id
    else: 
        speaker_id = role_config.speaker_id
    speaker: VoiceSpeaker = await VoiceSpeakerService.get_speaker_by_id(speaker_id) # type: ignore

    content = chat_history.content
    # 0. remove status line
    s_index = content.find("<status>")
    if s_index != -1:
        content = content[:s_index]

    # 1. remove xml tags in response
    # content = re.sub(r'<[^>]+>', '', content, flags=re.MULTILINE|re.DOTALL)
    match = re.search(r"<rep>(.*?)</rep>", content, flags=re.MULTILINE | re.DOTALL)
    if match:
        content = match.group(1)

    content = re.sub(r"<.*?>.*?</.*?>", "", content, flags=re.MULTILINE | re.DOTALL)
    content = content.strip()
    
    # 2. remove role name in response
    if content.startswith(role_config.role_name + ":"):
        content = content[len(role_config.role_name) + 1 :]
    if content.startswith(role_config.role_name + "："):
        content = content[len(role_config.role_name) + 1 :]

    if user.voice_content_type == VoiceContentType.ALL:
        final_content = content
    else:
        # 3. remove brackets in response
        message_no_quotes = re.sub(
            r"[\(（](.+?)[\)）]", "", content, flags=re.MULTILINE | re.DOTALL
        ).strip()
        if len(content) == len(message_no_quotes):
            #正则获取返回所有单引号或者双引号内的内容
            message_no_quotes = re.findall(r'["\']([^"\']*)["\']', content)
            message_no_quotes = "".join(message_no_quotes)
            
        log.info(
            f"tts message_id:{message_id},len:{len(message_no_quotes)},message: {message_no_quotes}"
        )
        if not message_no_quotes:
            log.warning(
                f"tts message empty:{message_id},original message: {message_no_quotes}"
            )
            message_no_quotes = content
        final_content = message_no_quotes

    if speaker.speaker_id.startswith('5'):
        voice = get_voice_fin(final_content, speaker)
        voice_url_id = upload_voice_binary(voice)
    else:
        voice = get_voice(final_content, speaker)
        voice_url_id = upload_voice(voice)
    # 使用全球加速域名加速访问
    voice_url = f"https://chat-voice.198432.xyz/{voice_url_id}.mp3"
    await chat_history_dao.update_voice_url_by_message_id_and_version(
        message_id, version, voice_url
    )
    await VoiceSpeakerService.add_voice_history(VoiceHistory(
        user_id=user_id, 
        role_id=role_id, 
        message_id=message_id, 
        speaker_id=speaker.speaker_id, 
        conversation_id=chat_history.conversation_id,
        message_version=version, 
        message_content=content,
        voice_message_content=final_content,
        voice_url=voice_url))
    # TODO 语音限时免费
    # await AccountService.create_pay_order(user_id, TTS_PRODUCT)
    return voice_url
