TRANS_PROMPT = """你需要扮演一个专业的语言学家，从一段中文的文本描述中，抽取场景和女性的描述类的关键短语，并翻译为英文。

下面是一个示例

--INPUT:
马军脸上一烫，赶紧闭上嘴巴，其实刚刚他只是想摸一摸女老师的丰硕豪乳，可是宋成英却抓住了他的手，而且还把他的手压在自己的丰硕豪乳上，这样他的手就可以隔着衣服抚摸女老师的乳房了，可是这个姿势让他的那根东西又勃起了，裤裆里那根东西顿时又硬硬的挺立着着。

--OUTPUT:
female teacher, voluptuous breasts, touch breasts through the clothes

请对下面这段文本进行抽取和翻译

--INPUT:
{input}

--OUTPUT:
"""

CHAT_PROMPT = """{s_profile}
下面是对话场景
{s_context}
{s_history}

接下来的对话:
{msg}

{bot_name}:
"""

CHARACTER_SETTING_PROMPT = '''
You are a famous fiction writer GPT.
Your task is to generate character setting based on the given context.
The given context relates to a role-playing game where one character is already in place. 

- Keep the output simple, up to 64 chars.
- DO NOT include the role's name.
- Always output in Chinese.
- The role must always be male.
- The role's age must always above 16.
Context:
{context}
'''

HISTORY_SUMMARY='''
请摘要以下对话和旁白，简化内容同时保留关键的人物、事件和信息。确保突出角色间的关系、重要决策点以及对话和旁白中的核心情感或主题。

目标是在保留尽可能多的信息的同时，将原始文本简化到200字以内。
只对对话中的事实进行摘要，不需要总结。
使用最简单的语言来描述，减少不必要的形容词。
以旁白的口吻使用中文输出。
以下是对话的内容：
{history}
'''

DESC_TO_INTRO='''你需要扮演一个专业的语言学家，从一段复杂的文本描述中，生成一个角色的简介。这段描述来自一个角色扮演游戏，是一个角色的详细介绍，包括外貌、性格、背景等信息，请从中生成一段200字以内简要的说明。
以下是描述信息：
{description}
'''