module.exports = {
    apps: [{
        name: 'aibot-app-server',
        script: 'uvicorn',
        args: 'aibot_app_server:app --host 0.0.0.0 --port 9200 --workers 1',
        interpreter: '/root/micromamba/envs/tavern-server/bin/python',
        error_file: "/data/logs/pm2/aibot-app-server/err.log",
        out_file: "/data/logs/pm2/aibot-app-server/out.log",
    },
    {
        name: 'aibot-task-server',
        script: 'uvicorn',
        args: 'aibot_task_server:app --host 0.0.0.0 --port 9220 --workers 1',
        interpreter: '/root/micromamba/envs/tavern-server/bin/python',
        error_file: "/data/logs/pm2/aibot-task-server/err.log",
        out_file: "/data/logs/pm2/aibot-task-server/out.log",
    },
    {
        name: 'aibot-app-admin-server',
        script: 'uvicorn',
        args: 'aibot_app_admin_server:app --host 0.0.0.0 --port 9230 --workers 1',
        interpreter: '/root/micromamba/envs/tavern-server/bin/python',
        error_file: "/data/logs/pm2/aibot-app-admin-server/err.log",
        out_file: "/data/logs/pm2/aibot-app-admin-server/out.log",
    },
    {
        name: 'image-bot-server',
        script: 'uvicorn',
        args: 'image_bot_app_server:app --host 0.0.0.0 --port 9240 --workers 1',
        interpreter: '/root/micromamba/envs/tavern-server/bin/python',
        error_file: "/data/logs/pm2/image-bot-server/err.log",
        out_file: "/data/logs/pm2/image-bot-server/out.log",
    }
    ]
}
