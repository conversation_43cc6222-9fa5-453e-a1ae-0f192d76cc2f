---


---
幻梦Ai-OpenApi-1.0

Base URLs:（管理员申请获取）

# Authentication

1、管理员申请：client_id、client_secret**

2、请求参数增加client_id与时间戳，参数进行排序，使用 HMAC-SHA256 算法和 client_secret 对字符串进行签名，获得签名数据

参考代码示例：

```python
# 配置信息
CLIENT_ID = "your_client_id"
CLIENT_SECRET = "your_client_secret"
def generate_signature(
	params: Optional[Dict] = None, 
	body: Optional[Union[Dict, str]] = None,
	client_id: str = CLIENT_ID,
	client_secret: str = CLIENT_SECRET
) -> Dict:
    # 初始化参数字典
    params = params or {}
    params = params.copy()  # 避免修改原始参数
  
    # 添加公共参数
    params['timestamp'] = str(int(time.time()))
    params['client_id'] = client_id
  
    # 处理请求体
    body_str = ""
    if body is not None:
        if isinstance(body, dict):
            body_str = json.dumps(body, sort_keys=True) 
        else:
            body_str = str(body)
  
    # 参数排序和拼接
    params = {k: str(v) for k, v in params.items()}  # 确保所有值都是字符串
    sorted_params = sorted(params.items(), key=lambda x: x[0])
    params_string = '&'.join([f"{k}={urllib.parse.quote(str(v))}" for k, v in sorted_params])
  
    # 组合待签名字符串
    string_to_sign = params_string
    if body_str:
        string_to_sign = f"{params_string}&body={urllib.parse.quote(body_str)}"
  
    # 生成签名
    hmac_obj = hmac.new(
        client_secret.encode('utf-8'),
        string_to_sign.encode('utf-8'),
        hashlib.sha256
    )
    return hmac_obj.hexdigest()
```

# API list

## GET History

GET /open/api/chat/history

### 请求参数

| 名称            | 位置   | 类型    | 必选 | 中文名     | 说明 |
| --------------- | ------ | ------- | ---- | ---------- | ---- |
| authorization   | header | string  | 是   | 签名信息   | none |
| role_id         | query  | integer | 是   | 角色ID     | none |
| conversation_id | query  | string  | 是   | 回合ID     | none |
| timestamp       | query  | integer | 是   | Tg-Bot-Id  | none |
| open_user_id    | query  | string  | 是   | 三方用户ID | none |
| client_id       | query  | string  | 是   | 合作方ID   | none |

> 返回示例

> 200 Response

```json
{
  "code":0,
  "data":{
	"chat_list":[
	  "content":"",
	  "type":"ai or user",
	  "message_id":"xxxxxx"
	],
	"conversation_id":"xxxxxx"
  }
}
```

### 返回结果

| 状态码 | 状态码含义                                                            | 说明                | 数据模型                                       |
| ------ | --------------------------------------------------------------------- | ------------------- | ---------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)                  | Successful Response | string                                         |
| 422    | [Unprocessable Entity](https://tools.ietf.org/html/rfc2518#section-10.3) | Validation Error    | [HTTPValidationError](#schemahttpvalidationerror) |

`<a id="opIdchat_new_chat_replay_post"></a>`

## POST Chat

POST /open/api/chat/replay

> Body 请求参数

```json
{
  “open_user_id":"xxxxx",
  "client_id":"xxxxxx",
  "role_id": 0,
  "message": "",
  "conversation_id": "", #新回合不需要传
  “new_start":1, # 1代表新回合
  "timestamp":0
}
```

### 请求参数

| 名称          | 位置   | 类型                           | 必选 | 中文名      | 说明 |
| ------------- | ------ | ------------------------------ | ---- | ----------- | ---- |
| authorization | header | string                         | 是   | 签名信息    |      |
| body          | body   | [ChatRequest](#schemachatrequest) | 是   | ChatRequest | none |

> 返回示例

> 200 Response

```json
{
 	"code":0,
	"data":{
		"content":"xxxxxx",
		"message_id":"xxxxxx",
		"conversation_id":"xxxxxx"
	}
}
```

### 返回结果

| 状态码 | 状态码含义                                                            | 说明                | 数据模型                                       |
| ------ | --------------------------------------------------------------------- | ------------------- | ---------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)                  | Successful Response | string                                         |
| 422    | [Unprocessable Entity](https://tools.ietf.org/html/rfc2518#section-10.3) | Validation Error    | [HTTPValidationError](#schemahttpvalidationerror) |
