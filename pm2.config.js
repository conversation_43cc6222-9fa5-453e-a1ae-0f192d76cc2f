module.exports = {
    apps: [{
        name: 'tavern-server',
        script: 'uvicorn',
        args: 'app_server:app --host 0.0.0.0 --port 8800 --workers 4',
        interpreter: '.venv/bin/python',
        error_file: "/data/logs/pm2/tavern-server/err.log",
        out_file: "/data/logs/pm2/tavern-server/out.log",
    },
    {
        name: 'tavern-admin-api',
        script: 'uvicorn',
        args: 'admin_server:app --host 0.0.0.0 --port 8900 --workers 2',
        watch: '.',
        interpreter: '.venv/bin/python',
        error_file: "/data/logs/pm2/tavern-admin-api/err.log",
        out_file: "/data/logs/pm2/tavern-admin-api/out.log",
    },
    {
        name: 'task-server',
        script: 'uvicorn',
        args: 'task_server:app --host 0.0.0.0 --port 8901',
        watch: '.',
        interpreter: '/root/micromamba/envs/tavern-server/bin/python',
        error_file: "/data/logs/pm2/task-server/err.log",
        out_file: "/data/logs/pm2/task-server/out.log",
    }, {
        name: 'tavern-server-preview',
        script: 'uvicorn',
        args: 'app_server:app --host 0.0.0.0 --port 18800 --workers 1',
        watch: '.',
        interpreter: '/root/micromamba/envs/tavern-server/bin/python',
        error_file: "/data/logs/pm2/tavern-server-preview/err.log",
        out_file: "/data/logs/pm2/tavern-server-preview/out.log",
    }, {
        name: 'tavern-admin-api-preview',
        script: 'uvicorn',
        args: 'admin_server:app --host 0.0.0.0 --port 18900 --workers 1',
        watch: '.',
        interpreter: '/root/micromamba/envs/tavern-server/bin/python',
        error_file: "/data/logs/pm2/tavern-admin-api-preview/err.log",
        out_file: "/data/logs/pm2/tavern-admin-api-preview/out.log",
    },
    {
        name: 'group-helper-webhook-server',
        script: 'uvicorn',
        args: 'gh_webhook_server:app --host 0.0.0.0 --port 8903 --workers 4',
        watch: '.',
        interpreter: '/root/micromamba/envs/tavern-server/bin/python',
        error_file: "/data/logs/pm2/group-helper-webhook-server/err.log",
        out_file: "/data/logs/pm2/group-helper-webhook-server/out.log",
    }, 
    {
        name: 'statistic-server',
        script: 'uvicorn',
        args: 'statistic_server:app --host 0.0.0.0 --port 8904 --workers 1',
        watch: '.',
        interpreter: '/root/micromamba/envs/tavern-server/bin/python',
        error_file: "/data/logs/pm2/statistic-server/err.log",
        out_file: "/data/logs/pm2/statistic-server/out.log",
    }
    ]
}
