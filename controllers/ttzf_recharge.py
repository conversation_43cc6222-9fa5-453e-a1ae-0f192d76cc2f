import logging
import os
import hashlib
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi import APIRouter, Depends, Request, Response
from pydantic import BaseModel
from controllers.user_check import get_current_user
from persistence.models.models import RechargeProduct

from controllers.user_check import user_service
from utils import env_const, response_util
from services import re_purchase_service, user_growth_service, ttzf_recharge_service

cwd = os.path.dirname(os.path.realpath(__file__))

with open(os.path.join(cwd, 'success.html'), 'r') as f:
    success_html = f.read()

ttzf_recharge_router = APIRouter()

class TTZFNotify(BaseModel):
    mchId: str
    tradeNo: str
    outTradeNo: str
    amount: int
    subject: str
    state: int
    notifyTime: int
    originTradeNo: str | None = None
    sign: str

    def verify_sign(self, app_key: str) -> bool:
        params = self.model_dump(exclude_unset=True, exclude={'sign'})
        params = [(k, str(v)) for k, v in params.items() if v is not None and v != '']
        sorted_params = sorted(params, key=lambda x: x[0])
        sign_str = '&'.join(f'{k}={v}' for k, v in sorted_params) + f'&key={app_key}'
        sign = hashlib.md5(sign_str.encode()).hexdigest()
        return sign.upper() == self.sign.strip().upper()

async def handle_result(notify_result: TTZFNotify):
    if not notify_result.verify_sign(env_const.TTZF_APP_KEY):
        return 'verify sign failed'
    if notify_result.state != 1:
        return 'FAIL'
    order = await ttzf_recharge_service.pay_success(notify_result.outTradeNo, notify_result.tradeNo, notify_result.model_dump_json())
    user = await user_service.get_user_by_id(order.user_id)
    await user_growth_service.add_fc_reward(user)
    await re_purchase_service.after_recharge(user, order)
    return 'success'

@ttzf_recharge_router.get('/ttzf/notify')
async def ttzf_recharge_notify(req: Request):
    data = req.query_params
    logging.info(f'ttzf_recharge_notify: {data}')
    notify_result = TTZFNotify(**data)
    r = await handle_result(notify_result)
    return Response(r, media_type="text/plain")

@ttzf_recharge_router.post('/ttzf/notify')
async def ttzf_recharge_notify_p(req: Request):
    body = await req.json()
    logging.info(f'ttzf_recharge_notify: {body}')
    notify_result = TTZFNotify(**body)
    r = await handle_result(notify_result)
    return Response(r, media_type="text/plain")
