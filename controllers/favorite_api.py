import logging
from fastapi import Form, Head<PERSON>, Depends, APIRouter, Response
from fastapi.responses import JSONResponse
from controllers.user_check import get_current_user, user_service
from common.common_constant import (
    ChatModeType,
    Language,
)
from services import (
    user_role_service,
)
from services.role import role_group_service, role_loader_service
from utils import (
    response_util,
)

favorite_api_router = APIRouter()

log = logging.getLogger(__name__)

UNAUTHORIZED = JSONResponse(status_code=401, content={"message": "Unauthorized"})


@favorite_api_router.get("/user/favorite/roles")
async def get_favorite_roles(
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    user = await user_service.get_user_by_id(user_id)
    ret = await user_role_service.favorite_roles(user, current_language)
    return response_util.ok({"list": ret})


@favorite_api_router.post("/user/favorite/delete")
async def delete_favorite_role(
    mode_type: ChatModeType,
    mode_target_id: int,
    user_id: int = Depends(get_current_user),
):
    user = await user_service.get_user_by_id(user_id)
    await user_role_service.del_favorite_role(user, mode_type.value, mode_target_id)
    return response_util.ok()


@favorite_api_router.post("/user/favorite/add")
async def add_favorite_role(
    mode_type: ChatModeType,
    mode_target_id: int,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    if mode_type == ChatModeType.GROUP:
        group = await role_group_service.load_detail_by_id(mode_target_id)
        if not group:
            return response_util.json_param_error("group not found")
    if mode_type == ChatModeType.SINGLE:
        role = await role_loader_service.get_by_id(mode_target_id)
        if not role or not role.status or not role.privacy:
            return response_util.json_param_error("role not found, or privacy")
    user = await user_service.get_user_by_id(user_id)
    await user_role_service.add_favorite_role(user, mode_type.value, mode_target_id)
    list = await user_role_service.favorite_roles(user, current_language)
    return response_util.ok({"list": list})
