from fastapi import (
    Depends,
    <PERSON><PERSON><PERSON><PERSON>,
    Header,
)
from pydantic import BaseModel
from common.common_constant import (
    Language,
)
from common.models.common_res_model import SpeakerRes
from services import (
    user_role_service,
    role_config_service,
)
from services.voice_speaker_service import VoiceSpeakerService
from utils import response_util
from utils.translate_util import _tl
from .user_check import get_current_user

user_role_config_router = APIRouter()

class UserRoleConfigDto(BaseModel):
    role_id: int
    speaker_id: str

class UserRoleConfigResponse(BaseModel):
    speaker_list: list[SpeakerRes]
    role_speaker_list: list[UserRoleConfigDto]

class UserRoleConfigRequest(BaseModel):
    role_id: int
    speaker_id: str

@user_role_config_router.get("/user_role_config")
async def get_user_role_config(
    role_ids: str,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value)):

    speaker_list = await VoiceSpeakerService.get_active_speakers()
    speaker_list.sort(key=lambda x: x.order)
    speaker_list = (
        [SpeakerRes.from_model(speaker) for speaker in speaker_list]
        if speaker_list
        else []
    )
    for speaker in speaker_list:
        speaker.speaker_name = _tl(speaker.speaker_name,current_language)

    if not role_ids:
        return response_util.ok(UserRoleConfigResponse(
            speaker_list=speaker_list,
            role_speaker_list=[]
        ))

    role_ids = [int(role_id) for role_id in role_ids.split(",")]
    user_role_configs = await user_role_service.get_user_role_configs(user_id, role_ids)

    exists_role_ids = [config.role_id for config in user_role_configs]
    non_exits_roles = [role_id for role_id in role_ids if role_id not in exists_role_ids]
    role_configs = await role_config_service.get_role_config_by_ids(non_exits_roles)

    user_role_config_list = [UserRoleConfigDto(role_id=config.id, speaker_id=config.speaker_id) for config in role_configs]
    user_role_config_list.extend([UserRoleConfigDto(role_id=config.role_id, speaker_id=config.speaker_id) for config in user_role_configs])

    return response_util.ok(UserRoleConfigResponse(
        speaker_list=speaker_list,
        role_speaker_list=user_role_config_list
    ))

@user_role_config_router.post("/user_role_config/update")
async def update_user_role_config(
    request: UserRoleConfigRequest,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value)):
    speaker = await VoiceSpeakerService.get_speaker_by_id(request.speaker_id)
    if not speaker:
        return response_util.error(400, _tl("speaker not found", current_language))
    config = await user_role_service.set_user_role_config(user_id, request.role_id, request.speaker_id)
    return response_util.ok(config)