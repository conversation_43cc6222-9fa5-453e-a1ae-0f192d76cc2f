import json
import logging
import os
from typing import Annotated, Optional
import uuid
from fastapi import (
    Depends,
    APIRouter,
    Form,
    HTTPException,
    Header,
    Response,
    UploadFile,
)
from fastapi.responses import JSONResponse, RedirectResponse
from fastapi.security import OAuth2<PERSON>ass<PERSON><PERSON>earer
import requests

from pydantic import BaseModel, constr, <PERSON>
from datetime import datetime, timedelta, UTC
from fastapi import Depends, HTTPException

from common.bot_common import MessageTemplate
from common.common_constant import (
    ERROR_CODE,
    OFFICIAL_BACKGROUND,
    ApiSource,
    CosPrefix,
    ErrorKey,
    Language,
    ProductPermission,
    ProductType,
    RoleFilterTag,
    RoleLevelType,
    VoiceContentType,
)
from common.role_model import ProductResponse
from controllers.request_depends import dep_api_source, dep_language, dep_register_source
from persistence.models.models import PersonalBg, RegexOption, User, UserRegisterSource
from services import (
    bot_message_service,
    product_service,
    regex_service,
    role_access_service,
    tag_service,
    translate_service,
    user_auth_service,
    user_role_service,
)
from services import user_service
from services.role import role_verify_service
from services.user_service import UserService
from services.role_config_service import RoleConfigService
from services.voice_speaker_service import VoiceSpeakerService
from utils import cos_util, env_util, ims_util, json_util, response_util, str_util
from utils.translate_util import _tl
from .user_check import get_current_user, create_access_token
from common.entity import RegexRuleResponse, UserRegexRuleResponse
from common.models.common_res_model import SpeakerRes

SECRET_KEY = "b'g5gFjPqzsi0V4sNUO9q5GyvYb1uiKvZ0'"
ALGORITHM = "HS256"

log = logging.getLogger(__name__)

PasswordStr = constr(min_length=6, max_length=20)

login_router = APIRouter()
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# user_service = UserService()
role_service = RoleConfigService()

GOOGLE_CLIENT_ID = os.environ.get("GOOGLE_CLIENT_ID")
GOOGLE_CLIENT_SECRET = os.environ.get("GOOGLE_CLIENT_SECRET")
GOOGLE_REDIRECT_URI = os.environ.get("GOOGLE_REDIRECT_URI")
GOOGLE_POST_LOGIN_REDIRECT_URI = os.environ.get("GOOGLE_POST_LOGIN_REDIRECT_URI")


class UserIn(BaseModel):
    email: str = Field(None, description="User email")
    password: str = Field(None, min_length=4, max_length=20)
    id_token: str = Field(None, description="FileBase OAuth2 id_token")


class UserTokenIn(BaseModel):
    id_token: str = Field(None, description="FileBase OAuth2 id_token")


class LoginRes(BaseModel):
    id: int
    email: str
    nickname: str
    avatar: str = ""
    msg: str = "Login successful"
class UserSettings(BaseModel):
    nickname: Optional[str] = None
    model: Optional[str] = None
    enable_nsfw: Optional[bool] = None
    show_nsfw: Optional[bool] = None
    show_nsfw_image: Optional[bool] = None
    show_chat_tips: Optional[bool] = None
    # 使用个人通用聊天背景
    use_personal_bg: Optional[bool] = None
    image_bgs: Optional[list[str]] = None
    selected_bg_index: Optional[int] = None
    opacity: Optional[int] = None
    voice_content_type: Optional[str] = None
    # 模型通道类型
    chat_channel: Optional[str] = None
    status_block_switch: Optional[bool] = None
    language: Optional[str] = None



@login_router.get("/login/google")
async def login_google(source: str = Depends(dep_register_source)):
    redirect_uri = env_util.get_google_redirect_url(source)

    return RedirectResponse(
        f"https://accounts.google.com/o/oauth2/v2/auth?response_type=code&client_id={GOOGLE_CLIENT_ID}&redirect_uri={redirect_uri}&scope=openid%20profile%20email&access_type=offline"
    )


@login_router.post("/auth/token")
async def auth_firebase(
    response: Response,
    user_token: UserTokenIn,
    source: str = Depends(dep_register_source),
):
    user = await user_auth_service.login_or_register_by_firebase(
        user_token.id_token, UserRegisterSource(source)
    )
    if user:
        await user_auth_service.add_login_token(response, user)
        return LoginRes(
            id=user.id,
            email=user.email,
            nickname=user.nickname,
            avatar=user.avatar,
        )
    return response_util.json_param_error("Invalid id_token")


@login_router.get("/auth/google")
async def auth_google(code: str, source: str = Depends(dep_register_source)):
    token_url = "https://accounts.google.com/o/oauth2/token"
    redirect_uri = env_util.get_google_redirect_url(source)
    data = {
        "code": code,
        "client_id": GOOGLE_CLIENT_ID,
        "client_secret": GOOGLE_CLIENT_SECRET,
        "redirect_uri": redirect_uri,
        "grant_type": "authorization_code",
    }
    response = requests.post(token_url, data=data)
    access_token = response.json().get("access_token")
    user_info = requests.get(
        "https://www.googleapis.com/oauth2/v1/userinfo",
        headers={"Authorization": f"Bearer {access_token}"},
    )
    user_info.raise_for_status()

    # user_info.json()返回的数据格式如下：
    # {
    # "id": "104432414592200044437",
    # "email": "<EMAIL>",
    # "verified_email": true,
    # "name": "footfish",
    # "given_name": "footfish",
    # "picture": "https://lh3.googleusercontent.com/a/ACg8ocL-Xf6Yv6aHMDAFNFPTTNQZy4f7plKs_S8EfAycJHdOOZQFYg=s96-c",
    # "locale": "zh-CN"
    # }

    print(user_info.json())
    user_info = user_info.json()
    if user_info["verified_email"] == False:
        raise HTTPException(status_code=400, detail="Email not verified")
    else:
        redirect_uri = env_util.get_google_post_login_redirect_uri(source)
        user = await user_auth_service.gmail_login(
            user_info["email"], user_info["name"], user_info, source
        )

        if not user:
            raise HTTPException(status_code=400, detail="Failed to authenticate with Google")

        access_token_expires = timedelta(days=30)
        access_token = create_access_token(
            data={"sub": str(user.id)}, expires_delta=access_token_expires
        )
        res = RedirectResponse(f"{redirect_uri}?from=oauth")
        res.set_cookie(
            "token",
            access_token,
            samesite="none",
            secure=True,
            expires=datetime.now(UTC) + timedelta(days=30),
        )
        return res


@login_router.post("/user/register")
async def register(user_in: UserIn) -> LoginRes:
    if os.environ.get("TG_USER_ONLY", False):
        raise HTTPException(status_code=400, detail="Registration is disabled")
    user = await user_service.register(user_in.email, user_in.password)
    if user:
        return LoginRes(
            id=user.id,
            email=user.email,
            nickname=user.nickname,
            msg="User registered successfully",
        )
    else:
        raise HTTPException(status_code=400, detail="Registration failed")


@login_router.post("/user/login")
async def login(response: Response, user_in: UserIn):
    user = await user_service.login(user_in.email, user_in.password)
    if user:
        access_token_expires = timedelta(minutes=3600)
        access_token = create_access_token(
            data={"sub": str(user.id)}, expires_delta=access_token_expires
        )
        response.set_cookie(
            "token",
            access_token,
            samesite="none",
            secure=True,
            expires=datetime.now(UTC) + timedelta(days=30),
        )
        return LoginRes(
            id=user.id,
            email=user.email,
            nickname=user.nickname,
            avatar=user.avatar,
        )
    else:
        return JSONResponse(
            content={"msg": "Invalid email or password"}, status_code=400
        )


@login_router.get("/user/me")
async def get_current_user_info(
    language: str = Depends(dep_language),
    user_id: int = Depends(get_current_user),
    api_source: ApiSource = Depends(dep_api_source),
):
    user = await user_service.get_user_by_id(user_id)

    return await user_role_service.get_user_detail_info(user, language, api_source)


# 获取其他用户的昵称和头像
@login_router.get("/user/other")
async def get_other_user_info(
    other_user_id: int,
    language: str = Depends(dep_language),
    user_id: int = Depends(get_current_user),
):
    if other_user_id <= 0:
        return response_util.ok()
    user = await user_service.safe_get_user(other_user_id)
    if not user:
        return response_util.ok()

    return response_util.ok(
        {
            "id": user.id,
            "nickname": user.nickname,
            "avatar": str_util.format_avatar(user.avatar),
        }
    )


@login_router.get("/user/logout")
async def logout():
    response = JSONResponse(content={"msg": "Logout successful"})
    response.set_cookie("token", "", expires=0, samesite="none", secure=True)
    return response


@login_router.post("/setting")
async def update_user_setting(
    setting: Annotated[str, Form()],
    avatar_img: Optional[UploadFile] = None,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
    api_source: ApiSource = Depends(dep_api_source),
):
    log.info(f"update_user_setting, user_id: {user_id}, setting: {setting}")
    user = await user_service.get_user_by_id(user_id)
    setting_json = json.loads(setting)
    user_setting = UserSettings(**setting_json)
    # 昵称和头像需要通过 user_alt_profile_api 来修改
    # if user_setting.nickname:
    #     user.nickname = user_setting.nickname
    # if avatar_img:
    #     user.avatar = cos_util.upload_image(avatar_img, CosPrefix.AVATAR)
    if user_setting.model:
        model_id = user_setting.model
        product = await product_service.get_original_chat_product_by_mid(model_id)
        if not product:
            return response_util.def_error("Model not found")
        if not await role_access_service.allow_model_by_product(product, user):
            error_message = _tl(ErrorKey.MODEL_ONLY_FOR_PAID_USER.message(), current_language)
            return response_util.error(ERROR_CODE.UN_SUPPORT_MODEL.value, error_message)
        user.chat_product_mid = product.mid
    if user_setting.enable_nsfw is not None:
        user.enable_nsfw = user_setting.enable_nsfw
    if user_setting.show_nsfw is not None:
        user.show_nsfw = user_setting.show_nsfw
    if user_setting.show_nsfw_image is not None:
        user.show_nsfw_image = user_setting.show_nsfw_image
    if user_setting.show_chat_tips is not None:
        user.show_chat_tips = user_setting.show_chat_tips
    if user_setting.use_personal_bg is not None:
        user.use_personal_bg = user_setting.use_personal_bg

    per_bg = PersonalBg(**json_util.convert_to_dict(user.personal_bg))
    original_user_language = user.language
    if user_setting.image_bgs is not None:
        per_bg.image_bgs = user_setting.image_bgs
    if user_setting.selected_bg_index is not None:
        per_bg.selected_bg_index = user_setting.selected_bg_index
    if user_setting.opacity is not None:
        per_bg.opacity = user_setting.opacity
    user.personal_bg = per_bg.model_dump()
    if user_setting.voice_content_type:
        user.voice_content_type = VoiceContentType(user_setting.voice_content_type)
    if user_setting.chat_channel:
        user.chat_channel = user_setting.chat_channel
    if user_setting.status_block_switch is not None:
        user.status_block_switch = user_setting.status_block_switch
    if user_setting.language:
        user.language = user_setting.language
    await user_service.update_user(user)
    if original_user_language != user.language:
        template = MessageTemplate(
            tips=_tl("小程序语言切换为：", user.language) + Language.load_desc(user.language)
        )
        await bot_message_service.send_user_template_message(user,message_template=template)
    user_res = await user_role_service.get_user_detail_info(
        user, current_language, api_source
    )
    return user_res
