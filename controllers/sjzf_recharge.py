import logging
import os
import hashlib
from fastapi.responses import HTMLResponse
from fastapi import APIRouter, Request
from pydantic import BaseModel

from controllers.user_check import user_service
from utils import env_const
from services import re_purchase_service, sjzf_recharge_service, user_growth_service

cwd = os.path.dirname(os.path.realpath(__file__))

with open(os.path.join(cwd, 'success.html'), 'r') as f:
    success_html = f.read()

sjzf_recharge_router = APIRouter()

class SJZFNotify(BaseModel):
    appId: str
    tradeNo: str
    merchantOrderNo: str
    utr: str | None = None
    faillMsg: str | None = None
    amount: str
    payAmount: str
    payStatus: str
    payTime: str | None = None
    createTime: str | None = None
    sign: str
    signType: str

    def verify_sign(self, app_key: str) -> bool:
        params = self.model_dump(exclude_unset=True, exclude={'sign'})
        params = [(k, str(v)) for k, v in params.items() if v is not None and v != '']
        sorted_params = sorted(params, key=lambda x: x[0])
        sign_str = '&'.join(f'{k}={v}' for k, v in sorted_params) + f'&key={app_key}'
        sign = hashlib.md5(sign_str.encode()).hexdigest()
        return sign.lower() == self.sign.strip()

async def handle_result(notify_result: SJZFNotify):
    if not notify_result.verify_sign(env_const.SJZF_APP_KEY):
        return 'verify sign failed'
    if notify_result.payStatus != '2':
        return 'FAIL'
    order = await sjzf_recharge_service.pay_success(notify_result.merchantOrderNo, notify_result.tradeNo, notify_result.model_dump_json())
    user = await user_service.get_user_by_id(order.user_id)
    await user_growth_service.add_fc_reward(user)
    await re_purchase_service.after_recharge(user, order)
    return 'success'

@sjzf_recharge_router.get('/sjzf/result')
async def sjzf_recharge_return(req: Request):
    return HTMLResponse(content=success_html)

@sjzf_recharge_router.get('/sjzf/notify')
async def sjzf_recharge_notify(req: Request):
    data = req.query_params
    logging.info(f'sjzf_recharge_notify: {data}')
    notify_result = SJZFNotify(**data)
    return await handle_result(notify_result)

@sjzf_recharge_router.post('/sjzf/notify')
async def sjzf_recharge_notify_p(req: Request):
    data = await req.json()
    logging.info(f'sjzf_recharge_notify: {data}')
    notify_result = SJZFNotify(**data)
    return await handle_result(notify_result)
