import logging
from fastapi import Depends, APIRouter
from fastapi.responses import JSONResponse
from controllers.user_check import get_current_user
from common.common_constant import (
    ChatModeType,
)
from services import user_dislike_service
from services.role import role_loader_service
from utils import (
    response_util,
)

dislike_api_router = APIRouter()

log = logging.getLogger(__name__)

UNAUTHORIZED = JSONResponse(status_code=401, content={"message": "Unauthorized"})


# 踩
@dislike_api_router.post("/user/dislike")
async def add_like_role(
    mode_type: ChatModeType,
    mode_target_id: int,
    user_id: int = Depends(get_current_user),
):
    if mode_type != ChatModeType.SINGLE:
        return response_util.json_param_error("mode type not support")
    
    role = await role_loader_service.load_by_id(mode_target_id)
    if not role or not role.status or not role.privacy:
        return response_util.json_param_error("role not found, or privacy")

    
    record, msg = await user_dislike_service.add_dislike_role(user_id, mode_type.value, mode_target_id)
    if record is None:
        return response_util.error(400, msg)
    
       
    return response_util.ok()

    
    

