from itertools import groupby
import json
import logging
import os
from fastapi import APIRouter, Depends
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from common.entity import MsgResponse, OkxDeposit, RechargeSetting
from controllers.user_check import get_current_user

from services import (
    gift_award_service,
    package_voucher_service,
    re_purchase_service,
    user_growth_service,
)
from services.user_service import user_service
from services.account_service import AccountService

pkg_voucher_router = APIRouter()


class RedeemVoucherRequest(BaseModel):
    voucher_code: str


class RedeemVoucherResponse(BaseModel):
    success: bool
    balance: int
    payed_balance: int = 0
    reward_balance: int = 0
    msg: str = ""


async def get_balance(user_id: int):
    balance = await AccountService.get_balance(user_id)
    expirable_balances = await gift_award_service.get_award_balance(user_id)
    balance += sum([exp.balance for exp in expirable_balances])
    return balance


@pkg_voucher_router.post("/package_voucher/redeem")
async def redeem_package_voucher(
    req: RedeemVoucherRequest, user_id: int = Depends(get_current_user)
) -> JSONResponse:
    voucher = await package_voucher_service.get_package_voucher(req.voucher_code)
    if voucher is not None and voucher.distributor == "OP_LOTTERY":
        today_free_voucher_orders = (
            await package_voucher_service.get_today_lottery_voucher(user_id)
        )
        if len(today_free_voucher_orders) >= 1:
            return JSONResponse(
                content=jsonable_encoder(
                    RedeemVoucherResponse(
                        success=False, msg="今日已经兑换过一次了", balance=0
                    )
                )
            )

    result, msg,recharge_order = await package_voucher_service.redeem_package_voucher(
        req.voucher_code, user_id
    )
    balance = await get_balance(user_id)
    payed_balance = await AccountService.get_payed_total_balance(user_id)
    reward_balance = await AccountService.get_reward_total_balance(user_id)
    if result:
        user = await user_service.get_user_by_id(user_id)
        await user_growth_service.add_fc_reward(user)
        await re_purchase_service.after_recharge(user,recharge_order=recharge_order)
        return JSONResponse(
            content=jsonable_encoder(
                RedeemVoucherResponse(
                    success=True,
                    balance=balance,
                    payed_balance=payed_balance,
                    reward_balance=reward_balance,
                )
            )
        )

    return JSONResponse(
        content=jsonable_encoder(
            RedeemVoucherResponse(
                success=False,
                balance=balance,
                payed_balance=payed_balance,
                reward_balance=reward_balance,
                msg=msg,
            )
        )
    )
