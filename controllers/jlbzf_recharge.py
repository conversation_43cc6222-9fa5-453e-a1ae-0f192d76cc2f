import logging
import os
import hashlib
from fastapi.responses import HTMLResponse
from fastapi import APIRouter, Request, Response
from pydantic import BaseModel
from common.common_constant import SKIP_QUEUE_RECHARGE_PRODUCT_ID
from services import re_purchase_service, user_growth_service, jlbzf_recharge_service
from services.bot_services import get_bot_by_bot_id
from controllers.user_check import user_service
from utils import env_const

cwd = os.path.dirname(os.path.realpath(__file__))

with open(os.path.join(cwd, 'success.html'), 'r') as f:
    success_html = f.read()

jlbzf_recharge_router = APIRouter()

class JLBPayNotify(BaseModel):
    memberid: str
    orderid: str
    amount: str
    transaction_id: str
    datetime: str
    returncode: str
    sign: str
    attach: str | None = None

    def verify_sign(self, app_key: str) -> bool:
        params = self.model_dump(exclude_unset=True, exclude={'sign', 'attach'})
        params = [(k, str(v)) for k, v in params.items() if v is not None and v != '']
        sorted_params = sorted(params, key=lambda x: x[0])
        sign_str = '&'.join(f'{k}={v}' for k, v in sorted_params) + f'&key={app_key}'
        sign = hashlib.md5(sign_str.encode()).hexdigest()
        return sign.lower() == self.sign.lower().strip()

async def handle_result(notify_result: JLBPayNotify):
    if notify_result.memberid != env_const.JLBZF_APP_ID:
        logging.info(f"Unknown app id from jlb pay: {notify_result.memberid}")
        return False
    if notify_result.returncode != '00':
        return 'FAIL'
    if not notify_result.verify_sign(env_const.JLBZF_APP_KEY):
        return 'verify sign failed'
    order = await jlbzf_recharge_service.pay_success(notify_result.orderid, notify_result.transaction_id, notify_result.model_dump_json())
    user = await user_service.get_user_by_id(order.user_id)
    await user_growth_service.add_fc_reward(user)
    await re_purchase_service.after_recharge(user, order)
    if order.from_bot_id != 0:
        bot = get_bot_by_bot_id(order.from_bot_id)
        if bot:
            tg_user = await user_service.get_tg_info_by_user_id(order.user_id)
            if tg_user:
                content = f'充值成功 {order.amount} 💎 + 🟡'
                if order.recharge_product_id == SKIP_QUEUE_RECHARGE_PRODUCT_ID:
                    content = f'充值成功，连续7天免费权益聊天次数不排队'
                try:
                    await bot.send_message(tg_user.tg_id, content)
                except Exception as e:
                    logging.warning(f'chat id {tg_user.tg_id} send message failed: {e}')
    return 'success'

@jlbzf_recharge_router.get('/jlbzf/result')
@jlbzf_recharge_router.get('/jlbzf/notify')
async def jlbzf_recharge_return(req: Request):
    data = req.query_params
    logging.info(f'jlbzf_recharge_notify: {data}')
    notify_result = JLBPayNotify(**data)
    r = await handle_result(notify_result)
    return Response(r, media_type="text/plain")

@jlbzf_recharge_router.post('/jlbzf/notify')
async def jlbzf_recharge_notify(req: Request):
    data = await req.form()
    logging.info(f'jlbzf_recharge_notify: {data}')
    notify_result = JLBPayNotify(**data)
    r = await handle_result(notify_result)
    return Response(r, media_type="text/plain")