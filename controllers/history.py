from io import BytesIO
from itertools import groupby
import logging
from random import Random
import re
from fastapi import Depends, APIRouter, <PERSON>, Header
from fastapi.responses import JSONResponse, StreamingResponse
from pydantic import BaseModel
from aiogram.types import BufferedInputFile
from common import copywriting_templates
from common.common_constant import ERROR_CODE, Language
from common.models.chat_model import ChatHistoryStatus
from controllers.user_check import get_current_user, user_service
from persistence import chat_history_dao
from persistence.chat_history import chat_history_persistence
from persistence.models.models import RoleConfig
from services import bot_message_service
from services.role import role_loader_service
from services.role_config_service import RoleConfigService
from utils import date_util, response_util
from utils.translate_util import _tl

history_router = APIRouter()

log = logging.getLogger(__name__)


async def extract_history(
    role: RoleConfig | None,
    conversation_id: str,
    user_id: int,
    mode_type: str | None = None,
) -> tuple[bool, JSONResponse | str]:
    if mode_type != "group":
        if role is None:
            return False, JSONResponse(
                status_code=404, content={"msg": "Role Not Found"}
            )
        # 不是自己的卡 && 不是系统卡 && 不是公开卡
        if role.uid != user_id and role.uid != 0 and not role.privacy:
            return False, JSONResponse(
                status_code=401, content={"msg": "Not Your Role"}
            )

    history = await chat_history_dao.list_user_history(conversation_id)
    history = [x for x in history if user_id == x.user_id]
    if len(history) == 0:
        return False, JSONResponse(status_code=404, content={"msg": "No History Found"})
    history = [
        max(g, key=lambda x: x.timestamp)
        for k, g in groupby(history, key=lambda x: x.message_id)
    ]

    user = await user_service.get_user_by_id(user_id)

    # 返回给前端时，需要去掉消息类型是 ai 的消息中的角色名前缀
    contents = []
    for msg in history:
        content = msg.content.lstrip()
        content = re.sub(r"<[^>]*>", "", content, flags=re.MULTILINE | re.DOTALL)
        content = re.sub(
            r"</output>", "", content, flags=re.MULTILINE | re.DOTALL
        ).strip()
        if mode_type != "group":
            prefix = f"{role.role_name}" if role else None
            if prefix:
                if content.startswith(f"{prefix}:") or content.startswith(
                    f"{prefix}："
                ):
                    content = content[len(prefix) + 1 :].lstrip()
            name = prefix if msg.type == "ai" else user.nickname
            contents.append(f"{name}:\n\n{content}")
        elif msg.type == "ai":
            contents.append(f"\n\n{content}")
        else:
            contents.append(f"{user.nickname}:\n\n{content}")

    history_text = "<br><br>--------------------<br>".join(contents)
    history_text = copywriting_templates.export_template.format(
        history_content=history_text
    )
    return True, history_text


@history_router.get("/history/export_tg")
async def history(
    role_id: int,
    conversation_id: str,
    mode_type: str | None = None,
    user_id: int = Depends(get_current_user),
):
    role = await RoleConfigService.get_role_config(role_id)
    success, result = await extract_history(role, conversation_id, user_id, mode_type)
    if not success:
        return result

    history_text = str(result)

    tg_user = await user_service.get_tg_info_by_user_id(user_id)
    if not tg_user:
        return JSONResponse(status_code=403, content={"msg": "Telegram user not found"})
    try:
        user = await user_service.get_user_by_id(user_id)
        f = BufferedInputFile(
            bytes(history_text, "utf-8"), f"history_{conversation_id}.html"
        )
        ret = await bot_message_service.send_user_history(user, f, None)
        if not ret:
            return response_util.def_error(
                "导出失败，需要关注最新陪聊，同时开启Bot的Block权限（更多帮助联系客服）"
            )
    except Exception as e:
        logging.exception(e, stack_info=True)
        return JSONResponse(status_code=500, content={"msg": "Send to Telegram failed"})
    return JSONResponse(content={"msg": "Success send to Telegram"})


@history_router.post("/chat/history/delete")
async def chat_history_delete(
    mode_type: str, mode_target_id: int, user_id: int = Depends(get_current_user)
):
    count = await chat_history_dao.update_status_by_mode_type_and_target_id(
        user_id, mode_type, mode_target_id, ChatHistoryStatus.DELETED
    )
    log.info(
        f"DeleteChatHistory {user_id},{mode_type},{mode_target_id}, count: {count}"
    )
    return response_util.ok({"modified_count": count})


@history_router.get("/chat/history/export")
async def export_to_by_chat(
    mode_type: str,
    mode_target_id: int,
    cid: str,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    log.info(f"ExportChatHistory {user_id},{mode_type},{mode_target_id}")
    user = await user_service.get_user_by_id(user_id)
    history = await chat_history_dao.list_by_export(
        user_id, mode_type, mode_target_id, cid
    )
    history.sort(key=lambda x: x.timestamp)
    role_ids = list(set([x.role_id for x in history]))
    role_name_map = await role_loader_service.map_role_names(role_ids)
    if len(history) == 0:
        return response_util.json_param_error("No History Found")
    history = [
        max(g, key=lambda x: x.timestamp)
        for k, g in groupby(history, key=lambda x: x.message_id)
    ]
    ai_len = len([x for x in history if x.type == "ai"])
    if ai_len < 15:
        error_message = _tl("聊天记录大于15条才能导出", current_language)
        return response_util.error(ERROR_CODE.PARAM_ERROR.value, error_message)
    # 返回给前端时，需要去掉消息类型是 ai 的消息中的角色名前缀
    contents = []
    for msg in history:
        content = msg.content.lstrip()
        content = re.sub(r"<[^>]*>", "", content, flags=re.MULTILINE | re.DOTALL)
        content = re.sub(
            r"</output>", "", content, flags=re.MULTILINE | re.DOTALL
        ).strip()
        if msg.type == "human":
            contents.append(f"{user.nickname}:\n\n{content}")
            continue

        role_name = role_name_map.get(msg.role_id, "")
        prefix = f"{role_name}" if role_name else None
        if prefix:
            if content.startswith(f"{prefix}:") or content.startswith(f"{prefix}："):
                content = content[len(prefix) + 1 :].lstrip()
        contents.append(f"{prefix}:\n\n{content}")

    html_history_text = "<br><br>--------------------<br>".join(contents)
    html_history_text = copywriting_templates.export_template.format(
        history_content=html_history_text
    )
    txt_history_test = "\n\n--------------------\n".join(contents)
    random_index = Random().randint(0, 10000)
    tg_user = await user_service.get_tg_info_by_user_id(user_id)
    if tg_user:
        html_file = BufferedInputFile(
            bytes(html_history_text, "utf-8"), f"history_{cid}.html"
        )
        txt_f = BufferedInputFile(
            bytes(txt_history_test, "utf-8"), f"history_{cid}.txt"
        )
        ret = await bot_message_service.send_user_history(user, html_file, txt_f)
        if not ret:
            return response_util.def_error(
                _tl(
                    "导出失败，需要关注最新陪聊，同时开启Bot的Block权限（更多帮助联系客服）",
                    current_language,
                )
            )
        return response_util.ok()

    headers = {
        "Content-Disposition": f"attachment; filename={mode_type}_{mode_target_id}_{random_index}.html",
        "Content-Encoding": "utf-8",
    }
    output = BytesIO(bytes(html_history_text, "utf-8"))
    return StreamingResponse(
        output, headers=headers, media_type="text/html; charset=utf-8"
    )


@history_router.get("/history/export")
async def export_to_link(
    role_id: int,
    conversation_id: str,
    mode_type: str | None = None,
    user_id: int = Depends(get_current_user),
):
    role = await RoleConfigService.get_role_config(role_id)
    success, result = await extract_history(role, conversation_id, user_id, mode_type)
    if not success:
        return result

    history_text: str = result
    headers = {
        "Content-Disposition": f"attachment; filename={conversation_id}.txt",
        "Content-Encoding": "utf-8",
    }

    output = BytesIO(bytes(history_text, "utf-8"))
    return StreamingResponse(
        output, headers=headers, media_type="text/plain; charset=utf-8"
    )
