

# import json
# import os
# from fastapi import APIRouter, Request
# from fastapi.encoders import jsonable_encoder
# from fastapi.responses import JSONResponse
# import requests

# from persistence.models.models import RegexRule, RoleCategoryOrder, RoleConfig, SubTag, TagListOrder


# sync_config_router = APIRouter()

# # @sync_config_router.get("/sync_info")
# # async def sync_info():
# #     roles = await RoleConfig.all()
# #     role_category_orders = await RoleCategoryOrder.all()
# #     sub_tags = await SubTag.all()
# #     regex_rules = await RegexRule.all()
# #     tag_list_order = await TagListOrder.all()
# #     return JSONResponse(content=jsonable_encoder({'roles': roles, 'role_category_orders': role_category_orders, 'sub_tags': sub_tags, 'regex_rules': regex_rules, 'tag_list_order': tag_list_order}))


# # @sync_config_router.get("/sync_info_to_stag")
# # async def roles_all(request: Request):
# #     env = os.getenv("ENV")
# #     if env != "stag" and env != 'local':
# #         return JSONResponse(content={"message":"This API is only available in stag environment."}, status_code=400)
    
# #     ret = requests.get("https://tavern-api.fancyme.xyz/sync_info")
# #     ret = json.loads(ret.text)
# #     roles = ret.get("roles")
# #     role_category_orders = ret.get("role_category_orders")
# #     sub_tags = ret.get("sub_tags")
# #     regex_rules = ret.get("regex_rules")
# #     tag_list_order = ret.get("tag_list_order")
# #     # 远程请求测试环境接口，roles_all接口

# #     await RoleConfig.all().delete()
# #     await RoleCategoryOrder.all().delete()
# #     await SubTag.all().delete()
# #     await RegexRule.all().delete()
# #     await TagListOrder.all().delete()
# #     for role in roles:
# #         role = RoleConfig(**role)
# #         await role.save()
# #     for role_category_order in role_category_orders:
# #         role_category_order = RoleCategoryOrder(**role_category_order)
# #         await role_category_order.save()
# #     for sub_tag in sub_tags:
# #         sub_tag = SubTag(**sub_tag)
# #         await sub_tag.save()
# #     for regex_rule in regex_rules:
# #         regex_rule = RegexRule(**regex_rule)
# #         await regex_rule.save()
# #     for tag in tag_list_order:
# #         tag = TagListOrder(**tag)
# #         await tag.save()
# #     return JSONResponse(content=jsonable_encoder({'roles_count':len(roles), 'role_category_orders_count':len(role_category_orders), 'sub_tags_count':len(sub_tags), 'regex_rules_count':len(regex_rules), 'tag_list_order_count':len(tag_list_order)}))

