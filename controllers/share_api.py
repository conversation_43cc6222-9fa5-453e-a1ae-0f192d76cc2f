import asyncio
import logging
import os
import re
import uuid
from fastapi import Form, Header, Depends, APIRouter
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from common.bot_common import MessageTemplate
from common.models.chat_model import BuildHistoryQuery, HistoryRequest
from common.role_model import (
    RoleShareBriefResponse,
    RoleShareResponse,
    UserRoleShareDetail,
    UserRoleShareRequest,
)
from controllers.user_check import get_current_user
from common.common_constant import (
    ChatModeType,
    Env,
    Language,
    ProductType,
)
from persistence import chat_history_dao
from persistence.models.models import ShareDescRecord, User
from services import (
    bot_config_service,
    bot_message_service,
    bot_services,
    tg_config_service,
    user_service,
    user_share_service,
)
from services import product_service
from services.account_service import AccountService
from services.chat import chat_message_service
from services.role import role_loader_service
from utils import (
    env_util,
    response_util,
    str_util,
)

share_api_router = APIRouter()

user_service_obj = user_service.UserService()

log = logging.getLogger(__name__)

UNAUTHORIZED = JSONResponse(status_code=401, content={"message": "Unauthorized"})

MAX_SHARE_COUNT = 10

SHARE_PROHIBITED_WORDS = [
    "幼女",
    "young girl",
    "loli",
    "萝莉",
    "小学生",
    "初中",
    "未满18岁",
    "未成年",
    "婴儿",
    "人兽",
    "尸体",
]
SHARE_PROHIBITED_WORDS_REG = [
    "0岁",
    "1岁",
    "2岁",
    "3岁",
    "4岁",
    "5岁",
    "6岁",
    "7岁",
    "8岁",
    "9岁",
    "10岁",
    "11岁",
    "12岁",
    "13岁",
    "14岁",
    "15岁",
    "16岁",
    "17岁",
]


# 分享带聊天记录的角色卡到群
@share_api_router.post("/user/share")
async def create_role_share(
    req: UserRoleShareRequest,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    if len(req.title) > 30 or len(req.description) > 500:
        return response_util.json_param_error("title or description too long")
    share_count = user_share_service.get_current_share_count(user_id)

    if share_count >= MAX_SHARE_COUNT:
        return response_util.error(400, "超过每日分享次数限制")

    if env_util.get_current_env() == Env.PROD:
        user_tg = await user_service_obj.get_tg_info_by_user_id(user_id)
        if not user_tg:
            return response_util.error(400, "未绑定telegram账号")
        is_in_group = await bot_services.check_in_any_chat_group(user_tg.tg_id)
        if not is_in_group:
            return response_util.error(
                400, "当前无法分享，需要加幻梦AI群后，才可以分享哦"
            )

    role = await role_loader_service.load_by_id(req.role_id)
    if not role or not role.status or not role.privacy:
        return response_util.json_param_error("role not found, or privacy")

    chat_history_list = await chat_message_service.get_latest_history_msg(
        user_id, req.conversation_id, 6
    )
    if not chat_history_list or len(chat_history_list) < 3:
        return response_util.json_param_error("chat history not found")
    if any([history.role_id != req.role_id for history in chat_history_list]):
        return response_util.json_param_error("role not match")
    is_repeated = await user_share_service.is_replicate_share_record(
        user_id,
        req.role_id,
        req.conversation_id,
        chat_history_list[-1].message_id,
        chat_history_list[-1].version,
    )
    if is_repeated:
        return response_util.error(400, "已分享过相同聊天记录")

    user = await user_service.get_by_id(user_id)
    content = req.title + " " + req.description
    matched_word = None
    for prohibited_word in SHARE_PROHIBITED_WORDS:
        if prohibited_word in content:
            matched_word = prohibited_word
            break
    if not matched_word:
        matched_word = _check_reg_in_text(content)
    if matched_word:
        log.warning(
            f"Prohibited word in share content: user_id:{user.id}, content:{content}"
        )
        template = MessageTemplate(
            tips=f"您分享的内容存在违规，涉及「{matched_word}」，请您使用合规的文字和AI聊天，再尝试分享到用户群"
        )
        await bot_message_service.send_user_template_message(user, template)

        # await bot_services.send_message(
        #     f"您分享的内容存在违规，涉及「{matched_word}」，请您使用合规的文字和AI聊天，再尝试分享到用户群",
        #     user,
        # )
        return response_util.error(400, "分享内容包含违规词汇")

    new_record = await user_share_service.add_user_role_share_info(
        req, user_id, chat_history_list
    )
    if not new_record:
        return response_util.error(400, "已分享过相同聊天记录")
    # 分享次数+1
    share_count = user_share_service.increase_share_count(user_id)

    # send share message
    asyncio.create_task(
        user_share_service.send_share_message(
            user, role, req.title, req.description, new_record.id
        )
    )
    return response_util.ok({"share_count": share_count})


def _check_reg_in_text(text):
    # 遍历年龄列表，返回第一个匹配的年龄
    for age in SHARE_PROHIBITED_WORDS_REG:
        # 构建正则表达式
        # (?<!\d) 表示前面不能是数字
        # (?!\d) 表示后面不能是数字
        pattern = f"(?<!\\d){age}(?!\\d)"
        if re.search(pattern, text):
            return age
    return None


# 返回用户当天已分享的次数和是否在群里
@share_api_router.get("/user/share/count")
async def get_user_current_day_share_count(user_id: int = Depends(get_current_user)):
    share_count = user_share_service.get_current_share_count(user_id)
    user_tg = await user_service_obj.get_tg_info_by_user_id(user_id)
    group = await tg_config_service.get_main_group()
    group_link = group.url
    if not user_tg:
        return response_util.ok(
            {"share_count": share_count, "is_in_group": False, "group_link": group_link}
        )

    is_in_group = await bot_services.check_in_any_chat_group(user_tg.tg_id)
    if not is_in_group:
        return response_util.ok(
            {"share_count": share_count, "is_in_group": False, "group_link": group_link}
        )
    else:
        return response_util.ok(
            {"share_count": share_count, "is_in_group": True, "group_link": group_link}
        )


# 返回分享详情，为分享落地页提供数据
@share_api_router.get("/share")
async def get_role_share(
    share_id: int,
    # user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    share_detail = await user_share_service.get_role_share_by_id(share_id)
    if not share_detail:
        return response_util.json_param_error("share not found")

    sharer_uid = share_detail.user_id
    sharer = await user_service.get_by_id(sharer_uid)

    chat_list = await _process_history_msg_helper(
        sharer, share_detail, current_language
    )

    recent_role_map = await role_loader_service.map_user_brief_by_filter(
        ids=[share_detail.role_id],
        language=current_language,
        nickname="",
        audit=False,
        format=False,
    )
    role_info = recent_role_map.get(share_detail.role_id)
    if not role_info:
        return response_util.json_param_error("role not found")
    result = RoleShareResponse(
        chat_list=chat_list,
        share_title=share_detail.title,
        share_description=share_detail.description,
        role_id=share_detail.role_id,
        role_name=role_info.role_name,
        role_avatar=role_info.role_avatar,
        sharer_uid=sharer_uid,
        sharer_name=sharer.nickname,
        sharer_avator=str_util.format_avatar(sharer.avatar),
        author_id=role_info.author_id if role_info.author else 0,
        author_name=role_info.author_name,
        author_avator=str_util.format_avatar(role_info.author_avatar),
        message_count=share_detail.message_count,
    )
    return response_util.ok(result.model_dump())


async def _process_history_msg_helper(
    sharer: User, share_detail: UserRoleShareDetail, current_language: str
):
    history_query = BuildHistoryQuery(
        user_id=sharer.id,
        nickname=sharer.nickname,
        mode_type=ChatModeType.SINGLE.value,
        mode_target_id=share_detail.role_id,
        conversation_id=share_detail.conversation_id,
        language=current_language,
        regex_option="",
        add_name=False,
        use_display_user_name=True,
        # regex_option=RegexOption.FORMAT_DISPLAY.value,
    )
    return await chat_message_service.build_history(
        history_query, share_detail.content_snapshot
    )


# 分页返回用户的分享列表
@share_api_router.get("/share/list/me")
async def get_share_list(
    offset: int = 0,
    limit: int = 10,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    return await _get_share_list_by_user_id_helper(
        user_id, offset, limit, current_language
    )


async def _get_share_list_by_user_id_helper(
    user_id: int, offset: int, limit: int, current_language: str
):
    if limit > 20:
        return response_util.json_param_error("limit too large")
    if offset < 0:
        offset = 0
    share_list, total = await user_share_service.get_share_list_by_user_id(
        user_id, offset, limit
    )
    if not share_list:
        return response_util.ok({"list": [], "total": total})
    role_ids = [share.role_id for share in share_list]
    recent_role_map = await role_loader_service.map_user_brief_by_filter(
        ids=role_ids,
        language=current_language,
        nickname="",
        audit=False,
    )
    result_list = []
    for share in share_list:
        role_info = recent_role_map.get(share.role_id)
        if not role_info:
            continue
        share_breif = RoleShareBriefResponse.from_model(share)
        share_breif.role_name = role_info.role_name
        share_breif.role_avatar = role_info.role_avatar
        result_list.append(share_breif)

    return response_util.ok({"list": result_list, "total": total})


# 分页返回其他用户的分享列表
@share_api_router.get("/share/list/other")
async def get_other_user_share_list(
    offset: int = 0,
    limit: int = 10,
    other_user_id: int = 0,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    if other_user_id <= 0:
        return response_util.ok({"list": [], "total": 0})
    return await _get_share_list_by_user_id_helper(
        other_user_id, offset, limit, current_language
    )


# 分页返回指定角色卡的分享列表
@share_api_router.get("/share/list/role")
async def get_share_list_by_role_id(
    offset: int = 0,
    limit: int = 10,
    role_id: int = 0,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    if limit > 20:
        return response_util.json_param_error("limit too large")
    if offset < 0:
        offset = 0
    role = await role_loader_service.load_by_id(role_id)
    if not role or not role.status or not role.privacy:
        return response_util.ok()
    share_list, total = await user_share_service.get_share_list_by_role_id(
        role_id, offset, limit
    )
    if not share_list:
        return response_util.ok({"list": [], "total": total})

    map_nicknames, map_avator = await user_service.map_nickname_avatar(
        [share.user_id for share in share_list]
    )
    result_list = []
    for share_detail in share_list:
        sharer_uid = share_detail.user_id
        sharer = await user_service.get_by_id_optional(sharer_uid)
        if not sharer:
            continue
        chat_list = await _process_history_msg_helper(
            sharer, share_detail, current_language
        )
        share_breif = RoleShareBriefResponse.from_detail_model(share_detail)
        share_breif.chat_list = chat_list
        share_breif.role_name = role.role_name
        share_breif.role_avatar = str_util.format_avatar(role.role_avatar)
        share_breif.sharer_name = map_nicknames.get(share_detail.user_id, "")
        share_breif.sharer_avator = map_avator.get(share_detail.user_id, "")
        result_list.append(share_breif)

    return response_util.ok({"list": result_list, "total": total})


class ShareDescription(BaseModel):
    request_id: str = ""
    mode_type: str
    mode_target_id: int
    message_ids: list[str]


@share_api_router.post("/share/description/generate")
async def generate_share_description(
    body: ShareDescription,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    body.request_id = body.request_id if body.request_id else str(uuid.uuid4().hex)
    user = await user_service.get_by_id(user_id)
    desc_record = await ShareDescRecord.filter(
        user_id=user_id, request_id=body.request_id
    ).first()
    if desc_record:
        return response_util.ok({"description": desc_record.description})

    balance = await AccountService.get_total_balance(user_id)
    share_product = await product_service.get_online_by_type_first(
        ProductType.SHARE.value
    )
    if not share_product:
        return response_util.def_error("share product not found")

    if balance < share_product.price:
        return JSONResponse(status_code=402, content={"msg": "Insufficient Balance"})

    message_ids = body.message_ids
    messages = await chat_history_dao.list_by_message_ids(message_ids)
    if not messages:
        return response_util.json_param_error("message not found")
    if any([message.user_id != user_id for message in messages]):
        return response_util.json_param_error("message not match")

    record = await user_share_service.generate_share_desc(
        user,
        messages,
        current_language,
        body.mode_type,
        body.mode_target_id,
        body.request_id,
        share_product,
    )

    return response_util.ok({"description": record.description})


# @share_api_router.get("/share/check")
# async def check_share_prohibited_words(share_id: int, llm_model: LlmModel):
#     ret = await user_share_service.check_share_content_by_share_id(share_id, llm_model)
#     return response_util.ok({"result": ret})


@share_api_router.get("/share/check/by_moderation")
async def check_by_modi(id_start: int, id_end: int):
    result = {}
    for i in range(id_start, id_end):
        ret = await user_share_service.check_content_by_moderation(i)
        result[i] = ret
    return response_util.ok({"result": result})
