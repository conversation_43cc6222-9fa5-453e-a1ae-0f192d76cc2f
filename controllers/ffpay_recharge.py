import logging
import os
import hashlib
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi import APIRouter, Depends, Request
from pydantic import BaseModel
from controllers.user_check import get_current_user
from persistence.models.models import RechargeProduct

from controllers.user_check import user_service
from utils import env_const, response_util
from services import ff_recharge_service, re_purchase_service, user_growth_service

cwd = os.path.dirname(os.path.realpath(__file__))

with open(os.path.join(cwd, 'success.html'), 'r') as f:
    success_html = f.read()

ffpay_recharge_router = APIRouter()

class FFPayNotify(BaseModel):
    pid: int
    trade_no: str
    out_trade_no: str
    type: str
    name: str
    money: str
    trade_status: str
    sign: str
    sign_type: str
    params: str | None = None

    def verify_sign(self, app_key: str) -> bool:
        params = self.model_dump(exclude_unset=True, exclude={'sign', 'sign_type'})
        params = [(k, str(v)) for k, v in params.items() if v is not None and v != '']
        sorted_params = sorted(params, key=lambda x: x[0])
        sign_str = '&'.join(f'{k}={v}' for k, v in sorted_params) + app_key
        sign = hashlib.md5(sign_str.encode()).hexdigest()
        return sign.lower() == self.sign.strip()

async def handle_result(notify_result: FFPayNotify):
    if not notify_result.verify_sign(env_const.FFPAY_APP_KEY):
        return 'verify sign failed'
    if notify_result.trade_status != 'TRADE_SUCCESS':
        return 'FAIL'
    order = await ff_recharge_service.pay_success(notify_result.out_trade_no, notify_result.trade_no, notify_result.model_dump_json())
    user = await user_service.get_user_by_id(order.user_id)
    await user_growth_service.add_fc_reward(user)
    await re_purchase_service.after_recharge(user, order)
    return 'success'

@ffpay_recharge_router.get('/ffpay/result')
async def ffpay_recharge_return(req: Request):
    return HTMLResponse(content=success_html)

@ffpay_recharge_router.get('/ffpay/notify')
async def ffpay_recharge_notify(req: Request):
    data = req.query_params
    logging.info(f'ffmpay_recharge_notify: {data}')
    notify_result = FFPayNotify(**data)
    return await handle_result(notify_result)

@ffpay_recharge_router.post('/ffpay/notify')
async def andada_recharge_notify_p(req: Request):
    data = await req.form()
    logging.info(f'ffpay_recharge_notify: {data}')
    notify_result = FFPayNotify(**data)
    return await handle_result(notify_result)
