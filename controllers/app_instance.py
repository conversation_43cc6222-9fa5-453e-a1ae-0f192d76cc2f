
import os
import dotenv
from fastapi import FastAP<PERSON>, HTTPException
from tortoise.contrib.fastapi import register_tortoise
from pydantic import BaseModel, EmailStr, constr, <PERSON>
from datetime import datetime, timedelta
from jose import JWTError, jwt
from fastapi import Depends, FastAPI, HTTPException, status, Cookie

from services.user_service import UserService
from utils import exception_util

SECRET_KEY = "b'g5gFjPqzsi0V4sNUO9q5GyvYb1uiKvZ0'"
ALGORITHM = "HS256"

app = FastAPI()

PasswordStr = constr(min_length=6, max_length=20)

dotenv.load_dotenv()
register_tortoise(app=app,
    db_url = os.environ['MYSQL_URL'],
    modules = {'models': ['persistence.models.models']},
    generate_schemas=True
)

user_service = UserService()

class UserIn(BaseModel):
    email: EmailStr
    password: str = Field(..., min_length=4, max_length=20)


@app.post("/user/register")
async def register(user_in: UserIn):
    user = await user_service.register(user_in.email, user_in.password)
    if user:
        return {"message": "User registered successfully"}
    else:
        raise HTTPException(status_code=400, detail="Registration failed")

@app.post("/user/login")
async def login(user_in: UserIn):

    user = await user_service.login(user_in.email, user_in.password)
    if user:
        access_token_expires = timedelta(minutes=3600)
        access_token = create_access_token(
        data={"sub": str(user.id)}, expires_delta=access_token_expires)
        return {"message": "Login successful", "token": access_token, "token_type": "bearer","pw":user.password_hash,"id":user.id,"email":user.email,"nickname":user.nickname,"password":user_in.password}
    else:
        raise HTTPException(status_code=400, detail="Invalid email or password")

def create_access_token(*, data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def get_current_user(token: str = Cookie(None)):
    credentials_exception = exception_util.http_auth("Invalid token")
    if not token:
        raise exception_util.http_auth("Not authenticated")
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=ALGORITHM)
        print(payload)
        userid_str: str = payload.get("sub","")
        if not userid_str:
            raise credentials_exception
        user_id = int(userid_str)
    except JWTError as e:
        print(e)
        raise credentials_exception
    user = user_service.get_user_by_id(user_id=user_id)
    if not user:
        raise credentials_exception
    return user_id