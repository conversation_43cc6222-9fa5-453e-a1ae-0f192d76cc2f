import logging
from textwrap import dedent
from fastapi import APIRouter, Depends, Header, Request
from pydantic import BaseModel, Field

from common.common_constant import (
    BenefitUsageScopeType,
    ChatBenefitType,
    ErrorCode,
    ErrorKey,
    GenImagePrivacy,
    ImageAspectRatio,
    ImageQuality,
    ImageStyle,
    Language,
    ProductType,
)
from common.image_model import UserImageDetail
from controllers.request_depends import limit_requests_by_tg
from controllers.user_check import get_current_user
from persistence.models.models import UserGenerateImageRecord
from services import account_service, image_service, product_service, user_service
from services.user import user_benefit_service
from utils import exception_util, response_util
from utils.translate_util import _tl


generate_image_api_router = APIRouter()


class GenerateImageRequest(BaseModel):
    prompt: str = Field(description="生成图片的提示词")
    image_style: str = Field(default=ImageStyle.ANIME.value, description="图片风格")
    image_quality: str = Field(default=ImageQuality.SMALL.value, description="图片质量")
    image_aspect_ratio: str = Field(
        default=ImageAspectRatio.SQUARE.value, description="图片长宽比例"
    )
    privacy: str = Field(
        default=GenImagePrivacy.PUBLIC.value,
        description="图片隐私设置，默认公开，可选值：public, private",
    )


@generate_image_api_router.get("/image/generate/user/benefits")
async def get_user_generate_image_benefits(user_id: int = Depends(get_current_user)):
    user = await user_service.get_user_by_id(user_id)
    usage_scope_ids = [x.value for x in ImageQuality]
    map_count_benefit = await user_benefit_service.map_count_remain_benefit(
        user_id, BenefitUsageScopeType.GENERATOR_IMAGE, usage_scope_ids
    )
    valid_benefits = await user_benefit_service.list_valid(
        user, usage_scope_type=BenefitUsageScopeType.GENERATOR_IMAGE.value
    )
    image_benefit_summary = {}
    for quality in ImageQuality:
        mid_benefits = [x for x in valid_benefits if x.usage_scope_id == quality.value]
        if not mid_benefits:
            continue
        image_benefit_summary[quality.value] = {
            "remain_times": sum(x.remain_times for x in mid_benefits),
            "reward_times": sum(x.reward_times for x in mid_benefits),
        }

    return response_util.ok(
        {
            "image_benefit_count": map_count_benefit,
            "image_benefit_summary": image_benefit_summary,
        }
    )


@generate_image_api_router.post("/image/generate/submit")
@limit_requests_by_tg(prefix="user:chat:save_status_block", expire=10)
async def generate_image(
    request: Request,
    body: GenerateImageRequest,
    user_id: int = Depends(get_current_user),
):
    logging.info(f"GenerateImage:user_id={user_id}, body={body}")
    if await image_service.exist_generating_image(user_id):
        raise exception_util.verify_exception(message="您有正在生成的图片，请稍后再试")
    if (
        not body.prompt
        or not body.image_aspect_ratio
        or not body.image_quality
        or not body.image_style
    ):
        raise exception_util.verify_exception(
            message="请提供有效的提示词、图片风格、图片质量和图片长宽比例"
        )

    image_style = ImageStyle(body.image_style)
    image_quality = ImageQuality(body.image_quality)
    image_aspect_ratio = ImageAspectRatio(body.image_aspect_ratio)
    privacy = GenImagePrivacy(body.privacy)
    total_balance = await account_service.get_total_balance(user_id)
    count_benefit = await user_benefit_service.count_remain_benefit(
        user_id, BenefitUsageScopeType.GENERATOR_IMAGE, image_quality.value
    )
    product = await product_service.get_online_product_by_type_and_mid(
        ProductType.GENERATE_IMAGE, image_quality.value
    )
    if not product:
        raise exception_util.verify_exception(message="未找到对应的产品")
    if product.price > total_balance and count_benefit <= 0:
        raise exception_util.verify_exception(
            error_code=ErrorCode.INSUFFICIENT_BALANCE.value,
            error_key=ErrorKey.INSUFFICIENT_BALANCE.value,
        )
    record_id = await image_service.user_generate_image(
        user_id, body.prompt, image_style, image_quality, image_aspect_ratio, privacy
    )
    return response_util.ok({"record_id": record_id})


class CheckGenerateData(BaseModel):
    status: str = Field(
        description="生成状态，可能的值包括：SUBMIT, GENERATING, SUCCESS, FAILED"
    )
    image_url: str = Field(default="", description="生成的图片URL")
    error_message: str = Field(default="", description="错误信息，如果有的话")
    image_width: int = Field(default=0, description="生成图片的宽度")
    image_height: int = Field(default=0, description="生成图片的高度")


@generate_image_api_router.get(
    "/image/generate/check", response_model=CheckGenerateData
)
async def check_generate_image_status(
    record_id: int, user_id: int = Depends(get_current_user)
):
    record = await image_service.check_generate_image_status(user_id, record_id)
    data = CheckGenerateData(
        status=record.status,
        image_url=record.image_url,
        image_width=record.image_width,
        image_height=record.image_height,
        error_message=record.error_message,
    )

    return response_util.ok(data.model_dump())


class DeleteRequest(BaseModel):
    record_id: int


@generate_image_api_router.post("/image/generate/user/record/delete")
async def delete_user_generate_image_record(
    delete_request: DeleteRequest, user_id: int = Depends(get_current_user)
):
    logging.info(f"DeleteImageRecord: user_id:{user_id}, request:{delete_request}")
    await image_service.delete_user_generate_image_record(
        user_id, delete_request.record_id
    )
    return response_util.ok()


@generate_image_api_router.get(
    "/image/generate/user/records", response_model=list[UserImageDetail]
)
async def user_generate_image_records(
    offset: int = 0,
    limit: int = 20,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    count, records = await image_service.user_records(user_id, offset, limit)

    def display_tags(record: UserGenerateImageRecord):
        image_style = ImageStyle(record.image_style)
        image_quality = ImageQuality(record.image_quality)
        image_aspect_ratio = ImageAspectRatio(record.image_aspect_ratio)
        tags = [
            image_style.to_desc(),
            image_quality.to_desc(),
            image_aspect_ratio.to_desc(),
        ]
        if current_language == Language.ZH.value:
            return tags
        return [_tl(x, current_language) for x in tags]

    ret_record = [
        UserImageDetail.from_model(x, display_tags=display_tags(x)) for x in records
    ]
    return response_util.ok({"records": ret_record, "count": count})


class LikeImageRequest(BaseModel):
    record_id: int = Field(description="生成图片记录的ID")
    like_status: int = Field(description="点赞状态:1 like 2 dislike", ge=0, le=2)


@generate_image_api_router.post("/image/like_status")
async def like_image(
    like_image_request: LikeImageRequest, user_id: int = Depends(get_current_user)
):
    if like_image_request.like_status not in [0, 1, 2]:
        raise exception_util.verify_exception(message="无效的点赞状态")
    await image_service.update_like_status(
        user_id=user_id,
        record_id=like_image_request.record_id,
        like_status=like_image_request.like_status,
    )
    return response_util.ok()
