import logging
import os
import hashlib
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi import APIRouter, Depends, Request
from pydantic import BaseModel
from common.bot_common import MessageTemplate
from controllers.user_check import get_current_user
from services import bot_message_service, re_purchase_service, user_growth_service, p91pay_recharge_service

from controllers.user_check import user_service
from utils import env_const

cwd = os.path.dirname(os.path.realpath(__file__))

with open(os.path.join(cwd, 'success.html'), 'r') as f:
    success_html = f.read()

p91pay_recharge_router = APIRouter()

class P91PayNotify(BaseModel):
    memberid: int
    orderid: str
    amount: str
    transaction_id: str
    datetime: str
    returncode: str
    sign: str
    attach: str | None = None

    def verify_sign(self, app_key: str) -> bool:
        params = self.model_dump(exclude_unset=True, exclude={'sign', 'attach'})
        params = [(k, str(v)) for k, v in params.items() if v is not None and v != '']
        sorted_params = sorted(params, key=lambda x: x[0])
        sign_str = '&'.join(f'{k}={v}' for k, v in sorted_params) + f'&key={app_key}'
        sign = hashlib.md5(sign_str.encode()).hexdigest()
        return sign.upper() == self.sign.strip()

class P91PayRequestRequest(BaseModel):
    recharge_id: str
    type: str = 'alipay'
    isIOS: bool = False

async def handle_result(notify_result: P91PayNotify):
    if not notify_result.verify_sign(env_const.P91PAY_APP_KEY):
        return 'verify sign failed'
    if notify_result.returncode != '00':
        return 'FAIL'
    order = await p91pay_recharge_service.pay_success(notify_result.orderid, notify_result.transaction_id, notify_result.model_dump_json())
    user = await user_service.get_user_by_id(order.user_id)
    await user_growth_service.add_fc_reward(user)
    await re_purchase_service.after_recharge(user, order)
    return 'OK'

@p91pay_recharge_router.get('/91pay/result')
async def p91pay_recharge_return(req: Request):
    return HTMLResponse(content=success_html)

@p91pay_recharge_router.get('/91pay/notify')
async def p91pay_recharge_notify(req: Request):
    data = req.query_params
    logging.info(f'91pay_recharge_notify: {data}')
    notify_result = P91PayNotify(**data)
    return await handle_result(notify_result)

@p91pay_recharge_router.post('/91pay/notify')
async def p91pay_recharge_notify_p(req: Request):
    data = await req.form()
    logging.info(f'91pay_recharge_notify: {data}')
    notify_result = P91PayNotify(**data)
    return await handle_result(notify_result)

#@p91pay_recharge_router.post('/91pay/recharge')
async def p91pay_recharge_recharge(req: P91PayRequestRequest, request: Request, user_id=Depends(get_current_user)):
    user = await user_service.get_user_by_id(user_id)
    await user_growth_service.notify_recharge_channels(user)
    order = await p91pay_recharge_service.create_recharge_order(user_id, req.recharge_id)
    if order is None:
        return JSONResponse(content={'message': '找不到套餐'}, status_code=404)
    data = p91pay_recharge_service.create_91pay_order(order, req.type, request.client.host)
    if data['status'] != '1':
        return JSONResponse(content={'message': '支付失败'}, status_code=400)

    pay_url = data['h5_url']
    sdk_url = data['sdk_url']
    if req.isIOS:
        template = MessageTemplate(
            tips=f'请点击下方链接完成充值\n\n{pay_url}',
        )
        await bot_message_service.send_user_template_message(user, template)
        # await send_tma_message(text=f'''请点击下方链接完成充值\n\n{pay_url}''', user=user)
        return JSONResponse(content={'message': 'success',
                                     'pay_url': pay_url, 'sdk_url': sdk_url,
                                     'tgbotUrl': env_const.TMA_BOT_URL})
    else:
        return JSONResponse(content={'message': 'success',
                                     'pay_url': pay_url,
                                     'sdk_url': sdk_url})
