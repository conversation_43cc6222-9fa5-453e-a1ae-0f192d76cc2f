from datetime import datetime
from decimal import Decimal
import logging
import os, json
import hmac
import hashlib
from dataclasses import dataclass
from typing import Optional
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi import APIRouter, Depends, Form, Request
from pydantic import BaseModel, Field
import requests
from controllers.user_check import get_current_user
from services import cc_recharge_service, user_growth_service
from controllers.user_check import user_service

cc_recharge_router = APIRouter()

cc_recharge_host = os.environ['CC_RECHARGE_HOST']
cc_recharge_app_id = os.environ['CC_RECHARGE_APP_ID']
cc_recharge_app_key = os.environ['CC_RECHARGE_APP_KEY']
cc_recharge_notify_url = f'{os.environ['TG_WEBHOOK_URL']}/cc_recharge/notify'

class CCNotify(BaseModel):
    app_id: str
    success: int
    app_order_no: str
    order_no: str
    amount: str
    pay_amount: str
    pay_time: str
    sign: str
    note: Optional[str] = Field(default=None)
    message: Optional[str] = Field(default=None)

    def verify_sign(self, app_key: str) -> bool:
        params = self.model_dump(exclude_unset=True, exclude={'sign'})
        params = [(k, str(v)) for k, v in params.items() if v is not None and v != '' and v != 0]
        sorted_params = sorted(params, key=lambda x: x[0])
        sign_str = '&'.join(f'{k}={v}' for k, v in sorted_params) + app_key
        sign = hashlib.md5(sign_str.encode()).hexdigest()
        return sign.lower() == self.sign.strip()

class CCRequestRequest(BaseModel):
    recharge_id: str
    type_id: int

@cc_recharge_router.post('/cc_recharge/notify')
async def cc_recharge_notify(req: Request):
    data = await req.form()
    logging.info(f'cc_recharge_notify: {data}')
    notify_result = CCNotify(**data)
    if not notify_result.verify_sign(cc_recharge_app_key):
        return 'verify sign failed'
    if int(notify_result.success) != 1:
        return 'FAIL'
    order = await cc_recharge_service.pay_success(notify_result.app_order_no, notify_result.pay_time or int(datetime.now().timestamp()), notify_result.order_no, notify_result.model_dump_json())
    return 'success'

@cc_recharge_router.post('/cc_recharge/recharge')
async def cc_recharge_recharge(req: CCRequestRequest, user_id=Depends(get_current_user)):
    order = await cc_recharge_service.create_deposit_order(user_id, req.recharge_id)
    if order is None:
        return JSONResponse(content={'message': '找不到套餐'}, status_code=404)
    
    await user_growth_service.notify_recharge_channels(user_id)
    user = await user_service.get_user_by_id(user_id)
    params = {
        'app_id': cc_recharge_app_id,
        'app_order_no': order.recharge_order_id,
        'amount': int(order.pay_fee / 1000),
        'notify_url': cc_recharge_notify_url,
        'type_id': req.type_id,
        'name': '张三'
    }
    sorted_params = sorted(params.items(), key=lambda x: x[0])
    sign_str = '&'.join(f'{k}={v}' for k, v in sorted_params) + cc_recharge_app_key
    sign = hashlib.md5(sign_str.encode()).hexdigest()
    params['sign'] = sign.lower()

    # post params as application/x-www-form-urlencoded
    proxies = None
    proxy = os.environ.get('CC_PROXY')
    if proxy:
        proxies = {
            'http': proxy,
            'https': proxy,
        }
    resp = requests.post(f'{cc_recharge_host}/order/deposit/create', 
                         data=params, proxies=proxies)
    logging.info(f'cc_recharge_recharge: {resp.text}')
    data = resp.json()
    if data['success'] != 1:
        return JSONResponse(content={'message': '支付失败'}, status_code=400)

    return JSONResponse(content={'message': 'success', 'pay_url': data['pay_url']})