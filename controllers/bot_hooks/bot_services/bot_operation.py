# bot运营类的处理

import logging
from aiogram import Bo<PERSON>
from aiogram.fsm.context import FSMContext
from aiogram.types import (
    Message,
)

from common import bot_common
from common.bot_common import Button, MessageTemplate
from common.common_constant import (
    BotSource,
    ChatApiVersion,
    ChatChannel,
    Language,
    ModelEventType,
)
from common.models.chat_model import ChatNextInput
from controllers.bot_hooks import bot_setting
from controllers.bot_hooks import charge_chat
from controllers.bot_hooks.bot_services import bot_model_switch
from controllers.bot_hooks.charge_chat import command_recharge
from persistence import chat_history_dao
from persistence.models.models import User
from services import (
    bot_config_service,
    bot_message_service,
    operation_service,
    product_service,
    tg_config_service,
    user_service,
)
from services.chat import chat_model_switch_history
from services.user import user_benefit_service
from utils import date_util, str_util
from utils.translate_util import _tl


async def new_user_guide_check(invite_uid: int, state: FSMContext) -> bool:
    if not invite_uid or invite_uid >= 100000:
        return False
    bot_state_data = await bot_setting.get_state(state)
    if not bot_state_data.new_user_guide:
        await bot_setting.update_state_new_user_guide(state, True)
        return True
    return False


async def new_user_benefit_popup(
    bot: Bot, tg_id: int, state: FSMContext, language: str, bot_source: str
) -> bool:
    bot_state_data = await bot_setting.get_state(state)
    user = await user_service.get_user_by_tg_id(tg_id)
    if not user:
        return False
    if bot_state_data.new_user_guide:
        popup = await operation_service.bot_new_user_benefit_popup_auto(
            user, bot, tg_id, language
        )
        return bool(popup)
    # 2025-05-26
    start_time = date_util.str2datetime("2025-05-26 00:00:00")
    if user.created_at.timestamp() < start_time.timestamp():
        return False
    if bot_source == BotSource.HM_BOT.value:
        await operation_service.bot_new_user_benefit_popup_manual(
            user, bot, tg_id, language
        )
    return True


async def auto_switch_chat_channel(
    user: User, input: ChatNextInput, bot: Bot, tg_id: int, state: FSMContext
):
    if await channel_model_auto_switch(bot, user, tg_id, state, input):
        return True
    if input.api_version != ChatApiVersion.V2.value:
        return False
    if input.chat_channel != ChatChannel.FREE_BENEFIT.value:
        return False
    if input.chat_free_benefit > 0:
        return False
    model = await product_service.get_original_chat_product_by_mid_with_def(
        user.chat_product_mid
    )
    model = await product_service.get_translated_chat_product_by_mid(
        model.mid, input.language
    )
    if not model:
        return False
    user.chat_channel = ChatChannel.PAID.value
    await user_service.update_user(user)
    await chat_model_switch_history.add_model_switch_history(
        user.id,
        input.mode_type,
        input.role_id,
        input.conversation_id,
        input.message_id,
        ModelEventType.CHAT_AUTO.value,
        user.chat_product_mid,
        model.display_name,
        user.chat_product_mid,
        model.display_name,
        ChatChannel.FREE_BENEFIT.value,
        ChatChannel.PAID.value,
    )
    channel_display = _tl(ChatChannel.PAID.short_display(), input.language)
    tips_template = _tl(
        "权益不足，已自动切换到： {model_name}（{channel_display}）", input.language
    )
    tips = tips_template.format(
        model_name=model.display_name, channel_display=channel_display
    )
    logging.info("AutoSwitchChannel common_user %s to %s", user.id, model.mid)
    await bot_message_service.send_by_tips(bot, tg_id, tips, auto_deleted=True)


async def channel_model_auto_switch(
    bot: Bot, user: User, tg_id: int, state: FSMContext, input: ChatNextInput
):
    bot_state_data = await bot_setting.get_state(state)
    if not bot_state_data.new_user_guide:
        return False
    if input.chat_free_benefit > 0:
        return False
    user_benefit_map = await user_benefit_service.map_valid_by_all_product_mids(user)
    products = await product_service.list_display_chat_product(input.language)
    products.sort(key=lambda x: x.price, reverse=True)
    products = [
        p
        for p in products
        if p.mid in user_benefit_map and user_benefit_map[p.mid].sum_remain_times > 0
    ]
    if not products:
        return False
    await bot_model_switch.auto_select(user, ChatChannel.FREE_BENEFIT, products[0].mid)
    tips_template = _tl(
        "当前免费次数不足，已自动帮你切换成新模式：{model_name}（{channel_display}）",
        input.language,
    )
    tips = tips_template.format(
        model_name=products[0].short_name,
        channel_display=ChatChannel.FREE_BENEFIT.short_display(),
    )
    logging.info(f"AutoSwitchModel channel_user {user.id} to {products[0].mid}")
    await bot_message_service.send_by_tips(bot, tg_id, tips, auto_deleted=True)
    return True


async def uhoney_stop_chat(user: User, bot_source: str) -> bool:
    if bot_source != BotSource.OVERSEAS_BOT.value:
        return False
    msg_count = await chat_history_dao.count_ai_msg(user.id)
    if msg_count >= 10:
        return True
    return False


async def uhoney_chat_notice(
    bot: Bot, user: User, tg_id: int, state: FSMContext, input: ChatNextInput
) -> bool:
    if input.language != Language.EN.value:
        return False
    msg_count = await chat_history_dao.count_ai_msg(user.id)
    if msg_count in [2, 4, 6, 10] or msg_count > 10:
        await uhoney_tma_guide(bot, tg_id, input.role_id, input.language)
    return True


async def chat_notice(
    bot: Bot, user: User, tg_id: int, state: FSMContext, input: ChatNextInput
) -> bool:
    if input.language == Language.EN.value:
        return False

    msg_count = await chat_history_dao.count_ai_msg(user.id)
    if msg_count == 5:
        tips = _tl("切换模式：更高级的模式AI会更聪明、更高情商、更真人", input.language)
        await bot_message_service.send_by_tips(bot, tg_id, tips, auto_deleted=True)
        await bot_model_switch.send_model_settings(user, tg_id, 0, bot)
        return True
    if msg_count == 20:
        payed_user = await user_service.is_payed_user(user.id)
        if not payed_user:
            tips = _tl(
                "恭喜🎉获得新用户充值大礼包\n充多送多，仅首充一次机会哦！聪明的朋友已经选择赠送比例多的套餐啦",
                input.language,
            )
            await bot_message_service.send_by_tips(bot, tg_id, tips, auto_deleted=True)
            await charge_chat.send_recharge(bot, user, tg_id, state)
            return True
    return False


async def channel_switch_guide(bot: Bot, tg_id: int, language: str) -> bool:
    user = await user_service.get_user_by_tg_id(tg_id)
    if not user:
        return False
    if user.chat_channel:
        return False
    benefit_count = await user_benefit_service.chat_model_count(user=user)
    if benefit_count <= 0:
        return False
    await bot_model_switch.send_model_settings(user, tg_id, 0, bot)
    return True


async def uhoney_tma_guide(bot: Bot, tg_id: int, role_id: int, language: str):
    encode_lang = str_util.base64url_encode(language)
    bot_config = await tg_config_service.get_bot_config_by_id(bot.id)
    url = f"https://t.me/{bot_config.username}/tavern"
    startapp = (
        f"?startapp=l_{encode_lang}"
        if not role_id
        else f"?startapp=u_0-r_{role_id}-l_{encode_lang}"
    )
    url += startapp
    template = MessageTemplate(
        tips=bot_common.U_HONEY_JUMP_TMP_TIPS,
        buttons=[
            Button(
                text=bot_common.U_HONEY_JUMP_BTN_TEXT,
                url=url,
            )
        ],
    )
    await bot_message_service.send_by_template(bot, tg_id, template, auto_deleted=False)
