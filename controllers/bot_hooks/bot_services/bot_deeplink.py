import json
import logging
from aiogram import Bo<PERSON>
from common.common_constant import ChatModeType, Language
from common.models.chat_model import Build<PERSON><PERSON>ory<PERSON><PERSON>y, ChatHistoryType
from common.models.chat_request import ChatHistoryItem

from aiogram.utils.deep_linking import decode_payload, create_start_link, encode_payload
from aiogram.types import Message
from controllers.bot_hooks.chat_roles import get_role_list, send_activity_role_card
from persistence.models.models import User
from services import (
    role_access_service,
    role_config_service,
    user_diamond_season_service,
    user_role_service,
    user_service,
    user_share_service,
)

from aiogram.enums import ParseMode
from aiogram.fsm.context import FSMContext

from services.chat import chat_message_service
from services.role import role_loader_service
from utils import message_utils, str_util

# 处理deeplink的入口函数，从role_bot中挪过来


# 处理「聊天返钻石活动」的报名deeplink
async def handle__diamond_season_enroll_deeplink(
    args: str | None, message: Message, bot: <PERSON><PERSON>, state: FSMContext, language: str
):
    if not args:
        return
    if not args.startswith("act_enroll_"):
        return
    task_id = args.replace("act_enroll_", "")
    if task_id == "":
        return
    user = await user_service.get_user_by_tg_id(message.from_user.id)
    if user is None:
        return
    role_ids, msg = await user_diamond_season_service.enroll_diamond_season_activity(
        user.id, task_id
    )
    await bot.send_message(message.chat.id, msg, parse_mode=ParseMode.HTML)
    if role_ids:
        # -1 表示支持所有角色卡
        if -1 not in role_ids:
            role_ids_str = ",".join([str(role_id) for role_id in role_ids])
            await send_activity_role_card(
                message, bot, state, 0, role_ids_str, False, task_id
            )
        else:
            await get_role_list(message, bot, state,language)
            await state.set_state(None)


# 处理角色卡分享的deeplink
async def handle_role_share_deeplink(
    args: str | None, message: Message, bot: Bot, state: FSMContext
):
    if not args or not args.startswith("roleshare_"):
        return
    share_id = int(args.replace("roleshare_", ""))
    share_detail = await user_share_service.get_role_share_by_id(share_id)
    role = await role_config_service.RoleConfigService.get_role_config(
        share_detail.role_id
    )
    if not role or not share_detail:
        logging.error(f"角色卡分享的deeplink参数错误: {args}")
        return
    if role.image_nsfw:
        avatar = str_util.handle_spoiler_avatar(role.role_avatar)
    else:
        avatar = str_util.format_avatar(role.role_avatar)

    sharer_user = await user_service.safe_get_user(share_detail.user_id)
    nickname = sharer_user.nickname if sharer_user else ""
    history_query = BuildHistoryQuery(
        user_id=share_detail.user_id,
        nickname=nickname,
        mode_type=ChatModeType.SINGLE.value,
        mode_target_id=role.id,
        conversation_id=share_detail.conversation_id,
        language=Language.ZH.value,
        add_name=False,
        use_display_user_name= True,
        # regex_option=RegexOption.FORMAT_DISPLAY.value,
    )
    chat_list = await chat_message_service.build_history(
        history_query, share_detail.content_snapshot
    )
    chat_msg_list = _handle_msg_history(chat_list, True, True, role.role_name, nickname)

    content = f"<b>{share_detail.title}</b>\n\n"
    if share_detail.description:
        content += f"{share_detail.description}\n\n"
    content += "<b>选择存档</b>\n"

    bot_info = await bot.get_me()
    bot_user_name = bot_info.username
    # {'role_id': 123, 'inviter': 456}
    payload = json.dumps({"role_id": share_detail.role_id})
    encoded_str = encode_payload(payload)
    html_text = f'1.<a href="https://t.me/{bot_user_name}?start={encoded_str}">重新开始</a>\n2.分享者存档，接着聊（功能开发中）'
    content += html_text
    await bot.send_photo(
        message.chat.id, photo=avatar, caption=content, parse_mode=ParseMode.HTML
    )
    for chat_msg in chat_msg_list:
        await bot.send_message(message.chat.id, chat_msg, parse_mode=ParseMode.HTML)


# 处理存档command的deeplink
async def handle_archive_deeplink(
    args: str | None, message: Message, bot: Bot, state: FSMContext
):
    if not args:
        return
    if args.startswith("archive_"):
        # 返回指定角色的存档列表
        role_id = int(args.replace("archive_", ""))
        await handle_archive_click(message.from_user.id, role_id, message, bot, state)
    elif args.startswith("role_archive_"):
        role_id, conversation_id = args.replace("role_archive_", "").split("_")
        await _handle_role_archive_link(
            message.from_user.id, int(role_id), conversation_id, message, bot, state
        )
    else:
        return


async def handle_archive_click(
    tg_user_id: int, role_id: int, message: Message, bot: Bot, state: FSMContext
):
    user = await user_service.get_user_by_tg_id(tg_user_id)
    # 获取角色详情
    role_map = await role_loader_service.map_user_brief_by_filter(
        ids=[role_id],
        language=Language.ZH.value,
        nickname=user.nickname,
        audit=False,
        author=False,
    )
    state_data = await state.get_data()
    now_conv_id = state_data.get("conversation_id", "") if state_data else ""
    role = role_map.get(role_id)
    if role is None:
        await bot.send_message(message.chat.id, "角色不存在", parse_mode=ParseMode.HTML)
        await state.set_state(None)
        return

    if role.image_nsfw:
        avatar = str_util.handle_spoiler_avatar(role.role_avatar)
    else:
        avatar = str_util.format_avatar(role.role_avatar)
    intro = str_util.format_tg_html_text(role.introduction)
    content = f"""<b>{role.role_name}</b>\n{intro}"""
    if len(content) > 1000:
        content = content[:1000] + "..."
    await bot.send_photo(message.chat.id, photo=avatar, caption=content)

    # 通过role_id和user获取用户的存储记录信息
    archive_list = await chat_message_service.list_conversation(
        user.id, ChatModeType.SINGLE.value, role_id
    )
    archive_list = archive_list[:3] if len(archive_list) > 3 else archive_list

    bot_info = await bot.get_me()
    bot_user_name = bot_info.username
    html_text = "<b>选择存档</b>\n"
    for archive in archive_list:
        conversation_id = archive.conversation_id
        line = f"{archive.title}{"（当前存档）" if now_conv_id == archive.conversation_id else ""}"
        html_text += f'<a href="https://t.me/{bot_user_name}?start=role_archive_{role_id}_{conversation_id}">{line}</a>\n首次聊天: {archive.latest_chat_at}\n最后聊天: {archive.latest_chat_at}\n\n'

    await bot.send_message(message.chat.id, html_text, parse_mode=ParseMode.HTML)
    await state.set_state(None)


# 获取指定存档的历史聊天记录，并发消息
async def _handle_role_archive_link(
    tg_user_id: int,
    role_id: int,
    conversation_id: str,
    message: Message,
    bot: Bot,
    state: FSMContext,
):
    role = await role_loader_service.load_by_id(role_id)
    user = await user_service.get_user_by_tg_id(tg_user_id)
    if not user or not role:
        return
    history_query = BuildHistoryQuery(
        user_id=user.id,
        nickname=user.nickname,
        mode_type=ChatModeType.SINGLE.value,
        mode_target_id=role_id,
        conversation_id=conversation_id,
        language=Language.ZH.value,
        add_name=False,
        use_display_user_name=True,
        # regex_option=RegexOption.FORMAT_DISPLAY.value,
    )
    chat_list = await chat_message_service.build_history(history_query)
    # 取最新的3轮聊天
    target_chat_list = chat_list[-6:]
    chat_text_list = _handle_msg_history(
        target_chat_list, False, user.status_block_switch, role.role_name, user.nickname
    )
    await state.update_data({"conversation_id": conversation_id, "role_id": role_id})
    try:
        await bot.send_message(
            message.chat.id, chat_text_list[0], parse_mode=ParseMode.HTML
        )
        # 检查当前用户选择的聊天模型是否支持该角色(不支持，就发对应消息)
        await role_access_service.handle_bot_chat_role_support_model_check(
            message.chat.id, bot, user, role, conversation_id, chat_list[-1].message_id
        )
    except Exception as e:
        logging.error(
            f"send message error,user_id:{user.id},chat_text:{chat_text_list[0]}"
        )
        raise e


def _handle_msg_history(
    target_chat_list: list[ChatHistoryItem],
    need_split: bool = False,
    status_block_switch: bool = True,
    role_name: str = "",
    user_name: str = "",
) -> list[str]:
    for history in target_chat_list:
        # content = history.content
        # # remove all <sp> content
        # content = re.sub(r'<!--.*?-->','', content, flags=re.DOTALL)
        # content = re.sub(r'<wit>.*?</wit>', '', content, flags=re.DOTALL)
        # content = re.sub(r"<StatusBlock>.*</StatusBlock>", "", content, flags=re.DOTALL)
        # content = re.sub(r'<sp>.*?</sp>', '', content, flags=re.DOTALL)
        # # remove all xml tags in fm
        # content = re.sub(r'<[^>]+>', '', content)

        content = message_utils.bot_message_display_format(
            role_name, history.content, status_block_switch
        )
        prefix = role_name if history.type == ChatHistoryType.AI.value else user_name
        history.content = f"<b>{prefix}: </b>{content.strip()}"
    if need_split:
        # target_chat_list 每2个元素1组来拆分
        target_chat_list_split = [
            target_chat_list[i : i + 2] for i in range(0, len(target_chat_list), 2)
        ]
        chat_text_list = []
        for split_chat_list in target_chat_list_split:
            chat_text = "\n\n\n".join([chat.content for chat in split_chat_list])
            chat_text_list.append(chat_text)
        return chat_text_list
    else:
        chat_text = "\n\n\n".join([chat.content for chat in target_chat_list])
        if len(chat_text) > 4000:
            chat_text = "..." + chat_text[-4000:]
        return [chat_text]
