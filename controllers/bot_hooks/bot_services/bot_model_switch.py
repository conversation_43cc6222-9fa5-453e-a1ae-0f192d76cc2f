import logging
from aiogram import <PERSON><PERSON>
from aiogram.utils.keyboard import <PERSON><PERSON><PERSON><PERSON>boardBuild<PERSON>
from aiogram.fsm.context import FSMContext
from aiogram.enums import ParseMode
from common.bot_common import MessageTemplate
from common.common_constant import (
    BotSettingType,
    ChatChannel,
    ChatModeType,
    ErrorKey,
    Language,
    ModelEventType,
)
from controllers.bot_hooks.bot_setting import BotSetting
from persistence.models.models import Product, User
from services import (
    product_service,
    role_access_service,
    role_config_service,
    user_role_service,
    user_service,
)
from services.chat import chat_model_switch_history
from services.user import user_benefit_service
from services.voice_speaker_service import VoiceSpeakerService
from utils.translate_util import _tl

log = logging.getLogger(__name__)


async def send_model_settings(
    user: User,
    tg_id: int,
    message_id: int,
    bot: Bot,
    state: FSMContext | None = None,
    language: str = Language.ZH.value,
) -> None:
    await send_settings(
        user,
        tg_id,
        message_id,
        bot,
        BotSetting(
            type=BotSettingType.MODEL_V1.value,
            select="",
        ),
        state,
        language,
    )


async def send_settings(
    user: User,
    tg_id: int,
    message_id: int,
    bot: Bot,
    callback_data: BotSetting | None,
    state: FSMContext | None = None,
    language: str = Language.ZH.value,
) -> None:
    message_template = None
    if not callback_data or not callback_data.type:
        message_template = await setting_index_template(user, state, language)
    elif callback_data.type == BotSettingType.CHAT_CHANNEL.value:
        message_template = await chat_channel_template(user, callback_data.select)
    elif callback_data.type == BotSettingType.MODEL.value:
        message_template = await model_select_template(user, callback_data.select)
    elif callback_data.type == BotSettingType.STATUS_BLOCK.value:
        message_template = await status_select_template(user, callback_data.select)
    elif callback_data.type == BotSettingType.MODEL_V1.value:
        message_template = await model_select_template_v1(user, callback_data.select)
    elif callback_data.type == BotSettingType.VOICE.value:
        message_template = await voice_select_template(user, callback_data.select)
    elif callback_data.type == BotSettingType.VOICE_SELECTED.value:
        message_template = await voice_selected(user, callback_data.select, state)
    if not message_id and message_template:
        await bot.send_message(
            chat_id=tg_id,
            text=message_template.tips,
            reply_markup=message_template.as_markup(),
            parse_mode=ParseMode.HTML,
        )
    if message_template and message_id:
        await bot.edit_message_text(
            chat_id=tg_id,
            message_id=message_id,
            text=message_template.tips,
            reply_markup=message_template.as_markup(),
            parse_mode=ParseMode.HTML,
        )


async def setting_index_template(
    user: User, state: FSMContext | None = None, language: str = Language.ZH.value
):
    chat_channel = (
        ChatChannel(user.chat_channel) if user.chat_channel else ChatChannel.PAID
    )
    product = await product_service.get_user_chat_product(user)
    model_name = product.short_name if product else "空模型"
    status_switch = "展示" if user.status_block_switch == 1 else "隐藏"
    chat_channel_desc = _tl("当前扣费方式：", language) + _tl(
        chat_channel.display(), language
    )
    model_name = _tl("当前模型：", language) + _tl(
        model_name, language, Product.__name__
    )
    status_switch = _tl("当前状态栏：", language) + _tl(status_switch, language)
    suffix = _tl("点击按钮进行设置", language)
    tips = f"{chat_channel_desc}\n{model_name}\n{status_switch}\n\n{suffix}"
    model_btn = _tl("切换模型", language)
    sb_btn = _tl("状态栏开关", language)
    buttons = [
        BotSetting.build_button(text=model_btn, type=BotSettingType.MODEL_V1.value),
        BotSetting.build_button(text=sb_btn, type=BotSettingType.STATUS_BLOCK.value),
    ]
    if not state:
        return MessageTemplate(tips=tips, buttons=buttons)

    state_data = await state.get_data()
    role_id = state_data.get("role_id", None)
    if not role_id:
        return MessageTemplate(tips=tips, buttons=buttons)
    voice_tips = _tl("当前朗读声音", language)
    speakers = await VoiceSpeakerService.get_active_speakers()
    speaker_map = {x.speaker_id: _tl(x.name, language) for x in speakers}
    user_role_speaker = await user_role_service.get_user_role_config(user.id, role_id)
    if user_role_speaker:
        tips += f"\n{voice_tips}：{speaker_map.get(user_role_speaker.speaker_id)}"
    else:
        role_config = await role_config_service.get_by_id(role_id)
        if role_config:
            tips += f"\n{voice_tips}：{speaker_map.get(role_config.speaker_id)}"
    voice_btn = _tl("切换语音", language)
    buttons.append(
        BotSetting.build_button(
            text=voice_btn, type=BotSettingType.VOICE.value, select=str(role_id)
        )
    )

    return MessageTemplate(tips=tips, buttons=buttons)


async def chat_channel_template(user: User, select: str = ""):
    if select and select in [ChatChannel.PAID.value, ChatChannel.FREE_BENEFIT.value]:
        from_chat_channel = user.chat_channel
        to_chat_channel = select
        user.chat_channel = select
        model = await product_service.get_original_chat_product_by_mid_with_def(
            user.chat_product_mid
        )
        await user_service.update_user(user)
        if from_chat_channel != to_chat_channel:
            await chat_model_switch_history.add_model_switch_history(
                user.id,
                ChatModeType.SINGLE.value,
                0,
                "",
                "",
                ModelEventType.MAN.value,
                user.chat_product_mid,
                model.display_name,
                user.chat_product_mid,
                model.display_name,
                from_chat_channel,
                to_chat_channel,
            )
        return await model_select_template(user)
    chat_channel = (
        ChatChannel(user.chat_channel) if user.chat_channel else ChatChannel.PAID
    )
    product = await product_service.get_user_chat_product(user)
    model_name = product.display_name if product else "空模型"
    model_desc = f"当前扣费方式：{chat_channel.display()}\n当前模型：{model_name}"
    tips = f"\n\n{model_desc}\n\n请选择和AI聊天的扣费方式\n1、共享：聊天消耗奖励的聊天模式次数；\n2、尊享：聊天消耗💎金币\n\n\n"
    buttons = [
        BotSetting.build_button(
            text=channel.display(), type="chat_channel", select=channel.value
        )
        for channel in [ChatChannel.FREE_BENEFIT, ChatChannel.PAID]
    ]
    buttons.append(BotSetting.build_button(text="返回"))
    template = MessageTemplate(tips=tips, buttons=buttons, adjust=2)
    return template


async def auto_select(user: User, chat_channel: ChatChannel, model: str):
    user.chat_channel = chat_channel.value
    user.chat_product_mid = model
    await user_service.update_user(user)
    return user


async def manual_select(user: User, chat_channel: ChatChannel, model: str):

    user.chat_channel = chat_channel.value
    user.chat_product_mid = model
    await user_service.update_user(user)
    return user


async def model_select_template(user: User, select: str = ""):
    chat_channel = (
        ChatChannel(user.chat_channel) if user.chat_channel else ChatChannel.PAID
    )
    if select:
        model = await product_service.get_original_chat_product_by_mid(select)
        if not model:
            log.error(f"Model not found: {select}")
            return MessageTemplate(
                tips="模型不存在",
                buttons=[BotSetting.build_button(text="返回", type="model")],
            )
        if not await role_access_service.allow_model_by_product(model, user):
            return MessageTemplate(
                tips=_tl(ErrorKey.MODEL_ONLY_FOR_PAID_USER.message(), "zh"),
                buttons=[BotSetting.build_button(text="返回", type="model")],
            )

        # 记录模型切换事件
        current_product = await product_service.get_user_chat_product(user)
        current_model_mid = ""
        current_model_name = "空模型"
        if current_product:
            current_model_mid = current_product.mid
            current_model_name = current_product.display_name
        user.chat_product_mid = select
        await user_service.update_user(user)
        await chat_model_switch_history.add_model_switch_history(
            user.id,
            ChatModeType.SINGLE.value,
            0,
            "",
            "",
            ModelEventType.MAN.value,
            current_model_mid,
            current_model_name,
            model.mid,
            model.display_name,
            user.chat_channel,
            user.chat_channel,
        )
        return MessageTemplate(
            tips=f"已切换模型为: {model.display_name}（{chat_channel.short_display()}）",
            buttons=[],
        )
    product_benefits = await user_benefit_service.map_valid_by_all_product_mids(user)
    product = await product_service.get_user_chat_product(user)
    model_name = product.display_name if product else "空模型"
    products = await product_service.list_chat_product_new()
    text = []
    for product in products:
        mid_text = f"{product.model_name}：({product.price}💎/条)\n{product.desc}"
        if user.chat_channel == ChatChannel.FREE_BENEFIT.value:
            ucb = product_benefits.get(product.mid)
            sum_reward = ucb.sum_reward_times if ucb else 0
            sum_remain = ucb.sum_remain_times if ucb else 0
            mid_text = f"{product.model_name} [剩余：{sum_remain}/{sum_reward}]\n{product.desc}"
        text.append(mid_text)
    text = "\n\n".join(text)
    tips = f"当前扣费方式：{chat_channel.display()}\n当前模型：{model_name}\n\n点击按钮选择模型：\n\n{text}"
    buttons = [
        BotSetting.build_button(product.model_name, type="model", select=product.mid)
        for product in products
    ]
    buttons.append(BotSetting.build_button(text="返回"))
    template = MessageTemplate(
        tips=tips,
        buttons=buttons,
    )
    return template


async def model_select_template_v1(
    user: User, select: str = "", language: str = Language.ZH.value
):
    chat_channel = ChatChannel.safe_parse(user.chat_channel)
    tips = ""
    return_back = _tl("返回", language)
    buttons = []
    if select:
        splits = select.split(",")
        chat_channel = ChatChannel.safe_parse(splits[0])
        model_mid = splits[1] if len(splits) > 1 else ""
        model = await product_service.get_translated_chat_product_by_mid(
            model_mid, language
        )
        if not model:
            log.error(f"Model not found: {model_mid}")
            tips = _tl("模型不存在", language)
            buttons = [BotSetting.build_button(text=return_back)]
            return MessageTemplate(tips=tips, buttons=buttons)
        if not await role_access_service.allow_model_by_product(model, user):
            tips = _tl(ErrorKey.MODEL_ONLY_FOR_PAID_USER.message(), language)
            buttons = [BotSetting.build_button(text=return_back)]
            return MessageTemplate(tips=tips, buttons=buttons)

        # 记录模型切换事件
        current_product = await product_service.get_user_chat_product(user)
        current_model_mid = ""
        current_model_name = "空模型"
        if current_product:
            current_model_mid = current_product.mid
            current_model_name = current_product.display_name
        user.chat_channel = chat_channel.value
        user.chat_product_mid = model_mid
        await user_service.update_user(user)
        await chat_model_switch_history.add_model_switch_history(
            user.id,
            ChatModeType.SINGLE.value,
            0,
            "",
            "",
            ModelEventType.MAN.value,
            current_model_mid,
            current_model_name,
            model.mid,
            model.display_name,
            user.chat_channel,
            user.chat_channel,
        )
        channel_display = _tl(chat_channel.display(), language)
        template = _tl("已切换模型为: ", language)
        template = f"{template}{model.display_name}（{channel_display}）"
        return MessageTemplate(tips=template, buttons=[])

    user_benefit_map = await user_benefit_service.map_valid_by_all_product_mids(user)
    user_benefits = [x for x in user_benefit_map.values() if x.sum_remain_times > 0]
    product = await product_service.get_user_chat_product(user)
    model_name = product.display_name if product else "空模型"
    products = await product_service.list_chat_product_new()
    text = []
    if user_benefits:
        start_tips = "<b>共享[慢速]：聊天消耗免费聊天模式次数，使用的算力是共享的，稳定性差、模型慢、文字输出需要排队</b>"
        text.append(start_tips)
        for product in products:
            ucb = user_benefit_map.get(product.mid)
            if not ucb or ucb.sum_remain_times <= 0:
                continue
            sum_reward = ucb.sum_reward_times if ucb else 0
            sum_remain = ucb.sum_remain_times if ucb else 0
            mid_text = f"{product.model_name} [剩余：{sum_remain}/{sum_reward}]<b>🐢慢速【共享AI算力】</b>\n{product.desc} "
            select_name = f"{ChatChannel.FREE_BENEFIT.short_display()}-{product.icon}{product.short_name}"
            select_id = f"{ChatChannel.FREE_BENEFIT.value},{product.mid}"
            button = BotSetting.build_button(
                select_name, BotSettingType.MODEL_V1.value, select_id
            )
            text.append(mid_text)
            buttons.append(button)
    start_tips = "<b>尊享[快速]：聊天消耗💎金币，使用的算力是专属的，稳定性高、模型快、文字输出无需排队</b>"
    text.append(start_tips)
    for product in products:
        mid_text = f"{product.model_name}：({product.price}💎/条)\n{product.desc}"
        text.append(mid_text)
        select_name = (
            f"{ChatChannel.PAID.short_display()}-{product.icon}{product.short_name}"
        )
        select_id = f"{ChatChannel.PAID.value},{product.mid}"
        button = BotSetting.build_button(
            select_name, BotSettingType.MODEL_V1.value, select_id
        )
        buttons.append(button)

    text = "\n\n".join(text)
    tips = f"当前扣费方式：{chat_channel.display()}\n当前模型：{model_name}\n\n点击按钮选择模型：\n\n{text}"
    buttons.append(BotSetting.build_button(text="返回"))
    template = MessageTemplate(
        tips=tips,
        buttons=buttons,
    )
    return template


async def status_select_template(user: User, select: str = ""):

    if select and select == "1":
        user.status_block_switch = True
        await user_service.update_user(user)
        return MessageTemplate(tips="已开启状态栏（展示）", buttons=[])
    elif select and select == "0":
        user.status_block_switch = False
        await user_service.update_user(user)
        return MessageTemplate(tips="状态栏已关闭", buttons=[])
    user_status_switch = (
        "状态栏(展示)" if user.status_block_switch == 1 else "状态栏(隐藏)"
    )
    tips = f"当前：{user_status_switch}\n\n点击按钮，对状态栏进行设置：展示或隐藏"
    text = "隐藏" if user.status_block_switch == 1 else "展示"
    select = "1" if user.status_block_switch == 0 else "0"
    buttons = [BotSetting.build_button(text=text, type="status_block", select=select)]
    buttons.append(BotSetting.build_button(text="返回"))
    return MessageTemplate(tips=tips, buttons=buttons)


async def voice_select_template(user: User, select: str = ""):
    role_id = int(select)
    speakers = await VoiceSpeakerService.get_active_speakers()
    speakers.sort(key=lambda x: x.order)
    speaker_map = {x.speaker_id: x.name for x in speakers}
    user_role_speaker = await user_role_service.get_user_role_config(user.id, role_id)
    if user_role_speaker:
        tips = f"当前朗读声音：{speaker_map.get(user_role_speaker.speaker_id)}"
    else:
        role_config = await role_config_service.get_by_id(role_id)
        if role_config:
            tips = f"当前朗读声音：{speaker_map.get(role_config.speaker_id)}"
    buttons = [
        BotSetting.build_button(
            text=speaker.name,
            type=BotSettingType.VOICE_SELECTED.value,
            select=f"{role_id}-{str(speaker.speaker_id)}",
        )
        for speaker in speakers
    ]
    buttons.append(BotSetting.build_button(text="返回"))
    return MessageTemplate(tips=tips, buttons=buttons, adjust=4)


async def voice_selected(user: User, select: str = "", state: FSMContext | None = None):
    splits = select.split("-")
    role_id = int(splits[0])
    speaker_id = splits[1]
    await user_role_service.set_user_role_config(user.id, role_id, speaker_id)
    return await setting_index_template(user, state)
