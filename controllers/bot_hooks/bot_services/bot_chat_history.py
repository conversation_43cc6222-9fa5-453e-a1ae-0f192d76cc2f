from common.role_model import UserRoleBrief
from controllers.bot_hooks.bot_setting import (
    ArchivedRoleSelectCallback,
    ArchivedRoleSlideCallback,
)
from aiogram.types import Message
from persistence.models.models import User
from services import user_role_service, user_service

from aiogram.enums import ParseMode
from aiogram.fsm.context import FSMContext
from aiogram.utils.keyboard import InlineKeyboardBuilder

# 从role_bot挪过来，之前的代码在role_bot中，太重了


# 获取用户的归档角色列表
async def get_archive_roles(
    user: User | None, offset: int = 0, limit: int = 12
) -> tuple[int, list[UserRoleBrief]]:
    if user is None:
        return 0, []
    count, recent_chat = await user_role_service.recent_single_chat_list(
        user, offset, limit
    )
    brief_roles = [target_role.role for target_role in recent_chat]
    return count, brief_roles


def generate_numbered_list(
    bot_user_name: str | None,
    roles: list[UserRoleBrief],
    offset: int,
    limit: int,
    count: int,
) -> tuple:
    builder = InlineKeyboardBuilder()
    html_text = "（聊天记录自动生成存档）\n"
    for i, role in enumerate(roles, offset + 1):
        role_desc = role.card_name + " - " + role.role_name
        html_text += f'<a href="https://t.me/{bot_user_name}?start=archive_{role.id}">{i}. {role_desc}</a>\n'
        builder.button(
            text=str(i), callback_data=ArchivedRoleSelectCallback(role_id=role.id)
        )

    msize = 0
    if offset > 0:
        builder.button(
            text="上一页",
            callback_data=ArchivedRoleSlideCallback(offset=offset - limit, limit=limit),
        )
        msize += 1
    if count > offset + limit:
        builder.button(
            text="下一页",
            callback_data=ArchivedRoleSlideCallback(offset=offset + limit, limit=limit),
        )
        msize += 1
    if len(roles) > 8:
        builder.adjust(8, len(roles) - 8, msize)
    else:
        builder.adjust(len(roles), msize)
    return html_text, builder.as_markup()


async def send_archive_roles(
    tg_user_id: int,
    message: Message,
    state: FSMContext,
    offset: int,
    limit: int,
    refresh: bool = False,
):
    user = await user_service.get_user_by_tg_id(tg_user_id)
    count, role_list = await get_archive_roles(user, offset, limit)
    if len(role_list) == 0:
        await message.answer("暂无存档")
        return
    bot_info = await message.bot.get_me()
    if not bot_info:
        await message.answer("信息获取失败，请稍后再试")
        return
    html_text, inline_markup = generate_numbered_list(
        bot_info.username, role_list, offset, limit, count
    )
    if refresh:
        await message.edit_text(
            html_text, parse_mode=ParseMode.HTML, reply_markup=inline_markup
        )
    else:
        await message.answer(
            html_text, parse_mode=ParseMode.HTML, reply_markup=inline_markup
        )
    await state.set_state(None)
