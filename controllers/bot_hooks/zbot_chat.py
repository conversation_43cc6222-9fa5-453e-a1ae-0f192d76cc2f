import asyncio
import logging
from typing import Optional
from aiogram import Bo<PERSON>, types
from aiogram.enums import ParseMode
from aiogram.filters import CommandObject
from aiogram.types import Message, InlineKeyboardMarkup
from aiogram.utils.keyboard import Inline<PERSON>eyboardBuilder
from aiogram.fsm.context import FSM<PERSON>ontext
from pydantic import BaseModel
from ai import voice
from common import bot_common
from common.models.chat_model import ChatBotStateData, ChatNextInput
from common.models.chat_request import ChatRequest
from controllers.bot_hooks.bot_services import bot_operation
from controllers.bot_hooks.bot_tasks import try_send_tasks

from persistence.models.models import User
from persistence.redis_client import async_func_lock
from services.account_service import AccountService
from services.chat import chat_message_service
from persistence.presets import Scenario
from common.common_constant import (
    ApiSource,
    BotSource,
    ChatApiVersion,
    ChatPlatform,
    ErrorKey,
    Language,
    PopupShowPeriod,
    PopupType,
)
from services import (
    operation_service,
    role_access_service,
    tg_config_service,
    user_diamond_season_service,
    user_service,
)
from services import message_service
from services.user import user_chat_service
from utils import exception_util
from utils.translate_util import _tl
from .bot_setting import (
    ActivityDiamondSeasonEnrollCallback,
    RegenerateCallback,
    VoiceCallback,
    router,
    safe_clear_markup,
    safe_clear_message,
    start_new_chat,
)

log = logging.getLogger(__name__)

background_tasks = set()


class BotChatRequest(BaseModel):
    tg_id: int
    user_input: str = ""
    role_id: int = 0
    conv_id: str = ""
    is_retry: bool = False
    retry_message_id: str = ""
    language: str = Language.ZH.value
    bot_source: str = BotSource.HM_BOT.value
    last_mids: list[int] = []


async def atomic_do_chat(
    # bot: Bot,
    # chat_id: int,
    # user: User,
    # user_input: str,
    # state: FSMContext,
    # is_retry: bool = False,
    # retry_message_id: str = "",
    # language: str = Language.ZH.value,
    # bot_source: str = BotSource.HM_BOT.value,
    # send_message: Message = None
    user: User,
    chat_request: BotChatRequest,
    bot: Bot,
    state: FSMContext,
    retry_message: Optional[Message] = None,
):
    # state_data = await state.get_data()
    # bot_state_data = ChatBotStateData(**state_data) if state_data else None
    # if (
    #     not bot_state_data
    #     or not bot_state_data.role_id
    #     or not bot_state_data.conversation_id
    # ):
    #     return
    # role_id = bot_state_data.role_id
    # conv_id = bot_state_data.conversation_id
    # last_mids = bot_state_data.last_mids if bot_state_data.last_mids else []

    async def lock_fail():
        fail_message = _tl(
            "（发送失败）\n（请等待上一条消息完成哦~）", chat_request.language
        )
        error_message = await bot.send_message(
            chat_request.tg_id,
            f"{chat_request.user_input}{fail_message}",
        )
        log.warning(f"repeat message, tg_uid: {user.id},uid: {user.id}")

        async def del_msg():
            await asyncio.sleep(5)
            await error_message.delete()

        asyncio.create_task(del_msg())
        return

    @async_func_lock("BotChat", str(user.id), 30, lock_fail)
    async def run_task():
        if await bot_operation.uhoney_stop_chat(user, chat_request.bot_source):
            await bot_operation.uhoney_tma_guide(
                bot,
                chat_request.tg_id,
                chat_request.role_id,
                chat_request.language,
            )
            return
        if chat_request.last_mids:
            await safe_clear_markup(bot, chat_request.tg_id, chat_request.last_mids)
        send_message = retry_message
        start_message = _tl("思考中✏️……", chat_request.language)
        if chat_request.is_retry and send_message:
            await send_message.edit_text(start_message)
        if not send_message or not chat_request.is_retry:
            send_message = await bot.send_message(chat_request.tg_id, start_message)
            tg_message_id = send_message.message_id
            await state.update_data({"last_mids": [tg_message_id]})

        failed_button = await build_chat_button(
            bot.id, "", 0, chat_request.language, False
        )
        try:
            await do_chat_new(user, chat_request, bot, state, send_message)
        except exception_util.VerifyException as e:
            message = _tl(e.safe_parse_message(), chat_request.language)
            await send_message.edit_text(message, reply_markup=failed_button)
        except Exception as e:
            message = _tl(ErrorKey.CHAT_SYS_ERR.message(), chat_request.language)
            await send_message.edit_text(message, reply_markup=failed_button)

    asyncio.create_task(run_task())


async def do_chat_new(
    user: User,
    chat_request: BotChatRequest,
    bot: Bot,
    state: FSMContext,
    send_message: Message,
):

    payload = ChatRequest(
        role_id=chat_request.role_id,
        message=chat_request.user_input,
        conversation_id=chat_request.conv_id,
        isRetry=chat_request.is_retry,
        retry_message_id=chat_request.retry_message_id,
        language=chat_request.language,
        api_version=ChatApiVersion.V2.value,
        platform=ChatPlatform.CHAT_BOT.value,
    )
    log.info(
        f"do_chat_new user: {user.id}, payload: {payload}, request: {chat_request}"
    )
    api_source = ApiSource.TMA
    if chat_request.bot_source == BotSource.OVERSEAS_BOT.value:
        api_source = ApiSource.OVERSEAS_WEB
    input = await user_chat_service.build_chat_input_param(
        user.id, payload, chat_request.language, api_source
    )
    verify = await role_access_service.verify_bot_chat_auth(
        user, bot, chat_request.tg_id, payload, input, chat_request.bot_source
    )
    if not verify:
        return

    if not chat_request.is_retry and len(chat_request.user_input) > 0:
        await chat_message_service.save_user_message(
            user, input, payload, ChatPlatform.CHAT_BOT.value
        )
    input = await chat_message_service.build_model_config(input, Scenario.CHAT.value)
    input = await chat_message_service.build_request_history(input)
    input = await chat_message_service.build_character_book(input)
    input = await chat_message_service.build_user_custom_persona(input)
    success_button = await build_chat_button(
        bot.id,
        input.message_id,
        input.version,
        chat_request.language,
        True,
    )

    async def async_role_next(input: ChatNextInput, retry: bool = True):
        chat_response = await chat_message_service.llm_call_auth_retry(input)
        if not chat_response.success or not chat_response.response:
            raise exception_util.verify_exception(
                error_key=ErrorKey.CHAT_CONTENT_ERROR.value
            )
        ret = await message_service.bot_chat_result_iter_new(
            user,
            payload,
            input,
            chat_response.first_chunk,
            chat_response.response,
            send_message,
            success_button,
            chat_response.token_sum,
        )
        if ret.success:
            return 
        if retry and not input.auto_retry:
            await async_role_next(input, False)
            return

    await async_role_next(input)

    if chat_request.bot_source == BotSource.HM_BOT.value:
        await try_send_tasks(
            bot,
            chat_request.tg_id,
            user,
            chat_request.role_id,
        )
        await _send_diamond_activity_related_msg(
            bot,
            chat_request.tg_id,
            user,
            chat_request.role_id,
        )
        await bot_operation.chat_notice(bot, user, chat_request.tg_id, state, input)
    if chat_request.bot_source == BotSource.OVERSEAS_BOT.value:
        await bot_operation.uhoney_chat_notice(
            bot, user, chat_request.tg_id, state, input
        )
    await bot_operation.auto_switch_chat_channel(
        user, input, bot, chat_request.tg_id, state
    )


async def build_chat_button(
    bot_id: int, message_id: str, version: int, language: str, success: bool
) -> InlineKeyboardMarkup:
    builder = InlineKeyboardBuilder()
    message_id = message_id
    version = version
    if success:
        builder.button(
            text=_tl("语音", language),
            callback_data=VoiceCallback(message_id=message_id, version=version),
        )
    if not success:
        url = await tg_config_service.get_tma_url_by_bot_id(bot_id)
        builder.button(text=_tl("访问小程序", language), url=url)
    builder.button(
        text=_tl("重新生成", language),
        callback_data=RegenerateCallback(message_id=message_id, version=version),
    )
    return builder.as_markup()


# 钻石消耗活动相关的消息
async def _send_diamond_activity_related_msg(
    bot: Bot, chat_id: int, user: User, role_id: int
):
    # en bot 不发送活动消息
    bot_config = await tg_config_service.get_bot_config_by_id(bot.id)  # type: ignore
    if bot_config and bot_config.en_bot():
        return

    user_id = user.id
    task, is_enrolled = (
        await user_diamond_season_service.get_enrolled_active_diamond_task_by_user_id(
            user_id
        )
    )
    if task is None:
        return
    task_id = str(task.task_id)
    if not is_enrolled:
        # 判断是否要提醒报名
        payed_balance = await AccountService.get_payed_total_balance(user_id)
        if payed_balance <= 0 or payed_balance >= task.required_diamond_amount:
            return
        result, _ = await user_diamond_season_service.check_max_participants(task)
        if not result:
            return
        result, _ = await user_diamond_season_service.check_exclusive_enrollment(
            task, user_id
        )
        if not result:
            return
        # 聊天过程中提醒报名的消息，只发一次
        identifier = f"{task_id}{PopupShowPeriod.ONCE.value}"
        pop_up_recored = await operation_service.get_popup_record(
            user_id, identifier, PopupType.DIAMOND_ACTIVITY_NOTICE_BOT
        )
        if pop_up_recored:
            return
        # 发送报名的消息
        msg = await user_diamond_season_service.generate_notify_enroll_msg(task)
        builder = InlineKeyboardBuilder()
        builder.button(
            text="报名",
            callback_data=ActivityDiamondSeasonEnrollCallback(task_id=task_id),
        )
        await bot.send_message(
            chat_id, msg, parse_mode=ParseMode.HTML, reply_markup=builder.as_markup()
        )
        await operation_service.add_popup_record(
            user_id, identifier, PopupType.DIAMOND_ACTIVITY_NOTICE_BOT
        )
    else:
        # 判断是否要提醒充值
        msg = await user_diamond_season_service.generate_recharege_msg_for_bot(
            task, user_id
        )
        if msg is None:
            return
        payed_balance = await AccountService.get_payed_total_balance(user_id)
        if payed_balance > 0:
            return
        # 聊天过程中提醒充值的消息，只发一次
        identifier = f"{task_id}{PopupShowPeriod.ONCE.value}"
        pop_up_recored = await operation_service.get_popup_record(
            user_id, identifier, PopupType.DIAMOND_ACTIVITY_RECHARGE_BOT
        )
        if pop_up_recored:
            return
        button_builder = await bot_common.create_charge_button_builder(
            bot, user.id, False
        )
        await operation_service.add_popup_record(
            user_id, identifier, PopupType.DIAMOND_ACTIVITY_RECHARGE_BOT
        )
        await bot.send_message(
            chat_id=chat_id,
            text=msg,
            parse_mode=ParseMode.HTML,
            reply_markup=button_builder.as_markup(),
        )


@router.callback_query(VoiceCallback.filter())
async def voice_callback(
    callback: types.CallbackQuery,
    callback_data: VoiceCallback,
):
    tg_id = callback.from_user.id
    user = await user_service.get_user_by_tg_id(tg_id)
    message_id = callback_data.message_id
    version = callback_data.version
    if not user or not message_id or not version:
        log.error(f"voice_callback param error: {tg_id}, {message_id}, {version}")
        return

    voice_url = await voice.generate_voice_new(user, message_id, callback_data.version)
    if not voice_url:
        log.error(f"voice_callback empty: {user.id}, {message_id}, {version}")
        return
    await callback.message.answer_audio(voice_url)


@router.callback_query(RegenerateCallback.filter())
async def regenerate_callback(
    callback: types.CallbackQuery,
    callback_data: RegenerateCallback,
    bot: Bot,
    state: FSMContext,
    language: str,
    bot_source: str,
):
    tg_user_id = callback.from_user.id
    user = await user_service.get_user_by_tg_id(tg_user_id)
    if not user:
        log.error(f"regenerate_callback user not found: {tg_user_id}")
        return
    log.info(
        f"regenerate_callback user: {user.id}, message_id: {callback_data.message_id}"
    )
    state_data = await state.get_data()
    state_data = ChatBotStateData(**state_data)
    bot_request = BotChatRequest(
        tg_id=callback.message.chat.id,
        role_id=state_data.role_id if state_data.role_id else 0,
        conv_id=state_data.conversation_id if state_data.conversation_id else "",
        language=language,
        bot_source=bot_source,
        last_mids=state_data.last_mids if state_data.last_mids else [],
        is_retry=True,
        retry_message_id=callback_data.message_id,
    )
    await atomic_do_chat(user,bot_request, bot, state, callback.message)  # type: ignore


async def reset_chat(
    message: Message, command: CommandObject, bot: Bot, state: FSMContext, language: str
) -> None:
    user = await user_service.get_user_by_tg_id(message.from_user.id)
    if not user:
        log.error(f"reset_chat user not found: {message.from_user.id}")
        return
    await start_new_chat(message.chat.id, bot, state, user, language)
