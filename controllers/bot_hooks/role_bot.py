from datetime import <PERSON><PERSON><PERSON>
import json
import logging
import os
from aiogram import Bo<PERSON>, types, F
from aiogram.enums import ParseMode
from aiogram.filters import CommandStart, CommandObject, Command
from aiogram.types import (
    CallbackQuery,
    Message,
)
from aiogram.types.bot_command import Bot<PERSON>ommand
from aiogram.utils.deep_linking import decode_payload
from aiogram.fsm.context import FSMContext
from aiogram.utils.keyboard import InlineKeyboardBuilder
from fastapi import Header
from fastapi.routing import APIRouter
from dotenv import load_dotenv
from common.common_constant import (
    SKIP_QUEUE_RECHARGE_PRODUCT_ID,
    ApiSource,
    BotReplace,
    BotSettingType,
    BotSource,
    ErrorKey,
    Language,
    PopupPosition,
    RoleTag,
)
from common.bot_common import (
    Button,
    CheckInCallback,
    DiamondSeasonRewardCallback,
    MessageTemplate,
    ModelAutoChangeCallback,
    RechargeMenuPopupCallback,
    send_tupian_message,
)
from common.models.chat_model import ChatBotStateData
from controllers.bot_hooks import bot_cmd
from controllers.bot_hooks import chat_roles
from controllers.bot_hooks.bot_services import (
    bot_chat_history,
    bot_deeplink,
    bot_model_switch,
    bot_operation,
)
from controllers.bot_hooks.bot_tasks import (
    send_welfare_tip,
    try_send_tasks,
)
from controllers.bot_hooks.charge_chat import (
    callback_recharge,
    command_recharge,
    handle_successful_payment,
)
from controllers.bot_hooks.chat_roles import (
    get_role_list,
    send_activity_role_card,
    send_role,
)
from controllers.bot_hooks.zbot_chat import (
    BotChatRequest,
    atomic_do_chat,
    reset_chat,
)
from controllers.user_check import user_service
from persistence import redis_client
from persistence.models.models import (
    User,
    UserStatus,
)
from common.bot_common import charge_url
from services import (
    bot_message_service,
    check_in_service,
    gift_award_service,
    operation_service,
    package_voucher_service,
    re_purchase_service,
    tg_config_service,
    tg_message_service,
    user_active_service,
    user_diamond_season_service,
    user_growth_service,
)
from services.account_service import AccountService
from services.chat import language_service
from services.user import user_benefit_service
from services.user_service import update_user_premium_status
from utils import (
    user_growth_constants,
)
from utils.translate_util import _tl
from .bot_setting import (
    ArchivedRoleSelectCallback,
    ArchivedRoleSlideCallback,
    BotSetting,
    LanguageSelectCallback,
    NewRoleSlideCallback,
    RoleListPageCallback,
    RoleSelectCallback,
    VoucherStateGroup,
    router,
    dp,
    send_login_link,
    start_new_chat,
)
from services.bot_services import (
    chat_bots_map as bots,
    chat_bot,
    get_register_source_by_bot,
    get_register_source_by_bot,
    send_go_tma_tip,
    send_invite_notice,
    send_transfer_message,
    send_unlock_tip,
    send_invitation_message,
)

from controllers.bot_hooks import bot_setting

# 这个类是所有role_chat_bot的入口，尽量避免复杂逻辑，将逻辑抽象放到底层中处理
## 枚举类型放到，constants_commen中
## bean类型 放到 bot_common中
## 通用的bot操作，可以放到bot_setting中(setting不依赖业务)

seo_special_role = 177

load_dotenv()

role_bot_router = APIRouter()

WEBHOOK_SECRET = os.environ["TG_WEBHOOK_SECRET"]
BASE_WEBHOOK_URL = os.environ["TG_WEBHOOK_URL"]


log = logging.getLogger(__name__)


# start param {'role_id': 123, 'inviter': 456}
@router.message(CommandStart())
async def command_start_handler(
    message: Message, command: CommandObject, bot: Bot, state: FSMContext, language: str, bot_source: str
) -> None:
    logging.info(f"command_start_handler args: {command.args}")
    tg_id = message.from_user.id
    user = await user_service.get_user_by_tg_id(message.from_user.id)
    params = {}
    is_check_in = command.args is not None and command.args.startswith("ct")
    is_archive_related = command.args is not None and (
        command.args.startswith("archive_") or command.args.startswith("role_archive_")
    )
    is_role_share = command.args is not None and command.args.startswith("roleshare_")
    is_act_enroll = command.args is not None and command.args.startswith("act_enroll_")
    if (
        command.args is not None
        and not command.args.startswith("web")
        and not is_check_in
        and not is_archive_related
        and not is_role_share
        and not is_act_enroll
    ):
        if command.args.startswith("u_") or command.args.startswith("r_"):
            for arg in command.args.split("-"):
                if arg.startswith("u_"):
                    uid = arg[2:]
                    if uid.startswith("0x"):
                        uid = int(uid, 16)
                    else:
                        uid = int(uid)
                    params["inviter"] = uid
                elif arg.startswith("r_"):
                    params["role_id"] = int(arg[2:])
        else:
            params = json.loads(decode_payload(command.args))
    is_invitation = False
    inviter_user_id = None
    role_id = params.get("role_id", "0")
    new_user = False
    register_source = get_register_source_by_bot(bot)
    if user is None:
        if "inviter" in params:
            inviter_user_id = params["inviter"]
            await user_service.register_tg_with_invitation(
                message.from_user.id,
                message.from_user.first_name,
                message.from_user.last_name,
                message.from_user.username,
                inviter_user_id,
                register_source,
                message.chat.id,
                role_id,
                message.from_user.is_premium or False,
                bot.id,
            )
            is_invitation = True
        else:
            await user_service.register_by_tg_with_start_role(
                message.from_user.id,
                message.from_user.first_name,
                message.from_user.last_name,
                message.from_user.username,
                message.chat.id,
                register_source,
                role_id,
                message.from_user.is_premium or False,
                bot.id,
            )
        user = await user_service.get_user_by_tg_id(message.from_user.id)
        award = gift_award_service.get_gift_award()
        await gift_award_service.add_award_by_user_gift(user.id, award)
        new_user = True
        await user_growth_service.forward_register(
            user.id, message.from_user.id, inviter_user_id
        )
    if not user:
        return
    await operation_service.send_bot_popup(
        bot, user, message.from_user.id, PopupPosition.CHAT_BOT_START, language
    )
    if command.args is not None and command.args.startswith("web"):
        await send_login_link(message.from_user.id, bot, language)
        await state.set_state(None)
        return
    if role_id and role_id != "0":
        await state.update_data({"role_id": role_id})
        await start_new_chat(message.chat.id, bot, state, user, language)
    elif (
        not is_check_in
        and not is_archive_related
        and not is_role_share
        and not is_act_enroll
    ):
        await get_role_list(message, bot, state, language, bot_source)

    if new_user and bot_source == BotSource.HM_BOT.value:
        await try_send_tasks(bot, message.chat.id, user, role_id, True)
        await send_go_tma_tip(bot, message.chat.id)
        # await bot.send_message(message.chat.id, new_user_tip)

    if is_archive_related:
        # 处理/archives 命令的交互中出现的deeplink
        await bot_deeplink.handle_archive_deeplink(command.args, message, bot, state)
    if is_role_share:
        # 处理角色卡分享功能中出现的deeplink
        await bot_deeplink.handle_role_share_deeplink(command.args, message, bot, state)
    if is_act_enroll:
        # 处理「聊天返钻石活动」的报名deeplink
        await bot_deeplink.handle__diamond_season_enroll_deeplink(
            command.args, message, bot, state, language
        )
    await state.set_state(None)
    if is_check_in:
        await check_in_service.process_in_bot_check_in(message, bot)

    if inviter_user_id is not None:
        await send_invite_notice(message, inviter_user_id)
    if new_user and inviter_user_id:
        await bot_operation.new_user_guide_check(inviter_user_id, state)

    await bot_setting.send_language_select(message.from_user.id, bot, language)


@router.message(Command(BotCommand(command="balance", description="查询余额")))
async def handle_balance(message: Message, bot: Bot, language: str):
    user = await user_service.get_user_by_tg_id(message.from_user.id)
    if not user:
        return
    await bot_cmd.balance(user, message.from_user.id, bot)
    await message.delete()


@router.message(Command(BotCommand(command="free_benefit", description="领取免费权益")))
async def command_free_benefit_handler(
    message: Message, bot: Bot, language: str
) -> None:
    user = await user_service.get_user_by_tg_id(message.from_user.id)
    if not user:
        answer_msg = await message.answer("请先注册，再领取免费权益")
        await tg_message_service.del_msg(
            answer_msg.message_id, bot.id, message.from_user.id
        )
        return
    await bot_cmd.free_benefit(user, message.from_user.id, bot, language)


@router.message(Command(BotCommand(command="recharge", description="充值")))
async def command_recharge_link(
    message: Message, bot: Bot, state: FSMContext, language: str
):
    await command_recharge(message, bot, state)


# 处理弹出recharge menu的inline button
@router.callback_query(RechargeMenuPopupCallback.filter())
async def handle_recharge_menu_pop_up(
    query: CallbackQuery,
    callback_data: ModelAutoChangeCallback,
    bot: Bot,
    state: FSMContext,
):
    await callback_recharge(query, bot, state)


@router.message(Command("list"))
async def handle_list(
    message: types.Message,
    bot: Bot,
    state: FSMContext,
    language: str = Language.ZH.value,
    bot_source: str = BotSource.HM_BOT.value
):
    user = await user_service.get_user_by_tg_id(message.from_user.id)
    await get_role_list(message, bot, state, language, bot_source)
    await state.set_state(None)


@router.message(Command(BotCommand(command="settings", description="设置")))
async def handle_settings(
    message: types.Message,
    bot: Bot,
    state: FSMContext,
    language: str = Language.ZH.value,
) -> None:
    tg_id = message.from_user.id
    user = await user_service.get_user_by_tg_id(tg_id)
    if not user:
        await message.answer("请先注册，再设置")
        return
    await bot_model_switch.send_settings(user, tg_id, 0, bot, None, state, language)
    await state.set_state(None)
    await message.delete()


@router.callback_query(BotSetting.filter())
async def handle_bot_setting(
    query: CallbackQuery,
    callback_data: BotSetting,
    bot: Bot,
    state: FSMContext,
    language: str,
):
    user = await user_service.get_user_by_tg_id(query.from_user.id)
    if not user:
        log.error(f"User not found: {query.from_user.id}")
        return
    log.info(f"handle_bot_setting,user_id: {user.id}, callback_data: {callback_data}")
    await bot_model_switch.send_settings(
        user,
        query.message.chat.id,
        query.message.message_id,
        bot,
        callback_data,
        state,
        language,
    )
    # 模型做了选择，则结束新手引导
    state_model = await bot_setting.get_state(state)
    if (
        state_model.new_user_guide
        and callback_data.type
        in [
            BotSettingType.MODEL.value,
            BotSettingType.CHAT_CHANNEL.value,
            BotSettingType.MODEL_V1.value,
        ]
        and callback_data.select
    ):
        await bot_setting.update_state_new_user_guide(state, False)


@router.message(Command("send_role"))
async def handle_send_role(
    message: types.Message,
    command: CommandObject,
    bot: Bot,
    state: FSMContext,
    language: str,
):
    await send_role(message, command, bot, state)
    await state.set_state(None)


@router.message(Command("reset"))
async def handle_reset(
    message: types.Message,
    command: CommandObject,
    bot: Bot,
    state: FSMContext,
    language: str,
):
    await reset_chat(message, command, bot, state, language)
    await state.set_state(None)


@router.message(Command("voucher"))
async def handle_voucher(
    message: types.Message,
    command: CommandObject,
    bot: Bot,
    state: FSMContext,
    language: str,
):
    await state.set_state(VoucherStateGroup.voucher)
    await message.answer(
        f'<b>在消息中直接输入卡密，发送给我，可完成卡密兑换💎</b>\n\n如果你还未购买卡密，请直接消息中输入 `/recharge` ，或在"输入消息"左侧点击菜单选择"充值"，去购买卡密'
    )


@router.message(Command("checkin"))
async def handle_check_in(message: types.Message, bot: Bot, language: str):
    await check_in_service.process_in_bot_check_in(message, bot)
    await message.delete()


@router.message(Command("group"))
async def handle_group(message: types.Message, language: str):
    builder = InlineKeyboardBuilder()
    main_group = await tg_config_service.get_main_group()
    builder.button(text="加入聊天群", url=main_group.url)
    await message.answer(
        f"点击链接或者按钮加群\n\n{main_group.url}",
        reply_markup=builder.as_markup(),
    )


@router.message(Command("channel"))
async def handle_channel(
    message: types.Message, command: CommandObject, bot: Bot, state: FSMContext
):
    builder = InlineKeyboardBuilder()
    builder.button(text="订阅频道", url=user_growth_constants.ROLE_CHANNEL_LINK)
    await message.answer(
        f"点击链接或者按钮订阅频道\n\n{user_growth_constants.ROLE_CHANNEL_LINK}",
        reply_markup=builder.as_markup(),
    )


@router.message(Command("invite_link"))
async def handle_invite(message: types.Message, bot: Bot, language: str):
    user: User = await user_service.get_user_by_tg_id(message.from_user.id)  # type: ignore
    await send_invitation_message(message, user, bot)


@router.message(Command("welfare"))
async def handle_welfare(message: types.Message, bot: Bot, language: str):
    await send_welfare_tip(bot, message)


@router.message(Command("help"))
async def handle_help_message(message: types.Message, bot: Bot, language: str):
    help_tip_text = f"""官方客服： @{BotReplace.MAIN_CUSTOMER_BOT.value}\n官方讨论组： @{BotReplace.MAIN_GROUP.value}\n角色卡频道： @{BotReplace.MAIN_CHANNEL.value}"""
    tips = await bot_message_service.format_content_replace(help_tip_text)
    sent_message = await bot.send_message(message.chat.id, tips)
    await tg_message_service.add_deleted_message(sent_message, bot_id=bot.id)


@router.message(Command("unlock"))
async def unlock_help(message: types.Message, bot: Bot):
    await send_unlock_tip(bot, message)


@router.message(Command("advanced"))
async def handle_advanced(message: types.Message, command: CommandObject, bot: Bot):
    builder = InlineKeyboardBuilder()
    info = await bot.get_me()
    url = f"https://t.me/{info.username}/tavern"
    builder.button(text="打开高级版", url=url)
    await bot.send_message(
        message.chat.id, "点击按钮打开高级版", reply_markup=builder.as_markup()
    )


@router.message(Command("tupian"))
async def handle_tupian(
    message: types.Message, command: CommandObject, bot: Bot, language: str
):
    return await send_tupian_message(message, bot, language)


@router.message(Command("hmai"))
async def handle_hmai(
    message: types.Message, command: CommandObject, bot: Bot, language: str
):
    return await handle_miniapp_open(message, command, bot, language)


@router.message(Command("miniapp"))
async def handle_miniapp(
    message: types.Message, command: CommandObject, bot: Bot, language: str
):
    return await handle_miniapp_open(message, command, bot, language)


async def handle_miniapp_open(
    message: types.Message, command: CommandObject, bot: Bot, language: str
):
    builder = InlineKeyboardBuilder()
    info = await bot.get_me()
    url = f"https://t.me/{info.username}/tavern"
    text = _tl("点击下方按钮打开小程序", language)
    btn_text = _tl("打开小程序", language)

    builder.button(text=btn_text, url=url)
    await bot.send_message(message.chat.id, text, reply_markup=builder.as_markup())


@router.message(VoucherStateGroup.voucher)
async def handle_voucher_message(message: types.Message, bot: Bot, state: FSMContext):
    logging.info(f"handle package voucher message: {message.text}")
    voucher_code = message.text
    user = await user_service.get_user_by_tg_id(message.from_user.id)
    if not user:
        await message.answer("请先注册，再兑换卡密")
        return
    if not voucher_code:
        await message.answer("卡密不能为空，请重新输入")
        return
    voucher = await package_voucher_service.get_package_voucher(voucher_code)
    if voucher is not None and voucher.distributor == "OP_LOTTERY":
        today_free_voucher_orders = (
            await package_voucher_service.get_today_lottery_voucher(user.id)
        )
        if len(today_free_voucher_orders) >= 1:
            await message.answer("今日已经兑换过一次了")
            return

    result, msg, recharge_order = await package_voucher_service.redeem_package_voucher(
        voucher_code, user.id, "CHAT_BOT"
    )
    if result:
        balance = await AccountService.get_total_balance(user.id)
        await user_growth_service.add_fc_reward(user)
        await re_purchase_service.after_recharge(user, recharge_order)
        if (
            recharge_order
            and recharge_order.recharge_product_id == SKIP_QUEUE_RECHARGE_PRODUCT_ID
        ):
            await message.answer(
                f"兑换成功！连续7天免费次数不排队。请重新选择角色卡开始聊天"
            )
        else:
            await message.answer(f"兑换成功，您的余额是 {balance} 💎")
    else:
        await message.answer(f"兑换失败，{msg}")


@router.message(F.successful_payment)
async def on_successful_payment(message: Message, bot: Bot, state: FSMContext):
    await handle_successful_payment(message, bot, state)


@router.message(~F.text.startswith("/"))
async def handle_message(
    message: types.Message, bot: Bot, state: FSMContext, language: str,bot_source:str
):
    if not message.from_user:
        log.error("message.from_user is None")
        return
    tg_user_id = message.from_user.id
    if message.from_user and message.from_user.is_premium is not None:
        await update_user_premium_status(
            message.from_user.id, message.from_user.is_premium
        )
    if not message or not message.text:
        log.warning(f"Handle message with no text,tg_user_id: {tg_user_id}")
        return

    text = message.text.strip()
    if text in ["画图", "出图", "生图"]:
        return await send_tupian_message(message, bot, language)

    user = await user_service.get_user_by_tg_id(tg_user_id=tg_user_id)
    if not user:
        log.error("User not found for tg_user_id: %s", tg_user_id)
        await message.answer("用户信息有误，请稍后尝试（或者联系客服）")
        return
    if user.status == UserStatus.CHAT_BLACK.value:
        error = _tl(ErrorKey.USER_IN_BLACKLIST.message(), language)
        await message.answer(error)
        return
    lock_key = "tg_chat_message_lock"
    lock_id = f"{tg_user_id}:{message.message_id}"
    if not redis_client.acquire_lock(lock_key, lock_id, 5 * 60, False):
        log.warning(f"repeat message, tg_uid: {tg_user_id},uid: {user.id}")
        return
    
    if bot_source == BotSource.HM_BOT.value:
        if await check_in_service.process_direct_message_check_in(message, bot, language):
            return
        await operation_service.send_bot_popup(
            bot, user, message.from_user.id, PopupPosition.USER_INPUT, language
        )
        # 未切换过通道的，提示通道
        if await bot_operation.channel_switch_guide(bot, tg_user_id, language):
            return
    
    # 新用户福利弹框 渠道用户：自动领取，非渠道用户：手动领取
    await bot_operation.new_user_benefit_popup(bot, tg_user_id, state, language, bot_source)

    chat_id = message.chat.id
    state_data = await state.get_data()
    state_data = ChatBotStateData(**state_data)
    bot_request = BotChatRequest(
        tg_id=chat_id,
        user_input=text,
        role_id=state_data.role_id if state_data.role_id else 0,
        conv_id=state_data.conversation_id if state_data.conversation_id else "",
        language=language,
        bot_source=bot_source,
        last_mids=state_data.last_mids if state_data.last_mids else []
    )
    await atomic_do_chat(user,bot_request, bot, state)


@router.callback_query(CheckInCallback.filter())
async def handle_recharge(
    query: CallbackQuery, bot: Bot, callback_data: CheckInCallback
):
    if not query.message or not isinstance(query.message, types.Message):
        log.error(f"Invalid message in callback query: {query.message}")
        return
    await check_in_service.process_in_bot_check_in(query.message, bot, query.from_user)


@router.message(Command(BotCommand(command="archives", description="聊天存档")))
async def handle_archives(message: Message, state: FSMContext, language: str) -> None:
    offset = 0
    limit = 12
    await bot_chat_history.send_archive_roles(
        message.from_user.id, message, state, offset, limit
    )


@router.message(Command(BotCommand(command="web", description="登录 web 版")))
async def handle_web_login(message: Message, bot: Bot, language: str) -> None:
    await send_login_link(message.chat.id, bot, language)


# 点击inline button 的翻页按钮，刷新消息内容到下一页
@router.callback_query(ArchivedRoleSlideCallback.filter())
async def handle_role_slide(
    query: CallbackQuery,
    callback_data: ArchivedRoleSlideCallback,
    bot: Bot,
    state: FSMContext,
):
    if not query.message or not isinstance(query.message, types.Message):
        log.error(f"Invalid message in callback query: {query.message}")
        return
    offset = callback_data.offset
    limit = callback_data.limit
    print(f"offset: {offset}, limit: {limit}")
    await bot_chat_history.send_archive_roles(
        query.from_user.id, query.message, state, offset, limit, True
    )


# 点击inline button， 返回指定角色的存档列表
@router.callback_query(ArchivedRoleSelectCallback.filter())
async def archive_role_select(
    query: CallbackQuery,
    callback_data: ArchivedRoleSelectCallback,
    bot: Bot,
    state: FSMContext,
):
    role_id = callback_data.role_id
    if not query.message or not isinstance(query.message, types.Message):
        log.error(f"Invalid message in callback query: {query.message}")
        return
    await bot_deeplink.handle_archive_click(
        query.from_user.id, role_id, query.message, bot, state
    )


@router.message(Command(BotCommand(command="new_roles", description="角色上新")))
async def handle_new_roles(
    message: types.Message, bot: Bot, state: FSMContext, language: str
):
    await chat_roles.send_role_card(message, bot, state, 1, tag=RoleTag.NEW.value)


@router.callback_query(NewRoleSlideCallback.filter())
async def handle_new_role_page(
    query: CallbackQuery,
    callback_data: NewRoleSlideCallback,
    bot: Bot,
    state: FSMContext,
    language: str,
    bot_source: str
):
    if not query.message or not isinstance(query.message, types.Message):
        log.error(f"Invalid message in callback query: {query.message}")
        return
    await chat_roles.send_role_card(
        query.message, bot, state, callback_data.page, True, callback_data.tag, language, bot_source
    )


@router.callback_query(RoleListPageCallback.filter())
async def handle_page(
    query: CallbackQuery,
    callback_data: RoleListPageCallback,
    bot: Bot,
    state: FSMContext,
    language: str,
    bot_source:str
):
    if not query.message or not isinstance(query.message, types.Message):
        log.error(f"Invalid message in callback query: {query.message}")
        return
    await chat_roles.send_role_card(
        query.message, bot, state, callback_data.page, language=language, bot_source=bot_source
    )


@router.callback_query(RoleSelectCallback.filter())
async def handle_role_select(
    query: CallbackQuery,
    callback_data: RoleSelectCallback,
    bot: Bot,
    state: FSMContext,
    language: str,
):
    role_id = callback_data.role_id
    user = await user_service.get_user_by_tg_id(query.from_user.id)
    if not user:
        await query.answer("请先注册，再选择角色")
        return
    await state.update_data({"role_id": role_id})
    await start_new_chat(query.message.chat.id, bot, state, user, language)


# 处理自动帮用户切换模型的inline button
@router.callback_query(ModelAutoChangeCallback.filter())
async def model_change(
    query: CallbackQuery,
    callback_data: ModelAutoChangeCallback,
    bot: Bot,
    state: FSMContext,
):
    mid = callback_data.mid
    if not mid:
        # 重新选择角色
        await handle_list(query.message, None, bot, state)
    else:
        user = await user_service.get_user_by_tg_id(query.from_user.id)
        if not user:
            return
        message_template = await bot_model_switch.model_select_template(user, mid)
        await bot.send_message(
            chat_id=query.message.chat.id,
            text=message_template.tips,
            parse_mode=ParseMode.HTML,
        )


# 处理「聊天返钻石活动」的 领取inline button
@router.callback_query(DiamondSeasonRewardCallback.filter())
async def handle_diamond_season_reward(
    query: CallbackQuery,
    bot: Bot,
    callback_data: DiamondSeasonRewardCallback,
    state: FSMContext,
):
    task_id = callback_data.task_id
    user = await user_service.get_user_by_tg_id(query.from_user.id)

    if user is None or user.id != callback_data.user_id:
        await bot.send_message(
            query.message.chat.id, "领取链接不合法", parse_mode=ParseMode.HTML
        )
        return

    _, msg = await user_diamond_season_service.receive_diamond_reward(user.id, task_id)
    await bot.send_message(query.message.chat.id, msg, parse_mode=ParseMode.HTML)


@router.callback_query(operation_service.ComCallbackCommand.filter())
async def handle_com_callback_command(
    query: CallbackQuery,
    callback_data: operation_service.ComCallbackCommand,
    bot: Bot,
    language: str,
    bot_source:str
):
    tg_id = query.from_user.id
    user = await user_service.get_user_by_tg_id(tg_id)
    if not user:
        await query.answer("请先注册")
        return
    if callback_data.command == "free_benefit":
        await bot_cmd.free_benefit(user, tg_id, bot, language)
    if callback_data.command == "settings":
        await bot_model_switch.send_settings(
            user, tg_id, query.message.message_id, bot, None
        )


@router.message(Command(BotCommand(command="role_bot", description="角色橱窗")))
async def command_role_bot(message: Message, language: str) -> None:
    button = Button(text="打开橱窗", url=user_growth_constants.ROLE_CHANNEL_LINK)
    template = MessageTemplate(
        tips="打开角色橱窗，一起浏览和讨论所有角色", buttons=[button]
    )
    await message.answer(template.tips, reply_markup=template.as_markup())


@router.message(Command(BotCommand(command="language", description="语言设置")))
async def command_language(message: Message, bot: Bot, language: str) -> None:
    await bot_setting.send_language_select(message.from_user.id, bot, language)
    await message.delete()


@router.callback_query(LanguageSelectCallback.filter())
async def handle_language_select(
    query: CallbackQuery, callback_data: LanguageSelectCallback, bot: Bot, language: str
):
    await bot_setting.select_language(
        query.from_user.id, bot, query.message, callback_data, language
    )


@role_bot_router.post("/role_hook/{bot_id}")
async def webhook_handler(
    bot_id: str, update: dict, x_telegram_bot_api_secret_token: str = Header(...)
):
    if x_telegram_bot_api_secret_token != WEBHOOK_SECRET:
        return "Invalid token"
    logging.info(f"role_bot webhook_handler: {bot_id}, {update}")
    try:
        bot = bots.get(bot_id.upper())
        if bot is None:
            bot = chat_bot
        bot_config = await tg_config_service.get_bot_config_by_id(bot.id)
        if not bot_config:
            log.error(f"Bot config not found for bot_id: {bot_id}")
            return "Bot config not found"
        language_code = await bot_setting.analysis_bot_language_code(update)
        user = await bot_setting.analysis_user_by_update(update)
        is_bot = await bot_setting.analysis_check_bot_by_update(update)
        language = await language_service.get_user_language(user, bot_config.lang, language_code)
        text = await bot_setting.analysis_text_by_update(update)
        tg_id = await bot_setting.analysis_tg_id_by_update(update)
        bot_source = BotSource.OVERSEAS_BOT.value if bot_config and bot_config.lang == Language.EN.value else BotSource.HM_BOT.value
        if bot_source == BotSource.OVERSEAS_BOT.value and not bot_setting.check_english_bot_command(text):
            log.warning(
                f"UnSupportCommand, bot_id: {bot_id}, text: {text}, language: {language}"
            )
            return
        
        await dp.feed_raw_update(bot=bot, update=update, language=language.value, bot_source=bot_source)
        if is_bot or not user or not tg_id:
            return
        if bot_source == BotSource.HM_BOT.value and user:
            await bot_cmd.free_benefit(user, tg_id, bot, language.value, True)
        await user_active_service.refresh_by_user_id(bot.id, user.id)
        await send_transfer_message(tg_id, bot)
    except Exception as e:
        logging.exception(e)
