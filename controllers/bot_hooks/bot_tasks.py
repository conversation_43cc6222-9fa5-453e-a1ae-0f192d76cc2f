from datetime import timedelta
from aiogram import Bot
from aiogram.types import CallbackQ<PERSON>y, Message, custom
from aiogram.enums import ParseMode
from aiogram.utils.keyboard import Inline<PERSON>eyboardBuilder
from aiogram.fsm.context import FSMContext
from common import copywriting_templates
from common.common_constant import BotCategory, BotReplace, ChannelCategory
from controllers.bot_hooks.bot_setting import (
    InlineCheckInCallback,
    RoleListPageCallback,
    RechargeCallback,
    router,
)
from persistence.models.models import User
from services import (
    bot_message_service,
    chat_bot_task_service,
    tg_config_service,
    tg_message_service,
    user_growth_service,
)
from services.account_service import AccountService
from services.user_service import UserService
from utils import env_const, user_growth_constants
from common.bot_common import chat_bot_charge_tip, charge_url

user_service = UserService()

# c3_task_tip = '''【免费领取金币奖励活动】恭喜你被幸运女神选中！您可参加如下活动，领取免费金币 🟡

# 【活动2】加入聊天群送 500 🟡，点击按钮加入，或使用 `/group` 命令（活动完成进度：{group_task_progress}）

# 【活动3】加入角色卡频道送 500 🟡，点击按钮加入，或使用 `/channel` 命令（活动完成进度：{channel_task_progress}）

# 【活动4】完成首次签到，送 300 🟡，使用 `/check_in` 命令签到，每日签到赠送300 🟡（活动完成进度：{check_in_task_progress}）
# '''

# role_task_tip = '''【活动5】恭喜你被幸运女神选中！现在只需和5个不同角色发起聊天，即可免费领取1000🟡奖励

# 您可以通过/list 命令浏览全部角色，然后点击对应角色卡片的编号按钮与角色发起聊天，并至少主动向角色成功发送一句话，祝您聊天愉快（活动完成进度：{progress}/5）'''

task_tip = """<b>🔥 新用户超级专属大礼包 - 仅限24小时 🔥

🎉 恭喜您加入AI伴侣世界！ 1000🟡注册奖励已到账！</b>

您的VIP级别宠爱礼包等待解锁，<b>⏳限时24小时特权，过期不补！</b>

<b>👉🎁 【活动1 - 限时尊享套餐】💰</b> 
<b>错过 = 损失一个亿！24小时内超值特权！
⏳ 限时24小时，错过等一年！点击下方按钮立即领取 →
✅ [解锁新用户专属套餐]</b>

<b>💎 【活动2 - 加入VIP官方群】
🎁 立刻领取500🟡！ 进入高端社群，与其他玩家交流心得，解锁隐藏玩法！
📌 点击加入 → [立即进群]</b>

<b>💎 【活动3 - 加入官方频道】
🎁 再送500🟡！ 发现更多性格各异的AI伴侣，找到最适合您的灵魂伴侣！
📌 点击加入 → [立即关注]</b>

<b>💎 【活动4 - 每日签到领钻石】
🎁 每日签到领奖励，最多1000🟡！ 连续签到奖励更多，每天都有惊喜！
📌 点击签到 → [立即领取]</b>

<b>💎 【活动5 - 多角色互动奖励】
和 5个不同角色互动，即可额外领取 1000🟡！
📌 点击探索 → [解锁更多角色]</b>

<b>🚀 立即领取您的专属福利！
💥 VIP级特权仅限24小时！
⬇️ 立即领取您的超级大礼包 ⬇️</b>

（此消息不影响您和AI伴侣聊天，您可以随时回到当前会话中）
"""

welfare_tip = f"""幻梦官方福利频道，已开通📣📣
速速加入，近期将发布多个福利活动，0元购抢🟡兑换码🙊🙊

👉记住 @huanmeng_ai 域名👈并关注频道，源源不断领福利
"""


def format_progress(finished: bool) -> str:
    return "已完成" if finished else "未完成"


async def try_send_tasks(
    bot: Bot, chat_id: int, user: User, role_id: int, new_user: bool = False
):
    balance = await AccountService.get_total_balance(user.id)
    builder = InlineKeyboardBuilder()
    main_tma = await tg_config_service.get_main_bot_by_category(BotCategory.TMA)
    charge_url = f"{main_tma.url}/tavern?startapp=e_eyJwIjoicGF5In0-u_1999"
    main_group = await tg_config_service.get_main_group()
    main_role_channel = await tg_config_service.get_main_channel_by_category(
        ChannelCategory.ROLE
    )

    builder.button(text="领40W金币超值礼包", url=charge_url)
    builder.button(text="2-领500🟡", url=main_group.url)
    builder.button(text="3-领500🟡", url=main_role_channel.url)
    builder.button(text="4-领最多1000🟡", callback_data=InlineCheckInCallback(c=1))
    builder.button(text="5-领1000🟡", callback_data=RoleListPageCallback(page=1))
    builder.adjust(3)
    if new_user:
        content = task_tip.format(
            balance=balance,
            group_task_progress=format_progress(False),
            channel_task_progress=format_progress(False),
            check_in_task_progress=format_progress(False),
            role_progress=0,
        )
        await bot.send_message(
            chat_id,
            content,
            parse_mode=ParseMode.HTML,
            reply_markup=builder.as_markup(),
        )
        await chat_bot_task_service.add_user_task(user.id, "CHAT_INIT", "chat_count")
        return

    group_joined = await user_growth_service.check_join_any_chat_group_task(user.id)
    channel_joined = await user_growth_service.check_join_chat_task(
        user.id, int(user_growth_constants.ROLE_CHANNEL_ID)
    )
    check_in_finished = await chat_bot_task_service.daily_check_in_finished(user.id)

    chat_roles = await chat_bot_task_service.get_user_chat_roles(user.id)
    chat_roles_count = len(chat_roles)

    if group_joined and channel_joined and check_in_finished and chat_roles_count >= 5:
        return

    chat_rounds = await chat_bot_task_service.get_user_chat_count(user.id)
    if chat_roles_count < 5 and (chat_rounds == 5 or chat_roles_count == 10):
        content = task_tip.format(
            balance=balance,
            group_task_progress=format_progress(group_joined),
            channel_task_progress=format_progress(channel_joined),
            check_in_task_progress=format_progress(check_in_finished),
            role_progress=chat_roles_count,
        )
        await bot.send_message(
            chat_id,
            content,
            parse_mode=ParseMode.HTML,
            reply_markup=builder.as_markup(),
        )
        await chat_bot_task_service.add_user_task(
            user.id, f"CHAT_COUNT_{chat_rounds}", "chat_count"
        )

    roles_chat = await chat_bot_task_service.roles_chat_satisfied(user.id, role_id)
    if roles_chat:
        await chat_bot_task_service.add_user_roles_chat_task(user.id)
        sent_message = await bot.send_message(
            chat_id,
            f"【活动5-完成通知】恭喜您成功完成和5个不同角色卡聊天任务，1000 🟡 已到账，您可以点击这里关注官方频道获取最新角色信息\n\n 当前总余额：{balance}",
        )
        await tg_message_service.add_deleted_message(
            sent_message, "CHAT_BOT", bot_id=bot.id
        )
    elif role_id not in chat_roles and chat_roles_count < 4:
        chat_roles_count += 1
        sent_message = await bot.send_message(
            chat_id,
            f"<i>【活动5-进度通知】恭喜您已经成功和{chat_roles_count}个角色发起聊天，活动完成进度{chat_roles_count}/5，进只需要再和{5-chat_roles_count}个角色聊天，即可领取 1000 🟡\n\n 当前总余额：{balance}</i>",
        )
        await tg_message_service.add_deleted_message(
            sent_message, "CHAT_BOT", bot_id=bot.id
        )


async def do_chatbot_check_in_task(bot: Bot, chat_id: int, user: User):
    balance = await AccountService.get_total_balance(user.id)
    checked = await chat_bot_task_service.daily_check_in_finished(user.id)
    send_message = None
    if checked:
        send_message = await bot.send_message(
            chat_id, f"今日已签到，明日再来哦\n\n<i> 当前总余额：{balance}</i>"
        )
    else:
        await chat_bot_task_service.add_user_check_in_task(user.id)
        check_in_times = await chat_bot_task_service.daily_check_in_count(user.id)
        if check_in_times == 1:
            send_message = await bot.send_message(
                chat_id,
                f"""<i>🏆【活动4-首次签到完成通知】恭喜您完成首次签到，300🟡 已到账</i>

<i>您可以每日使用 `/check_in` 命令进行签到，领取当天签到奖励</i>

<i>当前总余额：{balance}</i>""",
            )
        else:
            send_message = await bot.send_message(
                chat_id,
                f"""恭喜您完成每日签到任务，300 🟡 已到账\n\n<i>当前总余额：{balance}</i>""",
            )
    if send_message is not None:
        await tg_message_service.add_deleted_message(
            send_message, "CHAT_BOT", bot_id=bot.id
        )


@router.callback_query(InlineCheckInCallback.filter())
async def handle_role_select(query: CallbackQuery, bot: Bot):
    user = await user_service.get_user_by_tg_id(query.from_user.id)
    await do_chatbot_check_in_task(bot, query.message.chat.id, user)


@router.callback_query(RechargeCallback.filter())
async def handle_recharge(
    query: CallbackQuery, bot: Bot, callback_data: RechargeCallback, state: FSMContext
):
    builder = InlineKeyboardBuilder()
    builder.button(text="立即充值", url=charge_url)
    user = await user_service.get_user_by_tg_id(query.from_user.id)
    # is_pay_user = await user_service.is_payed_user(user.id)
    text = await chat_bot_charge_tip(user.id)
    send_message = await bot.send_message(
        chat_id=query.message.chat.id,
        text=text,
        parse_mode=ParseMode.HTML,
        reply_markup=builder.as_markup(),
    )
    await state.set_state(None)
    await tg_message_service.add_deleted_message(
        send_message, "CHAT_BOT", bot_id=bot.id
    )


async def send_welfare_tip(bot: Bot, message: Message):
    user = await user_service.get_user_by_tg_id(message.from_user.id)
    reply_message = await message.answer(text=welfare_tip, parse_mode=ParseMode.HTML)
    await tg_message_service.add_check_in_deleted_message(
        message, reply_message, user.id, timedelta(minutes=2), "CHAT_BOT", bot.id
    )
