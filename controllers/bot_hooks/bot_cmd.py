import datetime
import logging
from aiogram import Bo<PERSON>
from aiogram.enums import ParseMode
from common.bot_common import Button, MessageTemplate
from common.common_constant import ApiSource, Language
from persistence.models.models import ExpirableAward, RechargeOrder, User
from services import recharge_service, tg_config_service, tg_message_service, user_service, welfare_service
from services.account_service import AccountService
from services.operation_service import ComCallbackCommand
from services.user import user_benefit_service
from utils import date_util, descriptions, str_util
from aiogram.types import (
    CallbackQuery,
    Message,
    InlineKeyboardMarkup,
    InlineKeyboardButton,
    CopyTextButton,
)

from utils.translate_util import _tl

log = logging.getLogger(__name__)


async def free_benefit(user: User, tg_id: int, bot: Bot, language: str, from_auto_in_background: bool = False):
    if from_auto_in_background:
        api_source = ApiSource.TMA
        if language == Language.EN.value:
            api_source = ApiSource.OVERSEAS_WEB
        latest_received_benefit = await welfare_service.get_user_latest_received_benefit(user.id, api_source)
        # 第二次领取开始，才触发这个弹窗
        if not latest_received_benefit:
            return
        # 目前没有可领取的免费聊天次数福利，不触发弹窗
        if latest_received_benefit.finished_at > date_util.utc_now() - datetime.timedelta(days=1):
            return 
    try:      
        tips, result = await welfare_service.bot_receive_benefit(user, language)
        if not result and from_auto_in_background:
            return
        if not tips:    
            return
        button = Button(
            callback_data=ComCallbackCommand(command="settings"),
            text=_tl("切换模型", language),
        )
        msg_template = MessageTemplate(
            tips=tips,
            buttons=[button],
        )
        answer_msg = await bot.send_message(
            tg_id,
            msg_template.tips,
            reply_markup=msg_template.as_markup(),
            parse_mode=ParseMode.HTML,
        )
        await tg_message_service.del_msg(answer_msg.message_id, bot.id, tg_id)
        return
    except Exception as e:
        log.error(f"领取免费权益失败: {e}")
        if from_auto_in_background:
            return
        answer_msg = await bot.send_message(
            tg_id, _tl("领取免费权益失败，请稍后再试,或者联系客服", language)
        )
        await tg_message_service.del_msg(answer_msg.message_id, bot.id, tg_id)


async def balance(user: User, tg_id: int, bot: Bot):
    def format_datetime(dt: datetime.datetime) -> str:
        if dt.year > 2100:
            return "永久有效"
        dt = date_util.utc2utc8(dt)
        return dt.strftime("%Y/%m/%d")

    def format_display(idx: int, exp: ExpirableAward, order: RechargeOrder) -> str:
        desc = descriptions.format_charge_channel(order, "zh")
        return f"{idx+1}. {exp.balance}，{desc}， 创建时间: {format_datetime(exp.created_at)}， 过期时间: {format_datetime(exp.expires_at)}"

    def format_g1(idx: int, exp: ExpirableAward) -> str:
        return f"{idx+1}. {exp.balance}，新用户-限时礼包， 创建时间{format_datetime(exp.created_at)}， 过期时间: {format_datetime(exp.expires_at)}"

    payed_balance = await AccountService.get_payed_total_balance(user.id)
    reward_balance = await AccountService.get_reward_total_balance(user.id)
    all_items = await AccountService.get_effective_rewards(user.id)
    items = [exp for exp in all_items if exp.out_order_id != "G1"]
    order_ids = [order.out_order_id for order in items]
    recharge_orders = await recharge_service.get_recharge_orders_by_id(
        user.id, order_ids
    )
    recharge_order_dict = {
        str(order.recharge_order_id): order for order in recharge_orders
    }
    contents = [
        format_display(idx, exp, recharge_order_dict[exp.out_order_id])
        for idx, exp in enumerate(items)
        if exp.out_order_id in recharge_order_dict
    ]
    gift = [gift for gift in all_items if gift.out_order_id == "G1"]
    if len(gift) > 0:
        contents.append(format_g1(len(items), gift[0]))
    details = "\n".join(contents)
    details = str_util.tg_caption_cut(details)
    benefit_desc = await user_benefit_service.bot_user_benefit_desc(user)
    replay_message = f"您的余额是 {payed_balance}💎 {reward_balance}🟡"
    if benefit_desc:
        replay_message += f"\n\n{benefit_desc}"
    if details:
        replay_message += f"\n\n{details}"

    charge_url = "https://www.sdfkw.xyz/links/1275C4C5"

    sent_message = await bot.send_message(
        tg_id,
        replay_message,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[[InlineKeyboardButton(text="立即充值", url=charge_url)]]
        ),
    )
    await tg_message_service.del_msg(sent_message.message_id, bot.id, tg_id)
