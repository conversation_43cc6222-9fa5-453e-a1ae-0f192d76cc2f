from tortoise import <PERSON><PERSON><PERSON>
from fastapi import (
    APIRouter,
)
from fastapi.responses import JSONResponse
from controllers.group_helper_webhook import redis_client

health_check_router = APIRouter()

def check_redis() -> bool:
    try:
        redis_client.get('health_check')
        return True
    except Exception as e:
        return False

async def check_db() -> bool:
    try:
        conn = Tortoise.get_connection('default')
        await conn.execute_query('SELECT 1;')
        return True
    except Exception as e:
        return False

@health_check_router.get("/health_check")
async def health_check():
    redis_ok = check_redis()
    db_ok = await check_db()
    if redis_ok and db_ok:
        return 'ok'
    return JSONResponse(content={'redis': redis_ok, 'db': db_ok}, status_code=500)