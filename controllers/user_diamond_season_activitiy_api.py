from aiogram.enums import ParseMode
import logging
import re
from fastapi import APIRouter, Depends
from common.common_constant import <PERSON>t<PERSON>ategor<PERSON>, PopupShowPeriod, PopupType
from controllers.user_check import get_current_user
from services import bot_services, operation_service, tg_config_service, user_diamond_season_service, user_service
from services.account_service import AccountService
from utils import response_util


diamond_season_activity_router = APIRouter()

log = logging.getLogger(__name__)


# 查询用户在当前进行中的返钻活动的消耗进度
@diamond_season_activity_router.get("/user/diamond_season_activity/progress")
async def get_diamond_season_progress(user_id: int = Depends(get_current_user)):
    progress_info  = await user_diamond_season_service.get_diamond_task_progress_by_uid(user_id)
    if progress_info is None:
        return response_util.ok()
    payed_balance = await AccountService.get_payed_total_balance(user_id)
    if payed_balance > 0 or not progress_info.get("msg"):
        progress_info.pop('msg', None)
        return response_util.ok(progress_info) 
    # 聊天过程中提醒充值的消息，只发一次
    task_id = progress_info["task_id"]
    identifier = f"{task_id}{PopupShowPeriod.ONCE.value}"
    pop_up_recored = await operation_service.get_popup_record(user_id, identifier, PopupType.DIAMOND_ACTIVITY_RECHARGE)
    if pop_up_recored:
        progress_info.pop('msg', None)
        return response_util.ok(progress_info)
    await operation_service.add_popup_record(user_id, identifier, PopupType.DIAMOND_ACTIVITY_RECHARGE)
    markdown_msg = _html_bold_to_markdown(progress_info.get("msg"))
    progress_info["msg"] = markdown_msg
    return response_util.ok(progress_info)
    
    

# 报名
@diamond_season_activity_router.post("/user/diamond_season_activity/enroll")
async def enroll_diamond_season_activity(task_id:str, user_id: int = Depends(get_current_user)):
    role_ids, msg  = await user_diamond_season_service.enroll_diamond_season_activity(user_id, task_id)
    if role_ids is None:
        return response_util.ok({"result": False})
    else:
        # 额外通过bot发送报名成功的消息
        await _send_bot_msg(user_id, msg, role_ids)
        return response_util.ok({"result": True})



async def _send_bot_msg(user_id: int, msg: str, role_ids: list[int]):
    tma_bot_config = await tg_config_service.get_main_bot_by_category(BotCategory.TMA)
    tg_info = await user_service.get_tg_user_by_id(user_id)
    if not tg_info: 
        return
    sender = await tg_config_service.get_sender_bot_by_id(tma_bot_config.bot_id)
    # -1 表示所有角色卡都支持
    if -1 not in role_ids:
        await sender.send_message(tg_info.tg_id, msg, parse_mode=ParseMode.HTML)
    else:
        start_markup = await bot_services.get_start_markup(sender)
        await sender.send_message(tg_info.tg_id, msg, reply_markup=start_markup, parse_mode=ParseMode.HTML)

    extra_msg = await user_diamond_season_service.get_diamond_activity_role_msg(role_ids)
    if extra_msg:
        await sender.send_message(tg_info.tg_id, extra_msg, parse_mode=ParseMode.HTML) 

# 返钻活动的弹窗
@diamond_season_activity_router.get("/user/diamond_season_activity/popup")
async def pop_up(user_id: int = Depends(get_current_user)):
    task, is_enrolled = await user_diamond_season_service.get_enrolled_active_diamond_task_by_user_id(user_id)
    if task is None:
        return response_util.ok()
    task_id = str(task.task_id)
    if is_enrolled:
        # 报名成功的弹窗，只会弹出一次
        identifier = f"{task_id}{PopupShowPeriod.ONCE.value}"
        pop_up_recored = await operation_service.get_popup_record(user_id, identifier, PopupType.DIAMOND_ACTIVITY_ENROLL_SUCCESS)
        if pop_up_recored:
            return response_util.ok()
        msg = await user_diamond_season_service.generate_enroll_success_msg(task, False)
        await operation_service.add_popup_record(user_id, identifier, PopupType.DIAMOND_ACTIVITY_ENROLL_SUCCESS)
        markdow_msg = _html_bold_to_markdown(msg)
        return response_util.ok({"msg":markdow_msg, "type": PopupType.DIAMOND_ACTIVITY_ENROLL_SUCCESS.value, "task_id": task_id})
    else:
        # 提醒报名的弹窗，每次都会弹出
        payed_balance = await AccountService.get_payed_total_balance(user_id)
        if payed_balance <= 0 or payed_balance >= task.required_diamond_amount:
            return response_util.ok()
        result, _ = await user_diamond_season_service.check_max_participants(task)
        if not result:
            return response_util.ok()
        result, _ = await user_diamond_season_service.check_exclusive_enrollment(task, user_id)
        if not result:
            return response_util.ok()
        msg = await user_diamond_season_service.generate_notify_enroll_msg(task)
        markdow_msg = _html_bold_to_markdown(msg)
        return response_util.ok({"msg":markdow_msg, "type": PopupType.DIAMOND_ACTIVITY_NOTICE.value, "task_id": task_id})


def _html_bold_to_markdown(text):
    # 使用正则表达式查找所有<b>标签并替换
    pattern = r'<b>(.*?)</b>'
    markdown_text = re.sub(pattern, r'**\1**', text)
    return markdown_text
