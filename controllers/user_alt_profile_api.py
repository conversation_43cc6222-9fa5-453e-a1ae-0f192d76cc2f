import json
import logging
from typing import Annotated, Optional
from fastapi import (
    Depends,
    APIRouter,
    Form,
    Header,
    UploadFile,
)
from pydantic import BaseModel
from common.common_constant import (
    CosPrefix,
    Language,
)
from services import (
    user_alt_profile_service,
)
from utils import cos_util, response_util, token_util
from .user_check import get_current_user


user_alt_profile_api_router = APIRouter()

log = logging.getLogger(__name__)

TOKEN_MAX_LENGTH = 150
class UserProfileResponse(BaseModel):
    id: int
    user_id: int
    nickname: str
    avatar: str
    persona_setting: str = ''
    status: int

# 用户可以额外创建2组昵称和头像
# 每组都包含一个补充介绍：会在跟AI聊天时，用这个补充介绍去填充chatInput的personality字段

# 获取用户所有的未删除的profile
@user_alt_profile_api_router.get("/user/alt_profile")
async def get_user_all_alt_profile(user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value)):
    result = await user_alt_profile_service.get_user_all_alt_profile(user_id)
    if not result:
        return response_util.ok(
            data={"alt_profile_list": []},
        )
    alt_profile_list = []
    for profile in result:
        profile_response = UserProfileResponse(
            id=profile.id,
            user_id=profile.user_id,
            nickname=profile.nickname,
            avatar=profile.avatar,
            persona_setting=profile.persona_setting,
            status=profile.status,
        )
        alt_profile_list.append(profile_response)
    return response_util.ok(
        data={"alt_profile_list": alt_profile_list},
    )


class UserProfileSettings(BaseModel):
    profile_id: Optional[int] = None
    nickname: Optional[str] = None
    persona_setting:Optional[str] = None

# 创建profile
@user_alt_profile_api_router.post("/user/alt_profile/create")
async def create_user_alt_profile(
    setting: Annotated[str, Form()],
    avatar_img: Optional[UploadFile] = None,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    
    profile_list = await user_alt_profile_service.get_user_all_alt_profile(user_id)
    if profile_list and len(profile_list) >= 3:
        return response_util.def_error("Profile limit reached")

    setting_json = json.loads(setting)
    user_profile_setting = UserProfileSettings(**setting_json)
    if not user_profile_setting.nickname:
        return response_util.json_param_error()
    nickname = user_profile_setting.nickname

    avatar = ''
    if avatar_img:
        avatar = cos_util.upload_image(avatar_img, CosPrefix.AVATAR)

    persona_setting = ''
    if user_profile_setting.persona_setting:
        token_count = token_util.num_tokens_from_string(user_profile_setting.persona_setting)
        if token_count > TOKEN_MAX_LENGTH:
            return response_util.def_error("persona setting is too long")
        persona_setting = user_profile_setting.persona_setting

    new_profile = await user_alt_profile_service.create_user_alt_profile(
        user_id=user_id,
        nickname=nickname,
        avatar=avatar,
        persona_setting=persona_setting,
    )
    if not new_profile:
        return response_util.def_error("Failed to create profile")
    
    profile_response = UserProfileResponse(
        id=new_profile.id,
        user_id=new_profile.user_id,
        nickname=new_profile.nickname,
        avatar=new_profile.avatar,
        persona_setting=new_profile.persona_setting,
        status=new_profile.status,
    )
    return response_util.ok(
        data={"new_profile": profile_response},
    )

# 更新profile（不包括状态）
@user_alt_profile_api_router.post("/user/alt_profile/update")
async def update_user_alt_profile(
    setting: Annotated[str, Form()],
    avatar_img: Optional[UploadFile] = None,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    setting_json = json.loads(setting)
    user_profile_setting = UserProfileSettings(**setting_json)
    if not user_profile_setting.profile_id:
        return response_util.json_param_error()
    profile_id = user_profile_setting.profile_id
    
    nickname = None
    if user_profile_setting.nickname:
        nickname = user_profile_setting.nickname
    avatar = None
    if avatar_img:
        avatar = cos_util.upload_image(avatar_img, CosPrefix.AVATAR)
    persona_setting = None
    if user_profile_setting.persona_setting:
        token_count = token_util.num_tokens_from_string(user_profile_setting.persona_setting)
        if token_count > TOKEN_MAX_LENGTH:
            return response_util.def_error("persona setting is too long")
        persona_setting = user_profile_setting.persona_setting
    if not nickname and not avatar and not persona_setting:
        return response_util.json_param_error()
    
    result = await user_alt_profile_service.update_user_alt_profile(user_id, profile_id, nickname, avatar, persona_setting)
     
    return response_util.ok({'result': result})

# 切换profile
@user_alt_profile_api_router.post("/user/alt_profile/active")
async def active_user_alt_profile(
    profile_id: int,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    result = await user_alt_profile_service.active_user_alt_profile(user_id, profile_id)
    return response_util.ok({'result': result})

# 删除profile
@user_alt_profile_api_router.post("/user/alt_profile/delete")
async def delete_user_alt_profile(
    profile_id: int,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    result = await user_alt_profile_service.delete_user_alt_profile(user_id, profile_id)
    return response_util.ok({'result': result})
    

