import asyncio
import logging
from fastapi import Form, <PERSON><PERSON>, Depen<PERSON>, APIRouter, Request
from pydantic import BaseModel
from controllers.user_check import get_current_user, user_service
from common.common_constant import (
    Language,
)
from services.chat import (
    chat_snapshot_service,
)
from utils import (
    exception_util,
    response_util,
)
from utils.translate_util import _tl

chat_snapshot_api_router = APIRouter()

log = logging.getLogger(__name__)


class GenerateSnapshotRequest(BaseModel):
    role_id: int
    conversation_id: str


@chat_snapshot_api_router.post("/user/chat/snapshot/generate")
async def user_chat_snapshot(
    request: GenerateSnapshotRequest,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    logging.info(
        f"GenerateChatSnapshot user_id: {user_id}, request: {request.model_dump_json()}"
    )
    user = await user_service.get_user_by_id(user_id)
    ret = await chat_snapshot_service.generate_chat_snapshot_new(
        user=user,
        conversation_id=request.conversation_id,
        language=current_language,
    )
    if not ret:
        raise exception_util.verify_exception(message="快照生成失败(本次生成未扣费)")
    return response_util.ok(data=ret)


@chat_snapshot_api_router.get("/user/chat/snapshot/list")
async def user_chat_snapshot_list(
    offset: int = 0,
    limit: int = 20,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    user = await user_service.get_user_by_id(user_id)
    total, snapshots = await chat_snapshot_service.list_user_snapshots(
        user, offset=offset, limit=limit, language=current_language
    )
    return response_util.ok({"total": total, "user_snapshots": snapshots})


@chat_snapshot_api_router.post("/user/chat/snapshot/delete")
async def delete_user_snapshot(
    user_snapshot_id: int, user_id: int = Depends(get_current_user)
):
    ret = await chat_snapshot_service.delete_user_snapshot(user_id, user_snapshot_id)
    return response_util.ok({"result": ret})
