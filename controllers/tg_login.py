import json
from fastapi import (
    APIRouter,
)
from fastapi.responses import JSONResponse, RedirectResponse
import hmac
import hashlib

from pydantic import BaseModel
from datetime import datetime, timedelta, UTC
from aiogram.utils.deep_linking import decode_payload
from persistence import redis_client
from persistence.models.models import User, UserRegisterSource
from services.user_service import user_service
from utils import env_const, env_util
from .user_check import create_access_token
from persistence.redis_client import redis_client


tg_login_router = APIRouter()

class TgLoginRequest(BaseModel):
    uid: str
    session_code: str

class TgAuthRequest(BaseModel):
    auth_data: str

async def create_login_result(user: User):
    if env_util.is_prod_env():
        access_token_expires = timedelta(days=7)
    else:
        access_token_expires = timedelta(days=365)
    access_token = create_access_token(
        data={"sub": str(user.id)}, expires_delta=access_token_expires
    )
    res = JSONResponse({"message": "Login successful", "code": 0})
    res.set_cookie(
        "token",
        access_token,
        samesite="none",
        secure=True,
        expires=datetime.now(UTC) + timedelta(days=30),
    )
    return res

@tg_login_router.post("/tg_code_login")
async def login_from_tg(request: TgLoginRequest):
    session_value = redis_client.get(f'tg_login:{request.session_code}')
    if not session_value:
        return JSONResponse(status_code=401, content={'message': 'Invalid link'})
    param_uid = decode_payload(request.uid)
    session_uid = session_value.decode('utf-8')
    if session_uid != param_uid:
        return JSONResponse(status_code=401, content={'message': 'Invalid link'})
    redis_client.delete(f'tg_login:{request.session_code}')
    user = await user_service.get_user_by_tg_id(int(param_uid))
    if not user:
        return JSONResponse(status_code=401, content={'message': 'Invalid link'})

    return await create_login_result(user)

@tg_login_router.post("/tg_auth_login")
async def logout_from_tg(req: TgAuthRequest):
    data = json.loads(req.auth_data)
    hash = data.pop('hash')
    kvs = [f'{k}={v}' for k, v in data.items()]
    kvs.sort()
    org_value = '\n'.join(kvs)
    
    # Create HMAC-SHA256 with bot token
    token = env_const.LOGIN_BOT_TOKEN
    secret = hashlib.sha256(token.encode()).digest()
    hmac_obj = hmac.new(secret, org_value.encode(), hashlib.sha256)
    result = hmac_obj.hexdigest()
    
    if not hmac.compare_digest(result.lower(), hash.lower()):
        return JSONResponse(status_code=401, content={'message': 'Invalid auth data'})
    user = await user_service.get_user_by_tg_id(int(data['id']))
    if user:
        return await create_login_result(user)
    
    user = await user_service.register_by_tg(
        int(data['id']), data['first_name'],
        data.get('last_name', ''), data.get('username', ''), 
        register_source=UserRegisterSource.WEB,
        is_premium=data.get('is_premium', False)
    )

    return await create_login_result(user)