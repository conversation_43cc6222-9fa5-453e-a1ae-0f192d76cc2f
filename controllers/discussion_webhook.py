import json
import logging
import os
from aiogram import Dispatcher, Router, types
from aiogram.enums import ParseMode
from fastapi import Head<PERSON>
from fastapi.routing import APIRouter
from common.common_constant import BotCategory
from services import tg_config_service
from services.bot_services import adv_role_channel_bot
from utils import env_const
from persistence.redis_client import redis_client

bot_discussion_router = APIRouter()

WEBHOOK_PATH = '/discussion_webhook'
WEBHOOK_SECRET = os.environ['TG_WEBHOOK_SECRET']

router = Router()

bot = adv_role_channel_bot
dp = Dispatcher(bot=bot)
dp.include_router(router)

@router.message()
async def handle_message(message: types.Message):
    logging.info(f'handle_message: {message.model_dump()}')
    if message.forward_from_message_id is None:
        return
    if message.caption is None:
        return
    chat_id = message.chat.id
    rkey = f'role_br_message_{chat_id}:{message.forward_from_message_id}'
    r = redis_client.get(rkey)
    if not r:
        logging.info(f'{rkey} not found')
        return
    role = json.loads(r.decode('utf-8'))
    role_name = role['role_name']
    role_id = role['role_id']
    content = role['content']
    photo = role['photo']
    c_id = role['channel_id']

    content_lines = content.split('\n')
    content_lines = [line for line in content_lines if line.find('链接在评论区') < 0]

    tma_bot = await tg_config_service.get_main_bot_by_category(BotCategory.TMA)
    chat_bot = await tg_config_service.get_main_bot_by_category(BotCategory.CHAT_BOT)

    content_lines.insert(-2, f'''[👉和 {role_name} 激情文爱👈]({tma_bot.url}?startapp=u_{c_id}-r_{role_id})\n[👉和 {role_name} 的备用链接👈]({chat_bot.url}?start=u_{c_id}-r_{role_id})\n''')

    await bot.send_photo(chat_id=chat_id, photo=photo, caption='\n'.join(content_lines), parse_mode=ParseMode.MARKDOWN_V2, reply_to_message_id=message.message_id)

@bot_discussion_router.post(WEBHOOK_PATH)
async def webhook_handler(update: dict, x_telegram_bot_api_secret_token: str = Header(...)):
    if x_telegram_bot_api_secret_token != WEBHOOK_SECRET:
        return "Invalid token"
    # logging.info(f'webhook_handler: {update}')
    try:
        await dp.feed_raw_update(bot=bot, update=update)
    except Exception as e:
        logging.exception(e)
