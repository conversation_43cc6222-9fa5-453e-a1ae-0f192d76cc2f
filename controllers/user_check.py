import hashlib
import hmac
import json
import logging
import os
from typing import Annotated
from urllib.parse import unquote
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Header, Request
from tortoise.exceptions import IntegrityError
from datetime import UTC, datetime, timedelta
from jose import JWTError, jwt
from fastapi import Depends, HTTPException, status, Response, Cookie

from common.common_constant import ChatPlatform
from persistence.models.models import User, UserRegisterSource, UserStatus
from services.user_service import user_service
from services.bot_services import (
    bot as tma_bot,
    get_bot_and_register_source_by_id,
    get_register_source_by_bot,
    tma_bots_map,
)
from utils import env_util, exception_util

log = logging.getLogger(__name__)
SECRET_KEY = "b'g5gFjPqzsi0V4sNUO9q5GyvYb1uiKvZ0'"
ALGORITHM = "HS256"


def create_access_token(*, data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def get_user_id_by_tg(init_data: str, bot_id: str | None = None) -> int:
    c_str = "WebAppData"
    register_source, bot = get_bot_and_register_source_by_id(bot_id)
    token = bot.token
    if init_data.startswith(", "):
        init_data = init_data[2:]
    param_chunks = unquote(init_data).split("&")
    hash_str = next((x for x in param_chunks if x[: len("hash=")] == "hash="), None)
    hash_split_list = hash_str.split("=")
    if len(hash_split_list) < 1:
        log.error("Invalid tg init data hash_str,%s,boot_id: %s", init_data, bot_id)
        raise exception_util.http_auth("Invalid tg init data")
    hash_str = hash_split_list[1]

    sorted_init_data = sorted(
        [
            chunk.split("=")
            for chunk in param_chunks
            if chunk[: len("hash=")] != "hash="
        ],
        key=lambda x: x[0],
    )
    to_hash_data = "\n".join([f"{rec[0]}={rec[1]}" for rec in sorted_init_data])

    secret_key = hmac.new(c_str.encode(), token.encode(), hashlib.sha256).digest()
    data_check = hmac.new(secret_key, to_hash_data.encode(), hashlib.sha256)

    if hash_str == data_check.hexdigest():
        user_str = next((x for x in sorted_init_data if x[0] == "user"), None)
        user_info = json.loads(user_str[1])
        user = await user_service.get_user_by_tg_id(user_info["id"])
        is_premium: bool = user_info.get("is_premium", False)
        start_role = None
        if user is None:
            start_param = next(
                (x for x in sorted_init_data if x[0] == "start_param"), None
            )
            inviter = None
            try:
                if start_param:
                    for arg in start_param[1].split("-"):
                        if arg.startswith("u_"):
                            inviter = arg[2:]
                            if inviter.startswith("0x"):
                                inviter = int(inviter, 16)
                            else:
                                inviter = int(inviter)
                        elif arg.startswith("r_"):
                            start_role = int(arg[2:])
            except Exception as e:
                log.error(f"Invalid tg init data start_param,{init_data},{bot_id}", e)
            try:
                if inviter is None:
                    user = await user_service.register_by_tg(
                        user_info["id"],
                        user_info.get("first_name"),
                        user_info.get("last_name"),
                        user_info.get("username"),
                        register_source,
                        is_premium,
                        from_bot_id=bot.id
                    )
                else:
                    (nu, user) = await user_service.register_tg_with_invitation(
                        user_info["id"],
                        user_info.get("first_name"),
                        user_info.get("last_name"),
                        user_info.get("username"),
                        inviter,
                        start_role=start_role or 0,
                        register_source=register_source,
                        is_premium=is_premium,
                        from_bot_id=bot.id
                    )
            except Exception as e:
                if not isinstance(e, IntegrityError):
                    logging.exception(
                        f"register tg user failed, tg_user_id: {user_info['id']}"
                    )
                user = await user_service.get_user_by_tg_id(user_info["id"])
        return user.id
    else:
        log.warning("Invalid tg init data,init_data: %s,boot_id: %s", init_data, bot_id)
        raise exception_util.http_auth("Invalid tg init data")


async def depends_on_token(token: str = Cookie(None)) -> int | None:
    credentials_exception = exception_util.http_auth("Could not validate credentials")
    if not token:
        return None
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=ALGORITHM)
        print(payload)
        user_id_str: str = str(payload.get("sub"))
        if not user_id_str:
            raise credentials_exception
        if not user_id_str.isdigit():
            return None
        user_id = int(user_id_str)
    except JWTError as e:
        print(e)
        log.warning("Could not decode token,token: %s", token)
        return None
    user = await user_service.get_user_by_id(user_id=user_id)
    if user is None:
        log.error("Could not find user by token,token: %s,user_id: %s", token, user_id)
        raise credentials_exception
    return user_id


async def get_chat_platform(
    tg_init_data: Annotated[str, Header()] = None,
) -> ChatPlatform:
    if tg_init_data:
        return ChatPlatform.TMA
    return ChatPlatform.WEB


async def get_current_user(
    token_uid: int | None = Depends(depends_on_token),
    tg_init_data: Annotated[str, Header()] = None,
    tg_bot_id: Annotated[str, Header()] = None,
    response: Response = None,
) -> int:
    local_user_id = env_util.get_local_user_id()
    ret_user_id = None
    if local_user_id:
        ret_user_id = int(local_user_id)
        if not token_uid:
            access_token_expires = timedelta(days=30)
            access_token = create_access_token(
                data={"sub": str(ret_user_id)}, expires_delta=access_token_expires
            )
            response.set_cookie(
                "token",
                access_token,
                secure=True,
                expires=datetime.now(UTC) + timedelta(days=30),
            )

    if not ret_user_id and token_uid:
        ret_user_id = token_uid
    if not ret_user_id and tg_init_data:
        ret_user_id = await get_user_id_by_tg(tg_init_data, tg_bot_id)

    if not ret_user_id:
        log.warning(
            "Token is missing,ret_user_id: %s,tg_init_data: %s,tg_bot_id: %s",
            ret_user_id,
            tg_init_data,
            tg_bot_id,
        )
        raise exception_util.http_auth("缺少Token")
    user = await user_service.get_user_by_id(user_id=ret_user_id)
    if user is None:
        log.error(
            "Could not find user by token,ret_user_id: %s,tg_init_data: %s,tg_bot_id: %s",
            ret_user_id,
            tg_init_data,
            tg_bot_id,
        )
        raise exception_util.http_auth("Could not find user by token")
    if user.status == UserStatus.CHAT_BLACK.value:
        log.warning(
            "User is black,uid: %s,tg_init_data: %s,tg_bot_id: %s",
            ret_user_id,
            tg_init_data,
            tg_bot_id,
        )
        raise exception_util.http_forbidden(
            "当前请求存在违规敏感操作，已被封禁，详情联系客服"
        )
    response.headers["X-User-Id"] = str(ret_user_id)
    return ret_user_id


@exception_util.async_ignore_catch_exception
async def get_user_id_un_verify(request: Request) -> User | None:
    local_user_id = env_util.get_local_user_id()
    if local_user_id:
        return await user_service.get_user_by_id(int(local_user_id))
    token = request.cookies.get("token", "")
    tg_init_data = request.headers.get("tg-init-data", "")
    tg_bot_id = request.headers.get("tg-bot-id", "")

    user_id = await depends_on_token(token)
    if not user_id and tg_init_data:
        user_id = await get_user_id_by_tg(tg_init_data, tg_bot_id)
    if not user_id:
        return None
    user = await user_service.get_user_by_id(user_id=user_id)
    if user.status == UserStatus.CHAT_BLACK.value:
        return None
    return user


async def verify_tg_init_data(init_data: str, bot_id: str):
    c_str = "WebAppData"
    token = os.environ.get("TMA_BOT_TOKEN")
    if bot_id is not None:
        token = os.environ.get(f"TMA_BOT_TOKEN_{bot_id.upper()}", token)
    if init_data.startswith(", "):
        init_data = init_data[2:]
    param_chunks = unquote(init_data).split("&")
    hash_str = next((x for x in param_chunks if x[: len("hash=")] == "hash="), None)
    hash_split_list = hash_str.split("=")
    if len(hash_split_list) < 1:
        log.error("Invalid tg init data hash_str,%s,boot_id: %s", init_data, bot_id)
        raise exception_util.http_auth("Invalid tg init data")
    hash_str = hash_split_list[1]

    sorted_init_data = sorted(
        [
            chunk.split("=")
            for chunk in param_chunks
            if chunk[: len("hash=")] != "hash="
        ],
        key=lambda x: x[0],
    )
    to_hash_data = "\n".join([f"{rec[0]}={rec[1]}" for rec in sorted_init_data])

    secret_key = hmac.new(c_str.encode(), token.encode(), hashlib.sha256).digest()
    data_check = hmac.new(secret_key, to_hash_data.encode(), hashlib.sha256)

    if hash_str == data_check.hexdigest():
        log.info("Valid tg init data,init_data: %s,boot_id: %s", init_data, bot_id)
    else:
        log.warning("Invalid tg init data,init_data: %s,boot_id: %s", init_data, bot_id)


async def add_black_user(user_id: int):
    user = await user_service.get_user_by_id(user_id)
    if user is None:
        return
    user.status = UserStatus.CHAT_BLACK.value

    # 暂时不加入黑名单
    # await user_service.update_user(user)
