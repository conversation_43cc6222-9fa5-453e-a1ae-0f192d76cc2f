import logging
from fastapi import (
    Depends,
    APIRouter,
    Header,
)
from common.common_constant import (
    Language,
)
from persistence.models.models import UserPersonaConfig
from services import (
    user_alt_persona_service,
)
from utils import response_util
from .user_check import get_current_user


user_alt_persona_api_router = APIRouter()

log = logging.getLogger(__name__)

# 已废弃的controller

# 获取用户配置的persona列表
@user_alt_persona_api_router.get("/user/alt_persona")
async def get_user_all_alt_persona(user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value)):
    result = await user_alt_persona_service.get_user_all_alt_persona(user_id)  
    return response_util.ok(
        data={"persona_settings": result},
    )


# 更新persona
@user_alt_persona_api_router.post("/user/alt_persona/update")
async def create_user_alt_profile(
    persona_settings: list[UserPersonaConfig],
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    
    result = await user_alt_persona_service.update_user_alt_persona(
        user_id=user_id,
        persona_config_list=persona_settings,
    )
    return response_util.ok({'result': result})

