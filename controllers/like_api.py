import logging
import random
from fastapi import Form, <PERSON><PERSON>, Depen<PERSON>, APIRouter
from fastapi.responses import JSONResponse
from controllers.user_check import get_current_user
from common.common_constant import (
    ChatModeType,
    Language,
)
from services import (
    role_statistic_service,
    user_like_service,
)
from services.role import role_loader_service
from utils import (
    response_util,
)

like_api_router = APIRouter()

log = logging.getLogger(__name__)

UNAUTHORIZED = JSONResponse(status_code=401, content={"message": "Unauthorized"})


# 点赞
@like_api_router.post("/user/like")
async def add_like_role(
    mode_type: ChatModeType,
    mode_target_id: int,
    user_id: int = Depends(get_current_user),
):
    if mode_type != ChatModeType.SINGLE:
        return response_util.json_param_error("mode type not support")
    
    role = await role_loader_service.load_by_id(mode_target_id)
    if not role or not role.status or not role.privacy:
        return response_util.json_param_error("role not found, or privacy")

    count = await _add_like_count_randomly(mode_target_id)
    record = await user_like_service.add_like_role(user_id, mode_type.value, mode_target_id,count)
    if record is None:
        return response_util.error(400, "repeated operation")
    
       
    return response_util.ok()

async def _add_like_count_randomly(role_id: int):
    role_hot_map: dict[int, int] = await role_statistic_service.role_hot_all()
    sorted_roles = sorted(role_hot_map.items(), key=lambda item: item[1], reverse=True) 
    role_rank_map = {item[0]: index for index, item in enumerate(sorted_roles)}
    rank = role_rank_map.get(role_id, -1)

    if rank >= 0 and rank < 20:
        count = random.randint(80, 100)
    elif rank < 50:
        count = random.randint(60, 69)
    elif rank < 100:
        count = random.randint(50, 59)
    elif rank < 200:
        count = random.randint(40, 49)
    elif rank < 300:
        count = random.randint(30, 39)
    else:
        count = random.randint(10, 29)
    await user_like_service.add_like_count_randomly(ChatModeType.SINGLE.value, role_id, count)
    return count
    
    
    

