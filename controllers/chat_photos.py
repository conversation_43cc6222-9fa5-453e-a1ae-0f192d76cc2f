from itertools import groupby
import logging
from fastapi import Depends, APIRouter
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from ai import image_tool
from common.common_constant import ProductType
from controllers.user_check import get_current_user
from persistence.chat_history import chat_history_persistence
from services import product_service, user_role_service
from services.account_service import AccountService
from services.role import role_loader_service
from services.role_config_service import RoleConfigService


chat_photo_router = APIRouter()

log = logging.getLogger(__name__)

class TakePhotoRequest(BaseModel):
    message_id: str
    version: int

@chat_photo_router.post("/take_photo")
async def take_photo(req: TakePhotoRequest, user_id: int = Depends(get_current_user)):
    message_id: str = req.message_id
    version: int = req.version
    message = await chat_history_persistence.get_message(message_id, version)
    if message is None:
        return JSONResponse(status_code=404, content={"msg": "Message Not Found"})
    if message['user_id'] != user_id:
        return JSONResponse(status_code=401, content={"msg": "Not Your Message"})
    photo_url = message.get('photo_url')

    if photo_url is not None and len(photo_url) > 0:
        return JSONResponse(content={"photo_url": photo_url, "message_id": message_id})
    photo_product = await product_service.get_online_by_type_first(ProductType.PHOTO.value)
    if not photo_product:
        log.error("Photo Product Not Found")
        return JSONResponse(status_code=404, content={"msg": "Photo Product Not Found"})
    balance = await AccountService.get_total_balance(user_id)
    if balance < photo_product.price:
        return JSONResponse(status_code=402, content={"msg": "Insufficient Balance"})
    role_id = message['role_id']
    role_config = await role_loader_service.get_by_id(role_id)
    log.info(f"take_photo user_id: {user_id}, role_id: {role_id}, role_id: {role_id}")
    conversation_id = message['conversation_id']
    history = await chat_history_persistence.get_messages(user_id , role_id, conversation_id)
    history = [max(g, key=lambda x: x['timestamp']) for k, g in groupby(history, key=lambda x: x['message_id'])]

    history = history[-4:]
    if not role_config.support_photo:
        return JSONResponse(status_code=405, content={"msg": "Role Not Support Photo"})

    image_id, image_url = image_tool.get_image_v2(history, role_config.id, user_id)

    if len(image_url) <= 0:
        return JSONResponse(status_code=405, content={"msg": "Image Not Found"})

    await chat_history_persistence.save_message_photo(message['_id'], image_url, image_id)
    await AccountService.create_pay_order(user_id, photo_product, role_id)
    await user_role_service.add_user_role_photo_record(user_id, role_id, conversation_id, message_id, version, image_id, image_url)
    return JSONResponse(content={"photo_url": image_url, "message_id": message_id, "photo_id": image_id})


# 重试生成图片
@chat_photo_router.post("/take_photo/retry")
async def take_photo_retry(req: TakePhotoRequest, user_id: int = Depends(get_current_user)):
    message_id: str = req.message_id
    version: int = req.version
    message = await chat_history_persistence.get_message(message_id, version)
    if message is None:
        return JSONResponse(status_code=404, content={"msg": "Message Not Found"})
    if message['user_id'] != user_id:
        return JSONResponse(status_code=401, content={"msg": "Not Your Message"})
    
    photo_url = message.get('photo_url')
    # retry can be called only after take_photo api is called
    if photo_url is None or len(photo_url) == 0:
        return JSONResponse(status_code=401, content={"msg": "Original Photo Not Found"})
    
    photo_product = await product_service.get_online_by_type_first(ProductType.PHOTO_RETRY.value)
    if not photo_product:
        log.error("Photo Retry Product Not Found")
        return JSONResponse(status_code=404, content={"msg": "Photo Retry Product Not Found"})
    balance = await AccountService.get_total_balance(user_id)
    if balance < photo_product.price:
        return JSONResponse(status_code=402, content={"msg": "Insufficient Balance"})
    role_id = message['role_id']
    role_config = await role_loader_service.get_by_id(role_id)
    log.info(f"take_photo_retry user_id: {user_id}, role_id: {role_id}, role_id: {role_id}")
    conversation_id = message['conversation_id']
    history = await chat_history_persistence.get_messages(user_id , role_id, conversation_id)
    history = [max(g, key=lambda x: x['timestamp']) for k, g in groupby(history, key=lambda x: x['message_id'])]

    history = history[-4:]
    if not role_config.support_photo:
        return JSONResponse(status_code=405, content={"msg": "Role Not Support Photo"})

    image_id, image_url = image_tool.get_image_v2(history, role_config.id, user_id)

    if len(image_url) <= 0:
        return JSONResponse(status_code=405, content={"msg": "Image Not Found"})
    
    retry_photos = message.get('retry_photos')
    if retry_photos is None:
        retry_photos = []
    retry_photos.append({"photo_id": image_id, "photo_url": image_url})
    await chat_history_persistence.save_message_retry_photos(message['_id'], retry_photos)

    await AccountService.create_pay_order(user_id, photo_product, role_id)
    await user_role_service.add_user_role_photo_record(user_id, role_id, conversation_id, message_id, version, image_id, image_url)
    return JSONResponse(content={"photo_url": image_url, "message_id": message_id, "photo_id": image_id})

class PostPhotoLikeStatusRequest(BaseModel):
    role_id: int
    conversation_id: str
    message_id: str
    version: int
    photo_id: str
    like_status: int

# 用户赞或踩图片
@chat_photo_router.post("/photo/like_status")
async def post_photo_like_status(req: PostPhotoLikeStatusRequest, user_id: int = Depends(get_current_user)):
    role_id: int = req.role_id
    conversation_id: str = req.conversation_id
    message_id: str = req.message_id
    version: int = req.version
    photo_id: str = req.photo_id
    if req.like_status not in [0, 1, 2]:
        return JSONResponse(status_code=400, content={"msg": "Invalid Like Status"})
    like_status: int = req.like_status
    photo_record = await user_role_service.get_user_role_photo_record(user_id, role_id, conversation_id, message_id, version, photo_id)
    if photo_record is None:
        return JSONResponse(status_code=404, content={"msg": "Photo Record Not Found"})
    if photo_record.like_status == like_status:
        return JSONResponse(content={"message": "success"})
    photo_record.like_status = like_status
    await photo_record.save()
    return JSONResponse(content={"message": "success"})