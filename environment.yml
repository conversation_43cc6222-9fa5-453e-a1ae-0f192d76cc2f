name: tavern-server
channels:
  - defaults
  - conda-forge
dependencies:
  - micromamba
  - python==3.12.3
  - pip:
    - aerich==0.7.2
    - aiogram==3.17.0
    - aiohttp-socks==0.8.4
    - aioitertools==0.11.0
    - apscheduler==3.10.4
    - asyncache==0.3.1
    - cachetools==5.3.3
    - cos-python-sdk-v5==1.9.30
    - cryptography==42.0.8
    - email-validator==2.2.0
    - fastapi==0.112.0
    - firebase-admin==6.5.0
    - httpx[socks]==0.27.0
    - huey==2.5.1
    - langchain-community==0.0.34
    - langchain-core==0.1.45
    - langchain-openai==0.1.3
    - langchain-text-splitters==0.0.1
    - langchain==0.1.16
    - langchainhub==0.1.15
    - langfuse==2.40.0
    - langsmith==0.1.49
    - motor==3.5.1
    - mysql-connector-python==9.0.0
    - openai==1.66.3
    - openpyxl==3.1.5
    - pandas==2.1.1
    - passlib[bcrypt]==1.7.4
    - Pillow==10.4.0
    - posthog==3.5.0
    - pydantic==2.8.2
    - pydantic[email]
    - aioitertools
    - tiktoken
    - websockets
    - apscheduler
    - posthog
    - langfuse
    - aiogram
    - aiohttp-socks
    - langdetect
    - firebase-admin
    - redis
    - python-telegram-bot
    - cachetools
    - mysql-connector-python
    - resend
    - openpyxl
    - pymongo==4.8.0
    - pyrate-limiter==3.7.0
    - python-dotenv==1.0.1
    - python-jose==3.3.0
    - python-multipart==0.0.9
    - redis==5.0.8
    - requests==2.32.3
    - resend==2.4.0
    - result==0.17.0
    - sentry-sdk==2.12.0
    - sse-starlette==2.1.3
    - streamlit-multipage==0.0.18
    - streamlit==1.27.2
    - stripe==10.6.0
    - tcvectordb==1.3.9
    - tiktoken==0.7.0
    - tortoise-orm[asyncmy]==0.21.5
    - uvicorn[standard]==0.30.5
    - websockets==12.0
    - zhconv==1.4.3
    - boto3==1.35.76
    - tencentcloud-sdk-python
    - litellm==1.63.7
    - novelai-api==0.34.1
