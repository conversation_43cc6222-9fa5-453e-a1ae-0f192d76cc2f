-- Title: Add <PERSON>SFW flag to role_config, 2024-06-27
-- alter table role_config add column nsfw tinyint not null default 0;

alter table user_pw add status tinyint not null default 0;
alter table role_config add support_model_config json;
UPDATE tavern.role_config t SET t.support_model_config = '{"all_product":true}' where id > 0


# 公开卡相关功能上线 v7.18

alter table role_config
    add level_type varchar(32) default 'normal' not null comment '角色卡等级';
alter table role_config
    add operation_config json default '{}' not null comment '运营消息';
update role_config set privacy = 1 where uid = 0




---

-- mongodb index--
db.chat_history.createIndex({user_id: 1, role_id: 1,conversation_id:1});
db.chat_history.createIndex({message_id: 1});
---

----
alter table invitation drop index invitation_id;
alter table invitation drop index account_id;
alter table invitation drop index idx_invitation_account_daab9c;
alter table invitation drop index idx_invitation_inviter_ad1566;
alter table invitation drop column account_id;
alter table invitation drop column inviter_id;
alter table invitation add column invitee_user_id bigint not null;
alter table invitation add column inviter_user_id bigint not null;
alter table invitation add unique index uk_invitee(invitee_user_id);
alter table invitation add index idx_inviter(inviter_user_id);

----

ALTER table recharge_product add cny_price bigint not null default -1 after price

-----

create table user_stats(
    id bigint not null auto_increment primary key,
    user_id bigint not null,
    tg_first_name varchar(255) not null,
    tg_last_name varchar(255) not null default '',
    tg_user_name varchar(255) not null default '',
    nickname varchar(255) not null default '',
    llm_model varchar(64) not null default 'claude-3-haiku',
    account_balance bigint not null default 0,
    channel_id bigint not null default 0,
    invite_link varchar(255) not null default '',
    joined_chat_id bigint not null default 0,
    joined_chat_type varchar(255) not null default '',
    start_role bigint not null default 0,
    register_at timestamp not null,
    created_at timestamp not null default current_timestamp,
    updated_at timestamp not null default current_timestamp on update current_timestamp,
    unique key user_id_idx (user_id),
    index idx_channel_id (channel_id),
    index idx_invite_link (invite_link)
) engine=InnoDB default charset=utf8mb4;
----

alter table user_stats add column joined_group int not null default 0;
alter table user_stats add column joined_channel int not null default 0;

ALTER TABLE tg_deleted_message add column bot VARCHAR(128) NOT NULL DEFAULT 'TMA';

ALTER TABLE `users_pw` CHANGE COLUMN `nickname` `nickname` VARCHAR(255) NOT NULL;

ALTER TABLE pay_order ADD COLUMN `role_id` INT NOT NULL DEFAULT 0;

ALTER TABLE PAY_ORDER ADD COLUMN `free_amount` BIGINT NOT NULL DEFAULT 0;
ALTER TABLE PAY_ORDER ADD COLUMN `payed_amount` BIGINT NOT NULL DEFAULT 0;

ALTER TABLE `expirable_award` ADD COLUMN `from_type` VARCHAR(128) NOT NULL DEFAULT 'REWARD';
ALTER TABLE `expirable_award` DROP INDEX `uk_user_order`;
ALTER TABLE `expirable_award` ADD UNIQUE INDEX uk_user_order_type (user_id, out_order_id, from_type);

ALTER TABLE role_config ADD COLUMN image_nsfw INT NOT NULL DEFAULT 0;

ALTER TABLE users_pw ADD COLUMN show_nsfw_image INT NOT NULL DEFAULT 0;

ALTER TABLE recharge_product ADD COLUMN show_for_user INT NOT NULL DEFAULT 0;

ALTER TABLE tg_user ADD COLUMN is_premium INT NOT NULL DEFAULT 0;

ALTER TABLE recharge_order ADD COLUMN from_bot_id BIGINT NOT NULL DEFAULT 0;

ALTER TABLE tg_channel_config ADD COLUMN linked_group VARCHAR(128) NOT NULL DEFAULT '';

ALTER TABLE tg_bot_config ADD COLUMN bot_nsfw INT NOT NULL DEFAULT 0;
ALTER TABLE tg_bot_config ADD COLUMN show_nsfw_image INT NOT NULL DEFAULT 0;

alter table recharge_product add column `order` int not null default 0;

ALTER TABLE users_pw ADD COLUMN invitation_privilege INT NOT NULL DEFAULT 1;

ALTER TABLE user_chat_benefit ADD COLUMN benefit_type VARCHAR(32) NOT NULL DEFAULT 'RECHARGE';

alter table users_pw add column voice_content_type varchar(64) not null default 'INSIDE_ONLY';
alter table voice_speaker add column `order` int not null default 1;

alter table oauth_user add column provider_user_email varchar(512) not null default '' after provider_user_id;

alter table recharge_product add column fc_reward_amount bigint not null default 0 after reward_amount;
alter table recharge_product add column fc_corner_title varchar(128) not null default '' after corner_tip;

alter table tg_bot_config add lang varchar(32) not null default 'zh';

alter table oauth_user add column provider_user_email varchar(512) not null default '' after provider_user_id;

alter table recharge_product add column fc_reward_amount bigint not null default 0 after reward_amount;
alter table recharge_product add column fc_corner_title varchar(128) not null default '' after corner_tip;

alter table tg_bot_config add lang varchar(32) not null default 'zh';

alter table recharge_product add column fc_reward_amount bigint not null default 0 after reward_amount;
alter table recharge_product add column fc_corner_title varchar(128) not null default '' after corner_tip;

alter table tg_bot_config add lang varchar(32) not null default 'zh';

alter table tg_user add column reg_bot_id bigint not null default 0;

alter table recharge_product add column disabled_message varchar(128) not null default '';

alter table recharge_order add column exclude_from_stat int not null default 0;
alter table recharge_order add column exclude_reason varchar(128) not null default '';

alter table recharge_order add column pay_url varchar(1024) not null default '';
alter table recharge_order add column pay_redirect_url varchar(1024) not null default '';
alter table recharge_order add column platform varchar(64) not null default '';

alter table recharge_product add column fc_corner_tip varchar(128) not null default '' after corner_tip;

alter table recharge_order add column refunded int not null default 0;
