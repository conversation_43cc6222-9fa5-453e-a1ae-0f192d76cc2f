import hashlib
import hmac
import base64
from datetime import datetime
import logging
import os, json
import websockets
from typing import Union
from tortoise import Tortoise, run_async
from tortoise.contrib.fastapi import register_tortoise
from dotenv import load_dotenv

from common.entity import OkxDeposit
from services import recharge_service
load_dotenv()

import sentry_sdk

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s (%(filename)s:%(lineno)d) [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()],
)
log = logging.getLogger(__name__)

sentry_sdk.init(
    dsn="https://<EMAIL>/4506693877039104",
    # Set traces_sample_rate to 1.0 to capture 100%
    # of transactions for performance monitoring.
    traces_sample_rate=1.0,
    # Set profiles_sample_rate to 1.0 to profile 100%
    # of sampled transactions.
    # We recommend adjusting this value in production.
    profiles_sample_rate=1.0,
)

login_template = '''{{
 "op": "login",
 "args":
  [
     {{
       "apiKey": "{api_key}",
       "passphrase": "{passphrase}",
       "timestamp": "{ts}",
       "sign": "{sign}" 
      }}
   ]
}}'''

def gen_wss_sign():
    api_key = os.environ['OKX_API_KEY']
    secret = os.environ['OKX_API_SECRET']
    passphrase = os.environ['OKX_API_PASSPHRASE']
    timestamp = int(datetime.now().timestamp())
    constant = 'GET'+ '/users/self/verify'
    data = f'{timestamp}{constant}'
    sign = hmac.new(secret.encode(), data.encode(), hashlib.sha256).digest()
    sign_hex = base64.b64encode(sign).decode()
    return login_template.format(api_key=api_key, passphrase=passphrase,
                                 ts=timestamp, sign=sign_hex)

async def handle_message(message):
    m = json.loads(message)
    log.info(f'data received: {m['arg']['channel']}')
    if m['arg']['channel'] != 'deposit-info':
        log.info(f'none deposit channel info: {m['arg']['channel']}')
        return
    log.info(f'deposit info: {m}')
    succeed_txs = [d for d in m['data'] if d['state'] == "2"]
    for tx in succeed_txs:
        await recharge_service.usdt_recharge_success(OkxDeposit(**tx))

async def init():
    await Tortoise.init(
        db_url = os.environ['MYSQL_URL'],
        modules = {'models': ['persistence.models.models']},
    )
    # await Tortoise.generate_schemas()

    async for ws in websockets.connect('wss://ws.okx.com:8443/ws/v5/business'):
        print('connected')
        try:
            login = gen_wss_sign()
            await ws.send(login)
            login_data = await ws.recv()
            print(f'login: {login_data}')

            await ws.send('{"op": "subscribe", "args": [{"channel": "deposit-info"}]}')
            subscribe_data = await ws.recv()
            print(f'subscribe: {subscribe_data}')

            async for message in ws:
                await handle_message(message)

        except websockets.ConnectionClosed:
            continue

if __name__ == "__main__":
    run_async(init())