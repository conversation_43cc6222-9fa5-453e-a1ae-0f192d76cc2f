
import asyncio
from collections import defaultdict
from datetime import datetime, timedelta
import logging
import os
import time

from typing import Dict, List, Optional


from aiogram import <PERSON><PERSON>, Di<PERSON>atch<PERSON>, Router, types
# from aiogram import LoggingMiddleware
from aiogram.types import Update, Message, InlineKeyboardButton, CallbackQuery
from aiogram.fsm.storage.memory import MemoryStorage
from aiogram import F
from aiogram.filters import ChatMember<PERSON>pdatedFilter, IS_MEMBER, IS_NOT_MEMBER, Command
from aiogram.types import Chat<PERSON><PERSON>ber<PERSON>pdated, ChatPermissions
from aiogram.utils.keyboard import InlineKeyboardBuilder
from aiogram.fsm.state import State, StatesGroup
from aiogram.fsm.context import FSMContext

from dotenv import load_dotenv
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from tortoise import Tortoise
from tortoise.contrib.fastapi import register_tortoise
import uvicorn

from common import loggers
from common.models.ai_bot_admin.group_config import AutoReplyRuleCreate, AutoReplyRuleBO, AutoReplyRuleUpdate, BotGroupConfigBO, BotWelcomeConfigBO, BotWelcomeConfigCreate, BotWelcomeConfigUpdate, RubbishAction,SpamProtectionRuleCreate,SpamProtectionRuleUpdate, SpamProtectionRuleBO

from services.bot_group.bot_group_service import BotGroupConfigAdminService,AutoReplyRuleHandler, BotHandlerManager, SpamProtectionHandler,UserSpamBehaviorService,BotWelcomeConfigHandler

from services.bot_group.group_msg_service import GroupUserCardInfoService
from services.bot_group.aibot_help import HandleGroupUserMsgMiddleware, SaveMessageMiddleware,BotSendMsgPrivate,ForwardUserInfoMessageService
from persistence.models.models_bot_group import AutoReplyRule, BotGroupConfig, BotWelcomeConfigModel,SpamProtectionRule
from common.models.ai_bot_admin.aibot_state import StatSettingForm, ScheduleMessageForm

from aibot_handlers.group_help_dp_router import create_group_help_dp_router
from aibot_handlers.user_card_dp_router import create_user_card_router
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s (%(filename)s:%(lineno)d) [%(levelname)s] %(message)s",handlers=[logging.StreamHandler(),loggers.local_bot_help_handler])

log = logging.getLogger(__name__)


FULL_NAME_LEN = 15
# 中间件
# class RateLimitMiddleware:
#     async def __call__(self, handler, event, data):
#         user_id = event.from_user.id
#         current_time = time.time()
#         if current_time - message_cooldowns[user_id] < 5:
#             await event.delete()
#             return
#         message_cooldowns[user_id] = int(current_time)
#         return await handler(event, data)

# 加载环境变量
load_dotenv()







# Initialize FastAPI app and BotManager
app = FastAPI()



Tortoise.init_models(["persistence.models.models_bot_group","persistence.models.models"], "models")
register_tortoise(
    app=app,
    db_url=os.environ["MYSQL_URL"],
    modules={"models": ["persistence.models.models_bot_group","persistence.models.models"]},
    generate_schemas=True,
)



# Set webhooks on startup
@app.on_event("startup")
async def on_startup():
    log.info("Starting server...")
    asyncio.create_task(GroupUserCardInfoService.process_queue_task())


if __name__ == '__main__':
    uvicorn.run(app, host='0.0.0.0', port=9210)