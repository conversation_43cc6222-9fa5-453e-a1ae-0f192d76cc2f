import logging

from persistence import mongo_client
from persistence.models.mongo_models import RolePublishHistory


logger = logging.getLogger(__name__)

collection = mongo_client.TavernCollection.ROLE_PUBLISH_HISTORY


async def insert(history: RolePublishHistory):
    await collection.insert_one(history.model_dump())


async def list_by_user(
    user_id: int, mode_type: str, mode_target_id
) -> list[RolePublishHistory]:
    history: list[dict] = await collection.find(
        {
            "user_id": user_id,
            "mode_type": mode_type,
            "mode_target_id": mode_target_id,
        }
    ).to_list(None)
    return [RolePublishHistory(**x) for x in history]

async def get_latest_by_user(
    user_id: int,  count: int = 6
) -> list[RolePublishHistory]:
    history: list[dict] = await collection.find(
        {
            "user_id": user_id,
        }
    ).sort("created_at", -1).limit(count).to_list(None)
    return [RolePublishHistory(**x) for x in history]


async def list_by_mode(mode_type: str, mode_target_id: int) -> list[RolePublishHistory]:
    history: list[dict] = await collection.find(
        {
            "mode_type": mode_type,
            "mode_target_id": mode_target_id,
        }
    ).to_list(None)
    return [RolePublishHistory(**x) for x in history]


async def next_version(mode_type: str, mode_target_id: int) -> int:
    history: list[dict] = await collection.find(
        {
            "mode_type": mode_type,
            "mode_target_id": mode_target_id,
        }
    ).to_list(None)
    if not history:
        return 1
    return max([x["publish_version"] for x in history]) + 1


async def max_versions(mode_type: str, mode_target_ids: list[int]) -> dict[int, int]:
    pipeline = [
        {
            "$match": {
                "mode_type": mode_type,
                "mode_target_id": {"$in": mode_target_ids},
            }
        },
        {
            "$group": {
                "_id": "$mode_target_id",
                "max_version": {"$max": "$publish_version"},
            }
        },
        {"$project": {"_id": 0, "mode_target_id": "$_id", "max_version": 1}},
    ]

    results = await collection.aggregate(pipeline).to_list(None)
    if results is None:
        return {}
    return {doc["mode_target_id"]: doc["max_version"] for doc in results}
