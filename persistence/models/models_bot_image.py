from datetime import datetime, timezone
from enum import Enum, IntEnum, StrEnum
from tortoise.models import Model
from tortoise import fields


class ImgGenStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class ImgReviewStatus(StrEnum):
    INIT = "init"
    APPROVED = "approved"
    REJECTED = "rejected"


class BaseTimeModel(Model):
    created_at = fields.DatetimeField(auto_now_add=True, index=True)
    updated_at = fields.DatetimeField(auto_now=True, index=True)

    class Meta:
        abstract = True


class BotImgGenTask(BaseTimeModel):
    id = fields.IntField(pk=True)
    tg_id = fields.BigIntField(index=True)
    user_id = fields.BigIntField(index=True, default=0)
    prompt = fields.TextField()
    req_json = fields.JSONField(default={})
    status = fields.CharEnumField(ImgGenStatus, default=ImgGenStatus.PENDING)
    priority = fields.IntField(default=0, description="优先级,越大越优先")
    started_at = fields.DatetimeField(null=True)
    completed_at = fields.DatetimeField(null=True)
    gen_result = fields.JSONField(null=True)
    error_message = fields.TextField(null=True)
    retry_count = fields.IntField(default=0, description="重试次数")
    # 新增字段
    locked_by = fields.CharField(max_length=64, null=True)
    locked_at = fields.DatetimeField(null=True)
    last_heartbeat = fields.DatetimeField(null=True)
    sent_msg_id = fields.BigIntField(index=True, default=0, description="消息ID")

    class Meta:
        table = "bot_img_gen_task"


class BotImgRepeatTaskLog(BaseTimeModel):
    id = fields.IntField(pk=True)
    tg_id = fields.BigIntField(index=True)
    user_id = fields.BigIntField(index=True, default=0)
    task_id = fields.IntField(index=True, description="关联的画图任务ID")
    log_st = fields.CharField(
        max_length=32, description="日志状态", default="init"
    )  # init,balance_not_enough,failed,success

    class Meta:
        table = "bot_img_repeat_task_log"
        description = "画图任务重抽日志"


class BotImgBasicProfile(BaseTimeModel):
    id = fields.IntField(pk=True)
    tg_id = fields.BigIntField(index=True)
    user_id = fields.BigIntField(index=True, default=0)
    img_gen_profile = fields.JSONField(default={})

    class Meta:
        table = "bot_img_basic_profile"
        unique_together = (("tg_id", "user_id"),)
        description = "用户画图基础信息,包括style,清晰度dpx,隐私等"


class ImageBotSettings(BaseTimeModel):
    id = fields.IntField(pk=True)
    bot_id = fields.BigIntField(index=True)
    settings = fields.JSONField(default={})
    start_msg = fields.TextField(
        default="欢迎👏", description="启动消息配置"
    )  # 新增字段
    style_group_mapping = fields.JSONField(
        default={}, description="style 到群ID和话题ID的映射"
    )  # 新增字段

    class Meta:
        table = "bot_img_settings"
        description = "画图bot的设置"

    def __str__(self):
        return f"ImageBotSettings(bot_id={self.bot_id}, settings={self.settings})"


class BotStartImagePool(BaseTimeModel):
    id = fields.IntField(pk=True)
    bot_id = fields.BigIntField(index=True)
    help_msg = fields.TextField(
        default="欢迎使用画图bot，请输入 /help 查看帮助", description="帮助消息配置"
    )  # 新增字段
    start_msg = fields.TextField(
        default="欢迎👏", description="启动消息配置"
    )  # 新增字段
    img_url = fields.CharField(max_length=512, description="启动图片URL")  # 新增字段
    img_desc = fields.TextField(default="", description="启动图片描述")  # 新增字段

    class Meta:
        table = "bot_img_start_pool"
        description = "画图bot的启动图片池"


class GroupInviteLink(BaseTimeModel):
    id = fields.IntField(pk=True)  # 主键
    chat_id = fields.BigIntField(index=True)  # 群组的唯一标识
    invite_link = fields.TextField()  # 邀请链接
    name = fields.CharField(max_length=255, null=True)  # 邀请链接的名称
    creates_join_request = fields.BooleanField(default=False)  # 是否需要管理员批准
    member_limit = fields.IntField(default=0)  # 成员限制
    expire_date = fields.DatetimeField(null=True)  # 邀请链接的过期时间

    class Meta:
        table = "bot_img_group_invite_links"  # 数据库表名


class BotGroupInviteCofig(BaseTimeModel):
    id = fields.IntField(pk=True, description="主键ID")
    bot_id = fields.BigIntField(index=True, description="Bot id ")
    bot_token = fields.CharField(max_length=255, index=True, description="Bot token")
    group_id = fields.BigIntField(index=True, description="分享群组id")
    group_invite_text = fields.TextField(description="群组邀请文本")  # 邀请文本
    description = fields.TextField(default="", description="群组描述")
    creates_join_request = fields.BooleanField(
        default=False, description="是否需要管理员批准加入"
    )
    member_limit = fields.IntField(default=0, description="成员限制,0表示无限制")
    invite_cfg = fields.JSONField(default={}, description="邀请配置,包括邀请链接等")
    is_enable = fields.BooleanField(default=False, description="是否启用邀请配置")
    deleted = fields.BooleanField(default=False)
    bot_type = fields.CharField(max_length=50, default="image_bot")

    class Meta:
        table = "bot_img_group_invite_config"
        table_description = "bot和群组的邀请配置表"
        unique_together = (("bot_id", "group_id"),)


class BotImgGenTaskReview(BaseTimeModel):
    id = fields.IntField(pk=True)  # 主键
    task_id = fields.IntField(uqiue=True, description="关联的画图任务ID")
    tg_id = fields.BigIntField(index=True, default=0, description="Telegram用户ID")
    user_id = fields.BigIntField(index=True, default=0, description="用户ID")
    prompt = fields.TextField(description="用户输入的提示词")
    nai_prompt = fields.TextField(null=True, description="NAI生成的提示词")
    image_url = fields.CharField(max_length=512, null=True, description="生成的图片URL")
    review_status = fields.CharEnumField(
        index=True,  # 索引
        enum_type=ImgReviewStatus,
        default=ImgReviewStatus.INIT,
        description="审核状态",
    )
    send_ack = fields.BooleanField(
        default=False, index=True, description="是否发送确认消息"
    )
    send_msg_id = fields.IntField(
        null=True, index=True, default=0, description="发送到群组的消息ID"
    )  # 新增字段
    tags = fields.JSONField(default=[], description="标签列表")

    class Meta:
        table = "bot_img_gen_task_review"
        description = "画图任务审核数据表"


class ForwardImageBot(BaseTimeModel):
    id = fields.IntField(pk=True)
    bot_id = fields.BigIntField(index=True, description="Bot ID")
    bot_token = fields.CharField(max_length=255, index=True, description="Bot Token")
    img_bot_username = fields.CharField(
        max_length=64, index=True, description="Bot用户名"
    )

    description = fields.TextField(default="", description="Bot描述")

    class Meta:
        table = "bot_img_forward_config"
        description = "画图Bot的转发配置表"


class BotStartMsgLog(BaseTimeModel):
    id = fields.IntField(pk=True)
    bot_id = fields.BigIntField(index=True, description="Bot ID")
    tg_id = fields.BigIntField(index=True, description="Telegram用户ID")
    msg_id = fields.BigIntField(index=True, description="消息ID")
    msg_text = fields.TextField(description="启动消息内容")

    class Meta:
        table = "bot_img_start_msg"
        description = "记录bot启动消息的日志"


class BotPromoMsgStageLog(BaseTimeModel):
    """
    记录每个用户在累计生图张数达到5、10、20时推送的推广消息及其状态
    """

    id = fields.IntField(pk=True)
    bot_id = fields.BigIntField(index=True, description="Bot ID")
    tg_id = fields.BigIntField(index=True, description="Telegram用户ID")
    stage = fields.IntField(index=True, description="推广阶段，5/10/20")
    msg_id = fields.BigIntField(index=True, description="消息ID")
    is_deleted = fields.BooleanField(default=False, description="该消息是否已被删除")

    class Meta:
        table = "bot_img_promo_msg_stage_log"
        description = "记录用户推广消息推送及删除状态"


class BotDailyCheckinCount(BaseTimeModel):
    """
    用户每日打卡签到记录
    """

    id = fields.BigIntField(pk=True)
    bot_id = fields.BigIntField(index=True, description="Bot ID")
    tg_id = fields.BigIntField(index=True, description="Telegram用户ID")
    group_id = fields.BigIntField(index=True, description="群组ID")
    checkin_date = fields.DateField(index=True, description="签到日期（北京时间）")
    checkin_count = fields.IntField(default=1, description="当天打卡次数")
    last_msg_id = fields.BigIntField(null=True, description="最后一次签到消息ID")

    class Meta:
        table = "bot_img_daily_checkin"
        description = "用户每日打卡签到记录"
        unique_together = (("tg_id", "checkin_date"),)

    def __str__(self):
        return f"BotDailyCheckinCount(bot_id={self.bot_id}, tg_id={self.tg_id}, group_id={self.group_id}, checkin_date={self.checkin_date}, checkin_count={self.checkin_count})"


class UserCheckinGroupMsgLog(BaseTimeModel):
    """
    记录群组打卡签到消息
    """

    id = fields.BigIntField(pk=True)
    bot_id = fields.BigIntField(index=True, description="Bot ID")
    group_id = fields.BigIntField(index=True, description="群组ID")
    tg_id = fields.BigIntField(index=True, description="签到用户ID")
    msg_id = fields.BigIntField(index=True, description="签到消息ID")
    checkin_date = fields.DateField(index=True, description="签到日期（北京时间）")
    msg_text = fields.TextField(description="打卡消息内容")
    is_deleted = fields.BooleanField(default=False, description="消息是否已被删除")

    class Meta:
        table = "bot_img_checkin_msg_log"
        description = "群内打卡签到消息记录表"

    def __str__(self):
        return f"BotCheckinGroupMsg(bot_id={self.bot_id}, group_id={self.group_id}, tg_id={self.tg_id}, msg_id={self.msg_id}, checkin_date={self.checkin_date})"


class BotSentGroupMsgLog(BaseTimeModel):
    """
    机器人发送给群组用户的消息记录表
    """

    id = fields.BigIntField(pk=True)
    bot_id = fields.BigIntField(index=True, description="Bot ID")
    tg_id = fields.BigIntField(index=True, description="Telegram用户ID")
    group_id = fields.BigIntField(index=True, description="群组ID")
    sent_msg_id = fields.BigIntField(index=True, description="已发送消息ID")
    msg_text = fields.TextField(description="发送给用户的消息内容")
    is_deleted = fields.BooleanField(default=False, description="消息是否已被删除")

    class Meta:
        table = "bot_img_sent_group_msg"
        description = "机器人发送给群组用户的消息记录表"


class BotGroupCheckinLastMsg(BaseTimeModel):
    """
    记录群组打卡签到的最后一条消息
    """

    id = fields.IntField(pk=True)
    group_id = fields.BigIntField(unique=True, description="群组ID")
    last_checkin_log_id = fields.BigIntField(
        index=True, description="用户最后一条打卡消息ID"
    )
    last_sent_msg_log_id = fields.BigIntField(
        index=True, description="最后一条bot发送消息ID"
    )

    class Meta:
        table = "bot_img_group_checkin_last_msg"
        description = "记录群组打卡签到的最后一条消息"


class BotImgCheckinBotConfig(BaseTimeModel):
    """
    画图Bot打卡签到配置
    """

    id = fields.IntField(pk=True)
    bot_id = fields.BigIntField(index=True, description="Bot ID")
    bot_token = fields.CharField(max_length=255, index=True, description="Bot Token")
    group_id = fields.BigIntField(index=True, description="打卡签到群组ID")
    description = fields.TextField(default="", description="备注描述")

    class Meta:
        table = "bot_img_checkin_bot_config"
        description = "画图Bot打卡签到配置表"
        unique_together = (("bot_id", "group_id"),)

    def __str__(self):
        return f"BotImgCheckinBotConfig(bot_id={self.bot_id}, group_id={self.group_id})"
