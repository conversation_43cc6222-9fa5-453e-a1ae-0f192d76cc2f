from datetime import UTC, datetime
from enum import Enum
from typing import Optional
from uuid import uuid4
from fastapi.datastructures import <PERSON>fault
from pydantic import BaseModel
from tortoise.models import Model
from tortoise import fields
from tortoise.contrib.mysql.indexes import SpatialIndex
import bcrypt

from common.common_constant import BotType, RpDisplayFilter, VoiceContentType


class OrderStatusEnum(str, Enum):
    CREATED = "CREATED"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"


class TimestampMixin:
    created_at = fields.DatetimeField(auto_now_add=True, index=True)
    updated_at = fields.DatetimeField(auto_now=True, index=True)


class BaseIdModel(Model):
    id = fields.BigIntField(pk=True)

    class Meta:
        abstract = True


class Account(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True, unique=True)
    award_balance = fields.BigIntField()
    charge_balance = fields.BigIntField()
    total_balance = fields.BigIntField()

    class Meta:
        table = "account"


class GoogleUser(BaseIdModel, TimestampMixin):
    account_id = fields.UUIDField(index=True, unique=True)
    identity = fields.CharField(128, unique=True)
    name = fields.CharField(128)
    profile_image = fields.CharField(1024)

    class Meta:
        table = "google_user"

class AdminAuthUser(BaseIdModel, TimestampMixin):
    email = fields.CharField(128, unique=True)
    name = fields.CharField(128,default="")
    password_hash = fields.CharField(128,default="")
    role = fields.CharField(128,default="")
    class Meta:
        table = "admin_auth_user"

class AdminLog(BaseIdModel, TimestampMixin):
    email = fields.CharField(32,index=True)
    path = fields.CharField(128)
    method = fields.CharField(16)
    req_data = fields.TextField()
    res_data = fields.TextField()
    class Meta:
        table = "admin_log"


class Product(BaseIdModel, TimestampMixin):
    product_id = fields.UUIDField(index=True, unique=True, default=uuid4)
    name = fields.CharField(128,default="")
    short_name = fields.CharField(32, default="", description="产品名称")
    type=fields.CharField(32, default="", description="产品类型:ProductType")
    model = fields.CharField(128, default="")
    price = fields.BigIntField(default=0)
    online = fields.BooleanField(default=True)
    display_name = fields.CharField(128, default="", description="产品展示名称") 
    icon = fields.CharField(64, default="", description="产品图标")
    desc = fields.CharField(255, default="",description="产品描述")
    mid = fields.CharField(64, default="", description="产品额外标识")
    # model_int = fields.IntField(default=0, description="模型标识(int类型)")
    user_default = fields.BooleanField(default=False, description="是否为用户默认产品")
    # 使用权限
    permission = fields.CharField(32, default="ALL_USER", description="使用权限:ALL_USER,PAID_USER")

    class Meta:
        table = "product"


class RechargeChannelEnum(str, Enum):
    description: str
    
    STRIPE = ("STRIPE", "Stripe")
    CC = ("CC", "cc")
    ANDADA = ("ANDADA", "沈万三支付")
    CHECK_IN = ("CHECK_IN", "签到奖励")
    CHECK_IN_WITH_LINK = ("CHECK_IN_WITH_LINK", "签到带链接奖励")
    CHECK_IN_IN_ROLE_CHANNEL = ("CHECK_IN_IN_ROLE_CHANNEL", "签到已关注角色卡频道奖励")
    ACCOUNT_CREATION = ("ACCOUNT_CREATION", "新建账户奖励")
    INVITATION = ("INVITATION", "邀请奖励")
    INVITEE_RECHARGE_REWARD = ("INVITEE_RECHARGE_REWARD", "邀请用户充值奖励")
    MANUAL_AIRDROP = ("MANUAL_AIRDROP", "客服补发")
    USDT = ("USDT", "USDT")
    PACKAGE_VOUCHER = ("PACKAGE_VOUCHER", "兑换码")
    JOIN_CHAT_GROUP = ("JOIN_CHAT_GROUP", "加入聊天群奖励")
    JOIN_CHANNEL_GROUP = ("JOIN_CHANNEL_GROUP", "加入频道群奖励")
    CHAT_BOT_TASKS = ("CHAT_BOT_TASKS", "聊天机器人任务奖励")
    FC_REWARD = ("FC_REWARD", "首充奖励")
    RE_PURCHASE_REWARD = ("RE_PURCHASE_REWARD", "复购奖励")
    THIRD_PURCHASE_REWARD = ("THIRD_PURCHASE_REWARD", "第三次购买奖励")
    WELFARE=("CHAT_BENEFIT", "福利奖励")
    PUBLISH_CARD = ("PUBLISH_CARD", "角色卡通过奖励")
    P91PAY = ("P91PAY", "91支付")
    TMPAY = ("TMPAY", "同盟支付")
    FFPAY = ("FFPAY", "飞飞支付")
    CONSUMPTION_ACTIVITY_GRAND_PRIZE = ("CONSUMPTION_ACTIVITY_GRAND_PRIZE", "消费活动大奖")
    CONSUMPTION_ACTIVITY_RETURN = ("CONSUMPTION_ACTIVITY_RETURN", "消费活动返还")

    STAR_PAYMENT = ("STAR_PAYMENT", "Telegram Star 支付")

    QSZF = ("QSZF", "强胜支付")
    SDFKW_API = ("SDFKW_API", "迅雷API")
    SJZF = ("SJZF", "速捷支付")
    JLBZF = ("JLBZF", "健力宝支付")
    XJTZF = ("XJTZF", "鑫聚通支付")
    TTZF = ("TTZF", "天天支付")

    def __new__(cls, value, description):
        obj = str.__new__(cls, value)
        obj._value_ = value
        obj.description = description
        return obj

    def __str__(self):
        return self.value
    
    @staticmethod
    def pay_channels() -> list[str]:
        return [
            RechargeChannelEnum.STRIPE,
            RechargeChannelEnum.CC,
            RechargeChannelEnum.ANDADA,
            RechargeChannelEnum.USDT,
            RechargeChannelEnum.P91PAY,
            RechargeChannelEnum.TMPAY,
            RechargeChannelEnum.P91PAY,
            RechargeChannelEnum.FFPAY,
            RechargeChannelEnum.PACKAGE_VOUCHER,
            RechargeChannelEnum.RE_PURCHASE_REWARD,
            RechargeChannelEnum.THIRD_PURCHASE_REWARD,
            RechargeChannelEnum.STAR_PAYMENT,
            RechargeChannelEnum.QSZF,
            RechargeChannelEnum.SDFKW_API,
            RechargeChannelEnum.SJZF,
            RechargeChannelEnum.JLBZF,
            RechargeChannelEnum.XJTZF,
            RechargeChannelEnum.TTZF,
        ]

    @staticmethod
    def user_recharge_channels() -> list['RechargeChannelEnum']:
        return [
            RechargeChannelEnum.TMPAY,
            RechargeChannelEnum.FFPAY,
            RechargeChannelEnum.QSZF,
            RechargeChannelEnum.SJZF,
            RechargeChannelEnum.JLBZF,
            RechargeChannelEnum.XJTZF,
            RechargeChannelEnum.TTZF,
            RechargeChannelEnum.SDFKW_API,
            RechargeChannelEnum.STRIPE,
            RechargeChannelEnum.USDT,
            RechargeChannelEnum.PACKAGE_VOUCHER,
            RechargeChannelEnum.STAR_PAYMENT,
        ]

    @staticmethod
    def dynamic_recharge_channels() -> list['RechargeChannelEnum']:
        return [
            RechargeChannelEnum.TMPAY,
            #RechargeChannelEnum.FFPAY,
            # RechargeChannelEnum.QSZF,
            RechargeChannelEnum.SJZF,
            RechargeChannelEnum.JLBZF,
            RechargeChannelEnum.SDFKW_API,
        ]

    @staticmethod
    def alipay_recharge_channels() -> list['RechargeChannelEnum']:
        return [
            RechargeChannelEnum.TMPAY,
            #RechargeChannelEnum.FFPAY,
            RechargeChannelEnum.SJZF,
            # RechargeChannelEnum.QSZF,
            RechargeChannelEnum.JLBZF,
            RechargeChannelEnum.XJTZF,
            RechargeChannelEnum.TTZF,
            RechargeChannelEnum.SDFKW_API,
        ]

    @staticmethod
    def wechat_recharge_channels() -> list['RechargeChannelEnum']:
        return [
            RechargeChannelEnum.TMPAY,
            # RechargeChannelEnum.QSZF,
            RechargeChannelEnum.SJZF,
            RechargeChannelEnum.JLBZF,
            RechargeChannelEnum.XJTZF,
            RechargeChannelEnum.TTZF,
            RechargeChannelEnum.SDFKW_API,
        ]

class RechargeStatusEnum(str, Enum):
    INIT = "INIT"
    PENDING = "PENDING"
    SUCCEED = "SUCCEED"
    FAILED = "FAILED"
    CANCELED = "CANCELED"
    REFUNDED = "REFUNDED"
    USER_CANCELED = "USER_CANCELED"

class RechargeOrder(BaseIdModel, TimestampMixin):
    recharge_order_id = fields.UUIDField(unique=True, index=True, default=uuid4)
    user_id = fields.BigIntField(index=True)
    amount = fields.BigIntField()
    pay_fee = fields.BigIntField(default=0)
    pay_currency = fields.CharField(16, default="")
    status = fields.CharEnumField(RechargeStatusEnum, max_length=128)
    recharge_channel = fields.CharEnumField(RechargeChannelEnum, max_length=128)
    out_order_id = fields.CharField(128, unique=True, index=True)
    finished_at = fields.DatetimeField(null=True)
    raw_response = fields.TextField(null=True, default=None)
    recharge_product_id = fields.CharField(128, index=True, default="")
    from_bot_id = fields.BigIntField(default=0)
    pay_type = fields.CharField(128, default="", description="支付方式")
    exclude_from_stat = fields.BooleanField(default=False)
    exclude_reason = fields.CharField(128, default='')
    pay_url = fields.CharField(2048, default='')
    pay_redirect_url = fields.CharField(2048, default='')
    platform = fields.CharField(64, default='')
    refunded = fields.BooleanField(default=False, description="是否已退款")

    class Meta:
        table = "recharge_order"


class WithdrawChannelEnum(str, Enum):
    STRIPE = "STRIPE"
    CC = "CC"
    ANDADA = "ANDADA"


class WithdrawOrder(BaseIdModel, TimestampMixin):
    withdraw_order_id = fields.UUIDField(unique=True, index=True, default=uuid4)
    user_id = fields.BigIntField(index=True)
    fee = fields.BigIntField(default=0)
    currency = fields.CharField(16, default="CNY")
    card_name = fields.CharField(128)
    card_no = fields.CharField(128)
    bank_id = fields.CharField(128)
    bank_province = fields.CharField(32)
    bank_city = fields.CharField(32)
    bank_branch_name = fields.CharField(255)
    status = fields.CharEnumField(RechargeStatusEnum, max_length=128)
    withdraw_channel = fields.CharEnumField(WithdrawChannelEnum, max_length=128)
    out_order_id = fields.CharField(128, unique=True, index=True)
    finished_at = fields.DatetimeField(null=True)
    raw_response = fields.TextField(null=True, default=None)

    class Meta:
        table = "withdraw_order"


class PayOrder(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True)
    pay_order_id = fields.UUIDField(unique=True, index=True, default=uuid4)
    product_id = fields.UUIDField()
    product_price = fields.BigIntField()
    total_fee = fields.BigIntField()
    amount = fields.BigIntField()
    status = fields.CharEnumField(OrderStatusEnum, max_length=128)
    finished_at = fields.DatetimeField(null=True)
    role_id = fields.BigIntField(default=0)
    payed_amount = fields.BigIntField(default=0)
    free_amount = fields.BigIntField(default=0)
    extra_info = fields.JSONField(default={}, description="额外信息")

    class Meta:
        table = "pay_order"


class TransactionTypeEnum(str, Enum):
    DEBIT = ("DEBIT",)
    CREDIT = "CREDIT"


class TransactionSourceEnum(str, Enum):
    RE_CHARGE = "RE_CHARGE"
    PAY = "PAY"


class LedgerEntry(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True)
    ledger_entry_id = fields.UUIDField(unique=True, index=True, default=uuid4)
    transaction_id = fields.CharField(128, unique=True, index=True)
    amount = fields.BigIntField()
    type = fields.CharEnumField(TransactionTypeEnum, max_length=128)
    source = fields.CharEnumField(TransactionSourceEnum, max_length=128)
    before_balance = fields.BigIntField()
    after_balance = fields.BigIntField()
    description = fields.CharField(255)

    class Meta:
        table = "ledger_entry"


class CheckIn(BaseIdModel, TimestampMixin):
    account_id = fields.UUIDField()
    check_in_at = fields.DateField()

    class Meta:
        table = "check_in"
        unique_together = (("account_id", "check_in_at"),)


class Invitation(BaseIdModel, TimestampMixin):
    invitation_id = fields.UUIDField(unique=True, default=uuid4)
    invitee_user_id = fields.BigIntField(unique=True, index=True, description="被邀请者id")
    inviter_user_id = fields.BigIntField(index=True, description="邀请者id")
    invited_at = fields.DatetimeField(auto_now_add=True)

    class Meta:
        table = "invitation"


class AuditInfo(BaseModel):
    status: str = "editing"
    reason: str = ""


class RoleOperationConfig(BaseModel):
    user_role_name: str = ""
    chat_background: str = ""
    albums: list[str] = []
    author:bool = False


class TranslateInfo(BaseModel):
    card_name: dict = {}
    simple_info: dict = {}


class RoleConfig(BaseIdModel, TimestampMixin):
    card_name = fields.CharField(512)
    role_name = fields.CharField(512)
    role_avatar = fields.CharField(512)
    introduction = fields.TextField()
    data_config = fields.JSONField()
    tags = fields.TextField(default="")
    spec_version = fields.CharField(128, default="t_v2", description="默认酒馆v2版本")
    uid = fields.BigIntField(index=True, description="创建者id,0表示系统创建")
    status = fields.BooleanField(default=True, description="是否启用")
    privacy = fields.BooleanField(default=False, description="是否公开")
    speaker_id = fields.CharField(
        128, default="zh_female_speaker_1", description="zh_female_speaker_1"
    )
    sub_tags = fields.JSONField(default=[])
    nsfw = fields.BooleanField(default=False, description="是否为NSFW")
    # 已废弃
    support_model_config = fields.JSONField(
        default={}, description="已废弃：支持模型类型（默认全部支持）"
    )
    excluded_product_ids = fields.JSONField(default=[], description="排除的模型产品id")
    book_id = fields.CharField(128, default="", description="角色所属的书籍id")
    level_type = fields.CharField(32, default="normal", description="角色卡等级类型")
    operation_config = fields.JSONField(default={}, description="运营配置")
    def_language = fields.CharField(32, default="zh", description="默认语言")
    support_photo = fields.BooleanField(default=False, description="是否支持图片")
    image_nsfw = fields.BooleanField(default=False, description="图片是否为NSFW")
    real_role = fields.BooleanField(default=True, description="是否为独立角色卡")
    switch_en_desc = fields.BooleanField(default=False, description="是否开启英文描述")
    play_type = fields.CharField(32, default="RIVALRY", description="角色卡玩法类型")
    chat_type = fields.CharField(32, default="Chat", description="角色卡聊天类型")

    class Meta:
        table = "role_config"


class RoleAudit(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True)
    mode_type = fields.CharField(64, default="single", description="single/group")
    mode_target_id = fields.BigIntField(index=True, description="mode_target_id")
    role_id = fields.BigIntField(default=0, description="role_id")
    open_role_id = fields.BigIntField(default=0, description="open_role_id")
    open_group_id = fields.BigIntField(default=0, description="open_group_id")
    status = fields.CharField(
        128,
        not_null=True,
        description='status("editing","auditing","approved","rejected")',
    )
    reason = fields.TextField(default="", description="reason")
    published_at = fields.DatetimeField(
        default=datetime.min, description="published at"
    )
    published_version = fields.BigIntField(default=0, description="published version")
    receive_reward = fields.BooleanField(default=False, description="是否领取奖励")
    receive_reward_ids = fields.JSONField(default=[], description="领取奖励列表")
    read_receive_reward = fields.BooleanField(default=False, description="是否已读领取奖励")
    

    class Meta:
        table = "role_audit"


# 定义一个Tortoise模型来表示配置
# class SystemConfig(BaseIdModel, TimestampMixin):
#     openai_max_context = fields.IntField()
#     openai_max_tokens = fields.IntField()
#     openai_temperature = fields.FloatField()
#     openai_top_p = fields.FloatField()
#     openai_frequency_penalty = fields.FloatField()
#     openai_presence_penalty = fields.FloatField()
#     openai_stop = fields.TextField()
#     prompt_content = fields.TextField()
#     name = fields.TextField()
#     des = fields.TextField()

#     class Meta:
#         table = "system_config"


# 定义一个模型来存储标签列表
class TagListOrder(BaseIdModel, TimestampMixin):
    tags_order = fields.JSONField()  # 使用JSON字段来存储标签列表

    class Meta:
        table = "tag_list_order"


class UserStatus(int, Enum):
    NORMAL = 0
    #0-9 为特殊黑名单配置
    DELETED = 1
    CHAT_BLACK = 2
    ADMIN = -1
    #10 - 19 为卡片作者运营号段
    AUTHOR = 10

class UserRegisterSource(str, Enum):
    TMA = "TMA"
    CHAT_BOT = "CHAT_BOT"
    GROUP = "GROUP"
    CHANNEL = "CHANNEL"
    SUPER_GROUP = "SUPERGROUP"
    USA_WEB = "USA_WEB"
    WEB = "WEB"
    CHAT_BOT_1 = "CHAT_BOT_1"
    TMA_1 = "TMA_1"
    CHAT_BOT_2 = "CHAT_BOT_2"
    TMA_2 = "TMA_2"
    TMA_3 = "TMA_3"
    CHAT_BOT_3 = "CHAT_BOT_3"
    TMA_10086 = "TMA_10086"
    CHAT_BOT_10086 = "CHAT_BOT_10086"
    TMA_666 = "TMA_666"
    CHAT_BOT_666 = "CHAT_BOT_666"
    TMA_AIJG = "TMA_AIJG"
    CHAT_BOT_AIJG = "CHAT_BOT_AIJG"
    TMA_AI16Z = "TMA_AI16Z"
    CHAT_BOT_AI16Z = "CHAT_BOT_AI16Z"
    CHAT_BOT_LEAF = "CHAT_BOT_LEAF"
    CHAT_BOT_VINE = "CHAT_BOT_VINE"
    CHAT_BOT_SEED = "CHAT_BOT_SEED"
    CHAT_BOT_FERN = "CHAT_BOT_FERN"
    CHAT_BOT_MOSS = "CHAT_BOT_MOSS"
    CHAT_BOT_WILLOW = "CHAT_BOT_WILLOW"
    TMA_LEAF = "TMA_LEAF"
    TMA_VINE = "TMA_VINE"
    TMA_SEED = "TMA_SEED"
    TMA_FERN = "TMA_FERN"
    TMA_MOSS = "TMA_MOSS"
    TMA_WILLOW = "TMA_WILLOW"
    CHAT_BOT_JGXX = "CHAT_BOT_JGXX"
    TMA_JGXX = "TMA_JGXX"

    #橱窗
    CHANNEL_HM_AI = "CHANNEL_HM_AI"
    CHANNEL_ROLE = "CHANNEL_ROLE"
    CHANNEL_666 = "CHANNEL_666"

    #群组
    GROUP_666 = "GROUP_666"
    
    #图片bot
    IMAGE_BOT = "IMAGE_BOT"

    @staticmethod
    def of(v: str|None) -> "UserRegisterSource|None":
        try:
            return UserRegisterSource(v.upper()) if v else None
        except ValueError:
            return None
    
    def is_tma(self) -> bool:
        return self.startswith("TMA")
    
    def is_chat_bot(self) -> bool:
        return self.startswith("CHAT_BOT")
class PersonalBg(BaseModel):
    image_bgs: list[str] = []
    selected_bg_index: int = 0
    opacity: int = 85


class User(BaseIdModel, TimestampMixin):
    email = fields.CharField(max_length=255, unique=True)
    password_hash = fields.CharField(max_length=2048)
    nickname = fields.CharField(max_length=255)
    avatar = fields.CharField(max_length=512, default="")
    llm_model = fields.CharField(max_length=128, default="",description="默认模型[已删除]")
    status = fields.IntField(default=0)
    register_source = fields.CharEnumField(
        UserRegisterSource, max_length=128, default=UserRegisterSource.TMA
    )
    enable_nsfw = fields.BooleanField(default=True, description="是否启用NSFW")
    show_nsfw = fields.BooleanField(default=True, description="是否显示NSFW内容")
    show_nsfw_image = fields.BooleanField(default=True, description="是否显示NSFW图片")
    show_chat_tips = fields.BooleanField(default=True, description="是否显示聊天提示")
    role_count = fields.IntField(default=0, description="角色卡数量(结合角色类型付费等取最大)")
    status_block_switch = fields.BooleanField(default=True, description="是否开启状态栏开关")
    use_personal_bg = fields.BooleanField(default=False, description="是否使用个人背景")
    personal_bg = fields.JSONField(default={}, description="个人背景")
    chat_product_mid = fields.CharField(64, default="", description="聊天产品额外标识")
    invitation_privilege = fields.BooleanField(default=True, description="是否有邀请权限")
    publish_role_privilege = fields.JSONField(default={"reject_count":0, "ts":0}, description="count是连续被审核不通过的次数,ts是被运营手动解封的timestamp")
    voice_content_type = fields.CharEnumField(VoiceContentType, default='INSIDE_ONLY')
    chat_channel = fields.CharField(64, default="", description="聊天频道")
    # alter table users_pw add language varchar(32) default '' comment '用户语言'
    language = fields.CharField(32, default="", description="用户语言")

    @property
    def is_admin(self) -> bool:
        return self.status == UserStatus.ADMIN

    @classmethod
    async def create(cls, **kwargs):
        kwargs["password_hash"] = bcrypt.hashpw(
            kwargs.pop("password").encode("utf-8"), bcrypt.gensalt()
        ).decode("utf-8")
        return await super().create(**kwargs)

    @staticmethod
    def verify_password(password: str, password_hash: str) -> bool:
        return bcrypt.checkpw(password.encode("utf-8"), password_hash.encode("utf-8"))

    class Meta:
        table = "users_pw"

class UserAltConfigStatus(int, Enum):
    DELETED = 0
    INACTIVE = 1
    ACTIVE = 2


class UserAltProfiles(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True)
    nickname = fields.CharField(max_length=255)
    avatar = fields.CharField(max_length=512, default="")
    persona_setting = fields.CharField(max_length=512, default="", description="身份设置")
    status = fields.IntField(default=1, description="0-已删除,1-未激活,2-已激活")

    class Meta:
        table = "user_alt_profiles"

class UserPersonaConfig(BaseModel):
    title: str = ""
    description: str = ""
    status: int = 1

# UserAltPersonas table已废弃
class UserAltPersonas(BaseIdModel, TimestampMixin): # 已废弃
    user_id = fields.BigIntField(index=True)
    persona_settings = fields.JSONField(default=[], description="身份特点设置：做多支持3组")
    class Meta:
        table = "user_alt_personas"

class TgBotConfig(BaseIdModel, TimestampMixin):
    category = fields.CharField(128, default="TMA",description="类型: TMA,CHAT_BOT")
    token = fields.CharField(128, default="", description="token",unique=True)
    bot_id = fields.BigIntField(default=0, description="机器人id",index=True)
    tma_bot_id = fields.CharField(32, default="", description="小程序标识ID")
    #alter table tg_bot_config add chat_bot_id varchar(32) default '' comment '聊天机器人标识ID'
    chat_bot_id = fields.CharField(32, default="", description="聊天机器人标识ID")
    webhook_id = fields.CharField(32, default="", description="webhook标识ID")
    first_name = fields.CharField(128,default="", description="名称")
    username = fields.CharField(128,default="", description="用户名")
    url = fields.CharField(128,default="", description="投放链接")
    description = fields.TextField(default="",description="描述")
    init = fields.BooleanField(default=False, description="是否已经初始化")
    status = fields.BooleanField(default=True, description="是否使用中:1为使用中,0为停用")
    main_bot = fields.BooleanField(default=False, description="是否为主bot")
    referenceable = fields.BooleanField(default=True, description="是否可以被引用")
    # open_bottom_message = fields.BooleanField(default=False, description="是否打开置底消息")
    # refresh_resource = fields.BooleanField(default=False, description="是否刷新资源(简介、)")
    remark = fields.CharField(256,default="",description="备注（不对外）")
    bot_nsfw = fields.BooleanField(default=False, description="是否为NSFW")
    show_nsfw_image = fields.BooleanField(default=False, description="是否显示NSFW图片")
    lang = fields.CharField(32, description='bot 语言', default='')
    target_type = fields.CharField(32, default="UNI_BOT", description="目标类型:TMA,CHAT_BOT,IMAGE_BOT,UNI_BOT,HELPER_BOT")

    def en_bot(self): 
        return self.lang.startswith('en')

    class Meta:
        table = "tg_bot_config"

class TgChannelConfig(BaseIdModel, TimestampMixin):
    username = fields.CharField(128,default="", description="用户名",unique=True)
    chat_id = fields.BigIntField(default=0, description="聊天id")
    title = fields.CharField(128,default="", description="标题")
    description = fields.CharField(256,default="",description="描述")
    url = fields.CharField(128,default="", description="投放链接")
    category= fields.CharField(32, default="ROLE",description="类型: ROLE,WELFARE")
    nsfw = fields.BooleanField(default=True, description="是否为NSFW")
    # ai_content = fields.BooleanField(default=False, description="是否为AI内容")
    init = fields.BooleanField(default=False, description="是否已经初始化")
    status = fields.BooleanField(default=True, description="是否使用中:1为使用中,0为停用")
    referenceable = fields.BooleanField(default=True, description="是否可以被引用")
    main = fields.BooleanField(default=False, description="是否为主频道")
    channel_id = fields.BigIntField(default=0, description="渠道投放ID")
    open_bottom_message = fields.BooleanField(default=False, description="是否打开置底消息")
    # open_pined_message = fields.BooleanField(default=False, description="是否打开置顶消息")
    # refresh_message = fields.BooleanField(default=False, description="是否刷新消息")
    # push_message = fields.BooleanField(default=False, description="是否推送公共消息")
    remark = fields.CharField(256,default="",description="备注（不对外）")
    helper_bot_ids = fields.JSONField(default=[], description="帮助机器人id")
    linked_group = fields.CharField(128,default="", description="关联群组")
    language = fields.CharField(32, default="zh", description="语言")
    

    class Meta:
        table = "tg_channel_config"

class TgGroupConfig(BaseIdModel, TimestampMixin):
    username = fields.CharField(128,default="", description="用户名",unique=True)
    chat_id = fields.BigIntField(default=0, description="聊天id")
    title = fields.CharField(128,default="", description="标题")
    description = fields.CharField(256,default="",description="描述")
    url = fields.CharField(128,default="", description="投放链接")
    category = fields.CharField(32, default="CHAT",description="类型: CHAT,ROLE")
    referenceable = fields.BooleanField(default=True, description="是否可以被引用")
    status = fields.BooleanField(default=True, description="是否使用中:1为使用中,0为停用")
    init = fields.BooleanField(default=False, description="是否已经初始化")
    main = fields.BooleanField(default=False, description="是否为主群")
    # open_bottom_message = fields.BooleanField(default=False, description="是否打开置底消息")
    # open_pined_message = fields.BooleanField(default=False, description="是否打开置顶消息")
    remark = fields.CharField(256,default="",description="备注（不对外）")
    helper_bot_ids = fields.JSONField(default=[], description="帮助机器人id")
    support_share = fields.BooleanField(default=False, description="是否支持分享")
    share_topic_id = fields.BigIntField(default=0, description="分享话题id")
    language = fields.CharField(32, default="zh", description="语言")

    class Meta:
        table = "tg_group_config"


# 用户活跃信息
class UserBotActive(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(default=0,description="用户id")
    bot_id = fields.BigIntField(default=0, description="机器人id")
    last_active_at = fields.DatetimeField(null=True, description="最后活跃时间")
    blocked = fields.BooleanField(default=False, description="是否被屏蔽")

    class Meta:
        table = "user_bot_active"
        unique_together = (("user_id", "bot_id"),)

class UserPinnedMessage(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(default=0,description="用户id")
    bot_id = fields.BigIntField(default=0, description="机器人id")
    position = fields.CharField(32,default="",description="位置")
    message_id = fields.BigIntField(default=0, description="消息id")

    class Meta:
        table = "user_pined_message"
        unique_together = (("user_id", "bot_id", "position"),)
class GlobalPinedMessage(BaseIdModel, TimestampMixin):
    chat_id = fields.BigIntField(default=0,description="聊天id")
    position = fields.CharField(32, default="",description="位置")
    message_id = fields.BigIntField(default=0, description="消息id")
    class Meta:
        table = "global_pined_message"
        unique_together = (("chat_id", "position"),)


class BotResource(BaseIdModel, TimestampMixin):
    bot_id = fields.BigIntField(default=0, description="机器人id,0表示默认配置")
    bot_type = fields.CharField(32, default="", description="机器人类型:TMA,CHAT_BOT,CHANNEL,GROUP,HELPER,WEB,CUSTOMER")
    position = fields.CharField(32, default="", description="位置:user_bot_bottom,channel_bottom,description")
    message_template = fields.JSONField(default={}, description="消息模板")
    content = fields.TextField(default="", description="内容")
    class Meta:
        table = "bot_resource"
        unique_together = (("bot_id", "bot_type", "position"),)
class ResourceTemplate(BaseIdModel, TimestampMixin):
    platform = fields.CharField(32, default="TMA", description="平台")
    # category = fields.CharField(32, default="DEFAULT", description="分类")
    position = fields.CharField(32, default="DEFAULT", description="位置")
    target_id = fields.BigIntField(default=0, description="目标id")
    message_template = fields.JSONField(default={}, description="消息模板")
    content = fields.TextField(default="", description="内容")
    class Meta:
        table = "resource_template"
        unique_together = (("platform", "position", "target_id"),)


class OauthUser(BaseIdModel, TimestampMixin):
    uid = fields.BigIntField(index=True)
    provider_name = fields.CharField(50)
    provider_user_id = fields.CharField(512, index=True)
    provider_user_email = fields.CharField(512, index=True)
    user_info = fields.JSONField()

    class Meta:
        table = "oauth_user"
        unique_together = ("provider_name", "provider_user_id")


class VoiceSpeaker(BaseIdModel, TimestampMixin):
    speaker_id = fields.CharField(max_length=128, unique=True)
    name = fields.CharField(max_length=128, description="语音合成名称")
    speed = fields.FloatField(default=1.0, description="语速")
    sample_url = fields.TextField(desciption="语音合成示例URL")
    api_url = fields.TextField(description="语音合成API地址")
    status = fields.BooleanField(default=True, description="是否启用")
    order = fields.IntField(default=1)

    class Meta:
        table = "voice_speaker"


class TelegramUser(BaseIdModel, TimestampMixin):
    uid = fields.BigIntField(index=True, unique=True)
    tg_id = fields.BigIntField(index=True, unique=True)
    first_name = fields.CharField(128, default="")
    last_name = fields.CharField(128, default="")
    user_name = fields.CharField(128, default="")
    is_premium = fields.BooleanField(default=False)
    reg_bot_id = fields.BigIntField(default=0, index=True)

    class Meta:
        table = "tg_user"


class SubTag(BaseIdModel, TimestampMixin):
    tag_name = fields.CharField(32, unique=True)
    order = fields.IntField(default=1)
    enabled = fields.BooleanField(default=True)
    category = fields.CharField(32, default="", description="分类")
    role_count = fields.IntField(default=0, description="角色数量")

    class Meta:
        table = "sub_tags"


class RegexAffect(str, Enum):
    USER_INPUT = "USER_INPUT"
    AI_OUTPUT = "AI_OUTPUT"
    SLASH_COMMAND = "SLASH_COMMAND"
    AI_OUTPUT_REP = "AI_OUTPUT_REP"


class RegexOption(str, Enum):
    FORMAT_DISPLAY = "FORMAT_DISPLAY"
    FORMAT_PROMPT = "FORMAT_PROMPT"
    RUN_ON_EDIT = "RUN_ON_EDIT"
    AUTO_RETRY = "AUTO_RETRY"


class RegexRule(BaseIdModel, TimestampMixin):
    rule_id = fields.UUIDField(unique=True, default=uuid4)
    title = fields.CharField(128)
    description = fields.TextField(default="", description="描述")
    regex = fields.TextField()
    replacement = fields.TextField()
    affects = fields.JSONField(default=[])
    options = fields.JSONField(default=[])
    test_reg_txt = fields.TextField(default="", description="测试文本")
    min_depth = fields.IntField(default=-1)
    max_depth = fields.IntField(default=-1)
    enabled = fields.BooleanField(default=True)
    sort_order = fields.IntField(default=0, description="排序")

    class Meta:
        table = "regex_rule"


class RoleCategoryOrder(BaseIdModel, TimestampMixin):
    category = fields.CharField(128, index=True, unique=True)
    orders = fields.JSONField(default=[])

    class Meta:
        table = "role_category_order"
class RoleOrder(BaseIdModel, TimestampMixin):
    mode_type = fields.CharField(16, default="single", description="single/group")
    mode_target_id = fields.BigIntField(description="mode_target_id")
    position = fields.IntField(default=0, description="position")
    position_temp = fields.IntField(default=0, description="position_temp")

    class Meta:
        table = "role_order"
        unique_together = (("mode_type", "mode_target_id"),)


class ExpirableStatusEnum(str, Enum):
    NORMAL = "NORMAL"
    EXPIRED = "EXPIRED"


class ExpirableAward(BaseIdModel, TimestampMixin):
    award_id = fields.UUIDField(unique=True, default=uuid4)
    user_id = fields.BigIntField(index=True)
    out_order_id = fields.CharField(128, index=True)
    total_amount = fields.BigIntField()
    spend_amount = fields.BigIntField(default=0)
    balance = fields.BigIntField()
    status = fields.CharEnumField(ExpirableStatusEnum, max_length=128)
    claim_at = fields.DatetimeField()
    expires_at = fields.DatetimeField()
    from_type = fields.CharField(128, default="REWARD")

    class Meta:
        table = "expirable_award"
        unique_together = (("user_id", "out_order_id", "from_type"),)


class AwardLedger(BaseIdModel, TimestampMixin):
    ledger_id = fields.UUIDField(unique=True, default=uuid4)
    user_id = fields.BigIntField(index=True)
    award_id = fields.UUIDField(index=True)
    order_id = fields.UUIDField(index=True)
    amount = fields.BigIntField()
    pay_time = fields.DatetimeField()

    class Meta:
        table = "award_ledger"


class ChargeAmountGenerator(BaseIdModel, TimestampMixin):
    original_fee = fields.BigIntField()
    recipient_address = fields.CharField(128)
    delta = fields.BigIntField()

    class Meta:
        table = "charge_amount_generator"
        unique_together = ("original_fee", "recipient_address")


class USDTRechargeOrder(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True)
    recharge_id = fields.CharField(128)
    order_id = fields.UUIDField(unique=True, index=True, default=uuid4)
    original_fee = fields.BigIntField()
    final_fee = fields.BigIntField()
    recipient_address = fields.CharField(128)
    payment_address = fields.CharField(128, index=True, default="")
    platform = fields.CharField(128, default="")
    ccy = fields.CharField(128, default="")
    chain = fields.CharField(128, default="")
    status = fields.CharEnumField(RechargeStatusEnum, max_length=128)
    tx_id = fields.CharField(128, index=True, default="")
    out_order_id = fields.CharField(128, index=True, default="")
    finished_at = fields.DatetimeField(null=True)
    expired_at = fields.DatetimeField()
    recharge_product_id = fields.UUIDField(index=True, default="")

    class Meta:
        table = "usdt_recharge_order"


class ChatHistoryStatistic(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True,description="用户id")
    role_id = fields.BigIntField(index=True,description="角色id")
    mode_type = fields.CharField(16,default="single",description="single/group")
    mode_target_id = fields.BigIntField(default=0,description="目标ID,角色id/群组id")
    type = fields.CharField(16,default="AI",description="消息类型")
    model = fields.CharField(64,default="",description="模型")
    conversation_id = fields.CharField(128,description="对话id")
    message_id = fields.CharField(128,description="消息id")
    input_token_count = fields.IntField(default=0,description="输入token数量")
    output_token_count = fields.IntField(default=0,description="输出token数量")
    platform = fields.CharField(32, default="TMA", description="平台")
    consume = fields.BigIntField(default=0, description="消耗的积分")
    chat_product_mid = fields.CharField(32, default="", description="聊天产品额外标识")
    product_id = fields.CharField(128, default="", description="产品id")
    llm_input_tokens = fields.IntField(default=0, description="llm_prompt_tokens")
    llm_output_tokens = fields.IntField(default=0, description="llm_output_tokens")
    llm_cache_created_tokens = fields.IntField(default=0, description="llm_cache_created_tokens")
    llm_cache_read_tokens = fields.IntField(default=0, description="llm_cache_read_tokens")
    

    class Meta:
        table = "chat_history_statistic"


class RoleStatistic(BaseIdModel, TimestampMixin):
    role_id = fields.BigIntField(index=True, unique=True)
    total_conversation = fields.IntField(default=0, description="总对话数")
    total_message = fields.IntField(default=0, description="总消息数")
    base_count = fields.IntField(default=0, description="基础数量")
    user_base_count = fields.IntField(default=0, description="基础用户数量")
    user_count = fields.IntField(default=0, description="用户数量")
    total_diamond = fields.BigIntField(default=0, description="钻石数量")
    hot_v1 = fields.IntField(default=0, description="v1版本热度值")
    #最近24小时Haiku数量
    h24_haiku_count = fields.IntField(default=0, description="最近24小时Haiku数量")
    daily_hot = fields.IntField(default=0, description="日榜")
    weekly_hot = fields.IntField(default=0, description="周榜")
    monthly_hot = fields.IntField(default=0, description="月榜")

    class Meta:
        table = "role_statistic"

class RoleDailyStatistic(BaseIdModel, TimestampMixin):
    role_id = fields.BigIntField(not_null=True)
    date_index = fields.IntField(default=0, description="日期索引")
    total_message = fields.IntField(default=0, description="总消息数")
    total_diamond = fields.BigIntField(default=0, description="钻石数量")
    class Meta:
        table = "role_daily_statistic"
        unique_together = (("role_id", "date_index"),)


class GroupStatistic(BaseIdModel, TimestampMixin):
    group_id = fields.BigIntField(index=True, unique=True)
    total_conversation = fields.IntField(default=0, description="总对话数")
    total_message = fields.IntField(default=0, description="总消息数")
    base_count = fields.IntField(default=0, description="基础数量")
    user_count = fields.IntField(default=0, description="用户数量")
    total_diamond = fields.BigIntField(default=0, description="钻石数量")
    hot_v1 = fields.IntField(default=0, description="v1版本热度值")
    h24_haiku_count = fields.IntField(default=0, description="最近24小时Haiku数量")

    class Meta:
        table = "group_statistic"

class RoleRankStat(BaseIdModel, TimestampMixin):
    sort_type = fields.CharField(32, default="hot", description="排序类型")
    date_index = fields.IntField(default=0, description="日期索引")
    role_ids = fields.JSONField(default=[], description="角色id列表")
    class Meta:
        table = "role_rank_stat"
        unique_together = (("sort_type", "date_index"),)


class RechargeProductTypeEnum(str, Enum):
    ONE_TIME = "ONE_TIME"
    MULTIPLE_TIMES = "MULTIPLE_TIMES"


class RechargeProduct(BaseIdModel, TimestampMixin):
    recharge_product_id = fields.UUIDField(unique=True, default=uuid4)
    title = fields.CharField(128)
    desc = fields.CharField(255)
    price = fields.BigIntField()
    cny_price = fields.BigIntField(default=-1)
    amount = fields.BigIntField()
    reward_amount = fields.BigIntField()
    fc_reward_amount = fields.BigIntField()
    charged_expire_delta = fields.IntField()
    expired_at = fields.DatetimeField()
    corner_title = fields.CharField(128, default="")
    corner_tip = fields.CharField(128)
    fc_corner_title = fields.CharField(128, default="")
    fc_corner_tip = fields.CharField(128, default="")
    original_price_desc = fields.CharField(128, default="")
    promotion_price_desc = fields.CharField(128, default="")
    feature_list = fields.JSONField(default=[])
    max_charge_times = fields.IntField()
    product_type = fields.CharEnumField(RechargeProductTypeEnum, max_length=32)
    expire_desc = fields.CharField(255)
    promotion_desc = fields.CharField(255)
    reward_desc = fields.TextField()
    button_text = fields.CharField(128, default="", null=False)
    remarks = fields.TextField()
    enabled = fields.BooleanField(default=True)
    show_for_user = fields.BooleanField(default=False)
    order = fields.IntField(default=0)
    display_filters = fields.JSONField(default=[], description="展示过滤条件:")
    content_for_bot = fields.TextField(description="在tg直聊里展示的文案")
    disabled_message = fields.CharField(128, default="", description="禁用时的提示信息")

    def disabled(self) -> bool:
        return (self.display_filters is not None
                and RpDisplayFilter.DISABLED_AFTER_PURCHASE.value in self.display_filters)

    class Meta:
        table = "recharge_product"


class VoucherStatusEnum(str, Enum):
    NORMAL = "NORMAL"
    USED = "USED"
    EXPIRED = "EXPIRED"


class PackageVoucher(BaseIdModel, TimestampMixin):
    voucher_id = fields.UUIDField(unique=True, default=uuid4)
    recharge_product_id = fields.UUIDField(index=True)
    price = fields.BigIntField()
    distributor = fields.CharField(128)
    expire_at = fields.DatetimeField()
    status = fields.CharEnumField(VoucherStatusEnum, max_length=32)
    used_by_user_id = fields.BigIntField(default=0)
    used_at = fields.DatetimeField(null=True)
    used_from = fields.CharField(64, default="")

    class Meta:
        table = "package_voucher"


class ChatJoinTask(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True)
    chat_id = fields.BigIntField(index=True)
    chat_name = fields.CharField(128)
    join_time = fields.DatetimeField()

    class Meta:
        table = "chat_join_task"
        unique_together = (("user_id", "chat_id"),)


class CheckInTask(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField()
    check_in_at = fields.DateField()

    class Meta:
        table = "check_in_task"
        unique_together = (("user_id", "check_in_at"),)


class GlobalConfig(BaseIdModel, TimestampMixin):
    key = fields.CharField(128, unique=True)
    value = fields.JSONField()
    enabled = fields.BooleanField(default=True)

    class Meta:
        table = "global_config"


class UserRegisterChannel(BaseIdModel, TimestampMixin):
    channel_id = fields.IntField(index=True, default=0)
    inviter_user_id = fields.BigIntField(index=True, default=0)
    user_id = fields.BigIntField(index=True, unique=True)
    invite_link = fields.CharField(128, default="")
    chat_id = fields.CharField(128, index=True)
    joined_chat_type = fields.CharField(
        128, default="TMA"
    )  # TMA, CHAT_BOT, GROUP, CHANNEL
    start_role = fields.BigIntField(default=0)

    class Meta:
        table = "user_register_channel"


class TranslateTask(BaseIdModel, TimestampMixin):
    type = fields.CharField(32)
    task_key = fields.CharField(128)
    source = fields.JSONField(default={}, description="翻译内容")
    languages = fields.JSONField(default=[], description="翻译语言")
    target = fields.JSONField(default={}, description="翻译目标")
    status = fields.CharField(16, description="翻译状态")
    retry_times = fields.IntField(default=0, description="重试次数")

    class Meta:
        table = "translate_task"
class TranslateResource(BaseIdModel, TimestampMixin):
    category = fields.CharField(32, default="COMMON", description="分类")
    key = fields.CharField(128, default="", description="key")
    text = fields.TextField(default="", description="原文")
    zh_cn = fields.TextField(default="", description="中文")
    zh_tw = fields.TextField(default="", description="繁体中文")
    en_us = fields.TextField(default="", description="英文")
    # ES = "es_ES"  # 西班牙语
    # PT = "pt_PT"  # 葡萄牙语
    # RU = "ru_RU"  # 俄语
    # FR = "fr_FR"  # 法语
    # DE = "de_DE"  # 德语
    # AR = "ar_SR"  # 阿拉伯语
    # TR = "tr_TR"  # 土耳其语
    # NL = "nl_NL"  # 荷兰语
    # IT = "it_IT"  # 意大利语
    # UK = "uk_UA"  # 乌克兰语
    es_es = fields.TextField(default="", description="西班牙语")
    pt_pt = fields.TextField(default="", description="葡萄牙语")
    ru_ru = fields.TextField(default="", description="俄语")
    fr_fr = fields.TextField(default="", description="法语")
    de_de = fields.TextField(default="", description="德语")
    ar_sr = fields.TextField(default="", description="阿拉伯语")
    tr_tr = fields.TextField(default="", description="土耳其语")
    nl_nl = fields.TextField(default="", description="荷兰语")
    it_it = fields.TextField(default="", description="意大利语")
    uk_ua = fields.TextField(default="", description="乌克兰语")
    ja_jp = fields.TextField(default="", description="日语")
    ko_kr = fields.TextField(default="", description="韩语")
    # alter table translate_resource add column es_es text comment '西班牙语';
    # alter table translate_resource add column pt_pt text comment '葡萄牙语';
    # alter table translate_resource add column ru_ru text comment '俄语';
    # alter table translate_resource add column fr_fr text comment '法语';  
    # alter table translate_resource add column de_de text comment '德语';
    # alter table translate_resource add column ar_sr text comment '阿拉伯语';
    # alter table translate_resource add column tr_tr text comment '土耳其语';
    # alter table translate_resource add column nl_nl text comment '荷兰语';
    # alter table translate_resource add column it_it text comment '意大利语';
    # alter table translate_resource add column uk_ua text comment '乌克兰语';
    # alter table translate_resource add column ja_jp text comment '日语';
    # alter table translate_resource add column ko_kr text comment '韩语';

    status = fields.CharField(16, default="pending", description="翻译状态")
    review = fields.BooleanField(default=False, description="是否需要人工审核")

    class Meta:
        table = "translate_resource"
        unique_together = (("category", "key"),)
        #增加索引
        indexes = [("updated_at")]


class TgDeletedMessage(BaseIdModel, TimestampMixin):
    chat_id = fields.BigIntField()
    message_id = fields.BigIntField()
    user_id = fields.BigIntField(index=True)
    message = fields.TextField()
    message_type = fields.CharField(32)
    tg_deleted = fields.BooleanField(default=False)
    bot = fields.CharField(128, default="TMA")
    expire_at = fields.DatetimeField(not_null=True)
    bot_id = fields.BigIntField(default=0,description="删除消息的bot id")

    class Meta:
        table = "tg_deleted_message"


class ChatBotTask(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True)
    task_key = fields.CharField(128, index=True)
    task_type = fields.CharField(128, index=True)
    finished_at = fields.DatetimeField(null=True)

    class Meta:
        table = "chat_bot_tasks"
        unique_together = (("user_id", "task_key"),)


class SXUser(BaseIdModel, TimestampMixin):
    tg_id = fields.BigIntField(index=True)
    first_name = fields.CharField(max_length=64, null=True)
    last_name = fields.CharField(max_length=64, null=True)
    user_name = fields.CharField(max_length=128, null=True)
    group_id = fields.BigIntField(index=True, default=0, description="群组id")
    message_thread_id = fields.BigIntField(
        index=True, description="群组的forum_topic id"
    )
    forum_topic_name = fields.CharField(
        max_length=128, default="", description="群组的forum_topic name"
    )
    bot_id = fields.BigIntField(index=True, description="bot id")

    class Meta:
        table = "sx_bot_user"
        unique_together = (("tg_id", "bot_id"),)


class ChatGroupConfig(BaseIdModel, TimestampMixin):

    name = fields.CharField(128, default="", description="群组名称")
    user_id = fields.BigIntField(index=True, description="创建者id")
    simple_intro = fields.TextField(default="", description="简介")
    introduction = fields.TextField(default="", description="背景介绍")
    nsfw = fields.BooleanField(default=False, description="是否为NSFW")
    scenario = fields.TextField(default="", description="场景")
    first_message = fields.TextField(default="", description="首次进入群组的消息")
    avatar = fields.CharField(128, default="", description="头像")
    tags = fields.CharField(16, default="", description="分类")
    sub_tags = fields.JSONField(default=[], description="子分类标签")
    status = fields.BooleanField(default=True, description="是否启用")
    public = fields.BooleanField(default=False, description="是否公开")
    def_language = fields.CharField(32, default="zh", description="默认语言")
    author = fields.BooleanField(default=False, description="是否显示作者")

    class Meta:
        table = "chat_group_config"


class ChatGroupRelation(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True, description="用户id")
    group_id = fields.BigIntField(index=True, description="群组id")
    role_id = fields.BigIntField(default=0, description="角色id")
    deleted = fields.BooleanField(default=False, description="是否删除")

    class Meta:
        table = "chat_group_relation"
        unique_together = (("group_id", "role_id"),)

class RecallMessageHistory(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True, description="用户id")
    send_to_tma_success = fields.BooleanField(default=False, description="是否发送到tma")
    send_to_chatbot_success = fields.BooleanField(default=False, description="是否发送到chatbot")
    tma_blocked = fields.BooleanField(default=False, description="TMA 被封禁")
    chat_bot_blocked = fields.BooleanField(default=False, description="CHAT_BOT 被封禁")
    tma_uninitiated = fields.BooleanField(default=False, description="TMA 未初始化")    
    chat_bot_uninitiated = fields.BooleanField(default=False, description="CHAT_BOT 未初始化")
    content = fields.TextField(description="内容")
    type = fields.CharField(32, description="类型, CHAT|BALANCE")
    chatbot_error_msg = fields.CharField(255, default="", description="chatbot 错误信息")
    tma_error_msg = fields.CharField(255, default="", description="TMA 错误信息")

    class Meta:
        table = "recall_message_history"

class PopupConfig(BaseIdModel, TimestampMixin):
    title = fields.CharField(32, description="标题")
    content = fields.TextField(description="内容")
    start_at = fields.DatetimeField(description="开始时间")
    end_at = fields.DatetimeField(description="结束时间")
    show_period = fields.CharField(32,description="展示周期ONCE,EVERY_DAY,EVERY_WEEK",default="ONCE")
    chat_platform = fields.JSONField(description="聊天平台",default=[])
    position = fields.CharField(32, description="位置",default="")
    published = fields.BooleanField(description="是否发布",default=False)
    button_text = fields.CharField(32, description="按钮文字",default="")
    button_action_type = fields.CharField(32, description="操作类型",default="")
    button_link_url = fields.CharField(255, description="操作链接",default="")
    show_close_icon = fields.BooleanField(description="是否显示关闭按钮",default=True)
    user_scopes = fields.JSONField(description="用户范围",default=[])
    # alter table popup_config add column un_support_languages json default '[]' comment '不支持的语言';
    un_support_languages = fields.JSONField(description="不支持的语言",default=[])

    class Meta:
        table = "popup_config"

class PopupRecord(BaseIdModel, TimestampMixin):
    type = fields.CharField(32, description="类型")
    identifier = fields.CharField(128, description="标识")
    user_id = fields.BigIntField(index=True, description="用户id")
    read = fields.BooleanField(default=True, description="是否关闭")

    class Meta:
        table = "popup_record"
        unique_together = (("user_id","identifier","type"),)


class ChannelDeliveryPlan(BaseIdModel, TimestampMixin):
    plan_id = fields.BigIntField(index=True, unique=True, description="计划id")
    channel = fields.CharField(32, description="渠道",default="默认渠道")
    ad_type = fields.CharField(32, description="广告类型",default="默认广告类型")
    ad_position = fields.CharField(32, description="广告位",default="默认广告位")
    target_url = fields.CharField(255, description="目标链接",default="")
    start_at = fields.IntField(description="开始时间yyyyMMdd格式",default=0)
    end_at = fields.IntField(description="结束时间yyyyMMdd格式",default=0)
    sum_days = fields.IntField(description="投放天数",default=1)
    complete_days = fields.IntField(description="完成天数",default=0)
    pay_fee_usdt = fields.FloatField(description="支付费用,usdt",default=0)
    pay_fee = fields.BigIntField(description="支付费用,除10000后为1元",default=0)
    pay_fee_day = fields.BigIntField(description="每日支付费用,除10000后为1元",default=0)
    complete_pay_fee = fields.BigIntField(description="完成支付费用,除10000后为1元",default=0)
    remarks = fields.TextField(description="备注",default="")
    from_user_ids = fields.JSONField(description="渠道号列表",default=[])
    class Meta:
        table = "channel_delivery_plan"

class ChannelDeliveryPlanRelation(BaseIdModel, TimestampMixin):
    plan_id = fields.BigIntField(index=True, description="计划id")
    from_user_id = fields.BigIntField(description="投放用户ID")
    class Meta:
        table = "channel_delivery_plan_relation"

class TgAdCampaignLink(BaseIdModel, TimestampMixin):
    tg_link = fields.CharField(255, description="tg链接", unique=True)
    tg_chat_name = fields.CharField(128, description="tg群名称", unique=True)
    channel_id = fields.BigIntField(index=True, description="渠道id", unique=True)

    class Meta:
        table = "tg_ad_campaign_link"

class TgAdLink(BaseIdModel, TimestampMixin):
    tg_link = fields.CharField(255, description="tg链接", index=True)
    channel_id = fields.BigIntField(index=True, description="渠道id")
    role_id = fields.BigIntField(index=True, description="角色id")
    target_type = fields.CharField(32, description="目标类型, TMA | CHAT_BOT")
    target_link = fields.CharField(255, description="目标链接", unique=True)

    class Meta:
        table = "tg_ad_link"

class UserRoleShare(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True, description="用户id")
    role_id = fields.BigIntField(index=True, description="角色id")
    title = fields.CharField(32, description="分享的标题",default="")
    description = fields.CharField(512, description="分享内容的描述",default="")
    conversation_id = fields.CharField(128,description="对话id")
    last_message_id = fields.CharField(128, description="最后一条消息的id")
    last_message_version = fields.BigIntField(description="最后一条消息的版本号")
    content_snapshot = fields.JSONField(description="6条消息内容的快照", default=[]) # list, 每个元素是个dict
    status = fields.IntField(default=1)
    moderation_check = fields.JSONField(default={}, description="审核结果")
    check_result = fields.JSONField(default={}, description="审核结果")
    message_count = fields.IntField(default=0, description="消息数量")
    class Meta:
        table = "user_role_share"
        unique_together = (("user_id", "role_id", "conversation_id", "last_message_id", "last_message_version"),)

class UserFavoriteRecord(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True, description="用户id")
    mode_type = fields.CharField(16, default="single", description="single/group")
    mode_target_id = fields.BigIntField(default=0, description="mode_target_id")
    status = fields.BooleanField(default=True, description="是否收藏")
    class Meta:
        table = "user_favorite_record"
        unique_together = (("user_id", "mode_type", "mode_target_id"),)

class ShareDescRecord(BaseIdModel,TimestampMixin):
    user_id = fields.BigIntField(index=True, description="用户id")
    request_id = fields.CharField(128, description="请求id")
    mode_type = fields.CharField(16, default="single", description="single/group")
    mode_target_id = fields.BigIntField(default=0, description="mode_target_id")
    message_ids = fields.JSONField(default=[], description="消息id列表")
    description = fields.CharField(512, description="描述")

    class Meta:
        table = "share_desc_record"
        unique_together = (("user_id", "request_id"),)

# 记录用户的点赞记录
class UserLikeRecord(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True, description="用户id")
    mode_type = fields.CharField(16, default="single", description="single/group")
    mode_target_id = fields.BigIntField(default=0, description="mode_target_id")
    status = fields.BooleanField(default=True, description="是否点赞")
    count = fields.IntField(default=0, description="点赞数")
    class Meta:
        table = "user_like_record"
        unique_together = (("user_id", "mode_type", "mode_target_id"),)

# 记录用户的踩记录
class UserDisLikeRecord(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True, description="用户id")
    mode_type = fields.CharField(16, default="single", description="single/group")
    mode_target_id = fields.BigIntField(default=0, description="mode_target_id")
    status = fields.BooleanField(default=True, description="是否踩了")
    class Meta:
        table = "user_dislike_record"
        unique_together = (("mode_type", "mode_target_id", "user_id"),)


class LikeStats(BaseIdModel, TimestampMixin):
    mode_type = fields.CharField(16, default="single", description="single/group")
    mode_target_id = fields.BigIntField(default=0, description="mode_target_id")
    like_count = fields.IntField(default=0, description="点赞数")
    class Meta:
        table = "like_stats"
        unique_together = (("mode_type", "mode_target_id"),)

class FavoriteStats(BaseIdModel, TimestampMixin):
    mode_type = fields.CharField(16, default="single", description="single/group")
    mode_target_id = fields.BigIntField(default=0, description="mode_target_id")
    favorite_count = fields.IntField(default=0, description="收藏数")
    class Meta:
        table = "favorite_stats"
        unique_together = (("mode_type", "mode_target_id"),)


class ChatJoinRecord(BaseIdModel,TimestampMixin):
    user_id = fields.BigIntField(description="用户id")
    chat_id = fields.BigIntField(description="群组id")
    class Meta:
        table = "chat_join_record"
        unique_together = (("user_id", "chat_id"),)

class DiamondSeasonStatus(int, Enum):
    CREATED = 0
    IN_PROGRESS= 1
    TERMINATED = 2


class ActivityDiamondSeason(BaseIdModel, TimestampMixin):
    season_id = fields.UUIDField(unique=True, default=uuid4)
    title = fields.CharField(128, description="活动赛季标题")
    start_at = fields.DatetimeField(description="活动赛季开始时间")
    end_at = fields.DatetimeField(description="活动赛季结束时间")
    warming_up_notice = fields.CharField(1024, default="", description="活动赛季预热通知")
    warming_up_start_at = fields.DatetimeField(default=datetime(1970, 1, 1, tzinfo=UTC), description="活动赛季预热开始时间")
    warming_up_end_at = fields.DatetimeField(default=datetime(1970, 1, 1, tzinfo=UTC), description="活动赛季预热结束时间")
    warming_up_notice_method =  fields.JSONField(default={}, description="活动赛季预热通知方式")
    # 定时任务用的字段
    warming_up_notice_sent = fields.BooleanField(default=False, description="活动赛季预热通知是否已发送")
    status = fields.IntField(default=0, description="赛季的状态, 0表示未开始,1表示进行中,2表示已中止")
    class Meta:
        table = "activity_diamond_season"

## {"method": "timer", "interval":30}
## {"method": "amount", "interval":20}
class DiamondTaskStatus(int, Enum):
    ACTIVE = 1
    # 不需要进行中状态，跟赛季状态保持一致
    DELETED = 2

class ActivityDiamondTask(BaseIdModel, TimestampMixin):
    task_id = fields.UUIDField(unique=True, default=uuid4)
    season_id = fields.CharField(128, index=True, description="赛季id")
    max_participants = fields.IntField(description="任务最大参与人数")
    allow_repeated_enrolled = fields.BooleanField(default=False, description="已参加同一天其他场任务，该场任务是否可参加")
    role_ids = fields.JSONField(default=[], description="任务里的角色id列表")
    allowed_chat_models = fields.JSONField(default=[], description="允许的聊天模型列表")
    required_diamond_amount = fields.IntField(description="完成任务所需钻石数量")
    return_rate = fields.IntField(description="每场任务钻石返还比例,10000表示100%")
    grand_prize_count = fields.IntField(description="每场任务的中大奖人数")
    grand_prize_return_rate = fields.IntField(description="每场任务的大奖的钻石返还比例，10000表示100%")
    # 已废弃
    diamond_return_at = fields.DatetimeField(default=datetime(1970, 1, 1, tzinfo=UTC), description="任务的奖励返还时间(给用户推送领取返回奖励的消息时间)")
    # 已废弃
    diamond_expire_at = fields.DatetimeField(default=datetime(1970, 1, 1, tzinfo=UTC), description="任务的奖励过期时间(消息里的奖励的可领取的最晚时间)")
    diamond_gotten_manually = fields.BooleanField(default=True, description="是否需要手动领取奖励")
    start_at = fields.DatetimeField(index=True, description="任务的开始时间")
    end_at = fields.DatetimeField(index=True, description="任务的结束时间")
    start_notice = fields.CharField(1024, description="任务开始通知")
    end_notice = fields.CharField(1024, description="任务结束通知")
    left_participants_notice_interval = fields.IntField(default=0, description="剩余参与人数在用户群里每间隔多少条消息通知一次") 
    # 定时任务用的字段
    start_notice_sent = fields.BooleanField(default=False, description="任务开始通知是否已发送")
    # 定时任务用的字段
    end_notice_sent = fields.BooleanField(default=False, description="任务结束通知是否已发送")
    status = fields.IntField(default=1, index=True, description="任务的状态, 1表示状态正常,2表示已删除")
    class Meta:
        table = "activity_diamond_task"

class ParticipationStatus(int, Enum):
    ENROLLED = 0
    COMPLETED= 1
    REWARED_CLAIMED = 2

class ActivityDiamondTaskParticipant(BaseIdModel, TimestampMixin):
    season_id = fields.CharField(128, index=True, description="活动赛季id")
    task_id = fields.CharField(128, index=True, description="赛季的任务id")
    user_id = fields.BigIntField(description="用户id")
    join_at = fields.DatetimeField(description="用户报名的时间")
    diamond_consumption = fields.IntField(default=0, description="已消耗钻石数量")
    finish_at = fields.DatetimeField(default=datetime(1970, 1, 1, tzinfo=UTC), description="用户完成任务的时间")
    # 专门的冗余字段，从ActivityDiamondTask表里冗余过来的
    task_end_at = fields.DatetimeField(index=True, description="任务结束时间")
    # 专门的冗余字段，从ActivityDiamondTask表里冗余过来的
    # 已废弃
    diamond_return_at = fields.DatetimeField(default=datetime(1970, 1, 1, tzinfo=UTC), index=True, description="用户完成任务后，什么时间可领取奖励并发送领取奖励的消息")
    # 专门的冗余字段，从ActivityDiamondTask表里冗余过来的
    # 已废弃
    diamond_expire_at = fields.DatetimeField(default=datetime(1970, 1, 1, tzinfo=UTC), index=True, description="待领取的奖励的过期时间")
    obtained_returned_diamond = fields.IntField(default=0, description="成功领取到的返还金币数量")
    #这个字段是为了统计活动的整体数据时方便写代码，所以把ActivityDiamondLotteryResult表里的数据冗余了一份
    obtained_lottery_diamond = fields.IntField(default=0, description="中奖情况下，成功领取到的大奖金币数量")
    recharge_msg_sent = fields.BooleanField(default=False, description="钻石不足时，通知充值的消息是否已发送")
    # 定时任务用的字段
    failure_msg_sent = fields.BooleanField(default=False, description="任务未达标消息是否已发送")
    # 定时任务用的字段
    reward_msg_sent = fields.BooleanField(default=False, description="领奖消息是否已发送")
    # 定时任务用的字段
    expire_msg_sent = fields.BooleanField(default=False, description="过期消息是否已发送")
    status = fields.IntField(index=True, default=0, description="任务的状态, 0表示已报名，1表示任务达标。 2表示已领取奖励")
    class Meta:
        table = "activity_diamond_task_participant"
        unique_together = (("user_id", "task_id"),)

class LotteryQueueStatus(int, Enum):
    UNCOMPLETED = 0
    COMPLETED= 1

class ActivityDiamondLotteryQueue(BaseIdModel, TimestampMixin):
    task_id = fields.CharField(128, unique=True, description="赛季的任务id")
    lottery_at = fields.DatetimeField(index=True, description="抽奖的启动时间")
    status = fields.IntField(default=0, description="是否已抽奖, 0表示未抽，1表示已抽")
    class Meta:
        table = "activity_diamond_lottery_queue"

class LotteryClaimStatus(int, Enum):
    UNCLAIMED = 0
    CLAIMED= 1

class ActivityDiamondLotteryResult(BaseIdModel, TimestampMixin):
    season_id = fields.CharField(128, index=True, description="活动赛季id")
    task_id = fields.CharField(128, description="赛季的任务id")
    user_id = fields.BigIntField(index=True, description="用户id")
    diamond_amount = fields.IntField(description="中奖的钻石数量")
    status = fields.IntField(default=0, description="0表示未领取，1表示已领取")
    class Meta:
        table = "activity_diamond_lottery_result"
        unique_together = (("task_id", "user_id"),)


class WelfareTask(BaseIdModel, TimestampMixin):
    task_id = fields.CharField(64,unique=True,not_null = True,default="", description="任务id")
    task_type = fields.CharField(32, description="任务类型:INVITATION|CHECK_IN")
    target_id = fields.CharField(64, description="任务目标id")
    title = fields.CharField(128, description="任务名称")
    desc = fields.CharField(128, description="任务描述")
    sub_tasks = fields.JSONField(default={}, description="子任务")
    push_tips = fields.CharField(512, description="推送提示",default="")
    btn = fields.CharField(32, description="按钮文案")
    btn_done_desc = fields.CharField(32, description="按钮完成文案")
    action_type = fields.CharField(32,description="任务动作类型:link")
    link_url = fields.CharField(255, description="链接地址")
    done_action_type = fields.CharField(32,description="完成动作类型:link")
    done_link_url = fields.CharField(255, description="完成链接地址")
    diamond = fields.BigIntField(default=0,description="钻石奖励")
    order = fields.IntField(description="排序")
    online = fields.BooleanField(default=False)
    support_register_sources = fields.JSONField(default=[], description="注册来源")
    #任务重置周期
    reset_period = fields.CharField(32, not_null=True,default="", description="任务重置周期:DAILY|ONCE|AFTER_24H")
    class Meta:
        table = "welfare_task"

class UserTaskRecord(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(not_null = True,description="用户id")
    task_id = fields.CharField(64, description="任务id")
    unique_id = fields.CharField(64, description="唯一去重id")
    status = fields.CharField(32, description="任务状态:DONE|TODO|RECEIVE")
    finished_at = fields.DatetimeField(description="完成时间")
    class Meta:
        table = "user_task_record"
        unique_together = (("user_id", "task_id", "unique_id"),)

class WelfareTaskReward(BaseIdModel, TimestampMixin):
    task_id = fields.CharField(64, index=True, description="任务id")
    extra_bonus_sub_task_id = fields.CharField(64, default="", description="额外奖励的子任务id")
    title = fields.CharField(128, default="", description="奖励名称")
    desc = fields.CharField(128, default="", description="奖励描述")
    reward_type = fields.CharField(32,default="COIN",description="奖励类型:COIN|CHAT_BENEFIT")
    target_id = fields.CharField(64,default="",description="目标id")
    target_channel = fields.CharField(32, default="",description="目标渠道类型(需要的时候传递)")
    reward_count = fields.BigIntField(default=0, description="奖励数量")
    online = fields.BooleanField(default=False, description="是否上线")
    class Meta:
        table = "welfare_task_reward"
        
class UserRewardRecord(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True, description="用户id")
    task_id = fields.CharField(64, description="任务id")
    task_type = fields.CharField(32, description="任务类型")
    task_target_id = fields.CharField(64, description="任务目标id")
    reward_id = fields.CharField(64, description="任务奖励id")
    reward_type = fields.CharField(32, description="任务奖励类型")
    reward_target_id = fields.CharField(64, description="任务奖励目标id")
    reward_target_channel = fields.CharField(32, description="任务奖励目标渠道类型(需要的时候传递)")
    reward_count = fields.BigIntField(default=0, description="奖励数量")

    class Meta:
        table = "task_user_reward_record"                                 

class RechargeProductBenefit(BaseIdModel, TimestampMixin):
    recharge_product_id = fields.CharField(64,index=True, description="充值产品id")
    chat_benefit_id = fields.IntField(index=True, description="福利id")
    online = fields.BooleanField(default=False)
    class Meta:
        table = "recharge_product_benefit"


class ChatBenefit(BaseIdModel, TimestampMixin):
    type = fields.CharField(32, default="",description="类型")
    title = fields.CharField(256, default="",description="标题")
    limit_times = fields.IntField(default=0, description="限制次数")
    # alter table chat_benefit drop column limit_model
    # limit_model = fields.CharField(32,default="", description="限制模型")
    limit_chat_product_mid = fields.CharField(32,default="", description="限制产品mid")
    reward_times = fields.IntField(default=0, description="次数")
    reward_valid_days = fields.IntField(default=0, description="有效天数")
    usage_scope_type = fields.CharField(64, default="", description="使用范围类型")
    # alter table chat_benefit add column usage_scope_type varchar(64) default "" not null comment '使用范围标识'
    usage_scope_id = fields.CharField(64, default="", description="使用范围标识")
    # alter table chat_benefit add column usage_scope_id varchar(32) default "" not null comment '使用范围渠道'
    class Meta:
        table = "chat_benefit"

# 未来删除，可以使用common_constant中的枚举
class ChatBenefitEnum(str, Enum):
    RECHARGE = "RECHARGE"
    INVITATION = "INVITATION"
    WELFARE = "WELFARE"

class UserChatBenefit(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True, description="用户id")
    chat_benefit_id = fields.IntField(description="福利配置id")
    reward_times = fields.IntField(default=0, description="已领取次数")
    used_times = fields.IntField(default=0, description="已使用次数")
    sum_used_times = fields.IntField(default=0, description="总使用次数")
    valid_start_at = fields.DatetimeField(description="有效开始时间")
    valid_end_at = fields.DatetimeField(description="有效结束时间")
    received_at = fields.DatetimeField(description="领取时间")
    # benefit_type = fields.CharEnumField(ChatBenefitEnum, default=ChatBenefitEnum.RECHARGE)
    benefit_type = fields.CharField(32, default="WELFARE", description="福利类型:等同于BenefitChannel")
    recharge_order_id = fields.CharField(128, default="", index=True, description="充值订单id")
    class Meta:
        table = "user_chat_benefit"

class UcbRefreshRecord(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True, description="用户id")
    user_chat_benefit_id = fields.IntField(description="用户福利id")
    chat_benefit_id = fields.IntField(description="福利配置id")
    reward_times = fields.IntField(default=0, description="领取次数")
    last_received_at = fields.DatetimeField(description="上次领取时间")
    last_used_times = fields.IntField(default=0, description="上次使用次数")
    now_received_at = fields.DatetimeField(description="本次领取时间")

    class Meta:
        table = "ucb_refresh_record"


class ConsumeChatBenefitRecord(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True, description="用户id")
    chat_benefit_id = fields.IntField(description="福利配置id")
    user_chat_benefit_id = fields.IntField(description="用户福利id")
    consume_times = fields.IntField(default=1, description="消耗次数")
    model = fields.CharField(32, description="模型")
    product_id = fields.CharField(128, description="产品id（付费产品）")
    role_id = fields.BigIntField(default=0, description="角色id")
    class Meta:
        table = "consume_chat_benefit_record"


# class UserBackgroundImage(BaseIdModel, TimestampMixin):
#     user_id = fields.BigIntField(index=True, description="用户id")
#     image_id = fields.CharField(64, description="图片id")
#     image_url = fields.CharField(255, description="图片地址")
#     order = fields.IntField(default=0, description="排序")
#     category = fields.CharField(32,default = "CHAT_BACKGROUND",description="分类")
#     class Meta:
#         table = "user_background_image"

class StarPaymentOrder(BaseIdModel, TimestampMixin):
    invoice_id = fields.UUIDField(default=uuid4, unique=True, description="订单id")
    telegram_payment_charge_id = fields.CharField(255, default='', index=True)
    provider_payment_charge_id = fields.CharField(255, default='', index=True)
    user_id = fields.BigIntField(index=True, description="用户id")
    bot_id = fields.BigIntField(index=True, description="bot id")
    chat_id = fields.BigIntField(index=True, description="群组id")
    star_amount = fields.BigIntField(description="star 数量")
    final_amount = fields.BigIntField(description="最终金额", default=0)
    recharge_order_id = fields.UUIDField(index=True, description="充值订单id")
    status = fields.CharEnumField(RechargeStatusEnum, description="状态", default='INIT')
    raw_data = fields.TextField(null=True)

    class Meta:
        table = "star_payment_order"

class LlmModelConfig(BaseIdModel, TimestampMixin):
    llm_model = fields.CharField(64, description="模型标识",unique=True,index=True)
    model_int = fields.IntField(default=0, description="模型标识(int类型)")
    enabled = fields.BooleanField(default=True, description="是否启用")
    description = fields.CharField(255, default="", description="模型描述")
    request_llm_model = fields.CharField(64,default = "",description="请求模型名称")
    support_params = fields.JSONField(default=[], description="支持的额外参数")
    use_cache = fields.BooleanField(default=False, description="是否使用缓存")
    check_llm_request = fields.BooleanField(default=False, description="是否需要检查请求状态")
    request_cluster = fields.CharField(64, default="PRO", description="请求集群")
    cache_type = fields.CharField(32, default="", description="缓存类型")
    third_api_key = fields.CharField(128, default="", description="API Key")
    third_model_id = fields.CharField(128, default="", description="第三方模型ID")
    class Meta:
        table = "llm_model_config"


class LlmModelStatus(BaseIdModel, TimestampMixin):
    llm_model = fields.CharField(64, description="模型名称")
    status = fields.IntField(default=0, description="模型状态")
    success_count = fields.IntField(default=0, description="成功次数")
    fail_count = fields.IntField(default=0, description="失败次数")
    total_count = fields.IntField(default=0, description="总次数")
    # stat_at_index = fields.IntField(32, description="统计时间标识")
    stat_at = fields.DatetimeField(description="统计时间")
    class Meta:
        table = "llm_model_status"
        unique_together = (("llm_model", "stat_at"),)

class ModelWaterConfig(BaseIdModel, TimestampMixin):
    llm_model = fields.CharField(64, description="模型名称",index=True)
    use_filter = fields.CharField(64, description="使用场景",default="")
    to_llm_model = fields.CharField(64, description="转发模型名称")
    enabled = fields.BooleanField(default=True, description="是否启用")
    num = fields.IntField(default=0, description="转发次数")
    original_num = fields.IntField(default=0, description="原始转发次数")
    request_status = fields.IntField(default=200,description="模型请求状态")
    out_skip_count = fields.IntField(default=0, description="跳过次数")
    out_sleep_min = fields.FloatField(default=0.0, description="跳过间隔")
    out_sleep_max = fields.FloatField(default=0.0, description="跳过间隔")
    sort_order = fields.IntField(default=1, description="排序")
    backup_to_llm_models = fields.JSONField(default=[], description="备用转发模型列表")
    error_time = fields.IntField(default=0, description="报错时间")

    class Meta:
        table = "model_water_config"

class RechargeChannelConfig(BaseIdModel, TimestampMixin):
    recharge_product_id = fields.UUIDField(unique=True, description="充值产品id")
    wechat_channel = fields.CharField(128, description="微信渠道", default='DEFAULT')
    alipay_channel = fields.CharField(128, description="支付宝渠道", default='DEFAULT')
    voucher_channel = fields.CharField(128, description="卡密渠道", default='DEFAULT')
    star_channel = fields.CharField(128, description="star 渠道", default='DEFAULT')
    usdt_channel = fields.CharField(128, description="USDT 渠道", default='DEFAULT')

    def supported_pay_types(self):
        channels = []
        if self.wechat_channel != "CLOSED":
            channels.append("wechat")
        if self.alipay_channel != "CLOSED":
            channels.append("alipay")
        if self.voucher_channel != "CLOSED":
            channels.append("voucher")
        if self.star_channel != "CLOSED":
            channels.append("star")
        if self.usdt_channel != "CLOSED":
            channels.append("usdt")
        channels.append('stripe')
        return channels

    @staticmethod
    def all_pay_types() -> list[str]:
        return ["wechat", "alipay", "voucher", "star", "usdt"]

    @staticmethod
    def default_config() -> "RechargeChannelConfig":
        return RechargeChannelConfig(
            recharge_product_id=uuid4(),
            wechat_channel="DEFAULT",
            alipay_channel="DEFAULT",
            voucher_channel="DEFAULT",
            star_channel="DEFAULT",
            usdt_channel="DEFAULT",
        )

    class Meta:
        table = "recharge_channel_config"


class RechargeChannelStats(BaseIdModel, TimestampMixin):
    """充值渠道成功率统计表"""
    channel = fields.CharEnumField(RechargeChannelEnum, max_length=128, description="充值渠道")
    pay_type = fields.CharField(32, description="支付类型: alipay, wechat, star, usdt, voucher, stripe, ALL", index=True)
    total_orders = fields.IntField(default=0, description="总订单数")
    success_orders = fields.IntField(default=0, description="成功订单数")
    success_rate = fields.FloatField(default=0.0, description="成功率")
    last_updated = fields.DatetimeField(auto_now=True, description="最后更新时间")
    
    # Enhanced monitoring fields
    time_window_success_rate = fields.FloatField(default=0.0, description="时间窗口成功率(3小时)")
    window_total_orders = fields.IntField(default=0, description="时间窗口内总订单数")
    window_success_orders = fields.IntField(default=0, description="时间窗口内成功订单数")
    current_weight = fields.IntField(default=100, description="当前权重")
    auto_adjusted_weight = fields.IntField(default=100, description="自动调整权重")
    last_calculation_at = fields.DatetimeField(null=True, description="最后计算时间")
    newbie_protection_until = fields.DatetimeField(null=True, description="新手保护截止时间")

    class Meta:
        table = "recharge_channel_stats"
        unique_together = ("channel", "pay_type")

class RechargeChannelControl(BaseIdModel, TimestampMixin):
    """充值渠道手工控制配置表"""
    channel = fields.CharEnumField(RechargeChannelEnum, max_length=128, description="充值渠道")
    pay_type = fields.CharField(32, description="支付类型: alipay, wechat, star, usdt, voucher, stripe, ALL", index=True)
    min_amount = fields.BigIntField(default=0, description="最小金额")
    max_amount = fields.BigIntField(default=0, description="最大金额")
    enabled = fields.BooleanField(default=True, description="是否启用")
    ratio = fields.IntField(default=100, description="比例")
    remark = fields.CharField(255, default="", description="备注")

    class Meta:
        table = "recharge_channel_control"
        unique_together = ("channel", "pay_type")


class RechargeChannelWhitelist(BaseIdModel, TimestampMixin):
    """充值渠道白名单 - 用于渠道方测试"""
    tg_id = fields.BigIntField(index=True, description="Telegram用户ID")
    channel = fields.CharEnumField(RechargeChannelEnum, max_length=128, description="允许测试的渠道")
    enabled = fields.BooleanField(default=True, description="是否启用")
    remark = fields.CharField(255, default="", description="备注")

    class Meta:
        table = "recharge_channel_whitelist"
        unique_together = ("tg_id", "channel")


class RechargeChannelAlert(BaseIdModel, TimestampMixin):
    """充值渠道监控告警记录"""
    channel = fields.CharEnumField(RechargeChannelEnum, max_length=128, description="充值渠道")
    pay_type = fields.CharField(32, description="支付类型", index=True)
    alert_type = fields.CharField(64, description="告警类型: LOW_SUCCESS_RATE, LOW_VOLUME, ALL_CHANNELS_DOWN, CRITICAL")
    alert_level = fields.CharField(32, description="告警级别: WARNING, CRITICAL, EMERGENCY", default="WARNING")
    success_rate = fields.FloatField(null=True, description="触发告警时的成功率")
    order_count = fields.IntField(null=True, description="订单数量")
    message = fields.TextField(description="告警消息")
    resolved = fields.BooleanField(default=False, description="是否已解决")
    resolved_at = fields.DatetimeField(null=True, description="解决时间")
    phone_notified = fields.BooleanField(default=False, description="是否已电话通知")
    
    class Meta:
        table = "recharge_channel_alert"
        indexes = [
            ("channel", "pay_type", "alert_type"),
            ("created_at",),
            ("resolved",)
        ]


class RechargeChannelTimeWindow(BaseIdModel, TimestampMixin):
    """充值渠道时间窗口统计"""
    channel = fields.CharEnumField(RechargeChannelEnum, max_length=128, description="充值渠道")
    pay_type = fields.CharField(32, description="支付类型", index=True)
    window_start = fields.DatetimeField(description="窗口开始时间", index=True)
    window_end = fields.DatetimeField(description="窗口结束时间")
    window_minutes = fields.IntField(description="窗口时长(分钟)", default=30)
    total_orders = fields.IntField(default=0, description="窗口内总订单数")
    success_orders = fields.IntField(default=0, description="窗口内成功订单数")
    success_rate = fields.FloatField(default=0.0, description="窗口成功率")
    excluded_orders = fields.IntField(default=0, description="被排除的订单数")
    
    class Meta:
        table = "recharge_channel_time_window"
        unique_together = ("channel", "pay_type", "window_start")
        indexes = [
            ("window_start", "window_end"),
            ("created_at",)
        ]

class CursorAccount(BaseIdModel, TimestampMixin):
    email = fields.CharField(128, unique=True, description="邮箱")
    original_email = fields.CharField(128, description="原始邮箱")
    original_password = fields.CharField(128, default = "",description="原始密码")
    token = fields.TextField(default = "",description="token")
    imap_server = fields.CharField(128, description="IMAP服务器")
    imap_port = fields.IntField(description="IMAP端口")
    token_init = fields.BooleanField(default=False, description="token是否初始化")
    used = fields.BooleanField(default=False, description="是否使用")

    class Meta:
        table = "cursor_account"


class UserSummaryStats(BaseIdModel):
    user_id = fields.BigIntField(unique=True,default=0, description="用户id")
    nickname = fields.CharField(128, default="", description="昵称")
    email = fields.CharField(128, default="", description="邮箱")
    llm_model = fields.CharField(32, default="", description="模型")
    register_source = fields.CharField(32, default="", description="注册来源")
    register_at = fields.IntField(description="注册时间",default=0)
    register_at_day = fields.IntField(description="注册日期",default=0)
    tg_id = fields.BigIntField(default=0, description="tg用户id")
    tg_first_name = fields.CharField(32, default="", description="tg用户名字")
    tg_last_name = fields.CharField(32, default="", description="tg用户姓")
    tg_user_name = fields.CharField(32, default="", description="tg用户名")

    # 用户聊天信息
    role_count = fields.IntField(default=0, description="角色数量")
    cov_count = fields.IntField(default=0, description="对话数量")
    turn_count = fields.IntField(default=0, description="轮次")
    ai_count = fields.IntField(default=0, description="AI次数")
    ai_haiku_count = fields.IntField(default=0, description="AI haiku次数")
    ai_sonnet3_count = fields.IntField(default=0, description="AI sonnet3次数")
    ai_sonnet35_count = fields.IntField(default=0, description="AI sonnet35次数")
    ai_opus_count = fields.IntField(default=0, description="AI opus次数")
    activate_days = fields.IntField(default=0, description="激活天数")
    chat_days = fields.IntField(default=0, description="聊天天数")
    total_balance = fields.IntField(default=0, description="余额")
    pay_count = fields.IntField(default=0, description="付费次数")
    pay_amount_sum = fields.IntField(default=0, description="付费金额")
    first_pay_at = fields.IntField(default=0, description="首次付费时间")

    # 邀请相关信息
    channel_id = fields.BigIntField(default=0, description="渠道id")
    invite_link = fields.CharField(128, default="", description="邀请链接")
    invite_count = fields.IntField(default=0, description="邀请人数")
    from_user_id = fields.BigIntField(default=0, description="邀请人id")
    joined_chat_type = fields.CharField(32, default="", description="加入的聊天类型")
    joined_chat_id = fields.CharField(128, default="", description="加入的聊天id")
    start_role = fields.IntField(default=0, description="开始角色")
    joined_group_count = fields.IntField(default=0, description="加入群组次数")
    joined_channel_count = fields.IntField(default=0, description="加入频道次数")
    updated_at = fields.DatetimeField(auto_now=True)
    class Meta:
        table = "user_summary_stats"

class UserModelEventHistory(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(default=0, description="用户id")
    mode_type = fields.CharField(16, default="single", description="single/group")
    mode_target_id = fields.BigIntField(default=0, description="mode_target_id")
    conversation_id = fields.CharField(128, default="", description="对话id")
    message_id = fields.CharField(128, default="", description="消息id")
    event_type = fields.CharField(32, description="事件类型")
    event_ts = fields.BigIntField(default=0, description="事件时间戳(秒)")
    from_model = fields.CharField(32, default="", description="事件触发前模型mid")
    from_model_name = fields.CharField(32, default="", description="事件触发前模型名称")
    to_model = fields.CharField(32, default="", description="事件触发后模型mid")
    to_model_name = fields.CharField(32, default="", description="事件触发后模型名称")
    from_chat_channel = fields.CharField(32, default="", description="事件触发前聊天渠道")
    to_chat_channel = fields.CharField(32, default="", description="事件触发后聊天渠道")
    class Meta:
        table = "user_model_event_history"
        indexes = [("user_id", "mode_type", "mode_target_id", "conversation_id", "message_id")]

class VoiceHistory(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(default=0, description="用户id", index=True)
    role_id = fields.BigIntField(default=0, description="角色id", index=True)
    message_id = fields.CharField(128, default="", description="消息id", index=True)
    speaker_id = fields.CharField(128, default="", description="语音id", index=True)
    conversation_id = fields.CharField(128, default="", description="对话id", index=True)
    message_version = fields.BigIntField(default=0, description="消息版本号")
    message_content = fields.TextField(description="原始消息内容")
    voice_message_content = fields.TextField(description="语音消息内容")
    voice_url = fields.CharField(512, default="", description="语音url")

    class Meta:
        table = "voice_history"
        

class UserRolePhotoRecord(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(default=0, description="用户id")
    role_id = fields.IntField(default=0, index=True, description="角色id")
    conversation_id = fields.CharField(128, default="", description="对话id")
    message_id = fields.CharField(128, default="", description="消息id")
    version = fields.IntField(default=0, description="message版本号")
    photo_id = fields.CharField(128, default="", index=True, description="图片id")
    photo_url = fields.CharField(512, default="", description="图片url")
    # use one field to indicate like, dislike or no action: 0: no action, 1: like, 2: dislike
    like_status = fields.IntField(default=0, description="喜欢状态: 0: no action, 1: like, 2: dislike")
    class Meta:
        table = "user_role_photo_record"
        unique_together = [("user_id", "role_id", "conversation_id", "message_id", "version", "photo_id")]

class UserRoleConfig(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(default=0, description="用户id", index=True)
    role_id = fields.BigIntField(default=0, index=True, description="角色id")
    speaker_id = fields.CharField(128, index=True, default="", description="语音id")

    class Meta:
        table = "user_role_config"
        unique_together = [("user_id", "role_id")]
class SbtConfig(BaseIdModel,TimestampMixin):
    title = fields.CharField(128, description="模板名称")
    content = fields.TextField(default="",description="模板内容")
    content_placeholder = fields.TextField(default="", description="模板内容描述")
    rule = fields.TextField(default="", description="模板规则")
    enabled = fields.BooleanField(default=True, description="是否启用")
    sort_order = fields.IntField(default=1, description="排序")
    class Meta:
        table = "sbt_config"

class UserSbt(BaseIdModel,TimestampMixin):
    user_id = fields.BigIntField(description="用户id")
    conversation_id = fields.CharField(128,description="对话id")
    sbt_id = fields.BigIntField(default=0,description="状态块模板id")
    message_id = fields.CharField(128, description="消息id")
    message_version = fields.BigIntField(default=0, description="消息版本号")
    content = fields.TextField(description="状态块内容")
    rule = fields.TextField(description="状态块规则")
    last_saved_time = fields.BigIntField(description="最后一次保存时间")
    class Meta:
        table = "user_sbt"
            

class UserSbtHistory(BaseIdModel,TimestampMixin):
    user_id = fields.BigIntField(index=True,description="用户id")
    conversation_id = fields.CharField(128, description="对话id")
    sbt_id = fields.BigIntField(description="状态块模板id")
    message_id = fields.CharField(128, description="消息id")
    content = fields.TextField(description="状态块内容")
    rule = fields.TextField(description="状态块规则")

    class Meta:
        table = "user_sbt_history"

class UserSbtGenerateHistory(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True,description="用户id")
    conversation_id = fields.CharField(128, description="对话id")
    sbt_id = fields.BigIntField(description="状态块模板id")
    message_id = fields.CharField(128, description="消息id")
    content = fields.TextField(description="状态块内容")

    class Meta:
        table = "user_sbt_generate_history"

class VolEngineContext(BaseIdModel, TimestampMixin):
    cache_id = fields.CharField(128,description="缓存ID")
    third_model_id = fields.CharField(128, description="第三方模型ID")
    context_id = fields.CharField(128, description="上下文ID")
    request_model = fields.CharField(128, description="请求模型")
    expired_at = fields.IntField(default=0, description="过期时间戳(秒)")
    role_id = fields.BigIntField(default=0, description="角色ID")
    user_id = fields.BigIntField(default=0, description="用户ID")
    conversation_id = fields.CharField(128, description="对话ID")
    message_id = fields.CharField(128, description="消息ID")
    version = fields.BigIntField(default=0, description="消息版本号")

    class Meta:
        table = "vol_engine_context"
        unique_together = (("cache_id", "third_model_id"),)


class RechargeActivityReward(BaseIdModel, TimestampMixin):
    activity_name = fields.CharField(128, description="活动名称")
    activity_type = fields.CharField(64, description="活动类型", default='')
    recharge_product_id = fields.UUIDField(index=True, description="关联的充值产品ID")
    reward_amount = fields.BigIntField(description="活动奖励金额")
    start_at = fields.DatetimeField(description="活动开始时间")
    end_at = fields.DatetimeField(description="活动结束时间")
    enabled = fields.BooleanField(default=True, description="是否启用")
    priority = fields.IntField(default=0, description="优先级")
    conditions = fields.JSONField(default={}, description="活动条件，如用户类型、充值次数等")
    description = fields.CharField(255, default="", description="活动描述")

    class Meta:
        table = "recharge_activity_reward"
        unique_together = (("activity_name", "recharge_product_id"),)

class UserRoleSnapshot(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True, description="用户id")
    role_id = fields.BigIntField(description="角色id")
    generate_order = fields.IntField(default=0, description="快照顺序ID")
    first_message = fields.TextField(default="", description="首条消息内容")
    scenario = fields.TextField(default="", description="角色感知")
    status = fields.BooleanField(default=True, description="角色状态")
    from_conv_id = fields.CharField(128, default="", description="来源对话id")

    class Meta:
        table = "user_role_snapshot"

class UrsConversationRelation(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True, description="用户id")
    role_id = fields.BigIntField(description="角色id")
    conversation_id = fields.CharField(128, description="对话id")
    user_snapshot_id = fields.BigIntField(description="用户快照id")

    class Meta:
        table = "urs_conversation_relation"
        unique_together = (("user_id", "role_id", "conversation_id"),)

class UserQueueRecord(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True, description="用户id")
    person_ahead = fields.IntField(default=0, description="排队人数")
    wait_time_per_person = fields.IntField(default=0, description="每人排队时长,单位秒")
    start_ts = fields.BigIntField(default=0, description="排队开始的时间戳，单位秒")
    end_ts = fields.BigIntField(default=0, description="排队结束的时间戳，单位秒")
    trigger_next_queue = fields.IntField(default=0, description="是否触发下一次排队, 0表示不触发，1表示可以触发")

    class Meta:
        table = "user_queue_record"


class UserGenerateImageRecord(BaseIdModel, TimestampMixin):
    user_id = fields.BigIntField(index=True, description="用户id")
    prompt = fields.TextField(description="生成图片的提示词")
    image_style = fields.CharField(64, default="", description="图片风格")
    image_quality = fields.CharField(64, default="", description="图片质量")
    image_aspect_ratio = fields.CharField(32, default="", description="图片宽高比")
    image_privacy = fields.CharField(32, default="public", description="图片隐私设置")
    status = fields.CharField(32, default="", description="生成状态")
    deleted = fields.BooleanField(default=False, description="是否已删除")
    commit_id = fields.CharField(128, default="", description="生成任务的commit id")
    retry_count = fields.IntField(default=0, description="重试次数(如果有的话)")
    generate_start_time = fields.BigIntField(default=0, description="开始时间戳(秒)")
    generate_end_time = fields.BigIntField(default=0, description="生成结束时间戳(秒)")
    image_url = fields.CharField(512, default="", description="生成的图片URL")
    image_width = fields.IntField(default=0, description="生成的图片宽度(如果有的话)")
    image_height = fields.IntField(default=0, description="生成的图片高度(如果有的话)")
    error_message = fields.CharField(256, default="", description="错误信息")
    like_status = fields.IntField(default=0, description="喜欢状态0-def,1-like,2-dislike")

    class Meta:
        table = "user_generate_image_record"

class LlmModelCacheHistory(BaseIdModel, TimestampMixin):
    cache_id = fields.CharField(128, description="缓存ID")
    preset_model = fields.CharField(64, description="预设模型")
    role_id = fields.BigIntField(default=0, description="角色ID")
    sum_token = fields.IntField(default=0, description="总token数")
    cache_token = fields.TextField(description="缓存token内容")
    conversation_id = fields.CharField(128, description="对话ID")
    message_id = fields.CharField(128, description="消息ID")
    version = fields.BigIntField(default=0, description="消息版本号")
    user_id = fields.BigIntField(default=0, description="用户ID")

    class Meta:
        table = "llm_model_cache_history"

class ThirdCard(BaseIdModel, TimestampMixin):
    third_id = fields.CharField(128,description="第三方ID")
    third_platform = fields.CharField(64, description="第三方平台名称")
    url = fields.CharField(512, description="第三方卡片URL")
    image_url = fields.CharField(512, description="第三方卡片图片URL")
    title = fields.TextField(default="", description="卡片标题")
    tags = fields.TextField(default="", description="卡片标签,逗号隔开")
    description = fields.TextField(default="", description="卡片描述")
    personality = fields.TextField(default="", description="卡片个性化描述")
    first_message = fields.TextField(default="", description="首条消息内容")
    examples = fields.TextField(default="", description="示例对话")
    scenario = fields.TextField(default="", description="场景描述")
    priority = fields.IntField(default=0, description="卡片优先级")
    role_id = fields.BigIntField(default=0, description="关联的角色ID")
    imported = fields.BooleanField(default=False, description="是否已导入")
    
    class Meta:
        table = "third_card"
        unique_together = (("third_id", "third_platform"),)


class RolePushTask(BaseIdModel, TimestampMixin):
    # role_id是唯一索引
    role_id = fields.BigIntField(unique=True, default=0, description="角色ID")
    success = fields.BooleanField(default=False, description="任务是否成功")
    finished_at = fields.DatetimeField(nullable=True, description="完成时间")
    # alter table role_push_task add column on_pushing boolean default false not null comment '是否正在推送中'
    on_pushing = fields.BooleanField(default=False, description="是否正在推送中")
    class Meta:
        table = "role_push_task"

class RoleVideo(BaseIdModel, TimestampMixin):
    role_id = fields.BigIntField(index=True, description="角色ID")
    video_url = fields.CharField(512, description="视频URL")
    deleted = fields.BooleanField(default=False, description="是否已删除")
    sort_order = fields.IntField(default=0, description="排序")
    # alter table role_video add column pushed boolean default false not null comment '是否已推送'
    pushed = fields.BooleanField(default=False, description="是否已推送")
    class Meta:
        table = "role_video"