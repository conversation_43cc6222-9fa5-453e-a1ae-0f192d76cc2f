from datetime import datetime
import json
import os
from motor.motor_asyncio import (
    AsyncIOMotorClient,
)
import logging

from common.models.chat_model import ChatHistory, ChatHistoryStatus, ChatTipsHistory
from common.preset_model import Preset
from persistence import mongo_client
from persistence.models.models import UserRegisterSource
from persistence.presets import NSFWLevel
from utils import json_util


logger = logging.getLogger(__name__)

collection = mongo_client.TavernCollection.PRESETS


async def get_preset_v2(model: int, nsfw: int, scenario: int) -> dict:
    # 本地开发环境下，使用本地预设
    preset_switch = os.environ.get("LOCAL_PRESET_SWITCH", "false")
    preset_file_path = os.environ.get("LOCAL_PRESET_PATH", "")
    if preset_switch.lower() == "true" and len(preset_file_path) > 0:
        with open(preset_file_path, "r") as f:
            ps = json.load(f)
            return ps

    result = (
        await collection.find(
            {"model": model, "nsfw": nsfw, "scenario": scenario, "status": 1}
        )
        .sort("timestamp", -1)
        .limit(1)
        .to_list(1)
    )
    return result[0]["preset"]


async def get_preset_v3(
    model: int, scenario: int, role_nsfw: bool, register_source: str
) -> dict:
    nsfw = NSFWLevel.NSFW.value
    if not role_nsfw and register_source == UserRegisterSource.USA_WEB.value:
        nsfw = NSFWLevel.SFW.value
    return await get_preset_v2(model, nsfw, scenario)

async def get_preset_v4(
    model: int, scenario: int, role_nsfw: bool, register_source: str
) -> Preset:
    nsfw = NSFWLevel.NSFW.value
    if not role_nsfw and register_source == UserRegisterSource.USA_WEB.value:
        nsfw = NSFWLevel.SFW.value
    preset = await get_preset_v2(model, nsfw, scenario)
    return Preset.init_by_dict(preset)
