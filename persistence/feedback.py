from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorCollection, AsyncIOMotorDatabase
from pymongo.errors import CollectionInvalid
import logging

logger = logging.getLogger(__name__)

class FeedbackPersistence():
    def __init__(self, mongo_url: str, db_name: str = 'tg_bot', collection_name: str = 'feedback'):
        self.client = AsyncIOMotorClient(mongo_url)
        self.db = self.client[db_name]
        self.collection_name = collection_name

    async def post_init(self) -> None:
        cols = await self.db.list_collection_names()
        if self.collection_name not in cols:
            try:
                self.col = await self.db.create_collection(self.collection_name)
            except CollectionInvalid as e:
                if e.args[0] != f"collection {self.collection_name} already exists":
                    raise e
                logger.info(f"Collection {self.collection_name!r} already exists")
                self.col = self.db.get_collection(self.collection_name)
        else:
            self.col = self.db.get_collection(self.collection_name)
        self.summary_history_cole = self.db.get_collection('summary_history')

    async def save_feedback(self, user_id: int, prompt: str, choices: list[str], role: str) -> str:
        data = {
            "user_id": user_id,
            "prompt": prompt,
            "choices": choices,
            "role": role
        }
        doc = await self.col.insert_one(data)
        return str(doc.inserted_id)
    
    async def update_feedback_result(self, feedback_id: str, selected: int, alt: str | None = None) -> None:
        if feedback_id and len(feedback_id) > 0:
            update = {"$set": {"selected": selected}}
            if alt:
                update["$set"]["alt"] = alt
            await self.col.update_one({"_id": ObjectId(feedback_id)}, update)

    async def get_feedback_result(self, feedback_id: str) -> list[dict]:
        cursor = self.col.find({'_id': {"$gt": ObjectId(feedback_id)}})
        history = []
        for dc in await cursor.to_list(10000):
            history.append(dc)
        return history
    
    async def save_summary_history(self, user_id: int, summary: str, history: list[str]) -> str:
        data = {
            "user_id": user_id,
            "history": history,
            "summary": summary
        }
        doc = await self.summary_history_cole.insert_one(data)
        return str(doc.inserted_id)