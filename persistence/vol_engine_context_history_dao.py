from persistence import mongo_client
from persistence.models.mongo_models import VolEngineContextHistory


collection = mongo_client.TavernCollection.VOL_ENGINE_CONTEXT_HISTORY


async def insert(input: VolEngineContextHistory):
    await collection.insert_one(input.model_dump())


# async def get_by_context_id(context_id: str) -> VolEngineContextHistory | None:
#     history = await collection.find_one({"context_id": context_id})
#     if not history:
#         return None
#     return VolEngineContextHistory(**history)
