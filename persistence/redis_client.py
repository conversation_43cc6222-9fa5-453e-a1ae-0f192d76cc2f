import asyncio
from datetime import datetime, timedelta
from functools import wraps
import logging
import os

from dotenv import load_dotenv
from redis import Redis

load_dotenv()

log = logging.getLogger(__name__)
redis_client = Redis.from_url(os.environ.get("REDIS_URL", ""))

CHAT_LOCK_SWITCH = bool(os.environ.get("CHAT_LOCK_SWITCH", "True"))


def active_user_sadd(user_id: int):
    redis_client.sadd("active_user", user_id)
    redis_client.expire("active_user", 60 * 60 * 24)  # type: ignore


def pop_active_user(limit: int = 500):
    active_user = redis_client.spop("active_user", limit)
    if not active_user:
        return []
    result = []
    for user in active_user:
        result.append(int(user.decode("utf-8")))  # type: ignore
    return result


def upsert(key: str, value: str, expire: int = 60):
    redis_client.set(key, value, ex=expire)


def get(key: str):
    val = redis_client.get(key)
    if not val:
        return None
    return val.decode("utf-8")  # type: ignore


def delete(key: str):
    redis_client.delete(key)


def map_get_all(key: str):
    cache_val = redis_client.hgetall(key)
    if not cache_val:
        return {}
    result = {}
    for key, value in cache_val.items():  # type: ignore
        key_str = key.decode("utf-8")  # type: ignore
        result[key_str] = value.decode("utf-8")  # type: ignore
    return result


def exist_lock(lock_name: str, id: str) -> bool:
    identifier = f"locks:{lock_name}:{id}"
    result = redis_client.exists(identifier)
    return result == 1


# 设置一个分布式锁
def acquire_lock(
    lock_name: str, id: str, lock_timeout: int = 10, print_log: bool = False
) -> bool:
    identifier = f"locks:{lock_name}:{id}"
    lock_timeout = int(lock_timeout)
    expire_time = (datetime.now() + timedelta(seconds=lock_timeout)).timestamp()
    result = redis_client.set(identifier, expire_time, nx=True, ex=lock_timeout)
    if print_log:
        log.info(f"acquire lock {lock_name}:{id},result:{result}")
    if result:
        return True
    log.warning(f"acquire lock {lock_name}:{id} failed,already locked")
    return False


def release_lock(lock_name: str, id: str) -> bool:
    log.info(f"release lock {lock_name}:{id}")
    identifier = f"locks:{lock_name}:{id}"
    return redis_client.delete(identifier) == 1


MAX_CHAT_LOCK = 5
chat_semaphore = asyncio.Semaphore(MAX_CHAT_LOCK)


def exit_chat_lock(user_id: int, key: str = "chat"):
    return exist_lock(key, str(user_id))


def chat_lock(user_id: int, key: str = "chat"):
    try:
        if not CHAT_LOCK_SWITCH:
            return True
        if chat_semaphore.locked():
            log.error(f"chat semaphore locked {user_id},max: {MAX_CHAT_LOCK}")
            # return False
        ret = acquire_lock(key, str(user_id), 30)
        if not ret:
            log.warning(f"chat lock failed {user_id}")
        return ret
    except Exception as e:
        return True


def chat_lock_release(user_id: int, key: str = "chat"):
    try:
        if not CHAT_LOCK_SWITCH:
            return True
        chat_semaphore.release()
        return release_lock(key, str(user_id))
    except Exception as e:
        return True


def async_func_lock(key: str, id: str, timeout: int = 10, failure_func=None):
    def decorator(original_func):
        async def wrapper(*args, **kwargs):
            if acquire_lock(key, id, lock_timeout=timeout):
                try:
                    return await original_func(*args, **kwargs)
                finally:
                    release_lock(key, id)
            if failure_func:
                return await failure_func()
            return None

        return wrapper

    return decorator


def async_release_lock(key: str, id: str):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            finally:
                release_lock(key, id)

        return wrapper

    return decorator

async def async_run_with_lock(
    key: str, id: str, timeout: int = 10, run_func=None, failure_func=None
):
    if acquire_lock(key, id, lock_timeout=timeout):
        try:
            if run_func:
                return await run_func()
            return True
        finally:
            release_lock(key, id)
    if failure_func:
        return await failure_func()
    return False