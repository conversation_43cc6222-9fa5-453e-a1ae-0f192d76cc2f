from datetime import datetime
from enum import Enum
import json
import os
from motor.motor_asyncio import AsyncIOMotorClient
import logging
from persistence.models.models import UserRegisterSource
from services import config_service
from utils import preset_util

logger = logging.getLogger(__name__)


class NSFWLevel(Enum):
    SFW = 0
    NSFW = 1
    
    @classmethod
    def from_str(cls, input:str) -> int:
        logger.info(f"presets upload: nsfw: {input}")
        modified_input = input.replace('-', '_').upper()
        target_nsfw = cls.__members__.get(modified_input)
        if target_nsfw is None:
            raise ValueError(f"Invalid nsfw level: {input}")
        return target_nsfw.value
    
    @classmethod
    def from_int(cls, input:int) -> str:
        return cls(input).name.replace('_', '-').lower()
class Scenario(Enum):
    CHAT = 1 # 对话
    CHAT_SUMMARY = 2 # 对话+总结
    IMPERSONATE = 3 # ai代答
    IMPERSONATE_SUMMARY = 4 # ai代答+总结
    
    @classmethod
    def from_str(cls, input:str) -> int:
        logger.info(f"presets upload: scenario: {input}")
        modified_input = input.replace('-', '_').upper()
        target_scenario = cls.__members__.get(modified_input)
        if target_scenario is None:
            raise ValueError(f"Invalid scenario: {input}")
        return target_scenario.value
    
    @classmethod
    def from_int(cls, input:int) -> str:
        return cls(input).name.replace('_', '-').lower()

class PresetsPersistence():
    def __init__(self, mongo_url: str, db_name: str = 'tavern', collection_name: str = 'presets'):
        self.client = AsyncIOMotorClient(mongo_url)
        self.db = self.client[db_name]
        self.collection_name = collection_name
        self.col = self.db.get_collection(self.collection_name)
    
    # 废弃接口, 临时代码保证兼容性。前端上线后就可以删除
    # async def save_preset(self, preset: dict):
    #     # disable all previous presets
    #     for model in Model:
    #         await self.col.update_many({'model':model.value, 'nsfw':NSFWLevel.NSFW.value, 'scenario':Scenario.CHAT.value, 'status':1}, {'$set': {'status': 0}})

    #     timestamp = int(datetime.now().timestamp())
    #     insert_list = []
    #     for model in Model:  
    #         data = {
    #             "timestamp": timestamp,
    #             "preset": preset,
    #             "model": model.value,
    #             "nsfw": NSFWLevel.NSFW.value,
    #             "scenario": Scenario.CHAT.value,
    #             "status": 1,
    #         }
    #         insert_list.append(data)
    #     await self.col.insert_many(insert_list)
    
    async def save_preset_v2(self, preset:dict, model:str, nsfw:str, scenario:str) -> str:
        llm_model_map = await config_service.map_llm_model_to_int()
        model_int = llm_model_map.get(model)
        if not model_int:
            raise ValueError(f"Invalid model: {model}")
        await self._disable_previous_preset(model_int, NSFWLevel.from_str(nsfw), Scenario.from_str(scenario))
        data = {
            "timestamp": int(datetime.now().timestamp()),
            "preset": preset,
            "model": model_int,
            "nsfw": NSFWLevel.from_str(nsfw),
            "scenario": Scenario.from_str(scenario),
            "status": 1,
            "llm_model": model,
        }
        doc = await self.col.insert_one(data)
        return str(doc.inserted_id)
    
    async def _disable_previous_preset(self, model:int, nsfw:int, scenario:int) -> None:
        await self.col.update_many({'model':model, 'nsfw':nsfw, 'scenario':scenario, 'status':1}, {'$set': {'status': 0}})
    
    async def get_all_preset_for_admin(self) -> dict:
        # 硬编码 100，防止后续返回超大量数据
        result = await self.col.find({'status':1}).to_list(500)
        int_model_map = await config_service.map_int_to_llm_model()

        dict = {}
        for item in result: 
            if item['model'] not in int_model_map:
                logger.warning(f"Model {item['model']} not found in int_model_map")
                continue
            model_text = int_model_map.get(item['model'])  #Model.from_int(item['model'])
            nsfw_text = NSFWLevel.from_int(item['nsfw'])
            scenario_text = Scenario.from_int(item['scenario'])
            if model_text not in dict:
                dict[model_text] = {}
            if nsfw_text not in dict[model_text]:
                dict[model_text][nsfw_text] = {}
            dict[model_text][nsfw_text][scenario_text] = self._handle_preset_for_admin(item['preset'])
        return dict
    
    def _handle_preset_for_admin(self, preset:dict) -> dict:
        enabled_prompts = preset_util.get_prompts(preset)
        # for prompt in enabled_prompts:
        #     # 删除这个key（本身的赋值不准确）
        #     prompt.pop("enabled", None)
        preset["prompts"] = enabled_prompts
        preset.pop("prompt_order", None)
        return preset
    
    async def get_preset_v2(self, model:int, nsfw:int, scenario:int ) -> dict:
        # 本地开发环境下，使用本地预设
        preset_switch = os.environ.get('LOCAL_PRESET_SWITCH','false')
        preset_file_path = os.environ.get('LOCAL_PRESET_PATH', '')
        if preset_switch.lower() == 'true' and len(preset_file_path) > 0:
            with open(preset_file_path, "r") as f:
                ps = json.load(f)
                return ps

        result = await self.col.find({'model':model, 'nsfw':nsfw, 'scenario':scenario, 'status':1}).sort('timestamp', -1).limit(1).to_list(1)
        return result[0]['preset']
    
    async def get_preset_v3(self, model:int, scenario:int,role_nsfw:bool,register_source:str) -> dict:
        nsfw = NSFWLevel.NSFW.value
        if not role_nsfw and register_source == UserRegisterSource.USA_WEB.value:
            nsfw = NSFWLevel.SFW.value
        return await self.get_preset_v2(model, nsfw, scenario)

presetsPersistence = PresetsPersistence(os.environ.get('MONGO_URL',""), os.environ.get('MONGO_DB', 'tavern'))