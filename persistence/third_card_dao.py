from common.role_card import Tavern<PERSON><PERSON>, ThirdCardInfo
from persistence import mongo_client

from persistence.mongo_client import Tavern<PERSON>ollection


async def get_by_card_id(card_id: str):
    ret = await TavernCollection.CARDS.find_one({"card_id": card_id})
    if ret:
        return ThirdCardInfo(**ret)
    return None
async def list_all():
    ret = await TavernCollection.CARDS.find({}).to_list(None)
    return [ThirdCardInfo(**item) for item in ret]

async def list(platform: str, offset: int, limit: int):
    ret = (
        await TavernCollection.CARDS.find({"platform": platform})
        .sort("popularity", -1)
        .skip(offset)
        .limit(limit)
        .to_list(None)
    )
    return [ThirdCardInfo(**item) for item in ret]


async def list_no_role(platform: str, offset: int, limit: int):
    ret = (
        await TavernCollection.CARDS.find({"platform": platform, "role_id": 0})
        .sort("popularity", -1)
        .skip(offset)
        .limit(limit)
        .to_list(None)
    )
    return [ThirdCardInfo(**item) for item in ret]


async def update(card: ThirdCardInfo):
    await TavernCollection.CARDS.update_one(
        {"card_id": card.card_id}, {"$set": card.model_dump()}
    )


async def upsert(card: ThirdCardInfo):
    await TavernCollection.CARDS.update_one(
        {"card_id": card.card_id}, {"$set": card.model_dump()}, upsert=True
    )


async def insert(card: ThirdCardInfo):
    await TavernCollection.CARDS.insert_one(card.model_dump())


async def update_popularity(card_id: str, popularity: int):
    await TavernCollection.CARDS.update_one(
        {"card_id": card_id}, {"$set": {"popularity": popularity}}
    )
