import logging

from persistence import mongo_client
from persistence.models.mongo_models import RoleBroadcastHistory


logger = logging.getLogger(__name__)

collection = mongo_client.TavernCollection.ROLE_BROADCAST_HISTORY


async def insert(history: RoleBroadcastHistory):
    await collection.insert_one(history.model_dump())


async def list_by_chat_id(chat_id: int) -> list[RoleBroadcastHistory]:
    history: list[dict] = (
        await collection.find(
            {
                "chat_id": chat_id,
            }
        )
        .sort([("_id", -1)])
        .to_list(None)
    )
    if not history:
        return []
    return [RoleBroadcastHistory(**x) for x in history]


async def list_old_by_chat_id(chat_id: int) -> list[dict]:
    history: list[dict] = (
        await collection.find(
            {
                "chat_id": str(chat_id),
            }
        )
        .sort([("_id", -1)])
        .to_list(None)
    )
    if not history:
        return []
    return history


async def get_by_message(chat_id: int, message_id: int) -> RoleBroadcastHistory | None:
    history = await collection.find_one({"chat_id": chat_id, "message_id": message_id})
    if not history:
        return None
    return RoleBroadcastHistory(**history)


async def get_by_chat_and_role(chat_id: int, role: int) -> RoleBroadcastHistory | None:
    history = await collection.find_one({"chat_id": chat_id, "role_id": role})
    if not history:
        return None
    return RoleBroadcastHistory(**history)


async def list_by_role(role_id: int) -> list[RoleBroadcastHistory]:
    history: list[dict] = (
        await collection.find(
            {
                "role_id": role_id,
            }
        )
        .sort([("_id", -1)])
        .to_list(None)
    )
    if not history:
        return []
    return [RoleBroadcastHistory(**x) for x in history]

async def list_by_role_ids(role_ids: list[int]) -> list[RoleBroadcastHistory]:
    history: list[dict] = (
        await collection.find(
            {
                "role_id": {"$in": role_ids},
            }
        )
        .sort([("_id", -1)])
        .to_list(None)
    )
    if not history:
        return []
    return [RoleBroadcastHistory(**x) for x in history]


async def update_deleted_status(chat_id: int, message_id: int):
    await collection.update_one(
        {"chat_id": chat_id, "message_id": message_id}, {"$set": {"status": -1}}
    )
