from datetime import datetime
import os
from dotenv import load_dotenv
from motor.motor_asyncio import (
    AsyncIOMotorClient,
)
import logging

from common.role_card import CharacterBook
from persistence import mongo_client

# load_dotenv()
logger = logging.getLogger(__name__)
collection = mongo_client.TavernCollection.CHARACTER_BOOK


async def insert(book: CharacterBook):
    book.created_at = int(datetime.now().timestamp())
    book.updated_at = int(datetime.now().timestamp())
    await collection.insert_one(book.model_dump())


async def get_by_book_id(book_id: str) -> CharacterBook:
    if book_id is None or len(book_id) == 0:
        return None
    ret = await collection.find_one({"book_id": book_id})
    if ret:
        return CharacterBook(**ret)
    return None


async def list_all():
    mid_list = await collection.find({}).to_list(None)
    return [CharacterBook(**item) for item in mid_list]


async def update(book: CharacterBook):
    book.updated_at = int(datetime.now().timestamp())
    ret = await collection.replace_one({"book_id": book.book_id}, book.model_dump())
    if ret.modified_count == 1:
        return book
    return None
