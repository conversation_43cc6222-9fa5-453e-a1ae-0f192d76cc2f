import json
import time
import uuid
from langchain_core.embeddings import Embeddings

import tcvectordb
from tcvectordb.model.collection import Embedding
from tcvectordb.model.document import Document, SearchParams, Filter
from tcvectordb.model.enum import FieldType, IndexType, MetricType, EmbeddingModel, ReadConsistency
from tcvectordb.model.index import Index, VectorIndex, FilterIndex, HNSWParams

# disable/enable http request log print
tcvectordb.debug.DebugEnable = False

def print_object(obj):
    for elem in obj:
        if hasattr(elem, '__dict__'):
            print(json.dumps(vars(elem), indent=2))
        else:
            print(json.dumps(elem, indent=2))

class TCVectorDB:
    def __init__(self, embedding: Embeddings, url: str, username: str,
                 key: str, database: str, collection: str, timeout: int = 30):
        """
        初始化客户端
        """
        # 创建客户端时可以指定 read_consistency，后续调用 sdk 接口的 read_consistency 将延用该值
        self.tencent_client = tcvectordb.VectorDBClient(url=url, username=username, key=key,
                                                 read_consistency=ReadConsistency.EVENTUAL_CONSISTENCY, timeout=timeout)
        self.database_name = database
        self.collection_name = collection
        self.embedding_function = embedding
        self.timeout = timeout
        self.create_db_and_collection()

    def add_texts(self, texts: list[str], user_id: int, role_id: int, metadatas: list[dict] = []) -> list[str]:
        vectors = self.embedding_function.embed_documents(texts)
        ids = [uuid.uuid4().hex for _ in range(len(texts))]
        if len(metadatas) == 0:
            metadatas = [{} for _ in range(len(texts))]
        document_list = list(map(lambda x: Document(id=x[3], vector=x[1], text=x[0], metadata=json.dumps(x[2]), user_id=user_id, role=role_id),
            zip(texts, vectors, metadatas, ids)))
        self.collection.upsert(documents=document_list)
        return ids

    def create_db_and_collection(self):
        db_list = self.tencent_client.list_databases()

        if self.database_name in [db.database_name for db in db_list]:
            self.database = self.tencent_client.database(self.database_name)
        else:
            self.database = self.tencent_client.create_database(self.database_name)

        collections = self.database.list_collections()
        if self.collection_name in [collection.collection_name for collection in collections]:
            self.collection = self.database.collection(self.collection_name)
            return

        index = Index()
        index.add(VectorIndex('vector', 3072, IndexType.HNSW, MetricType.COSINE, HNSWParams(m=16, efconstruction=200)))
        index.add(FilterIndex('id', FieldType.String, IndexType.PRIMARY_KEY))
        index.add(FilterIndex('user_id', FieldType.Uint64, IndexType.FILTER))
        index.add(FilterIndex('role', FieldType.Uint64, IndexType.FILTER))
        #ebd = Embedding(vector_field='vector', field='text', model=EmbeddingModel.TEXT2VEC_LARGE_CHINESE)

        self.database.create_collection(
            name=self.collection_name,
            shard=3,
            replicas=0,
            description=self.collection_name,
            index=index,
            embedding=None,
            timeout=self.timeout
        )
        self.collection = self.database.collection(self.collection_name)

    def similarity_search_with_score(self, user_id: int, role: int, text: str, top_k: int) -> list[tuple[dict, float]]:
        vectors = self.embedding_function.embed_documents([text])

        res = self.collection.search(
            vectors=vectors,  # 指定检索向量，最多指定20个
            params=SearchParams(ef=200),  # 若使用HNSW索引，则需要指定参数ef，ef越大，召回率越高，但也会影响检索速度
            retrieve_vector=False,  # 是否需要返回向量字段，False：不返回，True：返回
            limit=top_k,  # 指定 Top K 的 K 值
            filter=Filter(f'user_id="{user_id}"').And(f'role="{role}"'),  # 指定过滤条件，只返回 user_id 为 1 的文档
        )
        def convert_result(x) -> tuple[dict, float]:
            x['metadata'] = json.loads(x['metadata'])
            x, x['score']
        return list(map(convert_result, res[0]))

