from typing import Any, Dict, List
import logging

from common.common_constant import ChatModeType
from common.models.chat_model import (
    ChatHistory,
    ChatHistoryStatus,
    ChatHistoryType,
    ChatTipsHistory,
)
from common.models.mongo_model import ChatErrorRecord
from common.role_model import <PERSON><PERSON><PERSON>
from persistence import mongo_client
from persistence.presets import <PERSON><PERSON><PERSON>
from utils import json_util


logger = logging.getLogger(__name__)

collection = mongo_client.TavernCollection.CHAT_ERROR_RECORD


async def insert_message(record: ChatErrorRecord):
    await collection.insert_one(record.model_dump())


async def list_error_records(
    preset_model: str,
    limit: int = 20,
) -> List[ChatErrorRecord]:
    query = {"preset_model": preset_model}
    cursor = collection.find(query).sort("_id", -1).limit(limit)
    records = await cursor.to_list(limit)
    return [ChatErrorRecord(**record) for record in records]
