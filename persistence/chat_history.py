from datetime import datetime
import os
from motor.motor_asyncio import (
    AsyncIOMotorClient,
)
import logging

from common.models.chat_model import Chat<PERSON><PERSON>ory, ChatHistoryStatus, ChatTipsHistory


logger = logging.getLogger(__name__)


class ChatHistoryMongoPersistence:
    def __init__(self, mongo_url: str, db_name: str = "tavern"):
        self.client = AsyncIOMotorClient(mongo_url)
        self.db = self.client[db_name]
        self.collection_name = "chat_history"
        self.col = self.db.get_collection(self.collection_name)

    async def insert_message(self, message: ChatHistory):
        await self.col.insert_one(message.model_dump())

    async def save_message_voice(self, oid: str, voice_url: str):
        await self.col.update_one({"_id": oid}, {"$set": {"voice_url": voice_url}})

    async def save_message_photo(self, oid: str, photo_url: str, photo_id: str):
        await self.col.update_one({"_id": oid}, {"$set": {"photo_url": photo_url, "photo_id": photo_id}})

    async def save_message_retry_photos(self, oid: str, retry_photos: list[dict]):
        await self.col.update_one({"_id": oid}, {"$set": {"retry_photos": retry_photos}})

    async def get_message(self, message_id: str, version: int):
        return await self.col.find_one({"message_id": message_id, "version": version})

    async def get_messages(
        self,
        user_id: int,
        role_id: int,
        conversation_id: str,
    ):
        return (
            await self.col.find(
                {
                    "user_id": user_id,
                    "role_id": role_id,
                    "conversation_id": conversation_id,
                    "status": {"$ne": ChatHistoryStatus.DELETED},
                },
                {"_id": 0},
            )
            .sort("timestamp", 1)
            .to_list(None)
        )


    async def insert_chat_tips(self, chat_tips: ChatTipsHistory):
        await self.db.get_collection("chat_tips_history").insert_one(chat_tips.model_dump())

chat_history_persistence = ChatHistoryMongoPersistence(os.environ.get("MONGO_URL",""), os.environ.get("MONGO_DB",""))
