import re
import copy
import json

Replacements = {
    'user': 'Human',
    'assistant': 'Assistant',
    'system': '',
    'example_user': 'H',
    'example_assistant': 'A'
}

regexLog = ''

def xmlPlot_merge(content, mergeTag):
    if re.search(r'(\n\n|^\s*)xmlPlot:\s*', content):
        content = re.sub(r'(\n\n|^\s*)(?<!\n\n(Human|Assistant):.*?)xmlPlot:\s*', '$1', content, flags=re.S)
        content = re.sub(r'(\n\n|^\s*)xmlPlot: *', '\n\nHuman: ' if (mergeTag['system'] and mergeTag['human'] and mergeTag['all']) else '$1', content)
    if mergeTag['all'] and mergeTag['human']:
        content = re.sub(r'(?:\n\n|^\s*)Human:(.*?(?:\n\nAssistant:|$))', lambda match: '\n\nHuman:' + match.group(1).replace('\n\nHuman:\s*', '\n\n'), content, flags=re.S)
    if mergeTag['all'] and mergeTag['assistant']:
        content = re.sub(r'\n\nAssistant:(.*?(?:\n\nHuman:|$))', lambda match: '\n\nAssistant:' + match.group(1).replace('\n\nAssistant:\s*', '\n\n'), content, flags=re.S)
    return content

def xmlPlot_regex(content, order):
    global regexLog
    matches = re.findall(rf'<regex(?: +order *= *{order}){"" if order == 2 else "?"}> *"(.*?)" *: *"(.*?)" *</regex>', content, flags=re.M)
    if matches:
        for match in matches:
            try:
                reg = re.search(r'<regex(?: +order *= *\d)?> *"(.*?)" *: *"(.*?)" *</regex>', match)
                regexLog += match + '\n'
                content = re.sub(reg.group(1), reg.group(2).replace(r'(\r\n|\r|\\n)', '\n'), content)
            except Exception as err:
                print(f'Regex error: {match}\n{err}')
    return content

def xmlPlot(content):
    global regexLog
    regexLog = ''
    content = xmlPlot_regex(content, 1)
    mergeTag = {
        'all': '<|Merge Disable|>' not in content,
        'system': '<|Merge System Disable|>' not in content,
        'human': '<|Merge Human Disable|>' not in content,
        'assistant': '<|Merge Assistant Disable|>' not in content
    }
    content = xmlPlot_merge(content, mergeTag)
    splitContent = re.split(r'\n\n(?=Assistant:|Human:)', content)
    match = re.search(r'<@(\d+)>(.*?)<\/@\1>', content, flags=re.S)
    while match is not None:
        index = len(splitContent) - int(match.group(1)) - 1
        if index >= 0:
            splitContent[index] += '\n\n' + match.group(2)
        content = content.replace(match.group(0), '')
        match = re.search(r'<@(\d+)>(.*?)<\/@\1>', content, flags=re.S)
    content = '\n\n'.join(splitContent).replace(r'<@(\d+)>.*?<\/@\1>', '')
    content = xmlPlot_regex(content, 2)
    content = xmlPlot_merge(content, mergeTag)
    content = xmlPlot_regex(content, 3)
    content = content.replace(r'<regex( +order *= *\d)?>.*?<\/regex>', '', flags=re.M)\
        .replace(r'(\r\n|\r|\\n)', '\n')\
        .replace(r'\\r', '\r')\
        .replace(r'\s*<\|curtail\|>\s*', '\n')\
        .replace(r'\s*<\|join\|>\s*', '')\
        .replace(r'\s*<\|space\|>\s*', ' ')\
        .replace(r'\s*\n\n(H(uman)?|A(ssistant)?): +', '\n\n$1: ')
    content = re.sub(r'(\n\nHuman:(?!.*?\n\nAssistant:).*?|(?<!\n\nAssistant:.*?))$', '$&\n\nAssistant:', content, flags=re.S)
    content = re.sub(r'\s*<\|noAssistant\|>\s*(.*?)(?:\n\nAssistant:\s*)?$', '\n\n$1', content, flags=re.S)
    if '<|reverseHA|>' in content:
        content = content.replace(r'\s*<\|reverseHA\|>\s*', '\n\n')\
            .replace('Assistant|Human', lambda match: 'Assistant' if match == 'Human' else 'Human')\
            .replace(r'\n(A|H): ', lambda match: '\nH: ' if match.group(1) == 'A' else '\nA: ')
    return re.sub(r'\s*<\|.*?\|>\s*', '\n\n', content).strip().replace(r'^.+:', '\n\n$&').replace(r'(?<=\n)\n(?=\n)', '', flags=re.S)

# 定义格式化字符串
PersonalityFormat = "{char}'s personality: {personality}"
ScenarioFormat = "Dialogue scenario: {scenario}"

# 定义函数处理消息
import re
import json
import copy

def process_messages(body_string):
    # Parse the JSON string into a Python dictionary
    body = json.loads(body_string)
    messages = body['messages']

    # Define the regular expressions
    rgx_scenario = re.compile(r'^\[Circumstances and context of the dialogue: ([\s\S]+?)\.?\]$', re.I)
    rgx_person = re.compile(r'^\[([\s\S]+?)\'s personality: ([\s\S]+?)\]$')

    # Create a deep copy of the messages
    messages_clone = copy.deepcopy(messages)

    # Filter out the system messages
    system_messages = [message for message in messages_clone if message['role'] == 'system']

    # Process the system messages
    for idx, message in enumerate(system_messages):
        scenario_match = rgx_scenario.match(message['content'])
        personality_match = rgx_person.match(message['content'])

        if scenario_match:
            scenario = scenario_match.group(1)
            message['content'] = ScenarioFormat.replace('{{scenario}}', scenario)
            message['scenario'] = True

        if personality_match and len(personality_match.groups()) == 3:
            char = personality_match.group(1)
            personality = personality_match.group(2)
            message['content'] = PersonalityFormat.replace('{{char}}', char).replace('{{personality}}', personality)
            message['personality'] = True

        message['main'] = (idx == 0)
        message['jailbreak'] = (idx == len(system_messages) - 1)
        if message['content'] == ' ':
            message['discard'] = True

    # Generate the new prompt
    prompt = []
    for idx, message in enumerate(messages_clone):
        if 'discard' in message and message['discard']:
            prompt.append('')
        elif len(message['content']) < 1:
            prompt.append(message['content'])
        else:
            spacing = '\n\n' if idx > 0 else ''
            prefix = 'xmlPlot: ' + Replacements[message['role']] if message['role'] == 'system' else Replacements[message['role']] + ': '
            prompt.append(f"{spacing}{prefix}{message['content']}")

    prompt = ''.join(prompt)

    # Define some variables
    newtokenizer = True
    messagesAPI = True
    fusion = messagesAPI and '<|Fusion Mode|>' in prompt
    wedge = '\r'
    type = 'msg_api'

    # Process the prompt
    prompt = xmlPlot(prompt)
    if fusion:
        prompt = re.sub(r'\n(?!\nAssistant:\s*$)(?=\n(Human|Assistant):)', '\n' + wedge, prompt, flags=re.S)
    else:
        prompt = re.sub(r'(?<!\n\nHuman:.*)\n(?=\nAssistant:)|\n(?=\nHuman:)(?!.*\n\nAssistant:)', '\n' + wedge, prompt, flags=re.S)

    # Split the prompt into rounds
    rounds = re.split('\n\nHuman:', prompt.replace('^(?!.*\n\nHuman:)', '\n\nHuman:', 1, flags=re.S))

    # Process the rounds into new messages
    messages_new = []
    for round in rounds[1:]:
        turns = re.split('\n\nAssistant:', round)
        messages_new.append({'role': 'user', 'content': turns[0].strip()})
        for turn in turns[1:]:
            messages_new.append({'role': 'assistant', 'content': turn.strip()})

    # Combine consecutive messages from the same role
    messages_new = reduce_messages(messages_new, newtokenizer)

    # Add the first system message
    system = {'role': 'system', 'content': rounds[0].strip()}
    messages_new.insert(0, system)

    return {'system': system, 'messagesNew': messages_new}

def reduce_messages(messages, newtokenizer):
    reduced_messages = []
    for message in messages:
        if len(reduced_messages) > 0 and (reduced_messages[-1]['role'] == message['role'] or not reduced_messages[-1]['content']):
            reduced_messages[-1]['content'] += ('Human' if message['role'] == 'user' else 'Assistant') + message['content']
        else:
            reduced_messages.append(message)
    return [message for message in reduced_messages if message['content']]