import logging
import os

home_dir = os.getenv("HOME")
logs_dir = os.path.join(home_dir, "logs")
log_file = os.path.join(logs_dir, "prompts.log")


local_api_handler = logging.FileHandler(os.path.join(logs_dir, "tavern-api.log"))
local_api_handler.formatter = logging.Formatter("%(asctime)s - %(message)s")

local_admin_api_handler = logging.FileHandler(os.path.join(logs_dir, "tavern-admin-api.log"))
local_admin_api_handler.formatter = logging.Formatter("%(asctime)s - %(message)s")

local_dx_bot_handler = logging.FileHandler(os.path.join(logs_dir, "dx-bot.log"))
local_bot_help_handler = logging.FileHandler(os.path.join(logs_dir, "bot-help.log"))
local_msg_handler = logging.FileHandler(os.path.join(logs_dir, "ff-msg.log"))
local_msg_handler.formatter = logging.Formatter("%(asctime)s - %(message)s")

