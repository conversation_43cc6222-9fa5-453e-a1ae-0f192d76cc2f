IMAGE_STYLE_DICT = {
    "image_style_1": "【⚡️二次元风】",
    "image_style_9": "【🐙触手Play】",
    "image_style_8": "【🍄原初二次元】",
    "image_style_3": "【💖写实风】",
    "image_style_4": "【👾像素风 】",
    "image_style_10": "【👄真实风】",
    "image_style_5": "【🏯吉卜力风】",
    "image_style_6": "【🌃赛博风】",
    "image_style_2": "【🦊福瑞风】",
}

IMAGE_RESOLUTION_DICT = {
    "img_resolution_low": "【🌘标清】",
    "img_resolution_medium": "【🌗高清】",
    "img_resolution_high": "【🌕超清】",
}

SHAPE_DICT = {
    "shape_portrait": "【竖屏】",
    "shape_landscape": "【横版】",
    "shape_square": "【1:1】",
}

IMAGE_PRIVACY_DICT = {
    "image_privacy_public": "【📢公开】",
    "image_privacy_private": "【㊙️私密】",
}

RESOLUTION_SHAPE_MAP = {
    ("img_resolution_low", "shape_portrait"): "small_portrait",
    ("img_resolution_low", "shape_landscape"): "small_landscape",
    ("img_resolution_low", "shape_square"): "small_square",
    ("img_resolution_medium", "shape_portrait"): "normal_portrait",
    ("img_resolution_medium", "shape_landscape"): "normal_landscape",
    ("img_resolution_medium", "shape_square"): "normal_square",
    ("img_resolution_high", "shape_portrait"): "large_portrait",
    ("img_resolution_high", "shape_landscape"): "large_landscape",
    ("img_resolution_high", "shape_square"): "large_square",
}


## checkin

IMAGE_BOT_CHECKIN_TEMPLATE = """
恭喜{tg_nickname}打卡簽到成功！簽到刷新今日的2次免費生圖权益！
AI生图入口:<a href="https://t.me/{bot_name}?start">「幻夢AI生圖」</a>
"""

IMAGE_BOT_REPET_CHECKIN_TEMPLATE = """
親愛的，一天只能打卡簽到一次，歡迎明天再來
"""
