from enum import Enum

from pydantic import BaseModel

from persistence.models.models import LlmModelConfig
from utils import json_util


class GlobalConfigKey(str, Enum):
    ADD_WATER_CONFIG = "add_water_config"

    @classmethod
    def from_str(cls, input: str) -> "GlobalConfigKey":
        return cls(input.lower())


class LlmModelDetail(BaseModel):
    llm_model: str
    request_llm_model: str
    support_params: list[str] = []
    use_cache: bool = False
    request_cluster: str = ""

    @staticmethod
    def from_config(llm_model: LlmModelConfig):
        return LlmModelDetail(
            llm_model=llm_model.llm_model,
            request_llm_model=llm_model.request_llm_model,
            support_params=json_util.convert_to_list(llm_model.support_params),
            use_cache=llm_model.use_cache,
            request_cluster=llm_model.request_cluster,
        )
