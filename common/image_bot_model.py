from typing import Optional, Dict
from pydantic import BaseModel
import json

from persistence.models.models_bot_image import BotImgBasicProfile, BotImgGenTaskReview


class GenImageBaseProfileBO(BaseModel):
    style: str = "image_style_1"
    resolution: str = "img_resolution_low"
    privacy: str = "private"
    shape: str = "shape_portrait"
    small_free_gen_count: int = 0
    small_free_gen_str: str = ""

    @staticmethod
    def from_model(basic_profile: BotImgBasicProfile):

        profile_json = basic_profile.img_gen_profile

        return GenImageBaseProfileBO.model_validate_json(json.dumps(profile_json))

    def to_json(self):
        return self.model_dump_json(exclude=None)


class ImageBotSettingsBO(BaseModel):
    bot_id: int
    settings: dict
    start_msg: str = "欢迎👏"
    style_group_mapping: Dict[str, Dict[str, int]] = {}  # 封装为嵌套字典

    def add_style_mapping(self, style: str, group_id: int, topic_id: int):
        """添加一个 style 到 group_id 和 topic_id 的映射"""
        self.style_group_mapping[style] = {"group_id": group_id, "topic_id": topic_id}

    def remove_style_mapping(self, style: str):
        """移除一个 style 的映射"""
        if style in self.style_group_mapping:
            del self.style_group_mapping[style]

    def get_style_mapping(self, style: str) -> Optional[Dict[str, int]]:
        """获取一个 style 的映射"""
        return self.style_group_mapping.get(style)

    def update_style_mapping(
        self, style: str, group_id: Optional[int] = None, topic_id: Optional[int] = None
    ):
        """更新一个 style 的映射"""
        if style in self.style_group_mapping:
            if group_id is not None:
                self.style_group_mapping[style]["group_id"] = group_id
            if topic_id is not None:
                self.style_group_mapping[style]["topic_id"] = topic_id


class BotImgUserCheckinBO(BaseModel):
    bot_id: int
    tg_id: int
    group_id: int
    message_thread_id: int = 0
    msg_txt: str
    msg_id: int
    tg_nickname: str = ""


class GenImageRequestBO(BaseModel):
    tg_id: int
    prompt: str

    def to_json(self):
        return self.model_dump_json(exclude_none=True)


class GenImageResultBO(BaseModel):
    tg_id: int
    prompt: str
    enhanced_prompt: str = ""
    image_url: str = ""
    request_id: int
    code: int = 501
    spent_time_s: str = "0"
    msg: str = ""

    def to_json(self):
        return self.model_dump_json(exclude_none=True)

    def to_dict(self):
        return self.model_dump(exclude_none=True)
