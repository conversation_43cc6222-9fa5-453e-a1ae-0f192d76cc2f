from datetime import datetime
import re
from typing import <PERSON>ync<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Optional
from litellm import ChatCompletionChunk, CustomStreamWrapper
from pydantic import BaseModel

from common.common_constant import (
    Language,
    PresetChatRegex,
    PresetSwitchPrefix,
    RoleChatType,
)
from common.role_card import CharacterBook
from common.role_model import RoleDataConfig


class AnalysisExampleRet(BaseModel):
    messages: list[str] = []
    need_merge: bool = False

    # 按顺序优先级获取有效的消息列表1、新版本列表 2、旧版本列表 3、单条消息列表（需要合并在一起）
    def fetch_merged_message(self) -> list[str]:
        if len(self.messages) == 0:
            return []
        if self.need_merge:
            return ["\n\n\n".join(self.messages)]
        return self.messages


class ChatParams(BaseModel):
    presets: dict
    data_config: RoleDataConfig
    role_name: str
    role_id: int
    level_type: str
    select_model_ret: dict

    # user: User
    user_id: int
    user_role_name: str

    history_data: dict

    regex_rules: list = []
    character_book: Optional[CharacterBook] = None
    language: str = Language.ZH.value
    copywriting: bool = False

    # mid out put
    open_ai_max_context: int = 0
    description: str = ""
    personality: str = ""
    example: Optional[dict] = None
    scenario: str = ""
    format_history: list[dict] = []
    last_message_id: str = ""
    last_message: str = ""
    format_character_book: Optional[CharacterBook] = None
    replay_max_count: int = 0

    # output
    token_sum: int = 0
    messages: list = []


# class ChatResponse:
#     response: AsyncIterator
#     token_sum: int

#     def __init__(self, response: AsyncIterator, token_sum: int):
#         self.response = response
#         self.token_sum = token_sum
class ChatReplayResponse:
    response: CustomStreamWrapper
    token_sum: int

    def __init__(self, response: CustomStreamWrapper, token_sum: int):
        self.response = response
        self.token_sum = token_sum
        
class ChatStreamResponse:
    success: bool
    error_type: str
    response: Optional[AsyncIterator] = None
    token_sum: int = 0
    error_message: Optional[str] = None
    first_chunk: Optional[ChatCompletionChunk] = None

    def __init__(self, success: bool, error_type: str, response: Optional[AsyncIterator] = None, token_sum: int = 0, error_message: Optional[str] = None, first_chunk: Optional[ChatCompletionChunk] = None):
        self.success = success
        self.error_type = error_type
        self.response = response
        self.token_sum = token_sum
        self.error_message = error_message
        self.first_chunk = first_chunk

class PresetSwitchQuery(BaseModel):
    status_enable:bool
    role_chat_type: str
    example_switch: bool
    scenario_switch: bool
    personality_switch: bool
    first_msg_switch: bool
    world_info_switch: bool
    kw_world_info_switch:bool
    card_public_switch:bool

class PresetQuery(BaseModel):
    model_int: int
    scenario: int
    role_nsfw: bool
    user_register_source: str

    status_enable:bool
    role_chat_type: str

    example_switch: bool
    scenario_switch: bool
    personality_switch: bool
    first_msg_switch: bool
    world_info_switch: bool

    def load_prefix_enable(self, name: str, def_enable: bool) -> bool:
        map_switch = {
            PresetSwitchPrefix.CHAT_EXAMPLE: self.example_switch,
            PresetSwitchPrefix.SCENARIO: self.scenario_switch,
            PresetSwitchPrefix.PERSONALITY: self.personality_switch,
            PresetSwitchPrefix.FIRST_MSG: self.first_msg_switch,
            PresetSwitchPrefix.WORLD_INFO: self.world_info_switch,
        }
        for k, v in map_switch.items():
            if name.startswith(k.value):
                return v
        return def_enable

    def load_chat_type_enable(self, name: str, def_enable: bool) -> bool:
        chat_type_list = {
            PresetChatRegex.CHAT.value: RoleChatType.CHAT.value,
            PresetChatRegex.ROLE_PLAY.value: RoleChatType.ROLE_PLAY.value,
            PresetChatRegex.GENERAL.value: RoleChatType.GENERAL.value,
        }
        ret_regex = None
        for k, v in chat_type_list:
            if re.search(k, name):
                ret_regex = v
            if ret_regex and self.role_chat_type == ret_regex:
                return True
        if ret_regex:
            return False
        return def_enable
    
    def load_status_enable(self, name: str, def_enable: bool) -> bool:
        contain_status_on = re.search("(?:^|:)StatusOn:", name)
        contain_status_off = re.search("(?:^|:)StatusOff:", name)
        if contain_status_on or contain_status_off:
            if self.status_enable and contain_status_on:
                return True
            if not self.status_enable and contain_status_off:
                return True
            return False
        return def_enable
    


class DfyRequest(BaseModel):
    inputs: dict[str, str] = {}
    query : str
    response_mode: str = "blocking"
    user:str = "tavern-task-robot"
    conversation_id: str = ""


class UserActiveBotDetail(BaseModel):
    bot_id: int = 0
    bot_username: str = ""
    bot_first_name: str = ""
    user_last_active_at: datetime = datetime.now()
    