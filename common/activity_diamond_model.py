from datetime import UTC, datetime
from enum import Enum
from pydantic import BaseModel

from persistence.models.models import ActivityDiamondSeason, ActivityDiamondTask, DiamondSeasonStatus, DiamondTaskStatus
from utils import json_util


class NoticeMethodConfig(BaseModel):
    method: int = 0
    interval: int = 0

class NoticeMethod(int, Enum):
    TIMER = 1
    AMOUNT= 2

class DiamondSeasonConfig(BaseModel):
    season_id: str = ""
    title : str = ""
    start_at : float
    end_at : float
    warming_up_notice : str = ""
    warming_up_start_at : float
    warming_up_end_at : float
    warming_up_notice_method : NoticeMethodConfig | None = None
    status : int = 0

    @staticmethod
    def from_model(diamond_season: ActivityDiamondSeason):
        notice_method_dict = json_util.convert_to_dict(diamond_season.warming_up_notice_method)
        notice_method = None
        if notice_method_dict:
            notice_method = NoticeMethodConfig(**notice_method_dict)
        return DiamondSeasonConfig(
            season_id=str(diamond_season.season_id),
            title=diamond_season.title,
            start_at=diamond_season.start_at.timestamp(),
            end_at=diamond_season.end_at.timestamp(), 
            warming_up_notice=diamond_season.warming_up_notice,
            warming_up_start_at=diamond_season.warming_up_start_at.timestamp(),
            warming_up_end_at=diamond_season.warming_up_end_at.timestamp(),
            warming_up_notice_method = notice_method,
            status=diamond_season.status,
        )
    
    # 插入数据库时使用
    def to_model(self):
        result = ActivityDiamondSeason(
            title=self.title,
            start_at=datetime.fromtimestamp(self.start_at, UTC),
            end_at=datetime.fromtimestamp(self.end_at, UTC),
            warming_up_notice=self.warming_up_notice,
            warming_up_start_at=datetime.fromtimestamp(self.warming_up_start_at, UTC),
            warming_up_end_at=datetime.fromtimestamp(self.warming_up_end_at, UTC),
            status=DiamondSeasonStatus.CREATED.value,
        )
        if self.warming_up_notice_method:
            result.warming_up_notice_method = self.warming_up_notice_method.model_dump()
        return result





class DiamondTaskConfig(BaseModel):
    task_id: str = ""
    season_id : str
    max_participants : int
    allow_repeated_enrolled : bool
    role_ids : list[int]
    allowed_chat_models : list[str] = [] # 允许的聊天模型列表
    role_id_name_map: dict = {} # 给前端返回时给该字段赋值，方便前端展示。 不参与数据库存储
    diamond_return_at : float = 0 # 已废弃
    diamond_expire_at : float = 0 # 已废弃
    diamond_gotten_manually : bool = True
    start_at : float
    end_at : float
    start_notice:str
    end_notice:str
    required_diamond_amount:int
    return_rate:int
    grand_prize_count:int
    grand_prize_return_rate:int
    left_participants_notice_interval:int = 0
    status : int = 0

    @staticmethod
    def from_model(diamond_task: ActivityDiamondTask):
        return DiamondTaskConfig(
            task_id=str(diamond_task.task_id),
            season_id=str(diamond_task.season_id),
            max_participants=diamond_task.max_participants,
            allow_repeated_enrolled=diamond_task.allow_repeated_enrolled,
            role_ids=json_util.convert_to_list(diamond_task.role_ids),
            allowed_chat_models=json_util.convert_to_list(diamond_task.allowed_chat_models),
            diamond_gotten_manually=diamond_task.diamond_gotten_manually,
            start_at=diamond_task.start_at.timestamp(),
            end_at=diamond_task.end_at.timestamp(),
            start_notice=diamond_task.start_notice,
            end_notice=diamond_task.end_notice,
            required_diamond_amount=diamond_task.required_diamond_amount,
            return_rate=diamond_task.return_rate,
            grand_prize_count=diamond_task.grand_prize_count,
            grand_prize_return_rate=diamond_task.grand_prize_return_rate,
            left_participants_notice_interval=diamond_task.left_participants_notice_interval,
            status=diamond_task.status,         
        )
    # 插入数据库时使用
    def to_model(self):
        return ActivityDiamondTask(
            season_id=self.season_id,
            max_participants=self.max_participants,
            role_ids=self.role_ids,
            allowed_chat_models=self.allowed_chat_models,
            allow_repeated_enrolled=self.allow_repeated_enrolled,
            diamond_gotten_manually=self.diamond_gotten_manually,
            start_at=datetime.fromtimestamp(self.start_at, UTC),
            end_at=datetime.fromtimestamp(self.end_at, UTC),
            start_notice=self.start_notice,
            end_notice=self.end_notice,
            required_diamond_amount=self.required_diamond_amount,
            return_rate=self.return_rate,
            grand_prize_count=self.grand_prize_count,
            grand_prize_return_rate=self.grand_prize_return_rate,
            left_participants_notice_interval=self.left_participants_notice_interval,
            status=DiamondTaskStatus.ACTIVE.value,
        )