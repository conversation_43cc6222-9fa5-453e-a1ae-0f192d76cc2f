from pydantic import BaseModel


from common.common_constant import Language, TaskStatus, WelfareTaskType
from persistence.models.models import WelfareTask


class UserTask(BaseModel):
    taskId: str = ""
    taskType: str = ""
    title: str = ""
    sub_title:str = ""
    subTitle:str = ""
    desc: str = ""
    btn: str = ""
    linkUrl: str
    btnDoneDesc: str = "已完成"
    status: str = "TODO"
    actionType: str = "link"
    nextTime: str = ""

    @staticmethod
    def init(
        welfare_task: WelfareTask,
        next_time: str = "",
        status: str = TaskStatus.TODO.value,
    ):
        return UserTask(
            taskId=welfare_task.task_id,
            taskType=welfare_task.task_type,
            title=welfare_task.title,
            desc=welfare_task.desc,
            btn=welfare_task.btn,
            linkUrl=welfare_task.link_url,
            btnDoneDesc=welfare_task.btn_done_desc,
            status=status,
            actionType=welfare_task.action_type,
            nextTime=next_time,
        )
