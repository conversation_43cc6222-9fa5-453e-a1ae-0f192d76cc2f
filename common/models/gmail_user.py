
class GmailUser:
    def __init__(self, id, email, verified_email, name, given_name, picture, locale):
        self.id = id
        self.email = email
        self.verified_email = verified_email
        self.name = name
        self.given_name = given_name
        self.picture = picture
        self.locale = locale

    def __str__(self):
        return f'GmailUser(id={self.id}, email={self.email}, verified_email={self.verified_email}, name={self.name}, given_name={self.given_name}, picture={self.picture}, locale={self.locale})'

    def __repr__(self):
        return self.__str__()

    def to_dict(self):
        return {
            'id': self.id,
            'email': self.email,
            'verified_email': self.verified_email,
            'name': self.name,
            'given_name': self.given_name,
            'picture': self.picture,
            'locale': self.locale
        }

    @staticmethod
    def from_dict(data):
        return GmailUser(
            id=data['id'],
            email=data['email'],
            verified_email=data['verified_email'],
            name=data['name'],
            given_name=data['given_name'],
            picture=data['picture'],
            locale=data['locale']
        )