


from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup


class StatSettingForm(StatesGroup):
    repate_send_time = State()
    topic_id = State()

# Define states for schedule message entry
class ScheduleMessageForm(StatesGroup):
    repeat_interval = State()
    message_content = State()
    start_time = State()
    end_time = State()
    more_messages = State()
    modify_message = State()