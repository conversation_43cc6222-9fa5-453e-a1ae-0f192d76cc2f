

class MediaModerateConstants:
    #status
    INIT = "INIT"
    
    # Suggestion
    PASS = "Pass"
    BLOCK = "Block"
    REVIEW = "Review"
    
    
    LABEL_AD = "Ad"
    LABEL_PORN = 'Porn'
    LABEL_ADMIN_SKIP = 'admin_skip'
    LABEL_TGS_SKIP = 'tgs_skip'
    LABEL_FORWARD_SKIP = 'forward_skip'
    
class SpamRubbishType:
    
    # Link detection
    LINK_DETECTION = 1

    # Nickname detection
    NICKNAME_DETECTION = 2

    # Prohibited words detection
    PROHIBITED_WORDS_DETECTION = 6

    # Auto-reply related (starting from 100)
    AUTO_REPLY_RELATED = 100

    # Card author
    CARD_AUTHOR = 101

    # Image and multimedia detection
    IMAGE_MULTIMEDIA_DETECTION = 200
    #photo spam
    PHOTO_SPAM = 201