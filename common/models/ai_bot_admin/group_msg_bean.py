
from pydantic import BaseModel
from typing import Dict, List, Optional

from aiogram.enums import ContentType
from datetime import datetime, timezone


class TgMsgContentBO(BaseModel):
    msg_type: str
    group_id: int
    msg_id: int
    tg_id: int
    date: datetime
    edit_date: Optional[int] = None
    gap_time_sec: int = 0
    mesage_thread_id: Optional[int] = None
    tg_nickname: str = ""
    tg_username: str = ""
    file_id: Optional[str] = None
    file_unique_id: Optional[str] = None
    file_url: Optional[str] = None
    msg_text: Optional[str] = None
    
