




from datetime import datetime
from pydantic import BaseModel


class FromGroupMsg(BaseModel):
    group_nickname: str
    tg_username: str
    f_group_id: int
    f_bot_id: int
    tg_id: int
    class Config:
        orm_mode = True
        schema_extra = {
            "example": {
                "group_id": 1,
                "msg_id": 1,
                "target_group_id": 2
            }
        }
        

class UserInfoBase(BaseModel):
    nickname: str = ""
    username: str = ""
    tg_id: int
    reg_time: datetime
    user_id: int

class GUserInfoCard(UserInfoBase):
    join_group_days: int
    total_fee: int
    recent_messages: list[str]