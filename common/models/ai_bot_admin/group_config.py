from datetime import datetime
from pydantic import BaseModel
from typing import Dict, List, Optional

def to_camel(string: str) -> str:
    parts = string.split('_')
    return parts[0] + ''.join(word.capitalize() for word in parts[1:])

class CamelModel(BaseModel):
    class Config:
        alias_generator = to_camel
        allow_population_by_field_name = True
        json_encoders = {
            # 如果有需要自定义的类型转换，可以在这里添加
        }
class MsgContentBO(BaseModel):
    msg_type: int
    msg_text: Optional[str] = None
    

class AutoReplyResultBO(BaseModel):
    msg_type: int
    message_text: Optional[str] = None
    image_url: Optional[str] = None
    # 多少s后删除机器人消息，0为不删除
    del_bot_msg_delay: int
    # 多少s后删除源消息，0为不删除
    del_src_msg_delay: int
    is_quote: bool = False





class AutoReplyRuleBase(BaseModel):
    group_id: int
    keyword_type: int
    keyword_list: str
    msg_type: int
    msg_content: Dict
    del_bot_msg_delay: int = 0
    del_src_msg_delay: int = 0
    rule_desc: str = ""
    skip_admin: bool = True
    is_quote: bool = False
    is_enable: bool = True
    pripority: int = 1

class AutoReplyRuleCreate(AutoReplyRuleBase):
    pass

class AutoReplyRuleUpdate(BaseModel):
    rule_id: int
    group_id: Optional[int]
    keyword_type: Optional[int]
    keyword_list: Optional[str]
    msg_type: Optional[int]
    msg_content: Optional[Dict]
    del_bot_msg_delay: int = 0
    del_src_msg_delay: int = 0
    rule_desc: Optional[str]
    skip_admin: bool = False
    is_quote: bool = False
    is_enable: bool = False
    pripority: int = 1

class AutoReplyRuleBO(AutoReplyRuleBase):
    rule_id: int
    created_at: datetime
    updated_at: datetime


    class Config:
        orm_mode = True
        from_attributes = True 




class KickAction(BaseModel):
    # 踢出时间，单位s 0为永久剔出
    kickTime: int = 0
    isKick: int = 0

class AlertAction(BaseModel):
    isAlert: int = 0
    alertTimes: int

class DeleteAction(BaseModel):
    isDel: int = 1

class ForbidAction(BaseModel):
    isForbid: int = 0
    # 禁言时间，单位s
    forbidTime: int 
    # 禁言次数 ,默认1 禁言时间随次数递增。超过禁言次数，直接踢出
    forbidCnt: int = 1

class RubbishAction(BaseModel):
    kick: Optional[KickAction]
    alert: Optional[AlertAction]
    delete: Optional[DeleteAction]  
    forbid: Optional[ForbidAction]

class SpamProtectionRuleBase(BaseModel):
    rubbish_type: int
    rubbish_desc: str
    rubbish_condition: str
    rubbish_action: RubbishAction
    rubbish_sort: int = 100
    is_enable: bool = True
    is_master: bool = False
    group_id: int
    delete_memo: int
    check_user_type: str = "all"
    white_link_list: List[str] = []
    white_user_list: List[int] = []

class SpamProtectionRuleCreate(SpamProtectionRuleBase):
    pass

class SpamProtectionRuleUpdate(BaseModel):
    rubbish_type: Optional[int]
    rubbish_desc: Optional[str]
    rubbish_condition: Optional[str]
    rubbish_action: Optional[RubbishAction]
    rubbish_sort: Optional[int]
    is_enable: Optional[bool]
    is_master: Optional[bool]
    group_id: Optional[int]
    delete_memo: Optional[int]
    check_user_type: Optional[str]
    white_link_list: Optional[List[str]]
    white_user_list: Optional[List[str]]

class SpamProtectionRuleBO(SpamProtectionRuleBase):
    rule_id: int

    class Config:
        orm_mode = True
        from_attributes = True


class BotGroupConfigBO(BaseModel):
    bot_name: str
    token: str
    group_id: int
    welcome_config: Optional[Dict]
    g_white_users: List[int] = []
    bot_type:str = "group_help"
    
    class Config:
        orm_mode = True
        from_attributes = True

class BotWelcomeConfigBase(BaseModel):
    group_id: int
    message_thread_id: Optional[int] = 0
    msg_type: int
    msg_content: Dict
    delete_memo: Optional[int] = 0
    is_enable: Optional[bool] = True

class BotWelcomeConfigCreate(BotWelcomeConfigBase):
    pass

class BotWelcomeConfigUpdate(BaseModel):
    message_thread_id: Optional[int]
    msg_type: Optional[int]
    msg_content: Optional[Dict]
    delete_memo: Optional[int]
    is_enable: Optional[bool]

class BotWelcomeConfigBO(BotWelcomeConfigBase):
    id: int

    class Config:
        orm_mode = True
        from_attributes = True



class AutoSendMsgTaskBO(BaseModel):
    id: int
    groupId: int =0
    topicId: int = 0
    title: str
    content: str
    type: str
    interval: int=0
    startDate: str
    endDate: str
    runDate: str
    enabled: bool
    isDelete: bool
    messageThreshold: int = 0
    imageUrl: Optional[str] = None
    class Config:
        orm_mode = True
        from_attributes = True
    
