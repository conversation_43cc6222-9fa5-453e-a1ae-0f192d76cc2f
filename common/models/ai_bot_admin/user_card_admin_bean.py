
from enum import Enum

from pydantic import BaseModel

class OpActionType(Enum):
    
    DELETE_MSG = ('msg_id_msg_del', '删除某条消息', 0)
    DELETE_ALL = ('all_msg_del', '删除所有消息', 0)
    DELETE_ALL_IMAGES = ('all_img_msg_del', '删除所有图片消息', 0)
    DELETE_IMAGES_10_MIN = ('10_min_img_msg_del', '10分钟后删除所有图片消息', 600)
    DELETE_IMAGES_30_MIN = ('30_min_img_msg_del', '30分钟后删除所有图片消息', 1800)
    
    FORBID_5_MIN = ('5min_msg_forbid', '禁言5分钟', 300)
    FORBID_10_MIN = ('10min_msg_forbid', '禁言10分钟', 600)
    FORBID_30_MIN = ('30min_msg_forbid', '禁言30分钟', 1800)
    FORBID_1_HOUR = ('1hour_msg_forbid', '禁言1小时', 3600)
    
    UNMUTE = ('unmute', '解除禁言', -1)
    
    #踢出60s
    KICK = ('now_kick', '踢出', 60)
    KICK_60_MIN = ('5min_kick', '踢出60分钟', 3600)
    KICK_1_YEAR = ('1year_kick', '踢出1年', 31536000)
    
    BAN = ('forever_ban', '封锁', 0)
    
    UNBAN = ('unban', '解封', 0)
    
    WARN_ONE_TIMES = ('1_time_warn', '被警告1次', 0)
    ClEAR_ALERT = ('clear_alert', '清除警告', 1)

    def __init__(self, code, description, duration: int ):
        self.code = code
        self.description = description
        self.duration = duration

    @classmethod
    def from_code(cls, code):
        for action in cls:
            if action.code == code:
                return action
        raise ValueError(f"Unsupported action type: {code}")
    
    @classmethod
    def all_kick_actions(cls) -> list["OpActionType"]:
        return [cls.KICK, cls.KICK_60_MIN, cls.KICK_1_YEAR]

class OpAction(BaseModel):
    
    operator_nickname: str
    operator_id: int
    tg_id: int
    tg_nick_name: str = ""
    action_type: OpActionType
    duration: int = 0
    group_id : int = 0
    message_id: int = 0