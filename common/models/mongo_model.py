from datetime import datetime
from enum import Enum
from typing import Optional
from pydantic import BaseModel


# db.chat_error_record.create_index("user_id")
# db.chat_error_record.create_index("created_at",)
class ChatErrorRecord(BaseModel):
    user_id: int
    conversation_id: str
    preset_model: str
    request_model: str
    error_type: str
    exception_message: str = ""
    extra: dict = {}
    created_at: int
    updated_at: int
