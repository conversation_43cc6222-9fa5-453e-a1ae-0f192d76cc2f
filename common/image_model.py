from pydantic import BaseModel

from persistence.models.models import UserGenerateImageRecord


class UserImageDetail(BaseModel):
    id: int  # 记录ID
    prompt: str
    image_style: str = ""  # 图像风格
    image_quality: str = ""  # 图像质量
    image_aspect_ratio: str = ""  # 图像长宽比
    status: str = ""  # 状态
    generate_time: int = 0  # 生成时间
    image_url: str = ""  # 图像URL
    error_message: str = ""  # 错误信息
    display_tags: list[str] = []  # 显示标签
    image_width: int = 0  # 宽度，默认为0
    image_height: int = 0  # 高度，默认为0

    @staticmethod
    def from_model(
        ugi: UserGenerateImageRecord, display_tags: list[str] = []
    ) -> "UserImageDetail":
        return UserImageDetail(
            id=ugi.id,
            prompt=ugi.prompt,
            image_style=ugi.image_style,
            image_quality=ugi.image_quality,
            image_aspect_ratio=ugi.image_aspect_ratio,
            status=ugi.status,
            generate_time=ugi.generate_end_time,
            image_url=ugi.image_url,
            error_message=ugi.error_message,
            display_tags=display_tags,
            image_width=ugi.image_width,
            image_height=ugi.image_height,
        )
