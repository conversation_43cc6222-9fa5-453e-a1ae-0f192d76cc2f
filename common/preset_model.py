from typing import Optional
from pydantic import BaseModel


class SinglePreset(BaseModel):
    identifier: str
    name: str = ""
    role: str = ""
    content: str = ""
    # 是否系统提示
    system_prompt: bool

    injection_position: Optional[int] = None
    injection_depth: Optional[int] = None
    enabled: bool = False
    order: int = 9999


class Preset(BaseModel):
    custom_include_body: str = ""
    top_p: int
    top_k: int
    openai_max_context: int
    openai_max_tokens: int
    temperature: float
    names_behavior: int

    # 例子
    new_example_chat_prompt: str = ""

    scenario_format: str = ""
    personality_format: str = ""

    prompts: list[SinglePreset] = []

    @staticmethod
    def init_by_dict(ps: dict) -> "Preset":
        preset = Preset(
            custom_include_body=ps.get("custom_include_body", ""),
            top_p=ps.get("top_p",1),
            top_k=ps.get("top_k",0),
            openai_max_context=ps.get("openai_max_context",8196),
            openai_max_tokens=ps.get("openai_max_tokens",1000),
            temperature=ps.get("temperature",1),
            names_behavior=ps.get("names_behavior",2),
            new_example_chat_prompt=ps.get("new_example_chat_prompt",""),
            scenario_format=ps.get("scenario_format",""),
            personality_format=ps.get("personality_format",""),
        )
        prompts = ps["prompts"]
        orders = ps["prompt_order"][1]["order"]
        orders_dict = {o["identifier"]: {**o, "order": i} for i, o in enumerate(orders)}
        ret_prompts = [SinglePreset(**x) for x in prompts]
        for p in ret_prompts:
            p.order = orders_dict[p.identifier]["order"]
            p.enabled = bool(orders_dict[p.identifier]["enabled"])
        ret_prompts.sort(key=lambda x: x.order)
        preset.prompts = ret_prompts
        return preset
