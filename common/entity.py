from datetime import timed<PERSON><PERSON>
from enum import Enum
import json
from pydantic import BaseModel
from typing import Generic, List, TypeVar, Optional

from persistence.models.models import RegexRule
from utils import json_util

T = TypeVar('T')
# 废弃
class BaseResponse(BaseModel, Generic[T]):
    msg: str = "OK" # 信息部分
    data: Optional[T] = None  # 数据部分可以是任何类型，根据实际接口返回的类型来定义

class MsgResponse(BaseModel):
    msg: str = "OK" # 信息部分


class RechargeSetting(BaseModel):
    recharge_id: str
    pay_fee: int
    amount: int
    display_fee: str

class Speaker():
    def __init__(self, name: str, url: str, speed: float):
        self.name = name
        self.url = url
        self.speed = speed


class RegexRuleRequest(BaseModel):
    regex: str
    replacement: str
    title: str = ''
    description: str = ''
    rule_id: str = ''
    affects: List[str] = []
    options: List[str] = []
    min_depth: int = -1
    max_depth: int = -1
    enabled: bool = True
    test_reg_txt: str = ''

    def copy_to_model(self, role: RegexRule):
        role.regex = self.regex
        role.replacement = self.replacement
        role.affects = self.affects
        role.title = self.title
        role.description = self.description
        role.options = self.options
        role.min_depth = self.min_depth
        role.max_depth = self.max_depth
        role.enabled = self.enabled
        role.test_reg_txt = self.test_reg_txt


class UserRegexRuleResponse(BaseModel):
    regex: str = ""
    replacement: str = ""
    affects: List[str] = []
    options: List[str] = []
    min_depth: int = -1
    max_depth: int = -1

    @staticmethod
    def from_model(role: RegexRule) -> "UserRegexRuleResponse":
        return UserRegexRuleResponse(
            regex=role.regex,
            replacement=role.replacement,
            affects=json_util.convert_to_list(role.affects),
            options=json_util.convert_to_list(role.options),
            min_depth=role.min_depth,
            max_depth=role.max_depth,
        )


class RegexRuleResponse(BaseModel):
    rule_id: str
    title: str
    description: str = ''
    regex: str
    replacement: str
    enabled: bool
    affects: List[str] = []
    options: List[str] = []
    min_depth: int = -1
    max_depth: int = -1
    test_reg_txt: str = ''

    @staticmethod
    def from_model(role: RegexRule) -> 'RegexRuleResponse':
        return RegexRuleResponse(rule_id=str(role.rule_id),
                                 title=role.title,
                                 description=role.description,
                                 regex=role.regex,
                                 replacement=role.replacement,
                                 enabled=role.enabled,
                                 affects=json_util.convert_to_list(role.affects),
                                 options=json_util.convert_to_list(role.options),
                                 min_depth=role.min_depth,
                                 max_depth=role.max_depth,
                                 test_reg_txt=role.test_reg_txt)

class GiftAward(BaseModel):
    id: str
    amount: int
    title: str
    desc: str
    expire_delta: timedelta

# convert this json to pydantic model
# {"actualDepBlkConfirm":"120","amt":"0.78","areaCodeFrom":"","ccy":"USDT","chain":"USDT-Avalanche C-Chain","depId":"204262170","from":"","fromWdId":"","pTime":"1717058380295","state":"2","subAcct":"","to":"0xaa01a4d08d74f974fac86a2b3f9812c7715cefd9","ts":"1717058138000","txId":"0x4655b56ef8b628881026d24077a130fc2abb39e62214ed00ffb85db22cf10d92","uid":"149357883140775936"}
class OkxDeposit(BaseModel):
    actualDepBlkConfirm: str
    amt: str
    areaCodeFrom: str
    ccy: str
    chain: str
    depId: str
    from_: str = ''
    fromWdId: str = ''
    pTime: str
    state: str
    subAcct: str
    to: str
    ts: str
    txId: str
    uid: str
    uid: str

class RawContract(BaseModel):
    rawValue: str
    address: str
    decimals: int

class TransactionLog(BaseModel):
    address: str
    topics: List[str]
    data: str
    blockNumber: str
    transactionHash: str
    transactionIndex: str
    blockHash: str
    logIndex: str
    removed: bool

class EvmTransaction(BaseModel):
    blockNum: str
    hash: str
    fromAddress: str
    toAddress: str
    value: float
    erc721TokenId: Optional[str] = None
    erc1155Metadata: Optional[str] = None
    asset: str
    category: str
    rawContract: RawContract
    typeTraceAddress: Optional[str] = None
    log: TransactionLog

class UserBioType(Enum):
    WITH_OUR_LINK='with_our_link'
    WITH_COMPETITOR_LINK='with_competitor_link'
    NO_LINK='no_link'
