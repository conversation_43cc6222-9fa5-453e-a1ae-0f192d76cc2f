{"cells": [{"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing file: /Users/<USER>/Documents/tg/sm/CU-V2-20240925035427.xlsx\n", "Processing file: /Users/<USER>/Documents/tg/sm/CU-V2-20240922045939.xlsx\n", "Processing file: /Users/<USER>/Documents/tg/sm/CU-V2-20240923195521.xlsx\n", "Processing file: /Users/<USER>/Documents/tg/sm/CU-V2-20240923092743.xlsx\n", "Processing file: /Users/<USER>/Documents/tg/sm/CU-V2-20240922100807.xlsx\n", "Processing file: /Users/<USER>/Documents/tg/sm/CU-V2-20240923101531.xlsx\n", "Total count of rows with non-empty 'username' and 'phoneNumber': 243377\n", "Count of rows with non-empty 'username': 99040\n", "Count of rows with non-empty 'phoneNumber': 1189\n"]}], "source": ["import os\n", "import pandas as pd\n", "\n", "# Define the directory containing the Excel files\n", "directory = '/Users/<USER>/Documents/tg/sm'\n", "out_file = f'{directory}/output.csv'\n", "\n", "if os.path.exists(out_file):\n", "    os.remove(out_file)\n", "\n", "# Initialize counters\n", "total_count = 0\n", "username_not_empty_count = 0\n", "phone_not_empty_count = 0\n", "\n", "# Iterate over all files in the directory\n", "for filename in os.listdir(directory):\n", "    if filename.endswith('.xlsx') or filename.endswith('.xls'):\n", "        file_path = os.path.join(directory, filename)\n", "        print(f\"Processing file: {file_path}\")\n", "        # Read the Excel file\n", "        df = pd.read_excel(file_path,sheet_name='Users')\n", "        \n", "        # # Filter rows where 'User' and 'phoneNumber' are not empty\n", "        # filtered_df = df.dropna(subset=['User', 'PhoneNumber'])\n", "        \n", "        # 保留 'User' 或 'PhoneNumber' 列中任意一个不为空的行\n", "        filtered_df = df[df['User'].notna() | df['PhoneNumber'].notna()]\n", "\n", "        # Update counters\n", "        total_count += len(df)\n", "        username_not_empty_count += filtered_df['User'].notna().sum()\n", "        phone_not_empty_count += filtered_df['PhoneNumber'].notna().sum()\n", "        \n", "        # Append the filtered data to the output file\n", "        if total_count > 0:\n", "            filtered_df.to_csv(out_file, mode='a', header=not os.path.exists(out_file), index=False)\n", "\n", "# Print the results\n", "print(f\"Total count of rows with non-empty 'username' and 'phoneNumber': {total_count}\")\n", "print(f\"Count of rows with non-empty 'username': {username_not_empty_count}\")\n", "print(f\"Count of rows with non-empty 'phoneNumber': {phone_not_empty_count}\")"]}], "metadata": {"kernelspec": {"display_name": "tavern-server", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}