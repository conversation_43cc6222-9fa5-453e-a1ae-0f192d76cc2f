import base64
import os
import uuid
import requests
import json
import re
import yaml
from io import BytesIO
from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client
from dotenv import load_dotenv

# from common.entity import Speaker
# from persistence.models.models import VoiceSpeaker
load_dotenv()

region = "ap-singapore"
COS_SECRET_ID='IKIDVs31sb8SaXHuJWWRk3Bg2NbgZGl5OWVJ'
COS_SECRET_KEY='g9OXVHrRs8AzbS6Wo36vYyBB1f5KSNfm'
config = CosConfig(Region=region, SecretId=COS_SECRET_ID, SecretKey=COS_SECRET_KEY, Token=None, Scheme='https')
cos_client = CosS3Client(config)

bucket = 'chat-voice-1323765209'

MAX_AGE = 60 * 60 # 1 hour

# speakers = {}
# with open('speakers.yaml', 'r', encoding='utf-8') as f:
#     ss = yaml.safe_load(f)
#     for k, v in ss.items():
#         speaker = Speaker(k, v['url'], v['speed'])
#         speakers[k] = speaker

# def replace_punctuation(text: str) -> str:
#     text = re.sub(r'[.]{3,6}', '……', text)
#     eng_punctuation = '''!"#$%&'()*+,-./:;<=>?@[\\]^_`{|}'''
#     chn_punctuation = '！“＃￥％＆’（）＊＋，－。／：；＜＝＞？＠［＼］＾＿｀｛｜｝'
#     trans_table = str.maketrans(eng_punctuation, chn_punctuation)
#     # 将所有的英文标点替换为中文标点
#     text = text.translate(trans_table)

#     # 中文符号只保留白名单的: '·~，。！？—…《》'
#     forbidden_punctuations = '；／％［｀｛￥～＋＜＿＝＆｜｝”＞＃：＠＾（）’“＊－］＼'
#     text = text.translate(str.maketrans('', '', forbidden_punctuations))
#     return re.sub(r'([·~，。！？—…《》])\1+', r'\1', text)

# def get_voice(text: str, speaker: VoiceSpeaker | None = None) -> str:
#     if speaker is None:
#         speaker = VoiceSpeaker(speaker_id='', api_url='http://43.156.173.20:9000/tts/t2s_v2_bin', speed=1.0, name='default', sample_url='')
#     text = replace_punctuation(text)
#     text = text.replace('\n', ' ')
#     data = {'text': text, 'speaker_id': speaker.speaker_id, 'speed': speaker.speed}
#     response = requests.post(speaker.api_url, data=json.dumps(data), timeout=10)
#     result = json.loads(response.content)
#     return result['data']

# def voice_2_txt(voice: bytes) -> str | None:
#     asr_url = os.environ.get('ASR_URL')
#     voice_file = {'audio': ('voice.wav', BytesIO(voice))}
#     response = requests.post(asr_url, files=voice_file, timeout=10)
#     if not response.ok:
#         return None
#     result = json.loads(response.content)
#     return result['text']

def upload_voice(body: bytes) -> str:
    file_id = uuid.uuid4().hex
    result = cos_client.upload_file_from_buffer(bucket, f'{file_id}.mp3', Body=BytesIO(body),CacheControl=f"max-age={MAX_AGE}")
    print(f'upload voice file DONE: {result}')
    return file_id

if __name__ == '__main__':
    load_dotenv()
    file_path = 'test_10.mp3'
    with open(file_path, 'rb') as f:
        audio_data = f.read()
    file_id = upload_voice(audio_data)
    
    print(f'file_id: {file_id}')