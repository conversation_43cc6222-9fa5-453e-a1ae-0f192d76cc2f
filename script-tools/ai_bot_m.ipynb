{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["id=-1002403997803 type='supergroup' title='幻梦Ai大哥群' username=None first_name=None last_name=None is_forum=True accent_color_id=4 active_usernames=None available_reactions=None background_custom_emoji_id=None bio=None birthdate=None business_intro=None business_location=None business_opening_hours=None can_set_sticker_set=None custom_emoji_sticker_set_name=None description='大哥群的消息转发' emoji_status_custom_emoji_id=None emoji_status_expiration_date=None has_aggressive_anti_spam_enabled=None has_hidden_members=None has_private_forwards=None has_protected_content=None has_restricted_voice_and_video_messages=None has_visible_history=True invite_link='https://t.me/+jMNfRYCx09ozM2M1' join_by_request=None join_to_send_messages=True linked_chat_id=None location=None message_auto_delete_time=None permissions=ChatPermissions(can_send_messages=True, can_send_audios=True, can_send_documents=True, can_send_photos=True, can_send_videos=True, can_send_video_notes=True, can_send_voice_notes=True, can_send_polls=True, can_send_other_messages=True, can_add_web_page_previews=True, can_change_info=True, can_invite_users=True, can_pin_messages=True, can_manage_topics=True, can_send_media_messages=True) personal_chat=None photo=None pinned_message=None profile_accent_color_id=None profile_background_custom_emoji_id=None slow_mode_delay=None sticker_set_name=None unrestrict_boost_count=None max_reaction_count=11 can_send_paid_media=None\n"]}], "source": ["from asyncio import sleep\n", "from aiogram import Bot, Dispatcher, types\n", "from telegram.helpers import mention_html\n", "import asyncio\n", "import csv\n", "\n", "from tortoise import Tor<PERSON>ise\n", "\n", "\n", "#双向转发配置 \n", "TMA_BOT_TOKEN_FF='7533808460:AAHuJ-chiEYu3MQh4KxhHaWNqrYgz2_2E_c'\n", "FORWARD_GROUP_ID=-1002403997803\n", "\n", "# 初始化 <PERSON><PERSON> 和 Dispatcher\n", "bot = Bot(token=TMA_BOT_TOKEN_FF)\n", "\n", "csv_file_path = '/Users/<USER>/Downloads/chat_history_msg_gt50.csv'\n", "\n", "output_file_path = 'output.txt'\n", "# 定义一个发送消息的函数\n", "async def send_message(chat_id: int, text: str,message_thread_id:int):\n", "    await bot.send_message(chat_id=chat_id, text=text,message_thread_id=message_thread_id,parse_mode='HTML')\n", "    await asyncio.sleep(2)  # 添加延迟以避免触发速率限制\n", "\n", "# 定义一个获取群组话题的函数\n", "async def get_group_topics(chat_id: int):\n", "    topics = await bot.get_chat(chat_id=chat_id)\n", "    return topics\n", "\n", "# 示例：发送消息到某个聊天\n", "if __name__ == '__main__':\n", "    \n", "    async def main():\n", "        topics = await get_group_topics(FORWARD_GROUP_ID)\n", "        print(topics)\n", "\n", "\n", "    await main()\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "tavern-server", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}