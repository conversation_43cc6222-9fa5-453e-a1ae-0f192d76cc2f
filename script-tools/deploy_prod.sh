#!/bin/bash

# 使用 SSH 连接到指定的服务器并在指定目录下执行 git pull

set -e  # 如果有任何命令失败，退出脚本

function git_pull() {
    local server_ip=$1
    ssh root@$server_ip <<EOF
        cd /root/tavern-server || exit 1
        git pull || exit 1
EOF
}

case $1 in
    1)
        git_pull "*************"
        ;;
    4)
        git_pull "**************"
        ;;
    *)
        echo "Usage: $0 {1|4}"
        exit 1
        ;;
esac
