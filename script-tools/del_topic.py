from asyncio import sleep
from aiogram import <PERSON><PERSON>, Dispatcher, types
from telegram.helpers import mention_html
import asyncio
import csv

from tortoise import Tortoise

from persistence.models.models import TelegramUser,SXUser


#双向转发配置 
TMA_BOT_TOKEN_FF='7533808460:AAHuJ-chiEYu3MQh4KxhHaWNqrYgz2_2E_c'
FORWARD_GROUP_ID=-1002403997803

# 初始化 Bot 和 Dispatcher
bot = Bot(token=TMA_BOT_TOKEN_FF)

csv_file_path = '/Users/<USER>/Downloads/chat_history_msg_gt50.csv'

output_file_path = 'output.txt'
message_id_file = 'message_id.txt'
# 定义一个发送消息的函数
async def send_message(chat_id: int, text: str,message_thread_id:int):
    await bot.send_message(chat_id=chat_id, text=text,message_thread_id=message_thread_id,parse_mode='HTML')
    await asyncio.sleep(2)  # 添加延迟以避免触发速率限制

async def send_message_block(chat_id: int, text: str):
    await bot.send_message(chat_id=chat_id, text=text)

# 初始化数据库连接
async def init():
    await Tortoise.init(
        db_url = 'mysql://tgbot_wr:abdKATtvoCB620NsfajOz7FK@172.22.16.12:3306/tavern',
        modules = {'models': ['persistence.models.models']}
    )

async def get_user():
    
    await init()
    #读取 CSV 文件
    results = []
    
    tg_message_ids = []
    with open(csv_file_path, 'r') as csv_file:
        csv_reader = csv.DictReader(csv_file)
        for row in csv_reader:
            user_id = int(row['user_id'])

            try:
            # 查询数据库
                user = await  TelegramUser.get(uid=user_id)
                tg_id = user.tg_id
                
                sx_user = await SXUser.get(tg_id=tg_id)

                message_thread_id = sx_user.message_thread_id
                # 保存结果
                results.append(tg_id) 
                tg_message_ids.append(message_thread_id)
            except Exception as e:
                print(e)
                print(f'用户 {user_id} 不存在')
        # 写入 TXT 文件
    with open(output_file_path, 'w') as txt_file:
        for result in results:
            txt_file.write(str(result) + '\n')
    with open(message_id_file, 'w') as txt_file:
        for result in tg_message_ids:
            txt_file.write(str(result) + '\n')
# 示例：发送消息到某个聊天
if __name__ == '__main__':
    
    asyncio.run(get_user())
    