#!/bin/bash

# 读取 CSV 文件并逐行处理
while IFS=, read -r filename filesize; do
  # 拼接完整的 URL
  url="https://chat-voice-1323765209.cos.ap-singapore.myqcloud.com/$filename"

  acclearate_url="https://chat-voice-1323765209.cos.accelerate.myqcloud.com/$filename"

  # 使用 curl 发送 GET 请求并测量延迟时间
  latency=$(curl -o /dev/null -s -w "%{time_total}\n" "$url")

  acclearate_latency=$(curl -o /dev/null -s -w "%{time_total}\n" "$acclearate_url")
  
  echo $filename
  echo $filesize
  echo $latency

  echo $acclearate_latency

  # 输出文件名、文件大小和延迟时间
  echo "文件名: $filename, 文件大小: $filesize, 延迟时间: $latency 秒"
#   result="$filename,$filesize,$latency"

#   echo "$result"
#   echo "$result" >> latency.txt

done < cos-object-list-1715655804266.csv