import requests
import csv
import time

with open('cos-object-list-1715655804266.csv', 'r') as f:
    reader = csv.reader(f)
    for row in reader:
        filename = row[0]  # 假设文件名在CSV文件的第二列
        filesize = row[1]  # 假设文件大小在CSV文件的第三列
        print(filename)
        print(filesize)
          # 拼接完整的 URL
        url = f"https://chat-voice-1323765209.cos.ap-singapore.myqcloud.com/{filename}"
        accelerate_url = f"https://chat-voice-1323765209.cos.accelerate.myqcloud.com/{filename}"

        # 使用 requests 发送 GET 请求并测量延迟时间
        start_time = time.time()
        response = requests.get(url)
        latency = time.time() - start_time

        start_time = time.time()
        response = requests.get(accelerate_url)
        accelerate_latency = time.time() - start_time


        print(latency)
        print(accelerate_latency)

        result = f"{filename},{filesize},{latency},{accelerate_latency}"


        with open('latency.txt', 'a') as outfile:
            outfile.write(result + '\n')