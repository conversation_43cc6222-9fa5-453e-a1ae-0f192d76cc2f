https://chat.655356.xyz {
    tls internal
    reverse_proxy :3000
    log {
        output file chat.log {
            roll_local_time
        }
    }
}

https://clewd.655356.xyz {
    tls internal
    reverse_proxy :3003
    log {
        output file clewd.log {
            roll_local_time
        }
    }
}

http://tavern.655356.xyz {
    reverse_proxy :3001
    log {
        output file tavern.log {
            roll_local_time
        }
    }
}

https://tavern-api.655356.xyz {
    tls internal
    @responseOptions method OPTIONS
    respond @responseOptions 'OK' 200
    header Access-Control-Allow-Origin {http.request.header.Origin}
    header Access-Control-Expose-Headers "Conversation-Id, Message-Id, Message-Version, Human-Message-Id, Current-Language"
    header Access-Control-Allow-Credentials true
    header Access-Control-Allow-Headers "Content-Type, Tg-init-data, Tg-bot-id,Current-Language"
    reverse_proxy localhost:8800 localhost:18800 {
    #reverse_proxy ***********:8802 {
        header_down -Access-Control-Allow-Origin
        header_down -Access-Control-Expose-Headers
        header_down -Access-Control-Allow-Credentials
        header_down -Access-Control-Allow-Headers
        header_down -X-User-Id
        lb_policy round_robin
        lb_retries 1
        lb_try_duration 5s
        handle_response {
            vars http.auth.user.id {rp.header.X-User-Id}
            copy_response
        }
    }
    log {
        output file tavern-api.log {
            roll_local_time
        time_format wall_milli
        }
    }
}

http://usa-tavern.655356.xyz {
    reverse_proxy :3001
    log {
        output file tavern.log {
            roll_local_time
        }
    }
}
https://usa-tavern-api.655356.xyz {
    @responseOptions method OPTIONS
    respond @responseOptions 'OK' 200
    header Access-Control-Allow-Origin {http.request.header.Origin}
    header Access-Control-Expose-Headers "Conversation-Id, Message-Id, Message-Version, Human-Message-Id, Current-Language"
    header Access-Control-Allow-Credentials true
    header Access-Control-Allow-Headers "Content-Type, Tg-init-data, Tg-bot-id,Current-Language"
    reverse_proxy localhost:8800 {
    header_down -Access-Control-Allow-Origin
    header_down -Access-Control-Expose-Headers
    header_down -Access-Control-Allow-Credentials
    header_down -Access-Control-Allow-Headers
    }
    log {
        output file tavern-api.log {
            roll_local_time
        }
    }
    tls {
        dns cloudflare ****************************************
    }
}

https://tavern-admin-api.655356.xyz {
    tls internal
    reverse_proxy :8900 {
    #reverse_proxy ***********:8902 {
    header_down Access-Control-Allow-Origin {http.request.header.Origin}
    header_down Access-Control-Expose-Headers Conversation-Id,Message-Id,Message-Version,Current-Language
    header_down Access-Control-Allow-Credentials true
    header_down Access-Control-Allow-Headers "Content-Type,Authorization,Current-Language"


 }
    log {
        output file tavern-admin-api.log {
            roll_local_time
        }
    }
}


https://tavern-admin.655356.xyz {
    tls internal
    reverse_proxy :8501
    log {
        output file tavern-admin.log {
            roll_local_time
        }
    }
}

https://st.655356.xyz {

    reverse_proxy :3002
    log {
        output file st.log {
            roll_local_time
        }
    }
    tls {
        dns cloudflare -4bNP3FIYY0olssXW-r6vdvWRKshrhK86eN5SFaC
    }
}

https://silly-stage.655356.xyz {
    tls internal
    reverse_proxy {
        to localhost:9880
    }
    log {
        output file silly-stage.log {
            roll_local_time
        }
    }
}

https://gh-hook.655356.xyz {
    tls internal
    reverse_proxy localhost:8903 {
        lb_policy round_robin
        lb_retries 1
        lb_try_duration 5s
    }
    log {
        output file gh-hook-api.log {
            roll_local_time
            time_format wall_milli
        }
    }
}

https://gh-bot-hook.655356.xyz {
    tls internal

    handle /tg_webhook* {
        reverse_proxy :8800
    }
    handle /role_hook* {
        reverse_proxy :8800
    }
    handle / {
        respond "Not Found" 404
    }

    log {
        output file gh-bot-hook-api.log {
            roll_local_time
        }
    }
}