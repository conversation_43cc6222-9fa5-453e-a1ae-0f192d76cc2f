

export NAI_PASSWORD="wYxmmK5FP2v\$C5w"
export NAI_USERNAME="<EMAIL>"

uvicorn aibot_app_server:app --host 0.0.0.0 --port 9200 --workers 1

#主系统
nohup uvicorn aibot_app_server:app --host 0.0.0.0 --port 9200 --workers 1 >aibot-help.log 2>&1 &

#任务系统
nohup uvicorn aibot_task_server:app --host 0.0.0.0 --port 9220 --workers 1 >aibot-task.log 2>&1 &

#admin系统
nohup uvicorn aibot_app_admin_server:app --host 0.0.0.0 --port 9230 --workers 1 >aibot-admin.log 2>&1 &


#imgbot系统
nohup uvicorn image_bot_app_server:app --host 0.0.0.0 --port 9240 --workers 1 >img_bot.log 2>&1 &

nohup uvicorn dxbot_app_server:app --host 0.0.0.0 --port 8443 --workers 1 >dx_bot.log 2>&1 &



curl https://api.telegram.org/bot7473250632:AAF8KHCAGa0V1R0VlXn561cyo0jnzID2Gsk/getWebhookInfo

curl https://api.telegram.org/bot7512150600:AAFhl7lTFb9DgWnlNPZPvV9b2JttBiE2KgA/getWebhookInfo

git remote set-url origin https://github.com/fancy-potato/tavern-server.git

https://bot-help.655356.xyz {

    # 针对 /webhook/hm_image 路径
    handle_path /webhook/hm_image* {
        reverse_proxy :9240
        log {
            output file img_bot.log
        }
    }

    # 其它所有路径
    handle {
        reverse_proxy :9200
        log {
            output file ai_bot.log
        }
    }

}



{
    "image_style_1": {
        "group_id": -1002411997003,
        "topic_id": 2367
    },
    "image_style_2": {
        "group_id": -1002411997003,
        "topic_id": 2368
    },

    "image_style_3": {
        "group_id": -1002411997003,
        "topic_id": 2914
    },
    "image_style_4": {
        "group_id": -1002411997003,
        "topic_id": 2918
    },
    "image_style_5": {
        "group_id": -1002411997003,
        "topic_id": 2914
    },
    "image_style_6": {
        "group_id": -1002411997003,
        "topic_id": 2918
    }
}