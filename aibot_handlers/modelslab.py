from time import sleep
import requests
import json
import time


def modelslab(prompt: str) -> tuple[str, str]:
    api_key = "igqt6ulkGKfuOtxHa2NkEOZ9cVBWiv9HIyC40nBDvgqTbZuTGkFkqivSSVER"
    # api_key = "ugaeurv4kvjo"
    url = "https://modelslab.com/api/v6/images/text2img"
    # url = "https://modelslab.com/api/v1/enterprise/interior/make"
    # url = "https://modelslab.com/api/v6/realtime/text2img"

    headers = {"Content-Type": "application/json"}

    data = {
        "key": api_key,
        # "model_id": "cyberRealistic-xl-v5",
        "model_id": "majicmixrealistic-v7",
        "prompt": prompt,
        "negative_prompt": "cartoon, illustration, anime, painting, CGI, 3D render, low quality, watermark, logo, label",
        "seed": 0,
        "width": 832,
        "height": 1216,
        "guidance_scale": 5,
        "strength": 0.99,
        "num_inference_steps": 35,
        "scheduler": "DPMSolverMultistepScheduler",
        "algorithm_type": "dpmsolver+++",
        "use_karras_sigmas": "yes",
        "safety_checker": False,
        "instant_response": "yes",
    }

    response = requests.post(url, headers=headers, json=data)
    response.raise_for_status()  # Raises an HTTPError for bad responses (4XX or 5XX)
    result = response.json()
    print(json.dumps(result, indent=2))
    if result["status"] == "success":
        return "success", result["output"][0]
    elif result["status"] == "processing":
        fetch_url = result["fetch_result"]
        print("fetch_url:", fetch_url)
        payload = json.dumps({"key": api_key})
        # 等待生成结果
        while True:
            response = requests.post(fetch_url, headers=headers, data=payload)
            result = response.json()
            if result["status"] == "success":
                return "success", result["output"][0]
            elif result["status"] == "processing":
                sleep(0.2)  # 等待0.2秒后重试
                print("Processing... Please wait.")
                continue
    return "error", "Unknown error occurred"


if __name__ == "__main__":
    start_time = time.time()
    status, result = modelslab(
        "women pending over a table, wearing a white shirt, black skirt, and high heels, with a city skyline in the background, realistic style"
    )
    print(f"Status: {status}, Result: {result}")
    print(f"Execution time: {time.time() - start_time:.2f} seconds")
