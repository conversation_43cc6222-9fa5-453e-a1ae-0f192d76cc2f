import json
from datetime import datetime
from logging import Logger, <PERSON><PERSON><PERSON><PERSON>
from os import environ as env
from pathlib import Path
from typing import Any, Optional
import os
import hashlib

from aiohttp import ClientSession
from utils.token_allocator import TokenAllocator

# from msgpackr.constants import UNDEFINED

from novelai_api import NovelAIA<PERSON>
from novelai_api.utils import get_encryption_key


class API:
    """
    Boilerplate for the redundant parts.
    Using the object as a context manager will automatically login using the environment variables
    ``NAI_USERNAME`` and ``NAI_PASSWORD``.

    Usage:

    .. code-block:: python

        async with API() as api:
            api = api.api
            encryption_key = api.encryption_key
            logger = api.logger
            ...  # Do stuff


    A custom base address can be passed to the constructor to replace the default
    (:attr:`BASE_ADDRESS <novelai_api.NovelAI_API.NovelAIAPI.BASE_ADDRESS>`)
    """

    # _username: str
    # _password: str

    _session: ClientSession

    logger: Logger
    api: Optional[NovelAIAPI]

    def __init__(self, request_id, base_address: Optional[str] = None):
        # self._username = env["NAI_USERNAME"]
        # self._password = env["NAI_PASSWORD"]
        # self._token = os.getenv("NAI_TOKEN")
        self._request_id = request_id
        self.allocator = TokenAllocator()
        self._proxy = os.getenv("NAI_PROXY", None)
        self.logger = Logger("NovelAI")
        self.logger.addHandler(StreamHandler())

        self.api = NovelAIAPI(logger=self.logger)
        if base_address is not None:
            self.api.BASE_ADDRESS = base_address

    async def __aenter__(self):
        # 创建带代理的会话
        if self._proxy:
            self._session = ClientSession(proxy=self._proxy)
            self.logger.info(f"使用代理: {self._proxy}")
        else:
            self._session = ClientSession()

        await self._session.__aenter__()
        self.api.attach_session(self._session)
        token = self.allocator.get_token(self._request_id)
        # token = self.get_token(self._request_id)
        token_info = self.allocator.get_token_info(token)
        self.logger.info(
            f"账号: {token_info.get('description')},权重: {token_info.get('weight')}"
        )
        await self.api.high_level.login_with_token(token)

        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self._session.__aexit__(exc_type, exc_val, exc_tb)
