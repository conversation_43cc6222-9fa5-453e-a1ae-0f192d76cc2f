import asyncio
import logging
from aiogram import Bo<PERSON>, Router, types


from common.models.ai_bot_admin.group_msg_bean import TgMsgContentBO
from utils.ims_util import upload_img_to_cos


admin_tg_ids = [
    5359176631,
    7810263302,
    7420374468,
    7601967963,
    7262116276,
    1087968824,
    777000,
]


def check_admin(user_id: int) -> bool:
    return user_id in admin_tg_ids


log = logging.getLogger(__name__)


async def get_tg_file_url(bot: Bot, file_id: str) -> str:
    file = await bot.get_file(file_id)
    file_path = file.file_path
    token = bot.token
    tg_file_url = f"https://api.telegram.org/file/bot{token}/{file_path}"
    image_key = upload_img_to_cos(tg_file_url)
    return image_key


async def extract_bot_msg_details(message: types.Message, bot_f: <PERSON><PERSON>) -> TgMsgContentBO:

    tg_msg_bo = TgMsgContentBO(
        msg_type=message.content_type,
        group_id=message.chat.id,
        msg_id=message.message_id,
        date=message.date,
        tg_id=message.from_user.id,
        mesage_thread_id=message.message_thread_id,
    )

    message_type = message.content_type
    bot = message.bot

    if message.edit_date:
        tg_msg_bo.edit_date = message.edit_date
        tg_msg_bo.gap_time_sec = message.edit_date - round(message.date.timestamp())

    if message_type == "text":
        tg_msg_bo.msg_text = message.text
    elif message_type == "photo":
        tg_msg_bo.file_id = message.photo[-1].file_id if message.photo else None
        tg_msg_bo.file_unique_id = (
            message.photo[-1].file_unique_id if message.photo else None
        )
        tg_msg_bo.msg_text = message.caption
    elif message_type == "video":
        tg_msg_bo.file_id = message.video.file_id if message.video else None
        tg_msg_bo.file_unique_id = (
            message.video.file_unique_id if message.video else None
        )
    elif message_type == "document":
        tg_msg_bo.file_id = message.document.file_id if message.document else None
    elif message_type == "audio":
        tg_msg_bo.file_id = message.audio.file_id if message.audio else None
    elif message_type == "voice":
        tg_msg_bo.file_id = message.voice.file_id if message.voice else None
    elif message_type == "sticker":
        tg_msg_bo.file_id = message.sticker.file_id if message.sticker else None
        tg_msg_bo.file_unique_id = (
            message.sticker.file_unique_id if message.sticker else None
        )
    elif message_type == "animation":
        tg_msg_bo.file_id = message.animation.file_id if message.animation else None
        tg_msg_bo.file_unique_id = (
            message.animation.file_unique_id if message.animation else None
        )

    if tg_msg_bo.file_id and bot:
        tg_msg_bo.file_url = await get_tg_file_url(bot, tg_msg_bo.file_id)
    elif tg_msg_bo.file_id and bot_f:
        tg_msg_bo.file_url = await get_tg_file_url(bot_f, tg_msg_bo.file_id)

    log.info(f"extract_bot_msg_details: {tg_msg_bo}")
    return tg_msg_bo


async def del_bot_msg_after_delay(bot: Bot, chat_id: int, message_id: int, delay: int):
    await asyncio.sleep(delay=delay)
    try:
        logging.info(
            f"Deleting message {message_id} in chat {chat_id} after {delay}seconds"
        )
        await bot.delete_message(chat_id=chat_id, message_id=message_id)
        logging.info(f"Message {message_id} deleted in chat {chat_id}")
    except Exception as e:
        logging.error(f"Error deleting message: {e}")
