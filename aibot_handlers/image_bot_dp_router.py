import asyncio
import re
import logging
import os


from aiogram import Bot, Router, types, F
from aiogram.fsm.context import FSMContext

# from aiogram import LoggingMiddleware
from aiogram.types import (
    CallbackQuery,
    InlineKeyboardButton,
    InlineKeyboardMarkup,
    PreCheckoutQuery,
)

from aiogram.utils.keyboard import Inline<PERSON>eyboardBuilder
from aiogram import Router
from aiogram.filters import (
    Command,
    and_f,
    CommandObject,
    ChatMemberUpdatedFilter,
    IS_MEMBER,
    IS_NOT_MEMBER,
)


from dotenv import load_dotenv
from common.bot_common import RechargeNavCallback
from common.image_bot_model import BotImgUserCheckinBO, GenImageBaseProfileBO
from controllers.bot_hooks.charge_chat import PayMethodCallback, RechargeProductCallback
from persistence.models.models import UserRegisterSource
from services import product_service
from services.account_service import AccountService

from common import loggers

from services.img_bot.bot_img_chekin_service import BotImgGroupCheckinService
from services.img_service import ImageBotService, ImageShareReviewService
from services.img_bot.img_review_service import ImageReviewService
from persistence.models.models_bot_image import ImgGenStatus, BotImgRepeatTaskLog

from controllers.bot_hooks.role_bot import command_recharge

from controllers.bot_hooks.charge_chat import (
    handle_voucher_cancel,
    handle_recharge_product,
    pre_checkout_query_handler,
)

from services.user_service import user_service
from aibot_handlers.image_server import (
    generate_image_v2,
    image_style_dict,
    image_resolution_dict,
    image_privacy_dict,
    shape_dict,
)
from services.img_service import ImageBotService

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s (%(filename)s:%(lineno)d) [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler(), loggers.local_bot_help_handler],
)

log = logging.getLogger(__name__)
# 加载环境变量
load_dotenv()

IMAGE_GROUP_ID = os.getenv("IMAGE_GROUP_ID")
IMAG_BOT_NAME = os.getenv("IMAG_BOT_NAME", "hmimage_bot")

EN_RECHARGE_MESSAGE = "⚠️⚠️Insufficient balance, please recharge and try again～click<a href='https://t.me/{IMAG_BOT_NAME}?start=recharge'>Recharge</a>"

LANGUAGE_CONTENT = {
    "zh": {
        "help_message": "AI作图，只需输入图片描述即可完成图片生成。\n\n1.支持中文和英文输入\n\n...",
        "recharge_message": "余额不足，请充值后再试～",
        "style_options": image_style_dict,
        "resolution_options": image_resolution_dict,
        "shape_options": shape_dict,
        "privacy_options": image_privacy_dict,
    },
    "en": {
        "help_message": "AI image generation, just input a description to create an image.\n\nStyle Settings: {style} \n\n Resolution Settings: {image_res}\n\nAspect Ratio Settings: {shape}\n\n1. Supports both Chinese and English input.\n\n2. Higher resolutions improve the generation of complex images.\n\n3. Pricing: SD 500💎, HD 1000💎, UHD 1500💎\n\n",
        "recharge_message": "Insufficient balance, please recharge and try again～",
        "style_options": {
            "image_style_1": "【✨Anime Style】",
            "image_style_2": "【🦊Furry Style】",
            "image_style_3": "【📸Realistic Style】",
            "image_style_4": "【👾Pixel Style】",
            "image_style_5": "【🏯Ghibli Style】",
            "image_style_6": "【🌃Cyber Style】",
        },
        "resolution_options": {
            "img_resolution_low": "【SD📱】",
            "img_resolution_medium": "【HD🎯】",
            "img_resolution_high": "【UHD💎】",
        },
        "shape_options": {
            "shape_portrait": "【Portrait📱⬆️】",
            "shape_landscape": "【Landscape📺➡️】",
            "shape_square": "【Square🎯⬛】",
        },
        # "privacy_options": {
        #     "image_privacy_public": "📢 Public",
        #     "image_privacy_private": "㊙️ Private +50💎",
        # },
    },
}


FREE_GEN_IMG_TIP = "剩余免费生图次数({cnt}/2)"


def get_language_content(lang: str, key: str):
    return LANGUAGE_CONTENT.get(lang, LANGUAGE_CONTENT["en"]).get(key, "")


def get_help_message_by_lang(
    image_base_profile: GenImageBaseProfileBO, lang: str = "en"
):
    help_message = get_language_content(lang, "help_message")
    image_res_str = get_language_content(lang, "resolution_options").get(
        image_base_profile.resolution, "SD"
    )
    style_str = get_language_content(lang, "style_options").get(
        image_base_profile.style, "【Anime Style】"
    )
    shape_str = get_language_content(lang, "shape_options").get(
        image_base_profile.shape, "Portrait"
    )
    # privacy_str = (
    #     get_language_content(lang, "privacy_options").get("image_privacy_public")
    #     if image_base_profile.privacy == "public"
    #     else get_language_content(lang, "privacy_options").get("image_privacy_private")
    # )
    return help_message.format(
        image_res=image_res_str, style=style_str, shape=shape_str
    )


def get_help_reply_markup_by_lang(lang: str = "en") -> InlineKeyboardMarkup:
    help_key_board = InlineKeyboardBuilder()

    # Style settings
    style_options = get_language_content(lang, "style_options")
    style_key_board = InlineKeyboardBuilder()
    style_key_board.add(
        InlineKeyboardButton(
            text="--Style Settings--" if lang == "en" else "---风格设置---",
            callback_data="set_style",
        )
    )
    for key, value in style_options.items():
        style_key_board.add(InlineKeyboardButton(text=value, callback_data=key))
    style_key_board.adjust(1, 3)

    # Resolution settings
    resolution_options = get_language_content(lang, "resolution_options")
    resolution_key_board = InlineKeyboardBuilder()
    resolution_key_board.add(
        InlineKeyboardButton(
            text="--Resolution Settings--" if lang == "en" else "---清晰度设置---",
            callback_data="set_resolution",
        )
    )
    for key, value in resolution_options.items():
        resolution_key_board.add(InlineKeyboardButton(text=value, callback_data=key))
    resolution_key_board.adjust(1, 3)

    # Aspect ratio settings
    shape_options = get_language_content(lang, "shape_options")
    shape_key_board = InlineKeyboardBuilder()
    shape_key_board.add(
        InlineKeyboardButton(
            text="--Aspect Ratio Settings--" if lang == "en" else "--长宽比设置--",
            callback_data="set_shape",
        )
    )
    for key, value in shape_options.items():
        shape_key_board.add(InlineKeyboardButton(text=value, callback_data=key))
    shape_key_board.adjust(1, 3)

    # Privacy settings
    # privacy_options = get_language_content(lang, "privacy_options")
    # privacy_key_board = InlineKeyboardBuilder()
    # privacy_key_board.add(
    #     InlineKeyboardButton(
    #         text="--Privacy Settings--" if lang == "en" else "--隐私设置--",
    #         callback_data="set_privacy",
    #     )
    # )
    # for key, value in privacy_options.items():
    #     privacy_key_board.add(InlineKeyboardButton(text=value, callback_data=key))
    # privacy_key_board.adjust(1, 2)

    help_key_board.attach(style_key_board).attach(resolution_key_board).attach(
        shape_key_board
    )

    return help_key_board.as_markup()


# def get_help_message(image_base_profile: GenImageBaseProfileBO):
#     help_message = (
#         "AI作图，只需输入图片描述即可完成图片生成。\n\n1.支持中文和英文输入\n\n2.高清模式以上有助于提升复杂图片的生成效果\n\n3. 价格说明：标清500💎，高清1000💎，超清1500💎\n\n4.偷走咒语☔️50💎,隐私模式50💎\n\n\n"
#         "清晰度(BOT): {image_res}\n"
#         "风格(BOT):{style}\n"
#         "长宽比设置: {shape}\n\n\n"
#         "🥷 隐私模式:\n"
#         "{privacy}\n\n"
#     )
#     image_res_str = image_resolution_dict.get(image_base_profile.resolution, "标清")

#     if image_base_profile.resolution == "img_resolution_low":
#         image_res_str += "（500💎）"
#         if image_base_profile.small_free_gen_count > 0:
#             image_res_str += image_base_profile.small_free_gen_str
#     elif image_base_profile.resolution == "img_resolution_medium":
#         image_res_str += "（1000💎)"
#     elif image_base_profile.resolution == "img_resolution_high":
#         image_res_str += "（1500💎）"

#     style_str = image_style_dict.get(image_base_profile.style, "【二次元风】")

#     shape_str = shape_dict.get(image_base_profile.shape, "竖图")
#     privacy_str = (
#         "🔗 自動分享到群"
#         if image_base_profile.privacy == "public"
#         else "「🥷 隱私模式」不分享到群（+50 💎)"
#     )
#     return help_message.format(
#         image_res=image_res_str, style=style_str, privacy=privacy_str, shape=shape_str
#     )


def create_retry_button(
    task_id: int, prompt: str, lang: str = "zh"
) -> InlineKeyboardMarkup:
    log.info(f"create_retry_button: {task_id},{lang}")
    # 创建键盘并添加按钮
    retry_keyboard = InlineKeyboardBuilder()
    if lang == "en":
        txt = "🪄Retry Drawing"
        same_style_txt = "🎨One-Click Same Style"
    else:
        txt = "🎰继续抽卡"
        same_style_txt = "🎨一键同款"

    # 创建一个一键同款按钮
    same_button = InlineKeyboardButton(
        text=same_style_txt,
        switch_inline_query_current_chat=f"{prompt}",
    )

    # 创建一个跳转按钮
    button = InlineKeyboardButton(
        text=txt,
        callback_data=f"retry_task_{task_id}",
    )
    retry_keyboard.add(same_button).add(button)
    retry_keyboard.adjust(1, 1)

    return retry_keyboard.as_markup()


def get_help_reply_markup() -> InlineKeyboardMarkup:
    # 生成一个帮助键盘
    help_key_board = InlineKeyboardBuilder()

    # 生成一个风格键盘
    style_key_board = InlineKeyboardBuilder()
    style_key_board.add(
        InlineKeyboardButton(text="---风格设置---", callback_data="set_style")
    )
    for key, value in image_style_dict.items():
        style_key_board.add(InlineKeyboardButton(text=value, callback_data=key))

    style_key_board.adjust(1, 2)

    # 生成一个清晰度键盘
    resolution_key_board = InlineKeyboardBuilder()
    resolution_key_board.add(
        InlineKeyboardButton(text="---清晰度设置---", callback_data="set_resolution")
    )

    for key, value in image_resolution_dict.items():
        resolution_key_board.add(InlineKeyboardButton(text=value, callback_data=key))

    resolution_key_board.adjust(1, 3)

    # 生成一个长宽比键盘
    shape_key_board = InlineKeyboardBuilder()
    shape_key_board.add(
        InlineKeyboardButton(text="---长宽比设置---", callback_data="set_shape")
    )
    for key, value in shape_dict.items():
        shape_key_board.add(InlineKeyboardButton(text=value, callback_data=key))

    shape_key_board.adjust(1, 3)

    # 生成一个隐私风格键盘
    privacy_key_board = InlineKeyboardBuilder()
    privacy_key_board.add(
        InlineKeyboardButton(text="--隐私设置--", callback_data="set_privacy")
    )

    for key, value in image_privacy_dict.items():
        privacy_key_board.add(InlineKeyboardButton(text=value, callback_data=key))

    privacy_key_board.adjust(1, 2)

    help_key_board.attach(style_key_board).attach(resolution_key_board).attach(
        privacy_key_board
    ).attach(shape_key_board)

    return help_key_board.as_markup()


def get_resolution(resolution: str):
    return


def check_image_prompt_args(prompt: str):
    return True


async def del_msg_delay(bot: Bot, chat_id: int, message_id: int, delay: int = 60):
    log.info(f"del_msg_delay: {chat_id},{message_id},{delay}")
    await asyncio.sleep(delay)
    try:
        await bot.delete_message(chat_id=chat_id, message_id=message_id)
    except Exception as e:
        log.error(f"del_msg_delay error: {e}", exc_info=True)


# 生成图片并发送
async def gen_and_send_image(
    tg_id: int,
    prompt: str,
    message: types.Message,
    del_pre_msg_id: int = 0,
    user_id: int = 0,
    lang: str = "zh",
    photo_product=None,
    free_times_pripority: bool = False,
):

    log.info(f"gen_and_send_image:{tg_id},{prompt},{message.text},user_id:{user_id}")

    basic_profile = await ImageBotService.get_basic_profile(tg_id)
    style = basic_profile.style
    resolution = basic_profile.resolution
    # check 有无生成任务
    is_have_progress = await ImageBotService.check_in_progress_img_gen_task(tg_id)
    if is_have_progress:
        await message.reply("上一个图片正在创作中,请等待上一个任务完成...")
        return
    # 生成图片
    log.info(
        f"gen_and_send_image:{tg_id},{prompt},{basic_profile},{style},{resolution}"
    )

    gen_task = await ImageBotService.add_img_gen_task(tg_id, prompt, basic_profile)

    # image_result = await generate_image(
    #     req_id=gen_task.id, prompt=prompt, style=style, resolution=resolution_v
    # )

    image_result = await generate_image_v2(
        req_id=gen_task.id,
        prompt=prompt,
        style=style,
        resolution=resolution,
        shape=basic_profile.shape,
        tg_id=tg_id,
        user_id=user_id,
    )

    log.info(f"gen_and_send_image:{image_result}")

    await ImageBotService.update_img_gen_task(
        gen_task.id, ImgGenStatus.COMPLETED, image_result
    )

    # 发送图片
    image_default = ""
    if image_result and image_result.get("code") == 200:

        image_url = image_result.get("image_url", image_default)
        # del pre_msg
        if del_pre_msg_id:
            try:
                await message.bot.delete_message(
                    chat_id=message.chat.id, message_id=del_pre_msg_id
                )
            except Exception as e:
                log.error(f"del pre_msg error: {e}", exc_info=True)

        try:
            # 发送图片
            reply_markup = create_retry_button(
                task_id=gen_task.id, prompt=prompt, lang=lang
            )
            sent_msg = await message.reply_photo(
                photo=image_url, reply_markup=reply_markup
            )

            if sent_msg:
                log.info(f"send_image success: {sent_msg.message_id}")
                # 更新任务的 sent_msg_id
                await ImageBotService.update_sent_msg_id(
                    gen_task.id, sent_msg.message_id
                )
        except Exception as e:
            log.error(f"send_image error: {e}", exc_info=True)
            return

        # 扣费逻辑
        try:
            # 求一个id 的负数来区分role
            role_id = -gen_task.id

            pay_order_flag = True

            if free_times_pripority:
                # 如果有免费次数，优先使用免费次数
                log.info(f"用户有免费生成次数,优先扣取免费次数")

                free_suc = await BotImgGroupCheckinService.decrease_free_gen_img_count(
                    user_id=user_id, task_id=role_id
                )
                if free_suc:
                    log.info(f"用户免费生成次数扣除成功{user_id}, {role_id}")
                    pay_order_flag = False
            
            if pay_order_flag:
                log.info(f"用户没有免费生成次数,扣除钻石{user_id}, {role_id}")
                # 扣除钻石
                await AccountService.create_pay_order(user_id=user_id, product=photo_product, role_id=role_id)  # type: ignore
        except Exception as e:
            log.error(f"create_pay_order error: {user_id},{e}", exc_info=True)

        if lang == "en":
            # en 版不支持群组分享功能
            return
        # 同时转发图片到群里
        if basic_profile.privacy == "public":
            try:
                log.info(f"用户隐私设置为公开,转发图片到群里:{image_result}")

                # 添加审核任务
                await ImageReviewService.create_review_task(
                    tg_id=tg_id,
                    user_id=user_id,
                    task_id=gen_task.id,
                    prompt=prompt,
                    nai_prompt=image_result.get("prompt", ""),
                    image_url=image_url,
                )

                # bot = message.bot
                # asyncio.create_task(
                #     send_photo_to_share_group(
                #         bot=bot,  # type: ignore
                #         photo_url=image_url,
                #         style=style,
                #         replay_markup=create_copy_prompt_button(
                #             bot_username=IMAG_BOT_NAME,
                #             copy_prompt_str="copy_prompt_" + str(gen_task.id),
                #         ),
                #     )
                # )
            except Exception as e:
                log.error(f"send_image error: {e}", exc_info=True)
        else:
            # 私密钻石扣费50钻
            log.info(f"用户隐私设置为私密,不转发图片到群里{role_id}")
            # share_product = await product_service.get_photo_bot_share_product()
            # if not share_product:
            #     log.error(f"Photo Bot Share Product Not Found")
            #     return
            # try:
            #     role_id = -gen_task.id
            #     await AccountService.create_pay_order(
            #         user_id=user_id, product=share_product, role_id=role_id
            #     )  # type: ignore
            # except Exception as e:
            #     log.error(f"create_pay_order error: {user_id},{e}", exc_info=True)
    else:
        log.error(f"gen_and_send_image error: {image_result}")

    # 发送推广消息
    asyncio.create_task(
        ImageBotService.check_and_send_promo_msg(
            bot=message.bot,  # type: ignore
            bot_id=message.bot.id,  # type: ignore
            tg_id=tg_id,
        )
    )


def create_image_bot_router() -> Router:
    image_bot_router = Router()

    async def log_repeat_task_log(
        tg_id: int, task_id: int, log_st: str, user_id: int = 0
    ):
        log.info(f"log_repeat_task_log: {tg_id}, {user_id}, {task_id}, {log_st}")
        try:
            await BotImgRepeatTaskLog.create(
                tg_id=tg_id,
                user_id=user_id,
                task_id=task_id,
                log_st=log_st,
            )
        except Exception as e:
            log.error(f"create BotImgRepeatTaskLog error: {e}", exc_info=True)

    # 处理用户 join
    @image_bot_router.chat_join_request()
    async def handle_chat_join_request(chat_join_request: types.ChatJoinRequest):
        log.info(f"handle_chat_join_request: {chat_join_request}")
        bot = chat_join_request.bot
        # 处理群组加入请求
        if chat_join_request.from_user.is_bot:
            log.info("Bot is not allowed to join the group.")
            return

        # 检查用户是否已注册
        user = await user_service.get_user_by_tg_id(chat_join_request.from_user.id)

        if user is None:
            log.info(
                f"New user request to join group: {chat_join_request.from_user.id}, {chat_join_request.from_user.first_name}"
            )
            # 如果用户未注册，拒绝加入请求
            await bot.decline_chat_join_request(
                chat_id=chat_join_request.chat.id,
                user_id=chat_join_request.from_user.id,
            )
        else:
            log.info(
                f"User already exists: {user.id}, tg_id: {chat_join_request.from_user.id}"
            )
            # 如果用户已注册，接受加入请求
            await bot.approve_chat_join_request(
                chat_id=chat_join_request.chat.id,
                user_id=chat_join_request.from_user.id,
            )

    # 处理用户加入群组的事件
    @image_bot_router.chat_member(ChatMemberUpdatedFilter(IS_NOT_MEMBER >> IS_MEMBER))
    async def handle_user_joined(chat_member_updated: types.ChatMemberUpdated):
        log.info(f"handle_user_joined: {chat_member_updated}")
        user = chat_member_updated.new_chat_member.user
        chat = chat_member_updated.chat

        log.info(f"join_group {user.full_name} |{chat.id}|{user.id}|加入群组！")

        # check_start_msg_exists = await ImageBotService.check_join_group(
        #     tg_id=user.id, bot_id=bot.id
        # )

        # if not check_start_msg_exists:
        #     log.info(f"用户 {user.id} 未记录/start消息，剔除用户")
        #     # 记录用户的 /start 消息
        #     until_date = int(time.time()) + 60 * 5  # 5分钟加群

        #     await bot.ban_chat_member(
        #         chat_id=chat.id,
        #         user_id=user.id,
        #         until_date=until_date,
        #     )

    # @image_bot_router.inline_query()
    # async def handle_inline_query(inline_query: InlineQuery, bot: Bot, state: FSMContext) -> None:
    #     log.info(f"handle_inline_query: {inline_query.query}, user_id: {inline_query.from_user.id}")
    #     query = inline_query.query.strip()  # 获取用户输入的文本
    #     if not query:
    #         # 如果没有输入内容，返回空结果
    #         await inline_query.answer(
    #             results=[],
    #             cache_time=0,
    #             switch_pm_text="请输入内容以生成图片",
    #             switch_pm_parameter="start"
    #         )
    #         return

    #     # 示例：根据用户输入生成结果

    #     # 返回结果
    #     await inline_query.answer(results, cache_time=0)
    @image_bot_router.message(Command("start"))
    async def handle_new_start_reg_message(
        message: types.Message, command: CommandObject, bot: Bot, state: FSMContext
    ) -> None:
        tg_id = message.from_user.id
        logging.info(f"command_start_handler args: {command.args}, tg_id: {tg_id}")
        msg_text = command.args or ""
        await ImageBotService.add_start_msg_log(
            tg_id=tg_id, msg_text=msg_text, bot_id=bot.id, msg_id=message.message_id
        )
        user = await user_service.get_user_by_tg_id(message.from_user.id)
        params = {}
        is_invitation = False
        match = re.match(r"u_(\d+)", command.args or "")
        if match:
            # 如果命令参数以 u_ 开头，提取邀请人 ID
            uid = match.group(1)
            uid = int(uid)
            params["inviter"] = uid
            is_invitation = True

        inviter_user_id = None
        role_id = 0
        new_user = False
        register_source = UserRegisterSource.TMA
        if user is None:
            log.info(
                f"新用户注册: {message.from_user.id}, {message.from_user.first_name}, {message.from_user.last_name}, {message.from_user.username},params: {params}"
            )
            if is_invitation:
                inviter_user_id = params["inviter"]
                _, user = await user_service.register_tg_with_invitation(
                    message.from_user.id,
                    message.from_user.first_name,
                    message.from_user.last_name,
                    message.from_user.username,
                    inviter_user_id,
                    register_source,
                    message.chat.id,
                    role_id,
                    message.from_user.is_premium or False,
                    bot.id,
                )
            else:
                user = await user_service.register_by_tg_with_start_role(
                    message.from_user.id,
                    message.from_user.first_name,
                    message.from_user.last_name,
                    message.from_user.username,
                    message.chat.id,
                    register_source,
                    role_id,
                    message.from_user.is_premium or False,
                    bot.id,
                )
        else:
            log.info(f"用户已存在: {user.id}, tg_id: {tg_id}")

        # 处理 /start 后的参数,/start copy_prompt_123
        copy_p_match = re.match(r"copy_prompt_(\d+)", command.args or "")

        recharge_match = re.match(r"recharge", command.args or "")

        one_try_match = re.match(r"one_try_(\d+)", command.args or "")
        if copy_p_match:
            # 处理复制提示词的逻辑
            log.info(f"copy_prompt_id:{command.args}")
            copy_prompt_id = copy_p_match.group(1)

            await ImageBotService.handle_copy_prompt(
                copy_prompt_id=int(copy_prompt_id),
                message=message,
                bot=bot,
                user_id=user.id,
                image_bot_name=IMAG_BOT_NAME,
            )
        elif recharge_match:
            # 处理充值的逻辑
            log.info(f"recharge:{command.args}")
            await command_recharge(message=message, bot=bot, state=state)
        elif one_try_match:
            # 处理一键同款的逻辑
            log.info(f"one_try_match:{command.args}")
            one_try_id = one_try_match.group(1)
            await ImageBotService.handle_one_try(
                task_id=int(one_try_id),
                message=message,
                bot=bot,
                user_id=user.id,
            )
        else:
            log.info(f"handle_new_start_reg_message: {message.text}, {tg_id}")
            # 发送欢迎消息
            try:
                invite_link_txt, link = (
                    await ImageShareReviewService.get_group_invite_link_txt(
                        bot_id=bot.id
                    )
                )

                if invite_link_txt != "" and link != "":
                    invite_text = f"{invite_link_txt}{link}"
                    invite_link_msg = await bot.send_message(
                        chat_id=message.chat.id, text=invite_text, parse_mode="HTML"
                    )

                    asyncio.create_task(
                        del_msg_delay(
                            bot=bot,  # type: ignore
                            chat_id=message.chat.id,
                            message_id=invite_link_msg.message_id,
                            delay=60 * 2,  # 2分钟后删除
                        )
                    )

                start_msg = await ImageBotService.get_img_bot_start_msg(bot_id=bot.id)
                sent_msg = await bot.send_message(
                    chat_id=message.chat.id,
                    text=start_msg,
                    parse_mode="HTML",
                    allow_sending_without_reply=True,
                )

                asyncio.create_task(
                    del_msg_delay(bot=bot, chat_id=message.chat.id, message_id=sent_msg.message_id, delay=60)  # type: ignore
                )
                # 发送一个随机图片
                s_img_url, desc_txt = await ImageBotService.get_start_random_image(
                    bot_id=bot.id
                )

                if s_img_url != "":
                    sent_photo = await bot.send_photo(
                        chat_id=message.chat.id,
                        photo=s_img_url,
                        caption=desc_txt,
                        allow_sending_without_reply=True,
                        parse_mode="HTML",
                    )

                    asyncio.create_task(
                        del_msg_delay(bot=bot, chat_id=message.chat.id, message_id=sent_photo.message_id, delay=60)  # type: ignore
                    )

            except Exception as e:
                log.error(
                    f"handle_new_start_reg_message error: {e},{message},{tg_id})",
                    exc_info=True,
                )

    @image_bot_router.message(and_f(Command("help"), (F.chat.type == "private")))
    async def send_image_help(message: types.Message):

        log.info(f"send_image_help:{message.text}")
        bot_id = message.bot.id  # type: ignore
        tg_id = message.from_user.id # type: ignore

        basic_img_profile = await ImageBotService.get_basic_profile(
            message.from_user.id
        )
        free_cnt = await BotImgGroupCheckinService.get_free_gen_img_count(
            message.from_user.id
        )
        basic_img_profile.small_free_gen_count = free_cnt
        basic_img_profile.small_free_gen_str = FREE_GEN_IMG_TIP.format(cnt=free_cnt)
        
        help_message = await ImageBotService.get_help_message(
            bot_id=bot_id,
            tg_id=tg_id,
            image_base_profile=basic_img_profile
        )
        log.info(f"send_image_help: {help_message}")
        help_reply_markup = get_help_reply_markup()

        try:
            bot = message.bot
            sent_msg = await bot.send_message(
                message.from_user.id,
                help_message,
                reply_markup=help_reply_markup,
                parse_mode="HTML",
            )
            # 删除消息
            asyncio.create_task(
                del_msg_delay(
                    bot=bot,  # type: ignore
                    chat_id=message.from_user.id,
                    message_id=sent_msg.message_id,
                    delay=60 * 2,  # 2分钟后删除
                )
            )
        except Exception as e:
            log.error(f"send_image_help error: {e}", exc_info=True)

    # 处理bot中的checkin签到命令
    @image_bot_router.message(and_f(Command("checkin"), (F.chat.type == "private")))
    async def send_checkin_private_tip(message: types.Message, bot: Bot):
        log.info(f"send_checkin_private_tip:{message.text}")

        # invite_link
        _, invite_link = await ImageShareReviewService.get_group_invite_link_txt(
            bot.id
        )
        await ImageBotService.send_checkin_group_tip(
            bot=bot,
            message=message,
            invite_link=invite_link 
        )

    @image_bot_router.message(
        and_f(
            Command("help"), ((F.chat.type == "group") | (F.chat.type == "supergroup"))
        )
    )
    async def send_image_help_group(message: types.Message):

        log.info(f"send_image_help:{message.text}")

        try:
            await message.reply("请在bot内使用该功能")
        except Exception as e:
            log.error(f"send_image_help error: {e}", exc_info=True)

    # 通过私聊bot的text作为prompt生成图片
    @image_bot_router.message(F.text & (F.chat.type == "private"))
    async def gen_image_by_text_promt(
        message: types.Message, bot: Bot, state: FSMContext
    ):

        log.info(
            f"gen_image_by_text_promt: {message.text}, user_id: {message.from_user.id}"
        )
        prompt = message.text if message.text else ""

        # 使用正则表达式替换以 @ 开头，且以 _bot 或 Bot 结尾的内容
        bot_name_pattern = r"@\w+(_bot|Bot)\b"
        prompt = re.sub(bot_name_pattern, "", prompt).strip()  # 替换并去掉多余的空格

        log.info(f"gen_image_by_text_promt: {prompt}")

        tg_id = message.from_user.id
        log.info(f"gen_image_by_text_promt:{message.text},tg_id:{tg_id}")
        try:

            user = await user_service.get_user_by_tg_id(tg_id)
            if not user:
                user = await user_service.register_by_tg(
                    tg_user_id=tg_id,
                    first_name=message.from_user.first_name,
                    last_name=message.from_user.last_name,
                    user_name=message.from_user.username,
                )
            balance = await AccountService.get_total_balance(user.id)
            free_cnt = 0
            basic_profile = await ImageBotService.get_basic_profile(tg_id)
            log.info(f"gen_image_by_text_promt: {tg_id}, {prompt}, {basic_profile}")
            mid = basic_profile.resolution
            
            if basic_profile.privacy == "private":
                mid = f"{mid}_private"
            
            photo_product = await product_service.get_online_photo_bot_product(
                mid=mid
            )
            if not photo_product:
                log.error(f"Photo Product Not Found{basic_profile}")
                await message.reply("Photo Product Not Found")
                return
            # 检查用户是否有免费生成次数
            if basic_profile.resolution == "img_resolution_low":
                # 低清免费生成次数
                free_cnt = await BotImgGroupCheckinService.get_free_gen_img_count(tg_id)

            if balance < photo_product.price and free_cnt <= 0:
                # todo: 余额不足
                log.warning(f"余额不足,当前余额:{balance},最低充值:{photo_product.price}")
                await message.reply(
                    f"余额不足,当前余额:{balance},请充值之后再试～",
                    allow_sending_without_reply=True,
                )
                await command_recharge(message=message, bot=bot, state=state)
            else:
                sent_msg = await message.reply("正在创作中,请稍等...")
                asyncio.create_task(
                    gen_and_send_image(
                        tg_id,
                        prompt,
                        message=message,
                        del_pre_msg_id=sent_msg.message_id,
                        user_id=user.id,
                        photo_product=photo_product,
                        free_times_pripority=free_cnt > 0,
                    )
                )

        except Exception as e:
            log.error(f"send_image_promt error: {e}", exc_info=True)

    # 处理群组的签到消息,通过签到机器人发送
    @image_bot_router.message(
        F.text & ((F.chat.type == "group") | (F.chat.type == "supergroup"))
    )
    async def handle_group_checkin_msg(
        message: types.Message, bot: Bot, state: FSMContext
    ):
        tg_id = message.from_user.id
        group_id = message.chat.id
        log.info(
            f"handle_group_checkin_msg: {message.text}, tg_id: {tg_id}, group_id: {group_id}"
        )

        if message.text in ["签到", "簽到", "打卡"]:
            # 处理签到逻辑
            log.info(f"Group checkin message detected: {message.text}")

            tg_nickname = (
                message.from_user.full_name if message.from_user.full_name else "用户"
            )

            message_thread_id = (
                message.message_thread_id if message.message_thread_id else 0
            )
            user_checkin_data = BotImgUserCheckinBO(
                bot_id=bot.id,
                tg_id=tg_id,
                group_id=group_id,
                tg_nickname=tg_nickname,
                message_thread_id=message_thread_id,
                msg_id=message.message_id,
                msg_txt=message.text,
            )

            await BotImgGroupCheckinService.user_checkin(user_checkin_data)
        else:

            return

    @image_bot_router.callback_query(lambda c: c.data.startswith("retry_task_"))
    async def retry_draw_image_task(call: types.CallbackQuery, bot: Bot):
        log.info(f"retry_draw_image_task:{call.data}")
        # 处理重试绘图任务的逻辑
        try:
            task_id = int(call.data.split("_")[-1])
            log.info(f"retry_draw_image_task: {task_id}")
            # 获取任务信息
            img_gen_task = await ImageBotService.get_img_gen_task_by_id(task_id)
            if not img_gen_task:
                log.error(f"retry_draw_image_task: Task {task_id} not found")
                await call.answer("", show_alert=True)

                await log_repeat_task_log(
                    tg_id=call.from_user.id,
                    task_id=task_id,
                    log_st="Task Not Found",
                )

                return

            # 检查用户余额
            user = await user_service.get_user_by_tg_id(call.from_user.id)
            balance = await AccountService.get_total_balance(user.id)
            basic_profile = await ImageBotService.get_basic_profile(call.from_user.id)
            mid = basic_profile.resolution
            if basic_profile.privacy == "private":
                mid = f"{mid}_private"
            photo_product = await product_service.get_online_photo_bot_product(
                mid=mid
            )
            if not photo_product:
                log.error(f"Photo Product Not Found{basic_profile}")
                await call.answer("Photo Product Not Found", show_alert=True)
                return

            if balance < photo_product.price:
                log.warning(
                    f"余额不足,当前余额:{balance},最低充值:{photo_product.price}"
                )
                await call.answer(
                    f"余额不足,当前余额:{balance},请充值之后再试～", show_alert=True
                )

                await log_repeat_task_log(
                    tg_id=call.from_user.id,
                    task_id=task_id,
                    log_st="Balance Not Enough",
                    user_id=user.id,
                )
                await command_recharge(message=call.message, bot=bot, state=None)  # type: ignore
                return

            # 重新生成图片
            sent_msg = await call.message.reply("正在创作中,请稍等...")

            await log_repeat_task_log(
                tg_id=call.from_user.id,
                task_id=task_id,
                log_st="success",
                user_id=user.id,
            )
            asyncio.create_task(
                gen_and_send_image(
                    tg_id=call.from_user.id,
                    prompt=img_gen_task.prompt,
                    message=call.message,  # type: ignore
                    del_pre_msg_id=sent_msg.message_id,
                    user_id=user.id,
                    photo_product=photo_product,
                )
            )
        except Exception as e:
            log.error(f"retry_draw_image_task error: {e}", exc_info=True)
            await call.answer("重试绘图任务失败，请稍后再试", show_alert=True)

    @image_bot_router.callback_query(lambda c: c.data.startswith("img_resolution_"))
    async def set_resolution(call: types.CallbackQuery):

        log.info(f"set_resolution:{call.data}")
        bot_id = call.bot.id  # type: ignore
        tg_id = call.from_user.id
        image_resolution = (
            call.data if call.data else list(image_resolution_dict.keys())[0]
        )
        b_profile = await ImageBotService.update_b_profile_resolution(
            call.from_user.id, image_resolution
        )
        help_message = await ImageBotService.get_help_message(
            bot_id=bot_id,
            tg_id=tg_id,
            image_base_profile=b_profile
        )
        help_reply_markup = get_help_reply_markup()

        try:
            bot = call.bot
            msg_id = call.message.message_id
            await bot.edit_message_text(
                text=help_message,
                chat_id=call.from_user.id,
                message_id=msg_id,
                reply_markup=help_reply_markup,
                parse_mode="HTML",
            )
        except Exception as e:
            log.error(f"set_resolution error: {e}", exc_info=True)
        finally:
            await call.answer()

    @image_bot_router.callback_query(lambda c: c.data.startswith("image_style_"))
    async def set_imag_style(call: types.CallbackQuery):

        log.info(f"set_imag_style:{call.data}")
        bot_id = call.bot.id  # type: ignore
        image_style = call.data if call.data else ""
        img_b_profile = await ImageBotService.update_b_profile_style(
            call.from_user.id, image_style
        )
        help_message = await ImageBotService.get_help_message(
            bot_id=bot_id,
            tg_id=call.from_user.id,
            image_base_profile=img_b_profile
            )
        help_reply_markup = get_help_reply_markup()

        try:
            bot = call.bot
            msg_id = call.message.message_id
            await bot.edit_message_text(
                text=help_message,
                chat_id=call.from_user.id,
                message_id=msg_id,
                reply_markup=help_reply_markup,
                parse_mode="HTML",
            )
        except Exception as e:
            log.error(f"set_style error: {e}", exc_info=True)
        finally:
            await call.answer()

    @image_bot_router.callback_query(lambda c: c.data.startswith("image_privacy_"))
    async def set_privacy(call: types.CallbackQuery):
        log.info(f"set_privacy:{call.data}")
        
        bot_id = call.bot.id  # type: ignore

        image_privacy = call.data if call.data else ""

        # 更新用户隐私设置
        if image_privacy == "image_privacy_public":
            image_privacy = "public"
        elif image_privacy == "image_privacy_private":
            image_privacy = "private"
        else:
            log.error(f"set_privacy error: {image_privacy} not in {image_privacy_dict}")
            image_privacy = "public"

        img_b_profile = await ImageBotService.update_b_profile_privacy(
            call.from_user.id, image_privacy
        )
        help_message = await ImageBotService.get_help_message(
            bot_id=bot_id,
            tg_id=call.from_user.id,
            image_base_profile=img_b_profile
        )
        help_reply_markup = get_help_reply_markup()

        try:
            bot = call.bot
            msg_id = call.message.message_id
            await bot.edit_message_text(
                text=help_message,
                chat_id=call.from_user.id,
                message_id=msg_id,
                reply_markup=help_reply_markup,
                parse_mode="HTML",
            )
        except Exception as e:
            log.error(f"set_privacy error: {e}", exc_info=True)
        finally:
            await call.answer()

    @image_bot_router.callback_query(lambda c: c.data.startswith("shape_"))
    async def set_shape(call: types.CallbackQuery):
        log.info(f"set_shape:{call.data}")
        bot_id = call.bot.id  # type: ignore
        # 定义形状字典

        # 处理形状设置逻辑
        shape = call.data if call.data else "shape_portrait"
        # 更新用户形状设置

        if shape not in shape_dict:
            log.error(f"set_shape error: {shape} not in {shape_dict}")
            shape = "shape_portrait"
        img_b_profile = await ImageBotService.update_b_profile_shape(
            call.from_user.id, shape
        )
        help_message = await ImageBotService.get_help_message(
            bot_id=bot_id,
            tg_id=call.from_user.id,
            image_base_profile=img_b_profile
        )
        help_reply_markup = get_help_reply_markup()
        try:
            bot = call.bot
            msg_id = call.message.message_id
            await bot.edit_message_text(
                text=help_message,
                chat_id=call.from_user.id,
                message_id=msg_id,
                reply_markup=help_reply_markup,
                parse_mode="HTML",
            )
        except Exception as e:
            log.error(f"set_shape error: {e}", exc_info=True)
        finally:
            await call.answer()

    # 通过 /image 命令生成图片
    @image_bot_router.message(Command("image"))
    async def image_by_prompt(message: types.Message):

        log.info(f"image_by_prompt:{message.text}")

        # 获取用户输入的参数
        command, *args = message.text.split()
        tg_id = message.from_user.id

        prompt = " ".join(args)
        if not check_image_prompt_args(prompt):
            await message.reply(
                "参数错误,请检查,正确格式为:/image promptxxx,例如:/image litter girl"
            )
            return

        await message.reply("正在创作中,请稍等")

        asyncio.create_task(gen_and_send_image(tg_id, prompt, message=message))

    return image_bot_router


def create_img_recharge_bot_router() -> Router:
    img_recharge_bot_router = Router()

    # 1.返回充值方式菜单
    @img_recharge_bot_router.message(Command("recharge"))
    async def handle_recharge_message(
        message: types.Message, bot: Bot, state: FSMContext
    ):
        log.info(f"handle_recharge_message:{message.text}")
        await command_recharge(message=message, bot=bot, state=state)

    # 2. 选择具体方式的某个产品
    @img_recharge_bot_router.callback_query(RechargeNavCallback.filter())
    async def handle_voucher_cancel_adp(
        query: CallbackQuery,
        callback_data: RechargeNavCallback,
        bot: Bot,
        state: FSMContext,
    ):
        state_data = await state.get_data()
        log.info(
            f"handle_voucher_cancel_adp:{query.data},{callback_data},state_data:{state_data}"
        )
        await handle_voucher_cancel(query, callback_data, bot, state)

    @img_recharge_bot_router.callback_query(RechargeProductCallback.filter())
    async def handle_recharge_product_adp(
        query: CallbackQuery,
        bot: Bot,
        callback_data: RechargeProductCallback,
        state: FSMContext,
    ):
        state_data = await state.get_data()
        log.info(
            f"handle_recharge_product_adp:{query.data},RechargeProductCallback:{callback_data},state:{state_data}"
        )
        await handle_recharge_product(query, bot, callback_data, state)

    @img_recharge_bot_router.callback_query(PayMethodCallback.filter())
    async def handle_pay_method(
        query: CallbackQuery,
        bot: Bot,
        callback_data: PayMethodCallback,
        state: FSMContext,
    ):
        # await on_product_selected(query, bot, callback_data, state)
        ...

    @img_recharge_bot_router.pre_checkout_query()
    async def pre_checkout_query_handler_adp(
        query: PreCheckoutQuery, bot: Bot, state: FSMContext
    ):
        await pre_checkout_query_handler(query, bot, state)

    return img_recharge_bot_router


def create_image_bot_router_en() -> Router:
    image_bot_router_en = Router()

    async def send_image_help_en_tgid(tg_id: int, bot: Bot):
        log.info(f"send_image_help_en:{tg_id},bot:{bot.id}")

        basic_img_profile = await ImageBotService.get_basic_profile(tg_id)
        help_message = get_help_message_by_lang(image_base_profile=basic_img_profile)
        help_reply_markup = get_help_reply_markup_by_lang()

        try:
            sent_msg = await bot.send_message(
                tg_id,
                help_message,
                reply_markup=help_reply_markup,
                parse_mode="HTML",
            )
            # 删除消息
            asyncio.create_task(
                del_msg_delay(
                    bot=bot,  # type: ignore
                    chat_id=tg_id,
                    message_id=sent_msg.message_id,
                    delay=60 * 2,  # 2分钟后删除
                )
            )
        except Exception as e:
            log.error(f"send_image_help_en error: {e}", exc_info=True)

    async def edit_help_message_en(tg_id: int, message_id: int, bot: Bot):
        log.info(f"edit_help_message_en: {tg_id},message_id:{message_id},bot:{bot.id}")
        basic_img_profile = await ImageBotService.get_basic_profile(tg_id)
        help_message = get_help_message_by_lang(image_base_profile=basic_img_profile)
        help_reply_markup = get_help_reply_markup_by_lang()

        try:

            await bot.edit_message_text(
                text=help_message,
                chat_id=tg_id,
                message_id=message_id,
                reply_markup=help_reply_markup,
                parse_mode="HTML",
            )
        except Exception as e:
            log.error(f"edit_help_message_en error: {e}", exc_info=True)

    @image_bot_router_en.message(and_f(Command("help"), (F.chat.type == "private")))
    async def send_image_help_en(message: types.Message, bot: Bot):
        log.info(f"send_image_help_en:{message.text}")

        await send_image_help_en_tgid(
            tg_id=message.from_user.id, bot=bot  # type: ignore
        )

    @image_bot_router_en.callback_query(lambda c: c.data.startswith("image_style_"))
    async def set_image_style_en(call: types.CallbackQuery, bot: Bot):
        log.info(f"set_imag_style_en:{call.data}")

        image_style = call.data if call.data else ""
        img_b_profile = await ImageBotService.update_b_profile_style(
            call.from_user.id, image_style
        )
        msg_id = call.message.message_id
        await edit_help_message_en(
            tg_id=call.from_user.id, message_id=msg_id, bot=bot  # type: ignore
        )

    @image_bot_router_en.callback_query(lambda c: c.data.startswith("img_resolution_"))
    async def set_resolution_en(call: types.CallbackQuery, bot: Bot):
        log.info(f"set_resolution_en:{call.data}")

        image_resolution = (
            call.data if call.data else list(image_resolution_dict.keys())[0]
        )
        b_profile = await ImageBotService.update_b_profile_resolution(
            call.from_user.id, image_resolution
        )
        await edit_help_message_en(
            tg_id=call.from_user.id, message_id=call.message.message_id, bot=bot  # type: ignore
        )

    @image_bot_router_en.callback_query(lambda c: c.data.startswith("image_privacy_"))
    async def set_privacy_en(call: types.CallbackQuery, bot: Bot):
        log.info(f"set_privacy_en:{call.data}")

        image_privacy = call.data if call.data else ""

        # 更新用户隐私设置
        if image_privacy == "image_privacy_public":
            image_privacy = "public"
        elif image_privacy == "image_privacy_private":
            image_privacy = "private"
        else:
            log.error(
                f"set_privacy_en error: {image_privacy} not in {image_privacy_dict}"
            )
            image_privacy = "public"

        img_b_profile = await ImageBotService.update_b_profile_privacy(
            call.from_user.id, image_privacy
        )
        await send_image_help_en_tgid(tg_id=call.from_user.id, bot=bot)  # type: ignore

    @image_bot_router_en.callback_query(lambda c: c.data.startswith("shape_"))
    async def set_shape_en(call: types.CallbackQuery, bot: Bot):
        log.info(f"set_shape_en:{call.data}")

        # 处理形状设置逻辑
        shape = call.data if call.data else "shape_portrait"
        # 更新用户形状设置

        if shape not in shape_dict:
            log.error(f"set_shape_en error: {shape} not in {shape_dict}")
            shape = "shape_portrait"
        img_b_profile = await ImageBotService.update_b_profile_shape(
            call.from_user.id, shape
        )
        await edit_help_message_en(
            tg_id=call.from_user.id, message_id=call.message.message_id, bot=bot  # type: ignore
        )

    @image_bot_router_en.message(F.text & (F.chat.type == "private"))
    async def gen_image_by_text_promt_en(
        message: types.Message, bot: Bot, state: FSMContext
    ):
        prompt = message.text if message.text else ""
        tg_id = message.from_user.id
        log.info(f"gen_image_by_text_promt_en:{message.text},tg_id:{tg_id}")
        try:
            user = await user_service.get_user_by_tg_id(tg_id)
            if not user:
                user = await user_service.register_by_tg(
                    tg_user_id=tg_id,
                    first_name=message.from_user.first_name,
                    last_name=message.from_user.last_name,
                    user_name=message.from_user.username,
                )
            balance = await AccountService.get_total_balance(user.id)

            basic_profile = await ImageBotService.get_basic_profile(tg_id)
            log.info(f"gen_image_by_text_promt_en: {tg_id}, {prompt}, {basic_profile}")
            photo_product = await product_service.get_online_photo_bot_product(
                basic_profile.resolution
            )
            if not photo_product:
                log.error(f"Photo Product Not Found{basic_profile}")
                await message.reply("Photo Product Not Found")
                return

            if balance < photo_product.price:
                await message.reply(
                    EN_RECHARGE_MESSAGE,
                    parse_mode="HTML",
                )
            else:
                sent_msg = await message.reply("Creating image, please wait...")
                asyncio.create_task(
                    gen_and_send_image(
                        tg_id,
                        prompt,
                        message=message,
                        del_pre_msg_id=sent_msg.message_id,
                        user_id=user.id,
                        lang="en",
                        photo_product=photo_product,
                    )
                )
        except Exception as e:
            log.error(f"gen_image_by_text_promt_en error: {e}", exc_info=True)
            return

    return image_bot_router_en
