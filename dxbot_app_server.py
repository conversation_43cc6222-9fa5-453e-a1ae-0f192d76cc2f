
import asyncio
from collections import defaultdict
from datetime import datetime
import logging
import os
import random
from typing import Dict
import json

from aiogram import BaseMiddleware, <PERSON><PERSON>, <PERSON><PERSON>atch<PERSON>, types
# from aiogram import LoggingMiddleware
from aiogram.types import Update
from aiogram.exceptions import TelegramBadRequest
from aiogram.fsm.storage.memory import MemoryStorage
from aiogram.filters import CommandStart
from aiogram import F


from dotenv import load_dotenv
from fastapi import FastAPI, Request, HTTPException
from fastapi.routing import APIRouter
from pydantic import BaseModel
from tortoise import Tortoise
from tortoise.contrib.fastapi import register_tortoise
import uvicorn

from common import loggers
from persistence.models.models_dx_bot import DxUser, MediaGroupMesssage, FormnStatus, MessageMap,DxBotConfig,DxBotBlacklist
from services.dx_bot.dx_msg_service import MessageService
from services.dx_bot.dx_user_service import DxUserService
from services.dx_bot.dx_bot_service import DxBotConfigService

# 加载环境变量
load_dotenv()


MONITOR_BOT = Bot(token=os.getenv("DX_MONITOR_BOT_TOKEN")) # type: ignore
MONITOR_GROUP_ID = int(os.getenv("DX_MONITOR_GROUP_ID")) # type: ignore
MONITOR_TOPIC_ID = int(os.getenv("DX_MONITOR_TOPIC_ID")) # type: ignore

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s (%(filename)s:%(lineno)d) [%(levelname)s] %(message)s",handlers=[loggers.local_dx_bot_handler,logging.StreamHandler()])
log = logging.getLogger(__name__)

def mention_html(user_id: int, full_name: str) -> str:
    """
    生成带有 HTML 格式的用户提及。

    :param user_id: 用户的 Telegram ID
    :param full_name: 用户的全名
    :return: 带有 HTML 格式的用户提及字符串
    """
    return f'<a href="tg://user?id={user_id}">{(full_name)}</a>'

async def send_media_group_later(delay: float, from_chat_id, chat_id, media_group_id: int, dir, bot: Bot):
    await asyncio.sleep(delay)
    logging.info(f"Media group with  {from_chat_id} ,{chat_id} ,{media_group_id} is ready to sent.")
    media_group_msgs =await MessageService.get_all_media_group_message(media_group_id)

    if dir == 'u2a':
        u = await DxUserService.get_user(user_id=from_chat_id,bot_id=bot.id)
        message_thread_id = u.message_thread_id
        sents = await bot.copy_messages(chat_id, from_chat_id,[m.message_id for m in media_group_msgs] , message_thread_id=message_thread_id)
        for sent, msg in zip(sents, media_group_msgs):
            msg_map = MessageMap(user_chat_message_id=msg.message_id, group_chat_message_id=sent.message_id, user_id=u.user_id)
            await MessageService.add_message_map(msg_map)      
    else:
        # 发送给用户
        sents = await bot.copy_messages(chat_id, from_chat_id,[m.message_id for m in media_group_msgs])
        for sent, msg in zip(sents, media_group_msgs):
            msg_map = MessageMap(user_chat_message_id=sent.message_id, group_chat_message_id=msg.message_id, user_id=from_chat_id)
            await MessageService.add_message_map(msg_map)


#处理黑名单用户

async def is_black_user(user_id: int, bot_id: int) -> bool:

    return await DxBotBlacklist.filter(user_id=user_id,bot_id=bot_id,is_deleted=False).exists()

async def add_black_user(user_id: int, bot_id: int,op_id:int,from_group_id:int):
    await DxBotBlacklist.create(user_id=user_id,bot_id=bot_id,op_id=op_id,from_group_id=from_group_id)

async def remove_black_user(user_id: int, bot_id: int):
    await DxBotBlacklist.filter(user_id=user_id,bot_id=bot_id).update(is_deleted=True)

# 定义一个用于存储用户数据的中间件
class UserDataMiddleware(BaseMiddleware):
    def __init__(self):
        super().__init__()
        self.user_data = defaultdict(dict)

    async def __call__(self, handler, event, data):
        if isinstance(event, Update) and event.message:
            user_id = event.message.from_user.id
            data['user_data'] = self.user_data[user_id]
        return await handler(event, data)

class BotManager:
    def __init__(self):
        self.bots  : dict[str,Bot] = {}
        self.dispatchers = {}
        self.configs = {}

    async def initialize_bots_from_db(self):
        bot_configs = await DxBotConfigService.get_all_active_bot_config()
        for bot_config in bot_configs:
            self.add_bot(bot_config.bot_name, bot_config.bot_token, bot_config.bot_config)

    async def monitor_bot_webhook_info(self):
        while True:
            bot_list = await DxBotConfigService.get_all_active_bot_config()
            for bot in bot_list:
                bot_id = bot.bot_name
                bot = self.bots.get(bot_id)
                if bot:
                    try:
                        webhook_info = await bot.get_webhook_info()
                        bot_me = await bot.get_me()
                        log.info(f"bot:{bot_id},webhook_info:{webhook_info},bot_info:{bot_me}")
                        
                        time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        monitor_msg = (
                            F"Time: {time}\n"
                            f"Bot {bot_id} \n"
                            f"Pending_update_count:{webhook_info.pending_update_count}\n"
                            f"webhook_info:{webhook_info}\n\n"
                            f"bot_info: {bot_me}"
                        )
                        
                        await MONITOR_BOT.send_message(chat_id=MONITOR_GROUP_ID,message_thread_id=MONITOR_TOPIC_ID,text=monitor_msg)
                    except Exception as e:
                        log.error(f"Error getting webhook info for bot {bot_id}: {e}",exc_info=True)
            sleep_time = 60*30 + random.randint(1, 10)*60
            await asyncio.sleep(sleep_time)        
            
    # config = {"group_chat_id": 123456789,"group_admin_id": 0,"welcome_msg": "Welcome!"}
    def add_bot(self, bot_name, token, config=None):
        
        log.info(f"Adding bot {bot_name} ,{config} with token {token}")
        if bot_name in self.bots:
            raise ValueError(f"Bot with name {bot_name} already exists.")
        
        bot = Bot(token=token)
        storage = MemoryStorage()
        dp = Dispatcher(bot=bot,storage=storage)
        # dp.middleware.setup(LoggingMiddleware())
        # dp.update.outer_middleware.register(UserDataMiddleware())
        
        self.bots[bot_name] = bot
        self.dispatchers[bot_name] = dp
        self.configs[bot_name] = config
        
        welccome_msg = config.get('welcome_msg', 'Welcome!') if config else 'Welcome!'
        
        # Define message handler for the new bot
        @dp.message(CommandStart())
        async def start_from_user(message: types.Message):
            log.info(f"Received /start from user {message.from_user.id} by bot {message.bot.id}")
            user_id = message.from_user.id
            user_first_name = message.from_user.first_name if message.from_user.first_name is not None else ''
            last_name = message.from_user.last_name if message.from_user.last_name is not None else ''
            user_name = message.from_user.username if message.from_user.username is not None else ''
            chat_id = message.chat.id
            bot_id = message.bot.id
            
            n_user = DxUser(user_id=user_id, first_name=user_first_name, last_name=last_name, username=user_name, bot_id=bot_id)
            
            try:
                await DxUserService.add_update_user(n_user)
            except Exception as e:
                log.error(f"Error adding/updating user {n_user}: {e}",exc_info=True)
                await message.reply(f"请联系群里的管理员")
                return
            await message.reply(f"Hello {user_first_name} {last_name}! {welccome_msg}")
            
        @dp.message(F.chat.type == 'private')
        async def forwarding_message_u2a(message: types.Message):
            log.info(f"Received message from user: {message.from_user.id} by bot {message.bot.id},msg_id:{message.message_id},msg_text:{message.text}")
            f_user = message.from_user
            bot = message.bot

            black_user = await is_black_user(f_user.id, bot.id)
            
            if black_user:
                log.warning(f"User {f_user.id} is in blacklist, ignore msg.")
                return
            
            forwad_group_id = config['group_chat_id']
            dx_user = await DxUserService.get_user(f_user.id, message.bot.id)
             ##todo 优化下，如果用户不存在，进行直接注册
            if dx_user is None:
                log.error(f"Error getting user {f_user.id}")
                await message.reply(f"请先发送/start")
                return
            message_thread_id = dx_user.message_thread_id
            
            if f := await MessageService.get_formn_status(message_thread_id):
                if f.status == 'closed':
                    await message.reply("客服已经关闭对话。如需联系，请利用其他途径联络客服回复和你的对话。")
                    return
            
            if not message_thread_id:
                formn = await bot.create_forum_topic(chat_id=forwad_group_id,name=f"用户{f_user.id}的对话")
                message_thread_id = formn.message_thread_id
                dx_user.message_thread_id = message_thread_id
                
                #发送新用户的消息
                await bot.send_message(chat_id=forwad_group_id,message_thread_id=message_thread_id, text=f"新的用户 {mention_html(f_user.id, f_user.full_name)} 开始了一个新的会话。",parse_mode='HTML')
                await DxUserService.add_update_user(dx_user)

            params = {"message_thread_id": message_thread_id}

            # step1 处理引用消息
            if message.reply_to_message:
                reply_in_user_chat_id = message.reply_to_message.message_id
                if msg_map := await MessageService.get_message_map(reply_in_user_chat_id,dx_user.user_id):
                    params['reply_to_message_id'] = msg_map.group_chat_message_id
            
            try:
                sent_msg = await bot.copy_message(chat_id=forwad_group_id,from_chat_id=message.chat.id,message_id=message.message_id,**params)
                #添加用户消息id和群聊消息id的映射
                msg_map = MessageMap(user_chat_message_id=message.message_id,group_chat_message_id=sent_msg.message_id, user_id=f_user.id)
                await MessageService.add_message_map(msg_map)
            except TelegramBadRequest as e:
                log.error(f"Error TelegramBadRequest:{params},{e}")
                
                if e.message and 'message thread not found' in e.message:
                    #重新创建对话
                    log.info(f"对话已经结束。请重新发送内容开始对话:{params},user_id:{f_user.id}")
                    await DxUserService.update_user_msg_thread_id(DxUser(user_id=f_user.id,bot_id=message.bot.id,message_thread_id=0))
                    await message.reply(f"上次对话已经结束。请重新发送内容开始对话。")
                    
                # await message.reply(f"发送失败: {e}\n")
            except Exception as e:
                log.error(f"forwarding_message_u2a,error: {e}",exc_info=True) 

        
        @dp.message( (F.chat.type == 'group') | (F.chat.type == 'supergroup'))
        async def forwarding_message_a2u(message: types.Message):
            
            from_group_id = message.chat.id
            
            config_group_id = config['group_chat_id']
            
            if from_group_id != config_group_id:
                log.warning(f"Received from group {from_group_id}is not  group {config_group_id}")
                return
            
            log.info(f"Received message from group {message.chat.id} by bot {message.bot.id},msg_id:{message.message_id},msg_text:{message.text}")
            
            message_thread_id = message.message_thread_id
            u_bot = message.bot
            bot = message.bot
            if not message_thread_id:
                # general message, ignore
                log.info(f"General message, ignore:{message.message_id}")
                
                return 
    
            if message.forum_topic_created:
                log.warning(f"forum_topic_created: thread_id:{message.message_thread_id},msg_id:{message.message_id}")
                f = FormnStatus(chat_id=0,message_thread_id=message.message_thread_id, status='opened')
                await MessageService.add_formn_status(f)
                return 
            if message.forum_topic_closed:
                log.warning(f"forum_topic_closed: thread_id:{message.message_thread_id},msg_id:{message.message_id}")
                # await bot.send_message(user_id, "对话已经结束。对方已经关闭了对话。你的留言将被忽略。")
                if f := await MessageService.get_formn_status(message_thread_id):
                    f.status = 'closed'
                    await MessageService.add_formn_status(f)
                return 
            if message.forum_topic_reopened:
                log.warning(f"forum_topic_reopened: {message}")
                # await bot.send_message(user_id, "对方重新打开了对话。可以继续对话了。")
                if f := await MessageService.get_formn_status(message_thread_id):
                    f.status = 'opened'
                    await MessageService.add_formn_status(f)    
                return
            if f := await MessageService.get_formn_status(message_thread_id):
                log.info(f"formn_status:{message_thread_id}, {f.status}")
                if f.status == 'closed':
                    await message.reply("对话已经结束。希望和对方联系，需要打开对话。")
                    return
            
            user_id = 0
            if u := await DxUserService.get_user_by_thread_id(message_thread_id,u_bot.id):
                user_id = u.user_id
            if not user_id:
                log.warning(f"消息回复错误：{message.message_id}, 没有找到对应的用户{user_id}, message_thread_id: {message_thread_id}")
                message.reply(f"消息回复错误：{message.message_id}, 没有找到对应的用户{user_id}, message_thread_id: {message_thread_id}")
                return 
            chat_id = user_id
            #添加 ban 和unban 的处理
            if message.text: 
                if message.text == "/ban": 
                    await add_black_user(user_id, bot.id,op_id=message.from_user.id,from_group_id=from_group_id)
                    await message.reply(f"已经把用户拉入黑明单:{user_id}")
                    return
                elif message.text == "/unban":
                    await remove_black_user(user_id, bot.id)
                    await message.reply(f"已经解除黑名单用户:{user_id}")
                    return  
            
            # 构筑下发送参数
            params = {}
            if message.reply_to_message:
                # 群组中，客服回复了一条消息。我们需要找到这条消息在用户中的id
                log.info(f"reply_to_message: msg_id:{message.reply_to_message.message_id},msg_text:{message.reply_to_message.text}")
                
                reply_in_admin = message.reply_to_message.message_id
                if msg_map := await MessageService.get_message_map_by_group_chat_message_id(reply_in_admin,user_id):
                    params['reply_to_message_id'] =  msg_map.user_chat_message_id
                
                    #del 消息
                    if message.text and message.text=="del":
                        log.warning(f"del message:{chat_id}, {message}")
                        try:
                            await bot.delete_message(chat_id=chat_id,message_id=msg_map.user_chat_message_id)

                            await message.reply(f"删除成功: message_id:{msg_map.user_chat_message_id}\n")
                            return
                        except Exception as e:
                            log.error(f"Error  del message: {e}")
                            await message.reply(f"删除失败: {e}\n")
                            return
                        
            try:
                log.info(f"Sending message to user begin: {chat_id} ,message_id:{message.chat.id,message.message_id},msg_txt:{message.text},params:{params}")
                sent_msg = await bot.copy_message(chat_id, message.chat.id,message.message_id, **params)
                log.info(f"Sent message to user,end {chat_id} ,message_id:{sent_msg.message_id}")
                msg_map = MessageMap(group_chat_message_id=message.message_id, user_chat_message_id=sent_msg.message_id, user_id=user_id)
                await MessageService.add_message_map(msg_map)

            except Exception as e:
                log.error(f"Error forwarding_message_a2u: {e}",exc_info=True)
                try:
                    await message.reply(f"发送失败: {e}\n")
                except Exception as reply_e:
                    log.error(f"Error forwarding_message_a2u {reply_e}")
            finally:
                log.info(f" finsh:Sending message")

    def remove_bot(self, bot_name):
        if bot_name not in self.bots:
            raise ValueError(f"Bot with name {bot_name} does not exist.")
        
        del self.bots[bot_name]
        del self.dispatchers[bot_name]

    async def set_webhook(self, bot_name):
        if bot_name not in self.bots:
            raise ValueError(f"Bot with name {bot_name} does not exist.")
        
        webhook_url = WEBHOOK_URL_TEMPLATE.format(bot_name=bot_name)
        allow_updates = ["message","edited_message","channel_post","edited_channel_post","callback_query","my_chat_member","chat_member"]
        await self.bots[bot_name].set_webhook(url=webhook_url,allowed_updates=allow_updates)

    async def delete_webhook(self, bot_name):
        if bot_name not in self.bots:
            raise ValueError(f"Bot with name {bot_name} does not exist.")
        
        await self.bots[bot_name].delete_webhook()

    async def process_update(self, bot_name, update):
        if bot_name not in self.dispatchers:
            raise ValueError(f"Bot with name {bot_name} does not exist.")
        bot = self.bots[bot_name]
        await self.dispatchers[bot_name].feed_update(bot, update) 



class BotConfigDetails(BaseModel):
    group_chat_id: int
    group_admin_id: int = 0
    welcome_msg: str = "Welcome!"

class BotConfig(BaseModel):
    bot_name: str
    token: str
    config: BotConfigDetails





# Initialize FastAPI app and BotManager
app = FastAPI()
bot_manager = BotManager()

WEBHOOK_HOST = os.getenv("WEBHOOK_HOST")
WEBHOOK_PATH_TEMPLATE = '/webhook/{bot_name}'
WEBHOOK_URL_TEMPLATE = f"{WEBHOOK_HOST}{WEBHOOK_PATH_TEMPLATE}"

# #增加测试的bot
# bot_manager.add_bot(bot_name='ai747_bot',token="7357790700:AAHt_84ueO_t6bN3Pw-ewANthYIW8I4cO_k",config={"group_chat_id":-1002226814292})


Tortoise.init_models(["persistence.models.models_dx_bot"], "models")
register_tortoise(
    app=app,
    db_url=os.environ["MYSQL_URL"],
    modules={"models": ["persistence.models.models_dx_bot"]},
    generate_schemas=True,
)

# Set webhook routes dynamically
@app.get(WEBHOOK_PATH_TEMPLATE)
async def get_telegram_webhook(bot_name: str):

    return {"status": "ok", "bot_name": bot_name}
@app.post(WEBHOOK_PATH_TEMPLATE)
async def telegram_webhook(request: Request, bot_name: str):
    try:
        update = Update(**await request.json())
        log.info(f"webhook for bot /webhook/{bot_name},update:{update.model_dump(exclude_none=True)}")
        await bot_manager.process_update(bot_name, update)
    except Exception as e:
        log.error(f"Error processing update: {update.update_id}, {e}",exc_info=True)
    
    return {"status": "ok"}
# API endpoint to add a new bot
@app.post("/add_bot")
async def add_bot(bot_config: BotConfig):
    try:
        bot_manager.add_bot(bot_config.bot_name, bot_config.token, bot_config.config.model_dump())
        await bot_manager.set_webhook(bot_config.bot_name)
        await DxBotConfigService.add_bot_config(DxBotConfig(bot_name=bot_config.bot_name, bot_token=bot_config.token, bot_config=bot_config.config.dict()))
        await bot_manager.initialize_bots_from_db()
        return {"message": f"Bot {bot_config} added and webhook set."}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

# API endpoint to remove a bot
# @app.post("/remove_bot")
# async def remove_bot(bot_name: str):
#     try:
#         await bot_manager.delete_webhook(bot_name)
#         bot_manager.remove_bot(bot_name)
#         await DxBotConfigService.delete_bot_config_by_name(bot_name)
#         return {"message": f"Bot {bot_name} removed and webhook deleted."}
#     except ValueError as e:
#         raise HTTPException(status_code=400, detail=str(e))

#Api endpoint to get all bots
@app.get("/get_all_bots")
async def get_all_bots(v_p:str):
    
    if v_p != 'bot_123':
        raise HTTPException(status_code=400, detail="Invalid view permission")
    return await DxBotConfigService.get_all_bot_config()

# Set webhooks on startup
@app.on_event("startup")
async def on_startup():
    
    # Initialize bots from database
    await bot_manager.initialize_bots_from_db()
    for bot_name in bot_manager.bots.keys():
        await bot_manager.set_webhook(bot_name)
    
    # Monitor bot webhooks 
    asyncio.create_task(bot_manager.monitor_bot_webhook_info())

# Delete webhooks on shutdown
@app.on_event("shutdown")
async def on_shutdown():
    # for bot_name in bot_manager.bots.keys():
    #     await bot_manager.delete_webhook(bot_name)
    pass

if __name__ == '__main__':
    uvicorn.run(app, host='0.0.0.0', port=8443)