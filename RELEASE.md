、、
db.chat_tips_history.createIndex({ "user_id": 1, "conversation_id": 1 })
、、

``
alter table role_config
    add support_photo bool NOT NULL DEFAULT false  COMMENT '是否支持图片';

#stag
update role_config set support_photo=true where id in (359,1010)

#prod

alert table sub_tags
    add role_count bigint NOT NULL DEFAULT 0 COMMENT '角色数量';

``

## 上线说明

## 1、修改caddy配置，增加/roles/filter_list_v2

2、修改所有的role_config chat_type


2、修改任务描述(task.json)
