addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request, event))
})

async function handleRequest(request, event) {
  const url = new URL(request.url)
  const cache_pathnames = ['/roles/filter_list_v2', '/roles/hello']
  if (cache_pathnames.includes(url.pathname)) {
    return await handleFilterListRequest(request, event)
  }
  // 对其他请求不做处理，直接转发
  return await fetch(request)
}
function role_filter_cache_key(request) {
  let url = new URL(request.url)
  let lang = request.headers.get('Current-Language')
  if (lang == null || lang == '') {
    lang = 'zh'
  }
  let tag = url.searchParams.get('tag')
  let nsfw = url.searchParams.get('nsfw')
  tag = tag == null ? '' : tag
  nsfw = nsfw == null ? 'True' : nsfw
  let file_path = '/roles/filter_list_v2?nsfw=' + nsfw + '&tag=' + tag + '&language=' + lang
  return url.origin + file_path
}
async function handleFilterListRequest(request, event) {
  const cache = caches.default  // 获取默认缓存

  // 获取查询参数
  // 为该请求生成唯一的缓存键
  let cache_key = ""
  let url = new URL(request.url)
  if (url.pathname === '/roles/filter_list_v2') {
    cache_key = role_filter_cache_key(request)
  }
  if (cache_key === "") {
    return await fetch(request)
  }
  let response = await cache.match(cache_key)
  if (!response) {
    // // 如果缓存未命中，转发请求到 aaa.com，同时保持原路径和查询参数
    // console.log("fetch newUrl:" + newUrl)
    // response = await fetch(newUrl)

    response = await fetch(request)
    // 如果请求成功，缓存该响应（例如设置缓存 10 分钟）
    if (response.ok) {
      const cacheExpiration = Date.now() + 10 * 1000 // 缓存 60 秒
      const headers = new Headers(response.headers)
      headers.set('Cache-Expiration', cacheExpiration.toString())
      const cacheResponse = response.clone() //new Response(response.body, response)
      console.log("put cache")
      await cache.put(cache_key, new Response(cacheResponse.body, { headers }))
    }
  } else {
    // 如果缓存命中，后台异步更新缓存
    const cacheExpiration = response.headers.get('Cache-Expiration')
    const currentTime = Date.now()
    if (cacheExpiration && currentTime < parseInt(cacheExpiration)) {
      return response
    } else {
      console.log("cacheExpiration:" + cacheExpiration)
      event.waitUntil(updateCache(request, cache, cache_key))
    }
  }
  // 返回缓存的响应（可能是过期的），但用户不会被阻塞
  return response
}

// 后台异步更新缓存
async function updateCache(request, cache, cache_key) {
  try {
    const response = await fetch(request)
    console.log("updateCache async response ok:" + response.ok)
    if (response.ok) {
      const cacheExpiration = Date.now() + 10 * 1000 // 缓存 60 秒
      const headers = new Headers(response.headers)
      headers.set('Cache-Expiration', cacheExpiration.toString())
      const cacheResponse = response.clone()
      console.log("put cache")
      await cache.put(cache_key, new Response(cacheResponse.body, { headers }))
    }
  } catch (error) {
    console.error('Failed to update cache:', error)
  }
}
