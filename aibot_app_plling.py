
import asyncio
from collections import defaultdict
import logging
import os
import re
import time
from typing import Dict
import json

from aiogram import BaseMiddleware, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Router, types
# from aiogram import LoggingMiddleware
from aiogram.types import Update, Message
from aiogram.fsm.storage.memory import MemoryStorage


from aiogram.filters import CommandStart, Command
from aiogram import F


from aiogram.filters import Chat<PERSON><PERSON>berUpdated<PERSON>ilter,IS_MEMBER,IS_NOT_MEMBER
from aiogram.types import ChatMemberUpdated




from dotenv import load_dotenv


from common import loggers





logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s (%(filename)s:%(lineno)d) [%(levelname)s] %(message)s",handlers=[loggers.local_dx_bot_handler])
log = logging.getLogger(__name__)



sensitive_words = ["111", "敏感词2", "敏感词3"]
message_cooldowns = defaultdict(lambda: 0)
#用户白名单 tg_id
USER_WHITE_LIST = [123456789, 987654321]

ADMIN_IDS = [123456789, 987654321]
# 中间件
class RateLimitMiddleware:
    async def __call__(self, handler, event, data):
        user_id = event.from_user.id
        current_time = time.time()
        if current_time - message_cooldowns[user_id] < 5:
            await event.delete()
            return
        message_cooldowns[user_id] = int(current_time)
        return await handler(event, data)

# 自定义过滤器
def is_admin(message: Message):
    return message.from_user.id in ADMIN_IDS  # 假设ADMIN_IDS是管理员ID列表

#是否在白名单
def is_user_white_list(message: Message) -> bool:
    return message.from_user.id in USER_WHITE_LIST

#@ai_749_bot
# token = "7706615835:AAHFiw4pQ_-cGK8Qffb4_rlY9dwqimazAMs"

# @ai747_bot7457866898:AAFhqjW6MLpblzZoffE5WxmIidsrBNEIL1A
token ="7644777527:AAHjRgUjqQPWXlrjoLLb4zJUuVXgWDvTyTo"

from_chat_group_id = -1002410058975
from_group_message_id = 69

bot = Bot(token=token)
storage = MemoryStorage()
dp = Dispatcher(bot=bot,storage=storage)

async def on_startup():
    
    user_me = await bot.get_me(request_timeout=5)
    print(f"on_startup user_me:{user_me}")
    
    await bot.send_message(5359176631, "Bot send test can.")
    await bot.delete_webhook()
    await dp.start_polling(bot)




# # 1. 欢迎新成员
# @dp.message(F.new_chat_members)
# async def welcome_new_member(message: types.Message):
#     for new_member in message.new_chat_members:
#         await message.reply(f"欢迎 {new_member.mention} 加入群组！请阅读群规。")

# 处理用户加入群组的事件
@dp.chat_member(ChatMemberUpdatedFilter(IS_NOT_MEMBER >> IS_MEMBER))
async def user_joined(event: ChatMemberUpdated):
    user = event.new_chat_member.user
    chat = event.chat
    await bot.send_message(chat.id, f"欢迎 {user.full_name} 加入群组！请阅读群规，玩的愉快！")

# 处理用户离开群组的事件
@dp.chat_member(ChatMemberUpdatedFilter(IS_MEMBER >> IS_NOT_MEMBER))
async def user_leaved(event: ChatMemberUpdated):
    user = event.new_chat_member.user
    chat = event.chat
    print(f"用户 {user.full_name} 离开群组！")
    # await bot.send_message(chat.id, f"欢迎 {user.full_name} 加入群组！")

@dp.message(F.text & ((F.chat.type == 'group') | (F.chat.type == 'supergroup')))
async def handle_text_message(message: types.Message):
    
    print(f"message:{message}")
    bot_user = await bot.get_chat_member(message.chat.id, message.from_user.id)
    print(f"bot_user:{bot_user},bot_user.status:{bot_user.status}")
    
    
    #一个链接
    if message.text and "@" in message.text:
        print(f"message--@:{message.text}")
        bot_pattern = re.compile(r'@[\w\d_-]+')
        match = bot_pattern.findall(message.text)
        print(f"match:{match}")

    # 一个链接
        for m in match:
            print(f"m:{m}")
            chat_id = m
            try:
                chat_info = await bot.get_chat(chat_id,request_timeout=3)
                print(f"chat_info:{chat_info}")
            except Exception as e:
                print(f"chat_info error:{e}")
                   # 4. 搜索公开聊天
                try:
                    results = await bot.search_public_chat(chat_id)
                    if results:
                        return f"{chat_id} 是一个公开的群组或频道"
                except:
                    pass
                    
         

    # 用户名的检测
    if message.from_user.username is None:
        await message.answer("请设置一个用户名，否则无法发送消息。")
    # 检查消息长度
    if len(message.text) > 500:
        await message.delete() 
        await message.answer("消息太长，已自动删除。")
        return
    # 检查敏感词
    if any(word in message.text for word in sensitive_words):
        await message.delete()
        await message.answer("消息含有敏感词，已被删除。")
        return
    # 检查广告
    if "广告" in message.text.lower():
        await message.delete()
        await message.answer("请不要发送广告！")
        return
    # 检查链接
    if message.text.startswith("http"):
        member = await bot.get_chat_member(message.chat.id, message.from_user.id)
        if member.status not in ['administrator', 'creator']:
            await message.chat.ban(message.from_user.id)
            await message.answer("发送链接的用户已被自动封禁。")
            return

    await message.answer("消息已收到xxx。")

@dp.message(F.text & (F.chat.type == 'private'))
async def handle_private_text_message(message: types.Message):
    print(f"message:{message.text}")
    bot = message.bot
    await message.answer("私聊消息已收到。")
    try:
        sent_msg = await bot.copy_message(chat_id=message.chat.id, from_chat_id=from_chat_group_id, message_id=from_group_message_id,request_timeout=1)
        
        await bot.send_message(from_chat_group_id, f"自动回复了内容",reply_to_message_id=from_group_message_id)
        print(f"sent_msg:{sent_msg}")
    except Exception as e:
        print(f"sent_msg error:{e}")


@dp.message(F.photo & ((F.chat.type == 'group') | (F.chat.type == 'supergroup')))
async def handle_photo_message(message: types.Message):
    print(f"message:{message.photo}")
    
    # 获取最高分辨率的照片
    photo = message.photo[-1] # type: ignore
    # 获取文件 ID
    file_id = photo.file_id
    file_name = f'{file_id}.jpg'
    # 确定保存图片的路径
    save_path = os.path.join('downloads', file_name)
    os.makedirs('downloads', exist_ok=True)
    
    file_info = await bot.get_file(file_id)
    print(f"file_info:{file_info}")
    # 下载图片
    file_path = file_info.file_path
    file_url = f"https://api.telegram.org/file/bot{token}/{file_path}"
    print(f"file_url:{file_url}")
    await bot.download(file_id, save_path)
    await message.answer("图片已收到。")


if __name__ == '__main__':
    asyncio.run(on_startup())
    
    