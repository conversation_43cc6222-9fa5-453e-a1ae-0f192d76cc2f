import asyncio
import json
import redis
import logging

log = logging.getLogger(__name__)
class RedisQueue:
    def __init__(self, queue_name='message_queue', url='redis://localhost:6379/0'):
        self.queue_name = queue_name
        self.kye_check = f"{queue_name}_check"
        self.redis = redis.from_url(url=url)

    async def check_add_queue_with_1min(self,tg_id:int) -> bool:
        """
        检查是否能添加队列。
        """
        check_key = f"{self.kye_check}_{tg_id}"
        if not self.redis.exists(check_key):
            self.redis.set(check_key,1,ex=60)
            return True
        
        return False

    async def start_length_monitor(self):
        """
        启动一个监视器，持续检查队列的长度，间隔一定时间。，每10秒检查一次长度。
        属性：
            queue_name (str): 正在监视的队列名称。
        用法：
            await start_length_monitor()
        """
        
        while True:
            length = await self.length()
            print(f"Current queue {self.queue_name} length: {length}")
            await asyncio.sleep(10)

    async def enqueue(self, msg_id, **kwargs):
        """
        将消息加入队列
        :param msg_id: 消息ID
        :param kwargs: 其他可变关键字参数
        """
        message_data = json.dumps({
            'msg_id': msg_id,
            **kwargs
        })
        self.redis.rpush(self.queue_name, message_data)

    async def dequeue(self):
        """
        从队列中取出一条消息
        :return: 返回一个包含msg_id和其他message参数的字典
        """
        result = self.redis.lpop(self.queue_name)
        log.info(f"RedisQueue: Dequeue message: {result}")
        if result:
            value = result.decode('utf-8') # type: ignore
            data = json.loads(value)
            return data
        return None

    async def length(self):
        """
        获取队列长度
        :return: 返回队列中的消息数量
        """
        return  self.redis.llen(self.queue_name)

    async def clear(self):
        """
        清空队列
        """
        await self.redis.delete(self.queue_name)
    