{"name": "front-admin-app", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "antd": "^5.24.1", "axios": "^1.7.9", "moment": "^2.30.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.2.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "env-cmd -f .env.dev react-scripts start", "build": "react-scripts build", "build:stag": "env-cmd -f .env.stag react-scripts build", "build:prod": "env-cmd -f .env.prod react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"env-cmd": "^10.1.0"}}