import React from 'react';
import { Layout, Menu } from 'antd';
import { Link, Route, Routes } from 'react-router-dom';
import { ManageMessages } from './pages/ManageMessages';
import { ManageDelImgCfg } from './pages/ManageDelImgCfg';
import { ManageKeyReplyMessages } from './pages/ManageKeyReplyMessages';

import { MailOutlined } from '@ant-design/icons';

const { Header, Content, Sider } = Layout;

export default function App() {
  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider width={200} className="site-layout-background">
        <Menu mode="inline" style={{ height: '100%', borderRight: 0 }}>
          <Menu.Item key="1" icon={<MailOutlined />}>
            <Link to="/manage-messages">定时消息设置</Link>
          </Menu.Item>
          <Menu.Item key="2" icon={<MailOutlined />}>
            <Link to="/group_img_cfg">群图片删除管理</Link>
          </Menu.Item>
          <Menu.Item key="3" icon={<MailOutlined />}>
            <Link to="/keyword-reply">自动回复消息</Link>
          </Menu.Item>
        </Menu>
      </Sider>
      <Layout style={{ padding: '0 24px 24px' }}>
        <Header className="site-layout-background" style={{ padding: 0 }} />
        <Content style={{ padding: 24, margin: 0, minHeight: 280 }}>
          <Routes>
            <Route path="/manage-messages" element={<ManageMessages />} />
            <Route path="/group_img_cfg" element={<ManageDelImgCfg />} />
            <Route path="/keyword-reply" element={<ManageKeyReplyMessages />} />
            <Route path="/" element={<ManageMessages />} />
          </Routes>
        </Content>
      </Layout>
    </Layout>
  );
}