import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { MessageForm } from '../components/MessageForm';


import moment from 'moment';

export const EditMessage = () => {
  const { id } = useParams();
  const [initialValues, setInitialValues] = useState(null);

  useEffect(() => {
    // 模拟从服务器获取消息数据
    const fetchMessage = async () => {
      // 这里可以替换为实际的 API 调用
      const message = {
        id:2,
        title: '示例标题',
        content: '示例内容',
        type: 'Interval',
        interval: 86400,
        startDate: moment(),
        endDate: moment().add(1, 'days'),
        enabled: true,
      };
      setInitialValues(message);
    };

    fetchMessage();
  }, [id]);

  return (
    <div>
      <h2>编辑定时消息</h2>
      <MessageForm initialValues={initialValues} />
    </div>
  );
};