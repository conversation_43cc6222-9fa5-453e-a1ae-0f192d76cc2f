import React, { useState } from 'react';
import { Button, Form, Modal } from 'antd';
import { MessageForm } from '../components/MessageForm';

export const NewMessage = () => {
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [form] = Form.useForm();
    const showModal = () => {
        setIsModalVisible(true);
    };

    const handleOk = () => {
        form.submit();
    };

    const handleCancel = () => {
        setIsModalVisible(false);
    };



    return (
        <div>
            <Button type="primary" onClick={showModal}>
                新建定时消息
            </Button>
            <Modal
                title="新建定时消息"
                visible={isModalVisible}
                onOk={handleOk}
                onCancel={handleCancel}
            >
                <MessageForm form={form} initialValues={ { id: 0 } } />
            </Modal>
        </div>
    );
};