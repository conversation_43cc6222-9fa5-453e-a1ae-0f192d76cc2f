import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Table, Button, Modal, Form, message } from 'antd';
// import { messages } from '../mocks/messageData';
import { KeywordSendMessageForm } from './KeywordSendMessageForm';
import moment from 'moment';

// 设置 axios 的 baseURL
axios.defaults.baseURL = process.env.REACT_APP_API_BASE_URL;

export const KeywordMessageList = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [initialValues, setInitialValues] = useState(null);
  const [form] = Form.useForm();

  const [messages, setMessages] = useState([]);
  const fetchMessages = async () => {
    try {
      const response = await axios.get('/auto_reply_rules');
      console.log('Received messages:', response.data);
      response.data.forEach(message => {
        message.status = message.is_enable ? '开启' : '关闭';
        if (message.keyword_type === 1) {
          message.keyword_type_desc = '完全匹配';
        }else if (message.keyword_type === 2) {
          message.keyword_type_desc = '包含关键词';
        }
        message.msg_text = message.msg_content.msg_text;
        if (message.msg_type === 2) {
          message.imageUrl = message.msg_content.image_url;
        }
      
      });
      setMessages(response.data);
    } catch (error) {
      message.error('获取自动回复列表失败');
      console.error('Error fetching messages:', error);
    }
  };
  useEffect(() => {
    fetchMessages();
  }, []);

  const showModal = (record) => {
    setInitialValues(null);
    setInitialValues({
      ...record
    });
    setIsModalVisible(true);
  };

  const handleOk = () => {
    form.submit();

  };

  const handleCancel = () => {

    setInitialValues(null);
    setIsModalVisible(false);
    fetchMessages(); // 关闭模态框时刷新页面
  };

  const columns = [
    { title: '消息配置id', dataIndex: 'rule_id', key: 'rule_id' },
    { title: '标题描述', dataIndex: 'rule_desc', key: 'rule_desc' },
    { title: '条件', dataIndex: 'keyword_type_desc', key: 'keyword_type_desc' },
    { title: '关键词', dataIndex: 'keyword_list', key: 'keyword_list' },
    { title: '状态', dataIndex: 'status', key: 'status' },
    {title: '优先级', dataIndex: 'pripority', key: 'pripority'},
    { title: '创建时间', dataIndex: 'created_at', key: 'created_at', render: (text) => moment(text).format('YYYY-MM-DD HH:mm:ss') },
    {
      title: '操作',
      key: 'action',
      render: (text, record) => (
        <>
          <Button type="primary" onClick={() => showModal(record)}>编辑</Button>
        </>
      ),
    },
  ];

  return (
    <div>
      <Button type="primary" style={{ marginBottom: 16 }} onClick={showModal}>
      新建自动回复消息
      </Button>
      <Modal
        title="新建自动回复消息"
        visible={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={600}
      >
        <KeywordSendMessageForm form={form} onClose={handleCancel} initialValues={initialValues} />
      </Modal>
      <Table dataSource={messages} columns={columns} rowKey="id" />
    </div>
  );
};