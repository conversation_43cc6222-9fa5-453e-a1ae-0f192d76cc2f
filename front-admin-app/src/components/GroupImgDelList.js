import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Table, Button, Modal, Form, message } from 'antd';
// import { messages } from '../mocks/messageData';
import { GroupImgDelCfgForm } from './GroupImgDelCfgForm';

// 设置 axios 的 baseURL
axios.defaults.baseURL = process.env.REACT_APP_API_BASE_URL;

export const GroupImgDelList = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [initialValues, setInitialValues] = useState(null);
  const [form] = Form.useForm();

  const [messages, setMessages] = useState([]);
  const fetchMessages = async () => {
    try { 
      const response = await axios.get('/group_msg/list_image_cfg');
      console.log('Received messages:', response.data);
      response.data.forEach(message => {
        message.status = message.isEnable ? '开启' : '关闭';
      });
        setMessages(response.data);
      } catch (error) {
        message.error('获取消息列表失败');
        console.error('Error fetching messages:', error);
      }
  };
  useEffect(() => {
    fetchMessages();
  }, []);

  const showModal = (record) => {
    setInitialValues(null);
    setInitialValues({
      ...record
    });
    setIsModalVisible(true);
  };

  const handleOk = () => {
    form.submit();
  };

  
  const handleCancel = () => {

    setInitialValues(null);
    setIsModalVisible(false);
    fetchMessages();
  };

  const columns = [
    { title: '配置id', dataIndex: 'id', key: 'id' },
    { title: '群id', dataIndex: 'groupId', key: 'groupId' },
    { title: '删除分钟数', dataIndex: 'deleteTime', key: 'delTime' },
    // { title: '开始时间', dataIndex: 'startDate', key: 'startDate' },
    { title: '状态', dataIndex: 'status', key: 'status' },
    {
      title: '操作',
      key: 'action',
      render: (text, record) => (
        <>
          <Button type="primary" onClick={() => showModal(record)}>编辑</Button>
        </>
      ),
    },
  ];

  return (
    <div>
      <Button type="primary" style={{ marginBottom: 16 }} onClick={showModal}>
        新建配置
      </Button>
      <Modal
        title="新建配置"
        visible={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={600}
      >
        <GroupImgDelCfgForm form={form} onClose={handleCancel} initialValues={initialValues} />
      </Modal>
      <Table dataSource={messages} columns={columns} rowKey="id" />
    </div>
  );
};