import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Table, Button, Modal, Form, message } from 'antd';
// import { messages } from '../mocks/messageData';
import { MessageForm } from './MessageForm';
import moment from 'moment';

// 设置 axios 的 baseURL
axios.defaults.baseURL = process.env.REACT_APP_API_BASE_URL;

export const MessageList = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [initialValues, setInitialValues] = useState(null);
  const [form] = Form.useForm();

  const [messages, setMessages] = useState([]);
  const fetchMessages = async () => {
    try {
      const response = await axios.get('/auto_send_msg/list');
      console.log('Received messages:', response.data);
      response.data.forEach(message => {
        message.status = message.enabled ? '开启' : '关闭';
        if (message.sendtype === 'Date') {
          message.sendtype = '指定日期';
        } else if (message.type === 'Interval') {
          message.sendtype = '间隔时间';
        } else if (message.type === 'Threshold') {
          message.sendtype = '消息累积数量';
        }
      });
      setMessages(response.data);
    } catch (error) {
      message.error('获取消息列表失败');
      console.error('Error fetching messages:', error);
    }
  };
  useEffect(() => {
    fetchMessages();
  }, []);

  const showModal = (record) => {
    setInitialValues(null);
    setInitialValues({
      ...record,
      startDate: moment(record.startDate).isValid() ? moment(record.startDate) : null,
      endDate: moment(record.endDate).isValid() ? moment(record.endDate) : null,
      runDate: moment(record.runDate).isValid() ? moment(record.runDate) : null,
    });
    setIsModalVisible(true);
  };

  const handleOk = () => {
    form.submit();

  };

  const handleCancel = () => {

    setInitialValues(null);
    setIsModalVisible(false);
    fetchMessages(); // 关闭模态框时刷新页面
  };

  const columns = [
    { title: '消息配置id', dataIndex: 'id', key: 'id' },
    { title: '标题', dataIndex: 'title', key: 'title' },
    { title: '类型', dataIndex: 'sendtype', key: 'sendtype' },
    { title: '间隔秒数', dataIndex: 'interval', key: 'interval' },
    // { title: '开始时间', dataIndex: 'startDate', key: 'startDate' },
    { title: '状态', dataIndex: 'status', key: 'status' },
    {
      title: '操作',
      key: 'action',
      render: (text, record) => (
        <>
          <Button type="primary" onClick={() => showModal(record)}>编辑</Button>
        </>
      ),
    },
  ];

  return (
    <div>
      <Button type="primary" style={{ marginBottom: 16 }} onClick={showModal}>
        新建定时消息
      </Button>
      <Modal
        title="新建定时消息"
        visible={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={600}
      >
        <MessageForm form={form} onClose={handleCancel} initialValues={initialValues} />
      </Modal>
      <Table dataSource={messages} columns={columns} rowKey="id" />
    </div>
  );
};