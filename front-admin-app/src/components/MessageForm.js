import React, { useEffect,useState } from 'react';
import { Form, Input, Select, DatePicker, Switch, message,Upload,Button } from 'antd';
import { UploadOutlined,DeleteOutlined } from '@ant-design/icons';
import axios from 'axios';

const { Option } = Select;
const { TextArea } = Input;

// 设置 axios 的 baseURL
axios.defaults.baseURL = process.env.REACT_APP_API_BASE_URL;



export const MessageForm = ({ form, initialValues = {}, onClose }) => {
    // const [fileList, setFileList] = useState([]);
    const [imageUrl, setImageUrl] = useState(null);
    const defaultValues = { id: 0, enabled: false, isDelete: false };
    const mergedInitialValues = { ...defaultValues, ...initialValues };

    useEffect(() => {
        form.setFieldsValue(mergedInitialValues);
        if(mergedInitialValues.imageUrl){
            setImageUrl(mergedInitialValues.imageUrl);
        }
    }, [initialValues]);

    console.log('MessageForm,Received initialValues:', mergedInitialValues);
    const onFinish = async (values) => {
        console.log('Received values:', values);
        if (imageUrl) {
            values.imageUrl = imageUrl;
        }
        try {
            // 发送 POST 请求到指定接口
            const response = await axios.post('/auto_send_msg/send_config', values);
            message.success('消息提交成功');

            console.log('Received response:', response);
            onClose()
        } catch (error) {
            message.error('消息提交失败');
            console.error('Error submitting message:', error);
        }
    };
    const handleUploadChange = async ({ file }) => {
        console.log('Received file:', file);
        // setFileList(fileList);
        if (file.status === 'uploading') {
            try {
                const formData = new FormData();
                formData.append('file', file.originFileObj);

                const response = await axios.post('/auto_send_msg/file_upload', formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                });
                console.log('Received response:', response);
                const url = response.data.imageUrl;
                console.log('Received image URL:', url);
                
                setImageUrl(url);

                message.success('图片上传成功');
                file.status = 'done';
            } catch (error) {
                message.error('图片上传失败');
                console.error('Error uploading image:', error);
            }
        }
    };

    const handleRemoveImage = () => {
        setImageUrl(null);
        message.success('图片删除成功');
    };
    return (
        <Form
            form={form}
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 16 }}
            layout="horizontal"
            onFinish={onFinish}
            initialValues={mergedInitialValues}
        >
            <Form.Item label='消息id' name="id" hidden>
                <Input />
            </Form.Item>
            <Form.Item label="标题" name="title" rules={[{ required: true }]}>
                <Input />
            </Form.Item>
            <Form.Item label="上传图片"  name="upload">
            {imageUrl && (
                    <div>
                        <img src={imageUrl} alt="图片预览" style={{ marginTop: '10px', maxWidth: '100%' }} />
                        <Button icon={<DeleteOutlined />} onClick={handleRemoveImage} style={{ marginTop: '10px' }}>
                            删除图片
                        </Button>
                    </div>
                )}
                <Upload name="file" listType="picture" showUploadList={false} customRequest={() => {}} onChange={handleUploadChange}>
                    <Button icon={<UploadOutlined />} onClick={() =>{}}>点击上传</Button>
                </Upload>
            </Form.Item>
            <Form.Item label="消息内容" name="content" rules={[{ required: true }]}>
                <TextArea rows={4} />
            </Form.Item>
            <Form.Item label="groupId" name="groupId" rules={[{ required: true }]}>
                <Input />
            </Form.Item>
            <Form.Item label="topicId" name="topicId">
                <Input />
            </Form.Item>
            <Form.Item label="类型" name="type" rules={[{ required: true }]}>
                <Select>
                    <Option value="Interval">重复间隔</Option>
                    <Option value="Date">指定日期</Option>
                    <Option value="Threshold">群消息累积数量</Option>
                </Select>
            </Form.Item>
            <Form.Item shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type}>
                {({ getFieldValue }) =>
                    getFieldValue('type') === 'Date' ? (
                        <Form.Item label="发送时间" name="runDate" rules={[{ required: true }]} labelCol={{ span: 7 }} wrapperCol={{ span: 12 }}>
                            <DatePicker showTime />
                        </Form.Item>
                    ) : getFieldValue('type') === 'Threshold' ? (
                        <Form.Item label="消息数量" name="messageThreshold" rules={[{ required: true }]} labelCol={{ span: 7 }} wrapperCol={{ span: 8 }}>
                            <Input type="number" />
                        </Form.Item>
                    ) : (
                        <Form.Item label="重复周期" name="interval" rules={[{ required: true }]} labelCol={{ span: 7 }} wrapperCol={{ span: 8 }}>
                            <Select>
                                <Option value={60}>每分钟</Option>
                                <Option value={300}>每5分钟</Option>
                                <Option value={600}>每10分钟</Option>
                                <Option value={900}>每15分钟</Option>
                                <Option value={1200}>每20分钟</Option>
                                <Option value={1800}>每30分钟</Option>
                                <Option value={3600}>每小时</Option>
                                <Option value={7200}>每2小时</Option>
                                <Option value={14400}>每4小时</Option>
                                <Option value={21600}>每6小时</Option>
                                <Option value={86400}>每天</Option>
                                <Option value={604800}>每周</Option>
                                <Option value={2592000}>每月</Option>
                            </Select>
                        </Form.Item>
                    )

                }
            </Form.Item>


            <Form.Item label="开始时间" name="startDate" rules={[{ required: true }]}>
                <DatePicker showTime />
            </Form.Item>
            <Form.Item label="结束时间" name="endDate" rules={[{ required: true }]}>
                <DatePicker showTime />
            </Form.Item>


            <Form.Item label="启用消息" name="enabled" valuePropName="checked">
                <Switch />
            </Form.Item>

            <Form.Item label="1min后删除消息" name="isDelete" valuePropName="checked">
                <Switch />
            </Form.Item>

            {/* <Form.Item wrapperCol={{ offset: 4, span: 16 }}>
        <Button type="primary" htmlType="submit">
          提交
        </Button>
        <Button style={{ marginLeft: '16px' }} htmlType="reset">
          重置
        </Button>
      </Form.Item> */}
        </Form>
    );
};