/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import { Form, Input, Select, DatePicker, Switch, message, Upload, Button } from 'antd';
// eslint-disable-next-line no-unused-vars
import { UploadOutlined, DeleteOutlined } from '@ant-design/icons';
import axios from 'axios';

const { Option } = Select;
const { TextArea } = Input;

// 设置 axios 的 baseURL
axios.defaults.baseURL = process.env.REACT_APP_API_BASE_URL;

export const GroupImgDelCfgForm = ({ form, initialValues = {}, onClose }) => {

    useEffect(() => {
        console.log('Received initialValues:', initialValues);
        form.setFieldsValue(initialValues);
        }, [form,initialValues]);

    const [loading, setLoading] = useState(false);

    //   useEffect(() => {
    //     if (id) {
    //       // 编辑模式，获取表单数据
    //       axios.get(`/api/group/${id}`).then(response => {
    //         form.setFieldsValue(response.data);
    //       }).catch(error => {
    //         message.error('获取表单数据失败');
    //       });
    //     }
    //   }, [id]);

    const onFinish = values => {
        setLoading(true);
        console.log('Received values:', values);

        const request = axios.post('/group_msg/image_del_cfg',
            values
        );
        request.then(() => {
            message.success('提交成功');
            onClose();
        }).catch(() => {
            message.error('提交失败');
        }).finally(() => {
            setLoading(false);
        });
    };

    return (
        <Form
            form={form}
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 16 }}
            layout="horizontal"
            onFinish={onFinish}
            initialValues={initialValues}>
            <Form.Item name="id" hidden>
                <Input type="hidden" />
            </Form.Item>
            <Form.Item name="groupId" label="群组ID" rules={[{ required: true, message: '请输入群组ID' }]}>
                <Input />
            </Form.Item>
            <Form.Item name="deleteTime" label="删除时间" rules={[{ required: true, message: '请输入删除时间' }]}>
                <Input type="number" />
            </Form.Item>
            <Form.Item name="isEnable" label="是否启用" valuePropName="checked">
                <Switch />
            </Form.Item>
            <Form.Item
                name="skipTgIds"
                label="跳过的用户ID列表(,英文逗号分隔)"
                labelCol={{ width: 12 }}
                wrapperCol={{ span: 16 }}
                rules={[
                    {
                        validator: (_, value) => {
                            if (!value || /^(\d+(,\d+)*)?$/.test(value)) {
                                return Promise.resolve();
                            }
                            return Promise.reject(new Error('请输入逗号分隔的数字或留空'));
                        },
                    },
                ]}
            >
                <TextArea rows={4} />
            </Form.Item>
            <Form.Item>
                {/* <Button type="primary" htmlType="submit" loading={loading}>
                    提交
                </Button> */}
            </Form.Item>
        </Form>
    );
};

