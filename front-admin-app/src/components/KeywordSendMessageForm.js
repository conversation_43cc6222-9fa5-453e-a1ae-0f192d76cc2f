import React, { useEffect, useState } from 'react';
import { Form, Input, Select, DatePicker, Switch, message, Upload, Button } from 'antd';
import { UploadOutlined, DeleteOutlined } from '@ant-design/icons';
import axios from 'axios';

const { Option } = Select;
const { TextArea } = Input;

// 设置 axios 的 baseURL
axios.defaults.baseURL = process.env.REACT_APP_API_BASE_URL;



export const KeywordSendMessageForm = ({ form, initialValues = {}, onClose }) => {
    // const [fileList, setFileList] = useState([]);
    const [imageUrl, setImageUrl] = useState(null);
    const defaultValues = { rule_id: 0, is_enable: false, del_bot_msg_delay: 0 };
    const mergedInitialValues = { ...defaultValues, ...initialValues };

    useEffect(() => {
        // 重置表单字段
        form.resetFields();
        setImageUrl(null);
        console.log('useEffect,Received mergedInitialValues:', mergedInitialValues);
        form.setFieldsValue(mergedInitialValues);
        if (mergedInitialValues.imageUrl) {
            setImageUrl(mergedInitialValues.imageUrl);
        }
    }, [initialValues]);

    console.log('MessageForm,Received initialValues:', mergedInitialValues);
    const onFinish = async (values) => {
        console.log('Received values:', values);
        if (imageUrl) {
            values.imageUrl = imageUrl;
            // 1代表文字模式，2代表图片模式
            values.msg_type = 2;
            values.msg_content = {
                msg_text: values.msg_text,
                msg_type: 2,
                image_url: imageUrl,
            };
        } else {
            values.msg_type = 1;
            values.msg_content = {
                msg_text: values.msg_text,
                msg_type: 1,
            };
        }
        try {
            if (values.rule_id === 0) {
                // create
                const response = await axios.post('/auto_reply_rules', values);
                message.success('消息提交成功');
                console.log('Received response:', response);
            } else {
                // update
                const url = `/auto_reply_rules/${values.rule_id}`
                const response = await axios.post(url, values);
                console.log('Received response:', response);
            }

            onClose()
        } catch (error) {
            message.error('消息提交失败');
            console.error('Error submitting message:', error);
        }
    };
    const handleUploadChange = async ({ file }) => {
        console.log('Received file:', file);
        // setFileList(fileList);
        if (file.status === 'uploading') {
            try {
                const formData = new FormData();
                formData.append('file', file.originFileObj);

                const response = await axios.post('/auto_send_msg/file_upload', formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                });
                console.log('Received response:', response);
                const url = response.data.imageUrl;
                console.log('Received image URL:', url);

                setImageUrl(url);

                message.success('图片上传成功');
                file.status = 'done';
            } catch (error) {
                message.error('图片上传失败');
                console.error('Error uploading image:', error);
            }
        }
    };

    const handleRemoveImage = () => {
        setImageUrl(null);
        message.success('图片删除成功');
    };
    return (
        <Form
            form={form}
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 16 }}
            layout="horizontal"
            onFinish={onFinish}
            initialValues={mergedInitialValues}
        >
            <Form.Item label='消息id' name="rule_id" hidden>
                <Input />
            </Form.Item>
            <Form.Item label="标题" name="rule_desc" rules={[{ required: true }]}>
                <Input />
            </Form.Item>
            <Form.Item label="条件：消息内容" style={{ width: 640 }} required >
                <Input.Group compact>
                    <Form.Item
                        name="keyword_type"
                        noStyle
                        rules={[{ required: true, message: '请选择条件类型' }]}
                    >
                        <Select placeholder="选择类型" style={{ width: '30%' }}>
                            <Option value={1}>完全匹配	</Option>
                            <Option value={2}>包含关键词</Option>
                        </Select>
                    </Form.Item>
                    <Form.Item
                        name="keyword_list"
                        noStyle
                        rules={[{ required: true, message: '请输入关键词,来分割' }]}
                    >
                        <Input placeholder="请输入关键词,来分割" style={{ width: '60%' }} />
                    </Form.Item>
                </Input.Group>
            </Form.Item>

            <Form.Item label="上传图片" name="upload">
                {imageUrl && (
                    <div>
                        <img src={imageUrl} alt="图片预览" style={{ marginTop: '10px', maxWidth: '100%' }} />
                        <Button icon={<DeleteOutlined />} onClick={handleRemoveImage} style={{ marginTop: '10px' }}>
                            删除图片
                        </Button>
                    </div>
                )}
                <Upload name="file" listType="picture" showUploadList={false} customRequest={() => { }} onChange={handleUploadChange}>
                    <Button icon={<UploadOutlined />} onClick={() => { }}>点击上传</Button>
                </Upload>
            </Form.Item>
            <Form.Item label="消息内容" name="msg_text" rules={[{ required: true }]}>
                <TextArea rows={4} />
            </Form.Item>
            <Form.Item label="group_id" name="group_id" rules={[{ required: true }]}>
                <Input />
            </Form.Item>

            <Form.Item label="启用" name="is_enable" valuePropName="checked">
                <Switch />
            </Form.Item>

            <Form.Item label="优先级" name="pripority" initialValue={1} rules={[{ required: true }]}>
                <Input type="number" min={1} />
            </Form.Item>

            <Form.Item label="删除bot发送消息" name="del_bot_msg_delay" labelCol={{ style: { width: '160px' } }} rules={[{ required: true }]}>
                <Select>
                    <Option value={0}>不删除</Option>
                    <Option value={60}>1min后删除消息</Option>
                    <Option value={300}>5min后删除消息</Option>
                </Select>
            </Form.Item>
        </Form>
    );
};