from datetime import datetime
import json
import logging
import os

import pytz
import requests

from persistence.models.models import ChannelDeliveryPlan, ChannelDeliveryPlanRelation
from utils import date_util, file_util
import pandas as pd


log = logging.getLogger(__name__)


def read_role_tags():
    role_tags_strs: list[str] = []
    file_path = os.path.join(os.getcwd(), "properties/role/role_sub_tags.csv")
    with open(file_path, "r") as f:
        lines = f.readlines()
        role_tags_strs.extend(lines)
    role_tags_strs = role_tags_strs[1:]
    ret = []
    for mid_str in role_tags_strs:
        mid_str = mid_str.split(",")
        sub_tags = mid_str[4].split(" ")
        sub_tags = [x for x in sub_tags if x.strip()]
        ret.append(
            {
                "id": int(mid_str[0]),
                "card_name": mid_str[1],
                "role_name": mid_str[3],
                "sub_tags": sub_tags,
            }
        )
    return ret


def read_line_sub_tags():
    sub_tags_strs: list[str] = []
    file_path = os.path.join(os.getcwd(), "properties/sub_tags.txt")
    with open(file_path, "r") as f:
        lines = f.readlines()
        sub_tags_strs.extend(lines)
    sub_tags = [x.replace("\n", "").split(",") for x in sub_tags_strs]
    return sub_tags


translate_buffer = {}

file_paths = file_util.list_file_path_by_dir(
    os.path.join(os.getcwd(), "properties/translate")
)
for file_path in file_paths:
    with open(file_path, "r") as f:
        ps = json.load(f)
        # ps如果是dict，添加到translate_buffer
        if isinstance(ps, dict):
            log.info(f"load file success:{file_path}")
            translate_buffer.update(ps)


def load_translate(key: str, lang: str) -> str:
    if key not in translate_buffer:
        return None
    if lang not in translate_buffer[key]:
        return None
    return translate_buffer[key][lang]


def _load_csv_config_by_url(url: str, temp_file_prefix: str) -> pd.DataFrame | None:
    data = None
    try:
        local_file_path = f"/tmp/{temp_file_prefix}.xlsx"
        response = requests.get(url)
        with open(local_file_path, "wb") as f:
            f.write(response.content)
        f.close()
        return pd.read_excel(local_file_path, sheet_name=0)
    except Exception as e:
        log.error("load_channel_config_by_url error", e)
        return None


def load_channel_config_by_url():
    file_url = "https://docs.google.com/spreadsheets/d/e/2PACX-1vR-JPKohN2n6DD27779dTit3u6nVvpQec8eizMj34vIKWh9fSpbZUx6wWQbbfppbeMfPqPCh7mehQrb/pub?output=xlsx"
    data = _load_csv_config_by_url(file_url, "ad_channel")
    if data is None:
        log.error("load_channel_config_by_url error")
        return None, None
    ad_details_list = []
    relation_list = []

    for _, row in data.iterrows():
        if (
            not row["计划ID"]
            or not row["投放渠道"]
            or not row["广告类型"]
            or not row["广告位"]
            or not row["支出价格"]
            or not row["有效期（天）"]
            or not row["起始时间"]
            or not row["结束时间"]
            or not row["渠道号"]
        ):
            log.error(f"load_channel_config_by_url error, row:{row}")
            return None, None

        # Timestamp格式转换为yyyyMMdd格式
        start_time: pd.Timestamp = pd.to_datetime(row["起始时间"])
        end_time: pd.Timestamp = pd.to_datetime(row["结束时间"])
        utc_8 = pytz.timezone("Etc/GMT+8")
        current_time = datetime.now(utc_8)
        start_date_time = start_time.to_pydatetime().astimezone(utc_8)
        end_date_time = end_time.to_pydatetime().astimezone(utc_8)
        days = (
            (end_date_time if current_time > end_date_time else current_time)
            - start_date_time
        ).days + 1
        ad_detail = ChannelDeliveryPlan(
            plan_id=row["计划ID"],
            channel=row["投放渠道"],
            ad_type=row["广告类型"],
            ad_position=row["广告位"],
            pay_fee_usdt=float(row["支出价格"]),
            pay_fee=int(float(row["支出价格"]) * 7.5 * 100000),
            pay_fee_day=int(
                float(row["支出价格"]) * 7.5 * 100000 / int(row["有效期（天）"])
            ),
            complete_pay_fee=int(
                float(row["支出价格"]) * 7.5 * 100000 / int(row["有效期（天）"])
            )
            * days,
            sum_days=int(row["有效期（天）"]),
            complete_days=days,
            start_at=start_time.strftime("%Y%m%d"),
            end_at=end_time.strftime("%Y%m%d"),
            remarks=row["备注"],
        )
        channel_code = str(row["渠道号"])
        codes = channel_code.split(",")
        if len(codes) == 1:
            codes = channel_code.split("、")
        if codes:
            re_list = [
                ChannelDeliveryPlanRelation(
                    plan_id=ad_detail.plan_id, from_user_id=int(code)
                )
                for code in codes
            ]
            relation_list.extend(re_list)
            ad_detail.from_user_ids = [mid.from_user_id for mid in re_list]

        ad_details_list.append(ad_detail)
    return ad_details_list, relation_list


def load_tag_config():
    url = "https://docs.google.com/spreadsheets/d/e/2PACX-1vTDVRJRon2JEEUAPYRRgV6JGybRMxS2rolNUQURPo59ltsAgLs-CdeapTPRt2RvES-ZPRuTWrSoNfAQ/pub?output=xlsx"
    data = _load_csv_config_by_url(url, "tag_config")
    ret = {}
    for _, row in data.iterrows():
        if not row["tags"] or not row["sub_tags"]:
            log.error(f"load_channel_config_by_url error, row:{row}")
            return {}
        sub_tags_str = row["sub_tags"].replace(" ", "")
        ret[row["tags"]] = sub_tags_str.split("、")
    return ret


def load_sub_tag_config():
    url = "https://docs.google.com/spreadsheets/d/e/2PACX-1vSkNEGI44NiMpeVl8-wvRl37lKY_Lj4ARwUj_PdsmqqR65RcxjSQiz87XxEShlHxFqrsXztaBuIbh_Y/pub?output=xlsx"
    data = _load_csv_config_by_url(url, "sub_tag_config")
    ret = []
    for _, row in data.iterrows():
        if not row["sub_tag_name"]:
            log.error(f"load_channel_config_by_url error, row:{row}")
            return []
        ret.append(
            {
                "sub_tag_name": row["sub_tag_name"],
                "sub_tag_name_en": row["sub_tag_name_en"],
                "sub_tag_name_zh_TW": row["sub_tag_name_zh_TW"],
            }
        )
    return ret
